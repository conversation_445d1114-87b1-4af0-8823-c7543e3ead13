interactions:
- request:
    body: '{"messages": [{"role": "system", "content": "You are Researcher. You''re
      an expert researcher, specialized in technology, software engineering, AI and
      startups. You work as a freelancer and is now working on doing research and
      analysis for a new customer.\nYour personal goal is: Make the best research
      and analysis on content about AI and AI agents\nYou ONLY have access to the
      following tools, and should NEVER make up tools that are not listed here:\n\nTool
      Name: Another Test Tool\nTool Arguments: {''query'': {''description'': ''Query
      to process'', ''type'': ''str''}}\nTool Description: Another test tool\n\nUse
      the following format:\n\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [Another Test Tool], just the name, exactly
      as it''s written.\nAction Input: the input to the action, just a simple python
      dictionary, enclosed in curly braces, using \" to wrap keys and values.\nObservation:
      the result of the action\n\nOnce all necessary information is gathered:\n\nThought:
      I now know the final answer\nFinal Answer: the final answer to the original
      input question"}, {"role": "user", "content": "\nCurrent Task: Write a test
      task\n\nThis is the expect criteria for your final answer: Test output\nyou
      MUST return the actual complete content as the final answer, not a summary.\n\nBegin!
      This is VERY important to you, use the tools available and give your best Final
      Answer, your job depends on it!\n\nThought:"}], "model": "gpt-4o", "stop": ["\nObservation:"],
      "stream": false}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1525'
      content-type:
      - application/json
      cookie:
      - _cfuvid=eQzzWvIXDS8Me1OIBdCG5F1qFyVfAo3sumvYRE7J41E-1734965710778-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.52.1
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.52.1
      x-stainless-raw-response:
      - 'true'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AmjYyKbTn42DzaLVOjDvJpLubTjSq\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1736178252,\n  \"model\": \"gpt-4o-2024-08-06\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Action: Another Test Tool\\nAction Input:
      {\\\"query\\\": \\\"AI and AI agents\\\"}\",\n        \"refusal\": null\n      },\n
      \     \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n
      \ \"usage\": {\n    \"prompt_tokens\": 295,\n    \"completion_tokens\": 18,\n
      \   \"total_tokens\": 313,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"system_fingerprint\":
      \"fp_5f20662549\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8fdcd3fc9a56bf66-ATL
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 06 Jan 2025 15:44:12 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=X1fuDKrQrN8tU.uxjB0murgJXWXcPtlNLnD7xUrAKTs-1736178252-*******-AME9VZZVtEpqX9.BEN_Kj9pI9uK3sIJc2LdbuPsP3wULKxF4Il6r8ghX0to2wpcYsGWbJXSqWP.dQz4vGf_Gbw;
        path=/; expires=Mon, 06-Jan-25 16:14:12 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=mv42xOepGYaNopc5ovT9Ajamw5rJrze8tlWTik8lfrk-1736178252935-*******-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '632'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999644'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_9276753b2200fc95c74fc43c9d7d84a6
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are Researcher. You''re
      an expert researcher, specialized in technology, software engineering, AI and
      startups. You work as a freelancer and is now working on doing research and
      analysis for a new customer.\nYour personal goal is: Make the best research
      and analysis on content about AI and AI agents\nYou ONLY have access to the
      following tools, and should NEVER make up tools that are not listed here:\n\nTool
      Name: Another Test Tool\nTool Arguments: {''query'': {''description'': ''Query
      to process'', ''type'': ''str''}}\nTool Description: Another test tool\n\nUse
      the following format:\n\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [Another Test Tool], just the name, exactly
      as it''s written.\nAction Input: the input to the action, just a simple python
      dictionary, enclosed in curly braces, using \" to wrap keys and values.\nObservation:
      the result of the action\n\nOnce all necessary information is gathered:\n\nThought:
      I now know the final answer\nFinal Answer: the final answer to the original
      input question"}, {"role": "user", "content": "\nCurrent Task: Write a test
      task\n\nThis is the expect criteria for your final answer: Test output\nyou
      MUST return the actual complete content as the final answer, not a summary.\n\nBegin!
      This is VERY important to you, use the tools available and give your best Final
      Answer, your job depends on it!\n\nThought:"}, {"role": "assistant", "content":
      "Action: Another Test Tool\nAction Input: {\"query\": \"AI and AI agents\"}\nObservation:
      Another processed: AI and AI agents"}], "model": "gpt-4o", "stop": ["\nObservation:"],
      "stream": false}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1687'
      content-type:
      - application/json
      cookie:
      - _cfuvid=mv42xOepGYaNopc5ovT9Ajamw5rJrze8tlWTik8lfrk-1736178252935-*******-604800000;
        __cf_bm=X1fuDKrQrN8tU.uxjB0murgJXWXcPtlNLnD7xUrAKTs-1736178252-*******-AME9VZZVtEpqX9.BEN_Kj9pI9uK3sIJc2LdbuPsP3wULKxF4Il6r8ghX0to2wpcYsGWbJXSqWP.dQz4vGf_Gbw
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.52.1
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.52.1
      x-stainless-raw-response:
      - 'true'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AmjYzChV9s4D4qOJJvTvBAt3kRh7n\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1736178253,\n  \"model\": \"gpt-4o-2024-08-06\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I now know the final answer\\nFinal
      Answer: Another processed: AI and AI agents\",\n        \"refusal\": null\n
      \     },\n      \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n
      \ ],\n  \"usage\": {\n    \"prompt_tokens\": 326,\n    \"completion_tokens\":
      19,\n    \"total_tokens\": 345,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"system_fingerprint\":
      \"fp_5f20662549\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8fdcd4011938bf66-ATL
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 06 Jan 2025 15:44:15 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '2488'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999613'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_5e3a1a90ef91ff4f12d5b84e396beccc
    http_version: HTTP/1.1
    status_code: 200
version: 1
