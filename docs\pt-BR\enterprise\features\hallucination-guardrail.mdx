---
title: Proteção contra Alucinações
description: "Previna e detecte alucinações de IA nas suas tarefas do CrewAI"
icon: "shield-check"
---

## Visão Geral

A Proteção contra Alucinações é um recurso empresarial que valida o conteúdo gerado por IA para garantir que esteja fundamentado em fatos e não contenha alucinações. Ela analisa as saídas das tarefas em relação ao contexto de referência e fornece feedback detalhado quando é detectado conteúdo potencialmente alucinado.

## O que são Alucinações?

Alucinações em IA ocorrem quando modelos de linguagem geram conteúdos que parecem plausíveis, mas estão factualmente incorretos ou não são suportados pelo contexto fornecido. A Proteção contra Alucinações ajuda a prevenir esses problemas por meio de:

- Comparação das saídas com o contexto de referência
- Avaliação da fidelidade ao material de origem
- Fornecimento de feedback detalhado sobre conteúdo problemático
- Suporte a limiares personalizados para rigor da validação

## Uso Básico

### Configurando a Proteção

```python
from crewai.tasks.hallucination_guardrail import HallucinationGuardrail
from crewai import LLM

# Uso básico - utiliza o expected_output da tarefa como contexto
protecao = HallucinationGuardrail(
    llm=LLM(model="gpt-4o-mini")
)

# Com contexto de referência explícito
protecao_com_contexto = HallucinationGuardrail(
    context="IA ajuda em várias tarefas, incluindo análise e geração.",
    llm=LLM(model="gpt-4o-mini")
)
```

### Adicionando às Tarefas

```python
from crewai import Task

# Crie sua tarefa com a proteção
minha_tarefa = Task(
    description="Escreva um resumo sobre as capacidades da IA",
    expected_output="Um resumo factual baseado no contexto fornecido",
    agent=meu_agente,
    guardrail=protecao  # Adiciona a proteção para validar a saída
)
```

## Configuração Avançada

### Validação com Limiar Personalizado

Para validação mais rigorosa, é possível definir um limiar de fidelidade personalizado (escala de 0-10):

```python
# Proteção rigorosa exigindo alta pontuação de fidelidade
protecao_rigorosa = HallucinationGuardrail(
    context="Computação quântica utiliza qubits que existem em estados de superposição.",
    llm=LLM(model="gpt-4o-mini"),
    threshold=8.0  # Requer pontuação >= 8 para validar
)
```

### Incluindo Contexto da Resposta de Ferramentas

Se sua tarefa utiliza ferramentas, você pode incluir as respostas das ferramentas para validação mais precisa:

```python
# Proteção com contexto de resposta da ferramenta
protecao_clima = HallucinationGuardrail(
    context="Informações meteorológicas atuais para o local solicitado",
    llm=LLM(model="gpt-4o-mini"),
    tool_response="API do Clima retornou: Temperatura 22°C, Umidade 65%, Céu limpo"
)
```

## Como Funciona

### Processo de Validação

1. **Análise de Contexto**: A proteção compara a saída da tarefa com o contexto de referência fornecido
2. **Pontuação de Fidelidade**: Usa um avaliador interno para atribuir uma pontuação de fidelidade (0-10)
3. **Determinação do Veredito**: Determina se o conteúdo é fiel ou contém alucinações
4. **Verificação de Limiar**: Se um limiar personalizado for definido, valida contra essa pontuação
5. **Geração de Feedback**: Fornece motivos detalhados caso a validação falhe

### Lógica de Validação

- **Modo Padrão**: Utiliza validação baseada em veredito (FIÉL vs ALUCINADO)
- **Modo com Limiar**: Requer que a pontuação de fidelidade atinja ou supere o limiar especificado
- **Tratamento de Erros**: Lida com erros de avaliação de forma elegante e fornece feedback informativo

## Resultados da Proteção

A proteção retorna resultados estruturados indicando o status da validação:

```python
# Exemplo de estrutura de resultado da proteção
{
    "valid": False,
    "feedback": "Content appears to be hallucinated (score: 4.2/10, verdict: HALLUCINATED). The output contains information not supported by the provided context."
}
```

### Propriedades do Resultado

- **valid**: Booleano indicando se a saída passou na validação
- **feedback**: Explicação detalhada quando a validação falha, incluindo:
  - Pontuação de fidelidade
  - Classificação do veredito
  - Motivos específicos para a falha

## Integração com o Sistema de Tarefas

### Validação Automática

Quando uma proteção é adicionada à tarefa, ela valida automaticamente a saída antes da tarefa ser marcada como concluída:

```python
# Fluxo de validação de saída da tarefa
task_output = meu_agente.execute_task(minha_tarefa)
resultado_validacao = protecao(task_output)

if resultado_validacao.valid:
    # Tarefa concluída com sucesso
    return task_output
else:
    # Tarefa falha com feedback de validação
    raise ValidationError(resultado_validacao.feedback)
```

### Rastreamento de Eventos

A proteção se integra ao sistema de eventos do CrewAI para fornecer observabilidade:

- **Validação Iniciada**: Quando a avaliação da proteção começa
- **Validação Concluída**: Quando a avaliação termina com resultados
- **Falha na Validação**: Quando ocorrem erros técnicos durante a avaliação

## Melhores Práticas

### Diretrizes para o Contexto

<Steps>
  <Step title="Forneça Contexto Abrangente">
    Inclua todas as informações factuais relevantes nas quais a IA deve basear sua saída:

    ```python
    contexto = """
    Empresa XYZ foi fundada em 2020 e é especializada em soluções de energia renovável.
    Possui 150 funcionários e faturou R$ 50 milhões em 2023.
    Seus principais produtos incluem painéis solares e turbinas eólicas.
    """
    ```
  </Step>

  <Step title="Mantenha o Contexto Relevante">
    Inclua apenas informações diretamente relacionadas à tarefa para evitar confusão:

    ```python
    # Bom: Contexto focado
    contexto = "O clima atual em Nova York é 18°C com chuva leve."

    # Evite: Informações irrelevantes
    contexto = "The weather is 18°C. The city has 8 million people. Traffic is heavy."
    ```
  </Step>

  <Step title="Atualize o Contexto Regularmente">
    Certifique-se de que seu contexto de referência reflita informações atuais e precisas.
  </Step>
</Steps>

### Seleção de Limiar

<Steps>
  <Step title="Comece com a Validação Padrão">
    Inicie sem limiares personalizados para entender a performance inicial.
  </Step>

  <Step title="Ajuste Conforme as Necessidades">
    - **Conteúdo crítico**: Use limiar 8-10 para máxima precisão
    - **Conteúdo geral**: Use limiar 6-7 para validação equilibrada
    - **Conteúdo criativo**: Use limiar 4-5 ou validação padrão baseada em veredito
  </Step>

  <Step title="Monitore e Itere">
    Acompanhe os resultados da validação e ajuste os limiares conforme falsos positivos/negativos.
  </Step>
</Steps>

## Considerações de Performance

### Impacto no Tempo de Execução

- **Sobrecarga de Validação**: Cada proteção adiciona ~1-3 segundos por tarefa
- **Eficiência do LLM**: Escolha modelos eficientes para avaliação (ex: gpt-4o-mini)

### Otimização de Custos

- **Seleção de Modelo**: Utilize modelos menores e eficientes para avaliação da proteção
- **Tamanho do Contexto**: Mantenha o contexto de referência conciso, mas abrangente
- **Cache**: Considere armazenar resultados de validação para conteúdos repetidos

## Solução de Problemas

<Accordion title="Validação Sempre Falha">
  **Possíveis Causas:**
  - Contexto muito restrito ou não relacionado à saída da tarefa
  - Limiar configurado alto demais para o tipo de conteúdo
  - Contexto de referência desatualizado

  **Soluções:**
  - Revise e atualize o contexto para corresponder aos requisitos da tarefa
  - Reduza o limiar ou utilize validação padrão baseada em veredito
  - Certifique-se de que o contexto esteja atual e correto
</Accordion>

<Accordion title="Falsos Positivos (Conteúdo Válido Marcado como Inválido)">
  **Possíveis Causas:**
  - Limiar alto demais para tarefas criativas ou interpretativas
  - Contexto não cobre todos os aspectos válidos da saída
  - Modelo de avaliação excessivamente conservador

  **Soluções:**
  - Reduza o limiar ou utilize validação padrão
  - Expanda o contexto para incluir um espectro maior do conteúdo aceitável
  - Teste com diferentes modelos de avaliação
</Accordion>

<Accordion title="Erros de Avaliação">
  **Possíveis Causas:**
  - Problemas de conexão de rede
  - Modelo LLM indisponível ou com limite de uso
  - Saída ou contexto da tarefa em formato inadequado

  **Soluções:**
  - Verifique a conectividade de rede e o status do serviço LLM
  - Implemente lógica de retentativas para falhas transitórias
  - Valide o formato da saída da tarefa antes da avaliação da proteção
</Accordion>

<Card title="Precisa de Ajuda?" icon="headset" href="mailto:<EMAIL>">
  Entre em contato com nosso suporte para assistência na configuração ou solução de problemas da proteção contra alucinações.
</Card>