interactions:
- request:
    body: '{"messages": [{"role": "system", "content": "You are Researcher. You''re
      an expert researcher, specialized in technology, software engineering, AI and
      startups. You work as a freelancer and are now working on doing research and
      analysis for a new customer.\nYour personal goal is: Make the best research
      and analysis on content about AI and AI agents. Use the tool provided to you.\nYou
      ONLY have access to the following tools, and should NEVER make up tools that
      are not listed here:\n\nTool Name: what amazing tool\nTool Arguments: {}\nTool
      Description: My tool\n\nIMPORTANT: Use the following format in your response:\n\n```\nThought:
      you should always think about what to do\nAction: the action to take, only one
      name of [what amazing tool], just the name, exactly as it''s written.\nAction
      Input: the input to the action, just a simple JSON object, enclosed in curly
      braces, using \" to wrap keys and values.\nObservation: the result of the action\n```\n\nOnce
      all necessary information is gathered, return the following format:\n\n```\nThought:
      I now know the final answer\nFinal Answer: the final answer to the original
      input question\n```"}, {"role": "user", "content": "\nCurrent Task: Give me
      a list of 5 interesting ideas to explore for an article, what makes them unique
      and interesting.\n\nThis is the expected criteria for your final answer: Bullet
      point list of 5 interesting ideas.\nyou MUST return the actual complete content
      as the final answer, not a summary.\n\nBegin! This is VERY important to you,
      use the tools available and give your best Final Answer, your job depends on
      it!\n\nThought:"}], "model": "gpt-4o-mini", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1666'
      content-type:
      - application/json
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.12
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BNNPLDDeGQYegE6neZK6ogJmDOMYs\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1744911223,\n  \"model\": \"gpt-4o-mini-2024-07-18\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"I need to generate interesting ideas
      for an article related to AI and AI agents. \\n\\nAction: what amazing tool
      \ \\nAction Input: {}  \",\n        \"refusal\": null,\n        \"annotations\":
      []\n      },\n      \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n
      \   }\n  ],\n  \"usage\": {\n    \"prompt_tokens\": 326,\n    \"completion_tokens\":
      28,\n    \"total_tokens\": 354,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_0392822090\"\n}\n"
    headers:
      CF-RAY:
      - 931dab4c79581b2e-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 17 Apr 2025 17:33:44 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '736'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999620'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_0767caa75d1851f392ea34a68763b1bc
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: !!binary |
      CqzaAQokCiIKDHNlcnZpY2UubmFtZRISChBjcmV3QUktdGVsZW1ldHJ5EoLaAQoSChBjcmV3YWku
      dGVsZW1ldHJ5EpYIChAm+VDVVrKTGFLYrGmqyFN/EgiVIKrLvczC6SoMQ3JldyBDcmVhdGVkMAE5
      uBnOOYMrNxhBWHDXOYMrNxhKGwoOY3Jld2FpX3ZlcnNpb24SCQoHMC4xMTQuMEobCg5weXRob25f
      dmVyc2lvbhIJCgczLjExLjEySi4KCGNyZXdfa2V5EiIKIDVlNmVmZmU2ODBhNWQ5N2RjMzg3M2Ix
      NDgyNWNjZmEzSjEKB2NyZXdfaWQSJgokMzI4MzZhOTctMGIyYi00MTAwLTgxZDYtYmIwZWJjY2I2
      ZDYyShwKDGNyZXdfcHJvY2VzcxIMCgpzZXF1ZW50aWFsShEKC2NyZXdfbWVtb3J5EgIQAEoaChRj
      cmV3X251bWJlcl9vZl90YXNrcxICGAFKGwoVY3Jld19udW1iZXJfb2ZfYWdlbnRzEgIYAUo6ChBj
      cmV3X2ZpbmdlcnByaW50EiYKJGQ5YmEwZDE3LTE5YjMtNGQ5Yi04ZTNmLThiMjNhOGM0YzgyM0o7
      ChtjcmV3X2ZpbmdlcnByaW50X2NyZWF0ZWRfYXQSHAoaMjAyNS0wNC0xN1QxNDozMzo0My4yMzg4
      MzhKzQIKC2NyZXdfYWdlbnRzEr0CCroCW3sia2V5IjogIjkyZTdlYjE5MTY2NGM5MzU3ODVlZDdk
      NDI0MGEyOTRkIiwgImlkIjogImMzMWMwNzRmLTg5Y2YtNGRkNi04ZTY2LWM3NDM1OTc0NmNkMSIs
      ICJyb2xlIjogIlNjb3JlciIsICJ2ZXJib3NlPyI6IGZhbHNlLCAibWF4X2l0ZXIiOiAyNSwgIm1h
      eF9ycG0iOiBudWxsLCAiZnVuY3Rpb25fY2FsbGluZ19sbG0iOiAiIiwgImxsbSI6ICJncHQtNG8t
      bWluaSIsICJkZWxlZ2F0aW9uX2VuYWJsZWQ/IjogZmFsc2UsICJhbGxvd19jb2RlX2V4ZWN1dGlv
      bj8iOiBmYWxzZSwgIm1heF9yZXRyeV9saW1pdCI6IDIsICJ0b29sc19uYW1lcyI6IFtdfV1K+wEK
      CmNyZXdfdGFza3MS7AEK6QFbeyJrZXkiOiAiMjdlZjM4Y2M5OWRhNGE4ZGVkNzBlZDQwNmU0NGFi
      ODYiLCAiaWQiOiAiYTNiODU1ODAtZTVjNC00YmU2LWI0ZmItMDU1NDU2Y2RkZWJkIiwgImFzeW5j
      X2V4ZWN1dGlvbj8iOiBmYWxzZSwgImh1bWFuX2lucHV0PyI6IGZhbHNlLCAiYWdlbnRfcm9sZSI6
      ICJTY29yZXIiLCAiYWdlbnRfa2V5IjogIjkyZTdlYjE5MTY2NGM5MzU3ODVlZDdkNDI0MGEyOTRk
      IiwgInRvb2xzX25hbWVzIjogW119XXoCGAGFAQABAAASgAQKED9XhldOUhgSlcVbrT/HuRwSCCF7
      8YboEaZnKgxUYXNrIENyZWF0ZWQwATlYmeU5gys3GEHwx+c5gys3GEouCghjcmV3X2tleRIiCiA1
      ZTZlZmZlNjgwYTVkOTdkYzM4NzNiMTQ4MjVjY2ZhM0oxCgdjcmV3X2lkEiYKJDMyODM2YTk3LTBi
      MmItNDEwMC04MWQ2LWJiMGViY2NiNmQ2MkouCgh0YXNrX2tleRIiCiAyN2VmMzhjYzk5ZGE0YThk
      ZWQ3MGVkNDA2ZTQ0YWI4NkoxCgd0YXNrX2lkEiYKJGEzYjg1NTgwLWU1YzQtNGJlNi1iNGZiLTA1
      NTQ1NmNkZGViZEo6ChBjcmV3X2ZpbmdlcnByaW50EiYKJGQ5YmEwZDE3LTE5YjMtNGQ5Yi04ZTNm
      LThiMjNhOGM0YzgyM0o6ChB0YXNrX2ZpbmdlcnByaW50EiYKJDc0OTEwMDcxLTM1MWQtNDljNy1h
      YmZlLTJmMWZhNWUzZTEyYko7Cht0YXNrX2ZpbmdlcnByaW50X2NyZWF0ZWRfYXQSHAoaMjAyNS0w
      NC0xN1QxNDozMzo0My4yMzg4MDFKOwoRYWdlbnRfZmluZ2VycHJpbnQSJgokYzMyNmIyMWUtYWJh
      YS00N2ZjLWE0NGQtMzk2NTQ4ZjczZTRiegIYAYUBAAEAABKYCAoQH3AbYnQyty/P5gHiJzrjLxII
      PR8P3tNCS74qDENyZXcgQ3JlYXRlZDABOYgCyj6DKzcYQSD71D6DKzcYShsKDmNyZXdhaV92ZXJz
      aW9uEgkKBzAuMTE0LjBKGwoOcHl0aG9uX3ZlcnNpb24SCQoHMy4xMS4xMkouCghjcmV3X2tleRIi
      CiA1ZTZlZmZlNjgwYTVkOTdkYzM4NzNiMTQ4MjVjY2ZhM0oxCgdjcmV3X2lkEiYKJDBkY2JhOGY4
      LTdhYzQtNDdmMC04ZWIxLWE0ZTJkODFjOGZkYkoeCgxjcmV3X3Byb2Nlc3MSDgoMaGllcmFyY2hp
      Y2FsShEKC2NyZXdfbWVtb3J5EgIQAEoaChRjcmV3X251bWJlcl9vZl90YXNrcxICGAFKGwoVY3Jl
      d19udW1iZXJfb2ZfYWdlbnRzEgIYAUo6ChBjcmV3X2ZpbmdlcnByaW50EiYKJDJiYzYzYWE1LWU5
      YzYtNDAxNi1hMTdiLWFmZDM5ZWJlYmEwNko7ChtjcmV3X2ZpbmdlcnByaW50X2NyZWF0ZWRfYXQS
      HAoaMjAyNS0wNC0xN1QxNDozMzo0My4zMjI3OTlKzQIKC2NyZXdfYWdlbnRzEr0CCroCW3sia2V5
      IjogIjkyZTdlYjE5MTY2NGM5MzU3ODVlZDdkNDI0MGEyOTRkIiwgImlkIjogImYyY2Y4YWRlLTdj
      YmYtNDkwMS1hODMwLWE3ZDkxODA4NjI3NCIsICJyb2xlIjogIlNjb3JlciIsICJ2ZXJib3NlPyI6
      IGZhbHNlLCAibWF4X2l0ZXIiOiAyNSwgIm1heF9ycG0iOiBudWxsLCAiZnVuY3Rpb25fY2FsbGlu
      Z19sbG0iOiAiIiwgImxsbSI6ICJncHQtNG8tbWluaSIsICJkZWxlZ2F0aW9uX2VuYWJsZWQ/Ijog
      ZmFsc2UsICJhbGxvd19jb2RlX2V4ZWN1dGlvbj8iOiBmYWxzZSwgIm1heF9yZXRyeV9saW1pdCI6
      IDIsICJ0b29sc19uYW1lcyI6IFtdfV1K+wEKCmNyZXdfdGFza3MS7AEK6QFbeyJrZXkiOiAiMjdl
      ZjM4Y2M5OWRhNGE4ZGVkNzBlZDQwNmU0NGFiODYiLCAiaWQiOiAiMWY3MTljNTktOTJjMC00NGU1
      LWFmMjUtNzIwYjE1NWE1Njg1IiwgImFzeW5jX2V4ZWN1dGlvbj8iOiBmYWxzZSwgImh1bWFuX2lu
      cHV0PyI6IGZhbHNlLCAiYWdlbnRfcm9sZSI6ICJTY29yZXIiLCAiYWdlbnRfa2V5IjogIjkyZTdl
      YjE5MTY2NGM5MzU3ODVlZDdkNDI0MGEyOTRkIiwgInRvb2xzX25hbWVzIjogW119XXoCGAGFAQAB
      AAASgAQKEIVZhLW3PhEOxntJYQn8IYkSCDNELyrmM+eYKgxUYXNrIENyZWF0ZWQwATlYr+0+gys3
      GEGIJO4+gys3GEouCghjcmV3X2tleRIiCiA1ZTZlZmZlNjgwYTVkOTdkYzM4NzNiMTQ4MjVjY2Zh
      M0oxCgdjcmV3X2lkEiYKJDBkY2JhOGY4LTdhYzQtNDdmMC04ZWIxLWE0ZTJkODFjOGZkYkouCgh0
      YXNrX2tleRIiCiAyN2VmMzhjYzk5ZGE0YThkZWQ3MGVkNDA2ZTQ0YWI4NkoxCgd0YXNrX2lkEiYK
      JDFmNzE5YzU5LTkyYzAtNDRlNS1hZjI1LTcyMGIxNTVhNTY4NUo6ChBjcmV3X2ZpbmdlcnByaW50
      EiYKJDJiYzYzYWE1LWU5YzYtNDAxNi1hMTdiLWFmZDM5ZWJlYmEwNko6ChB0YXNrX2ZpbmdlcnBy
      aW50EiYKJGJmMDU5YjBiLWFlYmYtNGIzMS04YTc4LTA2ZTlmMjcyZDQ2MEo7Cht0YXNrX2Zpbmdl
      cnByaW50X2NyZWF0ZWRfYXQSHAoaMjAyNS0wNC0xN1QxNDozMzo0My4zMjI3NTVKOwoRYWdlbnRf
      ZmluZ2VycHJpbnQSJgokY2IzZWQ2ZGQtNzEzNC00YTc3LThiMjctZWIwZGRkZGZlMjdiegIYAYUB
      AAEAABKdAQoQ6S1CnqyOxKwRN/Vq7X81HRIIso7ugOmXnjEqClRvb2wgVXNhZ2UwATnAIk4/gys3
      GEE4YlU/gys3GEobCg5jcmV3YWlfdmVyc2lvbhIJCgcwLjExNC4wSigKCXRvb2xfbmFtZRIbChlE
      ZWxlZ2F0ZSB3b3JrIHRvIGNvd29ya2VySg4KCGF0dGVtcHRzEgIYAXoCGAGFAQABAAASlggKENaM
      zYnzHCL03v+Ihe5ZidsSCIXobzKG03Z6KgxDcmV3IENyZWF0ZWQwATlQ8uM/gys3GEFIfOk/gys3
      GEobCg5jcmV3YWlfdmVyc2lvbhIJCgcwLjExNC4wShsKDnB5dGhvbl92ZXJzaW9uEgkKBzMuMTEu
      MTJKLgoIY3Jld19rZXkSIgogNWU2ZWZmZTY4MGE1ZDk3ZGMzODczYjE0ODI1Y2NmYTNKMQoHY3Jl
      d19pZBImCiQ5ZmYzZmU1ZC01YmYyLTRlNzEtODU0ZS05OTAyZGYxYzIxYTRKHAoMY3Jld19wcm9j
      ZXNzEgwKCnNlcXVlbnRpYWxKEQoLY3Jld19tZW1vcnkSAhAAShoKFGNyZXdfbnVtYmVyX29mX3Rh
      c2tzEgIYAUobChVjcmV3X251bWJlcl9vZl9hZ2VudHMSAhgBSjoKEGNyZXdfZmluZ2VycHJpbnQS
      JgokOTkwOTQ1YmUtOTczMS00ZTQxLTg5ZDAtYzEzMjljNGQ3NjQ5SjsKG2NyZXdfZmluZ2VycHJp
      bnRfY3JlYXRlZF9hdBIcChoyMDI1LTA0LTE3VDE0OjMzOjQzLjM0MTIyM0rNAgoLY3Jld19hZ2Vu
      dHMSvQIKugJbeyJrZXkiOiAiOTJlN2ViMTkxNjY0YzkzNTc4NWVkN2Q0MjQwYTI5NGQiLCAiaWQi
      OiAiMGM3NDhlNDgtOGNmNC00ZDJhLWFlYWMtZDY5OTUzMDkwZThmIiwgInJvbGUiOiAiU2NvcmVy
      IiwgInZlcmJvc2U/IjogZmFsc2UsICJtYXhfaXRlciI6IDI1LCAibWF4X3JwbSI6IG51bGwsICJm
      dW5jdGlvbl9jYWxsaW5nX2xsbSI6ICIiLCAibGxtIjogImdwdC00by1taW5pIiwgImRlbGVnYXRp
      b25fZW5hYmxlZD8iOiBmYWxzZSwgImFsbG93X2NvZGVfZXhlY3V0aW9uPyI6IGZhbHNlLCAibWF4
      X3JldHJ5X2xpbWl0IjogMiwgInRvb2xzX25hbWVzIjogW119XUr7AQoKY3Jld190YXNrcxLsAQrp
      AVt7ImtleSI6ICIyN2VmMzhjYzk5ZGE0YThkZWQ3MGVkNDA2ZTQ0YWI4NiIsICJpZCI6ICI2NzBj
      MjdhOS1kYTc3LTRhNTQtYmYxYS1mM2M0YjVlNTcwNDkiLCAiYXN5bmNfZXhlY3V0aW9uPyI6IGZh
      bHNlLCAiaHVtYW5faW5wdXQ/IjogZmFsc2UsICJhZ2VudF9yb2xlIjogIlNjb3JlciIsICJhZ2Vu
      dF9rZXkiOiAiOTJlN2ViMTkxNjY0YzkzNTc4NWVkN2Q0MjQwYTI5NGQiLCAidG9vbHNfbmFtZXMi
      OiBbXX1degIYAYUBAAEAABKABAoQ0PunZB/jswUd8i8ahTz20BIIFtRhrWbDsGIqDFRhc2sgQ3Jl
      YXRlZDABOejM8z+DKzcYQZAu9D+DKzcYSi4KCGNyZXdfa2V5EiIKIDVlNmVmZmU2ODBhNWQ5N2Rj
      Mzg3M2IxNDgyNWNjZmEzSjEKB2NyZXdfaWQSJgokOWZmM2ZlNWQtNWJmMi00ZTcxLTg1NGUtOTkw
      MmRmMWMyMWE0Si4KCHRhc2tfa2V5EiIKIDI3ZWYzOGNjOTlkYTRhOGRlZDcwZWQ0MDZlNDRhYjg2
      SjEKB3Rhc2tfaWQSJgokNjcwYzI3YTktZGE3Ny00YTU0LWJmMWEtZjNjNGI1ZTU3MDQ5SjoKEGNy
      ZXdfZmluZ2VycHJpbnQSJgokOTkwOTQ1YmUtOTczMS00ZTQxLTg5ZDAtYzEzMjljNGQ3NjQ5SjoK
      EHRhc2tfZmluZ2VycHJpbnQSJgokMjczZmM3YjYtZGJmYS00MzYyLWEwZTEtNzhhNjJjNDY0OTlh
      SjsKG3Rhc2tfZmluZ2VycHJpbnRfY3JlYXRlZF9hdBIcChoyMDI1LTA0LTE3VDE0OjMzOjQzLjM0
      MTE5NUo7ChFhZ2VudF9maW5nZXJwcmludBImCiQwNTc3MWI4Ny04ZGIzLTRhZGYtOWJhZC0yNzcw
      ZjgzYTZiYTN6AhgBhQEAAQAAEpgIChA5RacttE9X5amPuTPHLuYDEgiZ/RIthMTU5yoMQ3JldyBD
      cmVhdGVkMAE5EC+lQIMrNxhBsJGsQIMrNxhKGwoOY3Jld2FpX3ZlcnNpb24SCQoHMC4xMTQuMEob
      Cg5weXRob25fdmVyc2lvbhIJCgczLjExLjEySi4KCGNyZXdfa2V5EiIKIDVlNmVmZmU2ODBhNWQ5
      N2RjMzg3M2IxNDgyNWNjZmEzSjEKB2NyZXdfaWQSJgokMTc5MzYxNTItNDJmMi00YmY3LWIzOTEt
      ZTU0MDU1ZTY2NGU4Sh4KDGNyZXdfcHJvY2VzcxIOCgxoaWVyYXJjaGljYWxKEQoLY3Jld19tZW1v
      cnkSAhAAShoKFGNyZXdfbnVtYmVyX29mX3Rhc2tzEgIYAUobChVjcmV3X251bWJlcl9vZl9hZ2Vu
      dHMSAhgBSjoKEGNyZXdfZmluZ2VycHJpbnQSJgokNjI0ZTI1NjEtMDFkOC00YmNkLWJhMjEtZDcx
      ZTQ0MTZkNGJiSjsKG2NyZXdfZmluZ2VycHJpbnRfY3JlYXRlZF9hdBIcChoyMDI1LTA0LTE3VDE0
      OjMzOjQzLjM1Mzg3M0rNAgoLY3Jld19hZ2VudHMSvQIKugJbeyJrZXkiOiAiOTJlN2ViMTkxNjY0
      YzkzNTc4NWVkN2Q0MjQwYTI5NGQiLCAiaWQiOiAiYTEyMTFiNmYtMzFhMy00MzY4LWI5YzItZDNh
      NjA1NTZlOTk2IiwgInJvbGUiOiAiU2NvcmVyIiwgInZlcmJvc2U/IjogZmFsc2UsICJtYXhfaXRl
      ciI6IDI1LCAibWF4X3JwbSI6IG51bGwsICJmdW5jdGlvbl9jYWxsaW5nX2xsbSI6ICIiLCAibGxt
      IjogImdwdC00by1taW5pIiwgImRlbGVnYXRpb25fZW5hYmxlZD8iOiBmYWxzZSwgImFsbG93X2Nv
      ZGVfZXhlY3V0aW9uPyI6IGZhbHNlLCAibWF4X3JldHJ5X2xpbWl0IjogMiwgInRvb2xzX25hbWVz
      IjogW119XUr7AQoKY3Jld190YXNrcxLsAQrpAVt7ImtleSI6ICIyN2VmMzhjYzk5ZGE0YThkZWQ3
      MGVkNDA2ZTQ0YWI4NiIsICJpZCI6ICI3ZjJjNGIxMi00MjNhLTRmMTctOTZiMS0zZmEyNmUzMDM3
      MmMiLCAiYXN5bmNfZXhlY3V0aW9uPyI6IGZhbHNlLCAiaHVtYW5faW5wdXQ/IjogZmFsc2UsICJh
      Z2VudF9yb2xlIjogIlNjb3JlciIsICJhZ2VudF9rZXkiOiAiOTJlN2ViMTkxNjY0YzkzNTc4NWVk
      N2Q0MjQwYTI5NGQiLCAidG9vbHNfbmFtZXMiOiBbXX1degIYAYUBAAEAABKABAoQG2i2Mf2AK81I
      gFDuyTZ4hxIIGTCH8bEW9REqDFRhc2sgQ3JlYXRlZDABOajZvECDKzcYQbArvUCDKzcYSi4KCGNy
      ZXdfa2V5EiIKIDVlNmVmZmU2ODBhNWQ5N2RjMzg3M2IxNDgyNWNjZmEzSjEKB2NyZXdfaWQSJgok
      MTc5MzYxNTItNDJmMi00YmY3LWIzOTEtZTU0MDU1ZTY2NGU4Si4KCHRhc2tfa2V5EiIKIDI3ZWYz
      OGNjOTlkYTRhOGRlZDcwZWQ0MDZlNDRhYjg2SjEKB3Rhc2tfaWQSJgokN2YyYzRiMTItNDIzYS00
      ZjE3LTk2YjEtM2ZhMjZlMzAzNzJjSjoKEGNyZXdfZmluZ2VycHJpbnQSJgokNjI0ZTI1NjEtMDFk
      OC00YmNkLWJhMjEtZDcxZTQ0MTZkNGJiSjoKEHRhc2tfZmluZ2VycHJpbnQSJgokZjViNmU5YjUt
      ZjcxMi00YzM3LTkyYjAtMWFjNzQ2ZTYzYWJjSjsKG3Rhc2tfZmluZ2VycHJpbnRfY3JlYXRlZF9h
      dBIcChoyMDI1LTA0LTE3VDE0OjMzOjQzLjM1Mzg0M0o7ChFhZ2VudF9maW5nZXJwcmludBImCiQ1
      ZjhkM2YwYS1lODZhLTRiMmUtYWFmMC1jOWMyMDg2N2M1ODR6AhgBhQEAAQAAEpwBChBCoZs2F2Pk
      GqD1dlo+B0jIEgh/8w+r7HLXDioKVG9vbCBVc2FnZTABOfhQHUGDKzcYQeChJUGDKzcYShsKDmNy
      ZXdhaV92ZXJzaW9uEgkKBzAuMTE0LjBKJwoJdG9vbF9uYW1lEhoKGEFzayBxdWVzdGlvbiB0byBj
      b3dvcmtlckoOCghhdHRlbXB0cxICGAF6AhgBhQEAAQAAEpYIChAtKHG1/6WFVXbuKiUU/tulEggA
      5PFku/BBtSoMQ3JldyBDcmVhdGVkMAE5qOuzQYMrNxhBYNO5QYMrNxhKGwoOY3Jld2FpX3ZlcnNp
      b24SCQoHMC4xMTQuMEobCg5weXRob25fdmVyc2lvbhIJCgczLjExLjEySi4KCGNyZXdfa2V5EiIK
      IDVlNmVmZmU2ODBhNWQ5N2RjMzg3M2IxNDgyNWNjZmEzSjEKB2NyZXdfaWQSJgokNWQzODQyMmQt
      NTk2MC00NGQ0LWFmZjctYWM5MDFiMjU1NzM5ShwKDGNyZXdfcHJvY2VzcxIMCgpzZXF1ZW50aWFs
      ShEKC2NyZXdfbWVtb3J5EgIQAEoaChRjcmV3X251bWJlcl9vZl90YXNrcxICGAFKGwoVY3Jld19u
      dW1iZXJfb2ZfYWdlbnRzEgIYAUo6ChBjcmV3X2ZpbmdlcnByaW50EiYKJGZhNWYxODc1LWRiYTQt
      NDc2MS04ZDk4LTliNzlmNDg2ZTNlY0o7ChtjcmV3X2ZpbmdlcnByaW50X2NyZWF0ZWRfYXQSHAoa
      MjAyNS0wNC0xN1QxNDozMzo0My4zNzE2MzZKzQIKC2NyZXdfYWdlbnRzEr0CCroCW3sia2V5Ijog
      IjkyZTdlYjE5MTY2NGM5MzU3ODVlZDdkNDI0MGEyOTRkIiwgImlkIjogIjgwZDg1ZDY3LTg3M2Qt
      NDFhNi05MzY1LWJkODVjYTc5MGI2MyIsICJyb2xlIjogIlNjb3JlciIsICJ2ZXJib3NlPyI6IGZh
      bHNlLCAibWF4X2l0ZXIiOiAyNSwgIm1heF9ycG0iOiBudWxsLCAiZnVuY3Rpb25fY2FsbGluZ19s
      bG0iOiAiIiwgImxsbSI6ICJncHQtNG8tbWluaSIsICJkZWxlZ2F0aW9uX2VuYWJsZWQ/IjogZmFs
      c2UsICJhbGxvd19jb2RlX2V4ZWN1dGlvbj8iOiBmYWxzZSwgIm1heF9yZXRyeV9saW1pdCI6IDIs
      ICJ0b29sc19uYW1lcyI6IFtdfV1K+wEKCmNyZXdfdGFza3MS7AEK6QFbeyJrZXkiOiAiMjdlZjM4
      Y2M5OWRhNGE4ZGVkNzBlZDQwNmU0NGFiODYiLCAiaWQiOiAiZWYwNmQ3NTEtZWY2Yy00YjJiLWI2
      MTQtMmVhMmU1NGM0MjVlIiwgImFzeW5jX2V4ZWN1dGlvbj8iOiBmYWxzZSwgImh1bWFuX2lucHV0
      PyI6IGZhbHNlLCAiYWdlbnRfcm9sZSI6ICJTY29yZXIiLCAiYWdlbnRfa2V5IjogIjkyZTdlYjE5
      MTY2NGM5MzU3ODVlZDdkNDI0MGEyOTRkIiwgInRvb2xzX25hbWVzIjogW119XXoCGAGFAQABAAAS
      gAQKEJUYH+zYJdlQxAU/SEz07wwSCHIyR0YIxKoQKgxUYXNrIENyZWF0ZWQwATnYg8NBgys3GEFQ
      7cNBgys3GEouCghjcmV3X2tleRIiCiA1ZTZlZmZlNjgwYTVkOTdkYzM4NzNiMTQ4MjVjY2ZhM0ox
      CgdjcmV3X2lkEiYKJDVkMzg0MjJkLTU5NjAtNDRkNC1hZmY3LWFjOTAxYjI1NTczOUouCgh0YXNr
      X2tleRIiCiAyN2VmMzhjYzk5ZGE0YThkZWQ3MGVkNDA2ZTQ0YWI4NkoxCgd0YXNrX2lkEiYKJGVm
      MDZkNzUxLWVmNmMtNGIyYi1iNjE0LTJlYTJlNTRjNDI1ZUo6ChBjcmV3X2ZpbmdlcnByaW50EiYK
      JGZhNWYxODc1LWRiYTQtNDc2MS04ZDk4LTliNzlmNDg2ZTNlY0o6ChB0YXNrX2ZpbmdlcnByaW50
      EiYKJDA1ZTU2ZTIzLWI5YjgtNDIwMy05MWYwLTY2ZmE5MDgzNzYzNUo7Cht0YXNrX2ZpbmdlcnBy
      aW50X2NyZWF0ZWRfYXQSHAoaMjAyNS0wNC0xN1QxNDozMzo0My4zNzE2MDJKOwoRYWdlbnRfZmlu
      Z2VycHJpbnQSJgokZGZiMDEzYTItNzg0MC00NDFhLTg1YzMtMzI0OWQ1OGJhNmIzegIYAYUBAAEA
      ABKWCAoQ67KSQgBBFIpwJAjqKwKTNxIIPHptHAGKIGYqDENyZXcgQ3JlYXRlZDABOeB7T0KDKzcY
      QVhEVUKDKzcYShsKDmNyZXdhaV92ZXJzaW9uEgkKBzAuMTE0LjBKGwoOcHl0aG9uX3ZlcnNpb24S
      CQoHMy4xMS4xMkouCghjcmV3X2tleRIiCiA1ZTZlZmZlNjgwYTVkOTdkYzM4NzNiMTQ4MjVjY2Zh
      M0oxCgdjcmV3X2lkEiYKJDRmOTE0YWZhLTVlMTAtNDU3Ni1hYjJjLWVkZmNlZWQzYTZiYkocCgxj
      cmV3X3Byb2Nlc3MSDAoKc2VxdWVudGlhbEoRCgtjcmV3X21lbW9yeRICEABKGgoUY3Jld19udW1i
      ZXJfb2ZfdGFza3MSAhgBShsKFWNyZXdfbnVtYmVyX29mX2FnZW50cxICGAFKOgoQY3Jld19maW5n
      ZXJwcmludBImCiQzODFkOTIwYi1iMGE1LTRiNGUtYTQ0OS1kZjg5OGNjZjVmZDdKOwobY3Jld19m
      aW5nZXJwcmludF9jcmVhdGVkX2F0EhwKGjIwMjUtMDQtMTdUMTQ6MzM6NDMuMzgxODc1Ss0CCgtj
      cmV3X2FnZW50cxK9Agq6Alt7ImtleSI6ICI5MmU3ZWIxOTE2NjRjOTM1Nzg1ZWQ3ZDQyNDBhMjk0
      ZCIsICJpZCI6ICI5ZjVhZTRmOC02MjkwLTQ5NTUtOGI2OC00YmNjOTM5ZjhhMDkiLCAicm9sZSI6
      ICJTY29yZXIiLCAidmVyYm9zZT8iOiBmYWxzZSwgIm1heF9pdGVyIjogMjUsICJtYXhfcnBtIjog
      bnVsbCwgImZ1bmN0aW9uX2NhbGxpbmdfbGxtIjogIiIsICJsbG0iOiAiZ3B0LTRvLW1pbmkiLCAi
      ZGVsZWdhdGlvbl9lbmFibGVkPyI6IGZhbHNlLCAiYWxsb3dfY29kZV9leGVjdXRpb24/IjogZmFs
      c2UsICJtYXhfcmV0cnlfbGltaXQiOiAyLCAidG9vbHNfbmFtZXMiOiBbXX1dSvsBCgpjcmV3X3Rh
      c2tzEuwBCukBW3sia2V5IjogIjI3ZWYzOGNjOTlkYTRhOGRlZDcwZWQ0MDZlNDRhYjg2IiwgImlk
      IjogIjViODI1OWU3LWI2Y2YtNGJlYi1iYTRiLWY3MDg4Y2E4YWM3NiIsICJhc3luY19leGVjdXRp
      b24/IjogZmFsc2UsICJodW1hbl9pbnB1dD8iOiBmYWxzZSwgImFnZW50X3JvbGUiOiAiU2NvcmVy
      IiwgImFnZW50X2tleSI6ICI5MmU3ZWIxOTE2NjRjOTM1Nzg1ZWQ3ZDQyNDBhMjk0ZCIsICJ0b29s
      c19uYW1lcyI6IFtdfV16AhgBhQEAAQAAEoAEChCDE114uxBCLFPLICeD1DCNEgiFJ3IambqX4yoM
      VGFzayBDcmVhdGVkMAE5+P9iQoMrNxhBcGljQoMrNxhKLgoIY3Jld19rZXkSIgogNWU2ZWZmZTY4
      MGE1ZDk3ZGMzODczYjE0ODI1Y2NmYTNKMQoHY3Jld19pZBImCiQ0ZjkxNGFmYS01ZTEwLTQ1NzYt
      YWIyYy1lZGZjZWVkM2E2YmJKLgoIdGFza19rZXkSIgogMjdlZjM4Y2M5OWRhNGE4ZGVkNzBlZDQw
      NmU0NGFiODZKMQoHdGFza19pZBImCiQ1YjgyNTllNy1iNmNmLTRiZWItYmE0Yi1mNzA4OGNhOGFj
      NzZKOgoQY3Jld19maW5nZXJwcmludBImCiQzODFkOTIwYi1iMGE1LTRiNGUtYTQ0OS1kZjg5OGNj
      ZjVmZDdKOgoQdGFza19maW5nZXJwcmludBImCiRmZWIzZDVjYy0yMzEwLTRhNDgtOWQ5My1jMzQ5
      MTI0MGU3NTlKOwobdGFza19maW5nZXJwcmludF9jcmVhdGVkX2F0EhwKGjIwMjUtMDQtMTdUMTQ6
      MzM6NDMuMzgxODQ4SjsKEWFnZW50X2ZpbmdlcnByaW50EiYKJDdhYzY3YTkzLTkwNjctNGJjNC1i
      NmFmLTcxZWVjZmRjNzEzOHoCGAGFAQABAAASmAgKEGmCIFMw9GayxClAketnXWsSCPvb3mKDZYhn
      KgxDcmV3IENyZWF0ZWQwATkA88JGgys3GEGAhMpGgys3GEobCg5jcmV3YWlfdmVyc2lvbhIJCgcw
      LjExNC4wShsKDnB5dGhvbl92ZXJzaW9uEgkKBzMuMTEuMTJKLgoIY3Jld19rZXkSIgogNWU2ZWZm
      ZTY4MGE1ZDk3ZGMzODczYjE0ODI1Y2NmYTNKMQoHY3Jld19pZBImCiRmNmE1MGVkMy02ODk5LTQ4
      MTItODVkNy1iNDZlYTQyNzUwN2FKHgoMY3Jld19wcm9jZXNzEg4KDGhpZXJhcmNoaWNhbEoRCgtj
      cmV3X21lbW9yeRICEABKGgoUY3Jld19udW1iZXJfb2ZfdGFza3MSAhgBShsKFWNyZXdfbnVtYmVy
      X29mX2FnZW50cxICGAFKOgoQY3Jld19maW5nZXJwcmludBImCiQzYzRlM2VmZS01YmQ0LTRkNDgt
      YThmNS0yOGY0NDdhNDI0OGRKOwobY3Jld19maW5nZXJwcmludF9jcmVhdGVkX2F0EhwKGjIwMjUt
      MDQtMTdUMTQ6MzM6NDMuNDU2MDg4Ss0CCgtjcmV3X2FnZW50cxK9Agq6Alt7ImtleSI6ICI5MmU3
      ZWIxOTE2NjRjOTM1Nzg1ZWQ3ZDQyNDBhMjk0ZCIsICJpZCI6ICJmNzMwNTE0Mi1jOTViLTQ0MmQt
      YjJiOS1jYTVhN2IzMzZlYjMiLCAicm9sZSI6ICJTY29yZXIiLCAidmVyYm9zZT8iOiBmYWxzZSwg
      Im1heF9pdGVyIjogMjUsICJtYXhfcnBtIjogbnVsbCwgImZ1bmN0aW9uX2NhbGxpbmdfbGxtIjog
      IiIsICJsbG0iOiAiZ3B0LTRvLW1pbmkiLCAiZGVsZWdhdGlvbl9lbmFibGVkPyI6IGZhbHNlLCAi
      YWxsb3dfY29kZV9leGVjdXRpb24/IjogZmFsc2UsICJtYXhfcmV0cnlfbGltaXQiOiAyLCAidG9v
      bHNfbmFtZXMiOiBbXX1dSvsBCgpjcmV3X3Rhc2tzEuwBCukBW3sia2V5IjogIjI3ZWYzOGNjOTlk
      YTRhOGRlZDcwZWQ0MDZlNDRhYjg2IiwgImlkIjogIjU5MDRiMDg5LTMzNzYtNDZhMy04NWU1LTRk
      ZTc0NDQyYTljYyIsICJhc3luY19leGVjdXRpb24/IjogZmFsc2UsICJodW1hbl9pbnB1dD8iOiBm
      YWxzZSwgImFnZW50X3JvbGUiOiAiU2NvcmVyIiwgImFnZW50X2tleSI6ICI5MmU3ZWIxOTE2NjRj
      OTM1Nzg1ZWQ3ZDQyNDBhMjk0ZCIsICJ0b29sc19uYW1lcyI6IFtdfV16AhgBhQEAAQAAEoAEChD9
      DkCz0iVsHA4a1S3+yN8lEgjsMseCiYSvFioMVGFzayBDcmVhdGVkMAE5+ATcRoMrNxhB0F7cRoMr
      NxhKLgoIY3Jld19rZXkSIgogNWU2ZWZmZTY4MGE1ZDk3ZGMzODczYjE0ODI1Y2NmYTNKMQoHY3Jl
      d19pZBImCiRmNmE1MGVkMy02ODk5LTQ4MTItODVkNy1iNDZlYTQyNzUwN2FKLgoIdGFza19rZXkS
      IgogMjdlZjM4Y2M5OWRhNGE4ZGVkNzBlZDQwNmU0NGFiODZKMQoHdGFza19pZBImCiQ1OTA0YjA4
      OS0zMzc2LTQ2YTMtODVlNS00ZGU3NDQ0MmE5Y2NKOgoQY3Jld19maW5nZXJwcmludBImCiQzYzRl
      M2VmZS01YmQ0LTRkNDgtYThmNS0yOGY0NDdhNDI0OGRKOgoQdGFza19maW5nZXJwcmludBImCiQ3
      MjExMmY3OS1iMWU3LTRkYTctYTg4YS00NjU3NTJiZTIwZDdKOwobdGFza19maW5nZXJwcmludF9j
      cmVhdGVkX2F0EhwKGjIwMjUtMDQtMTdUMTQ6MzM6NDMuNDU2MDU5SjsKEWFnZW50X2ZpbmdlcnBy
      aW50EiYKJGJiMzQ0NzIxLTE3M2QtNGRmNS1iMWRmLWQ2NjBkZjZlZmVjZnoCGAGFAQABAAASnAEK
      EOg/b+TBCd7kQOaEdNwvgZoSCGYJctohLuuaKgpUb29sIFVzYWdlMAE58JA0R4MrNxhBqOM9R4Mr
      NxhKGwoOY3Jld2FpX3ZlcnNpb24SCQoHMC4xMTQuMEonCgl0b29sX25hbWUSGgoYQXNrIHF1ZXN0
      aW9uIHRvIGNvd29ya2VySg4KCGF0dGVtcHRzEgIYAXoCGAGFAQABAAASlwoKEMn/aYwi4Jc7AohP
      Y7puRXsSCB83OjVnCZfLKgxDcmV3IENyZWF0ZWQwATlw8dVHgys3GEGA9NtHgys3GEobCg5jcmV3
      YWlfdmVyc2lvbhIJCgcwLjExNC4wShsKDnB5dGhvbl92ZXJzaW9uEgkKBzMuMTEuMTJKLgoIY3Jl
      d19rZXkSIgogZDQyNjA4MzNhYjBjMjBiYjQ0OTIyYzc5OWFhOTZiNGFKMQoHY3Jld19pZBImCiQx
      ZjA0NjU0YS05ODE5LTQ0MTEtOTVlZC1kMmMwMTJlNWU3YjJKHAoMY3Jld19wcm9jZXNzEgwKCnNl
      cXVlbnRpYWxKEQoLY3Jld19tZW1vcnkSAhAAShoKFGNyZXdfbnVtYmVyX29mX3Rhc2tzEgIYAkob
      ChVjcmV3X251bWJlcl9vZl9hZ2VudHMSAhgBSjoKEGNyZXdfZmluZ2VycHJpbnQSJgokYjhhYjU0
      YzEtYjFjZS00OGIyLTlkODMtNDVkNzkyMTkxOWQ0SjsKG2NyZXdfZmluZ2VycHJpbnRfY3JlYXRl
      ZF9hdBIcChoyMDI1LTA0LTE3VDE0OjMzOjQzLjQ3NDIyN0rlAgoLY3Jld19hZ2VudHMS1QIK0gJb
      eyJrZXkiOiAiOTJlN2ViMTkxNjY0YzkzNTc4NWVkN2Q0MjQwYTI5NGQiLCAiaWQiOiAiMjg5YzIz
      NzgtNWUxOC00YzJiLWE1NDMtNzVkOTk5YWUyYWQyIiwgInJvbGUiOiAiU2NvcmVyIiwgInZlcmJv
      c2U/IjogdHJ1ZSwgIm1heF9pdGVyIjogMjUsICJtYXhfcnBtIjogbnVsbCwgImZ1bmN0aW9uX2Nh
      bGxpbmdfbGxtIjogImdwdC0zLjUtdHVyYm8tMDEyNSIsICJsbG0iOiAiZ3B0LTQtMDEyNS1wcmV2
      aWV3IiwgImRlbGVnYXRpb25fZW5hYmxlZD8iOiBmYWxzZSwgImFsbG93X2NvZGVfZXhlY3V0aW9u
      PyI6IGZhbHNlLCAibWF4X3JldHJ5X2xpbWl0IjogMiwgInRvb2xzX25hbWVzIjogW119XUrkAwoK
      Y3Jld190YXNrcxLVAwrSA1t7ImtleSI6ICIyN2VmMzhjYzk5ZGE0YThkZWQ3MGVkNDA2ZTQ0YWI4
      NiIsICJpZCI6ICJiNTAxMWUwNi02YTdmLTQzYWItYWQzNy1mNGI4ODBhMmJlYjgiLCAiYXN5bmNf
      ZXhlY3V0aW9uPyI6IGZhbHNlLCAiaHVtYW5faW5wdXQ/IjogZmFsc2UsICJhZ2VudF9yb2xlIjog
      IlNjb3JlciIsICJhZ2VudF9rZXkiOiAiOTJlN2ViMTkxNjY0YzkzNTc4NWVkN2Q0MjQwYTI5NGQi
      LCAidG9vbHNfbmFtZXMiOiBbXX0sIHsia2V5IjogIjYwOWRlZTM5MTA4OGNkMWM4N2I4ZmE2NmFh
      NjdhZGJlIiwgImlkIjogIjRiODE5NjQ5LTYyMjQtNGQ3Mi1hZDFkLTM0ODZhYzBkODQwNSIsICJh
      c3luY19leGVjdXRpb24/IjogZmFsc2UsICJodW1hbl9pbnB1dD8iOiBmYWxzZSwgImFnZW50X3Jv
      bGUiOiAiU2NvcmVyIiwgImFnZW50X2tleSI6ICI5MmU3ZWIxOTE2NjRjOTM1Nzg1ZWQ3ZDQyNDBh
      Mjk0ZCIsICJ0b29sc19uYW1lcyI6IFtdfV16AhgBhQEAAQAAEoAEChCdiwcYoV5twj//vpjaNfMl
      EghUv7y5H7+dXioMVGFzayBDcmVhdGVkMAE5wN3kR4MrNxhBsDPlR4MrNxhKLgoIY3Jld19rZXkS
      IgogZDQyNjA4MzNhYjBjMjBiYjQ0OTIyYzc5OWFhOTZiNGFKMQoHY3Jld19pZBImCiQxZjA0NjU0
      YS05ODE5LTQ0MTEtOTVlZC1kMmMwMTJlNWU3YjJKLgoIdGFza19rZXkSIgogMjdlZjM4Y2M5OWRh
      NGE4ZGVkNzBlZDQwNmU0NGFiODZKMQoHdGFza19pZBImCiRiNTAxMWUwNi02YTdmLTQzYWItYWQz
      Ny1mNGI4ODBhMmJlYjhKOgoQY3Jld19maW5nZXJwcmludBImCiRiOGFiNTRjMS1iMWNlLTQ4YjIt
      OWQ4My00NWQ3OTIxOTE5ZDRKOgoQdGFza19maW5nZXJwcmludBImCiQ3NDcyMTM3Yi0wYzBkLTRi
      OTEtYTgwYy01YzZkYWQwZmM3YTBKOwobdGFza19maW5nZXJwcmludF9jcmVhdGVkX2F0EhwKGjIw
      MjUtMDQtMTdUMTQ6MzM6NDMuNDc0MTc1SjsKEWFnZW50X2ZpbmdlcnByaW50EiYKJDVlMWU1Nzdh
      LWY5N2QtNDA2OS04NzNmLTc1ZjYyMDE0ZWJlNnoCGAGFAQABAAASgAQKEA6OsjCwXi+o2MUfKyS+
      TmgSCOIWlM7TtxNCKgxUYXNrIENyZWF0ZWQwATmQ915Igys3GEHYaF9Igys3GEouCghjcmV3X2tl
      eRIiCiBkNDI2MDgzM2FiMGMyMGJiNDQ5MjJjNzk5YWE5NmI0YUoxCgdjcmV3X2lkEiYKJDFmMDQ2
      NTRhLTk4MTktNDQxMS05NWVkLWQyYzAxMmU1ZTdiMkouCgh0YXNrX2tleRIiCiA2MDlkZWUzOTEw
      ODhjZDFjODdiOGZhNjZhYTY3YWRiZUoxCgd0YXNrX2lkEiYKJDRiODE5NjQ5LTYyMjQtNGQ3Mi1h
      ZDFkLTM0ODZhYzBkODQwNUo6ChBjcmV3X2ZpbmdlcnByaW50EiYKJGI4YWI1NGMxLWIxY2UtNDhi
      Mi05ZDgzLTQ1ZDc5MjE5MTlkNEo6ChB0YXNrX2ZpbmdlcnByaW50EiYKJGYwZWUxMjY2LWExNzIt
      NDhiMi1iMTM2LTczN2I3YWNkYWYwNko7Cht0YXNrX2ZpbmdlcnByaW50X2NyZWF0ZWRfYXQSHAoa
      MjAyNS0wNC0xN1QxNDozMzo0My40NzQyMDVKOwoRYWdlbnRfZmluZ2VycHJpbnQSJgokNWUxZTU3
      N2EtZjk3ZC00MDY5LTg3M2YtNzVmNjIwMTRlYmU2egIYAYUBAAEAABL/CQoQOon8z16iVXeCtEnr
      visR+hII1ztFvfUBPyAqDENyZXcgQ3JlYXRlZDABOYC2CUmDKzcYQVhjEUmDKzcYShsKDmNyZXdh
      aV92ZXJzaW9uEgkKBzAuMTE0LjBKGwoOcHl0aG9uX3ZlcnNpb24SCQoHMy4xMS4xMkouCghjcmV3
      X2tleRIiCiBhOTU0MGNkMGVhYTUzZjY3NTQzN2U5YmQ0ZmE1ZTQ0Y0oxCgdjcmV3X2lkEiYKJDFh
      MDA5NjZhLTcwYmQtNDc4OS1hZmQxLTY5NjgzMzZjYjc2NkocCgxjcmV3X3Byb2Nlc3MSDAoKc2Vx
      dWVudGlhbEoRCgtjcmV3X21lbW9yeRICEABKGgoUY3Jld19udW1iZXJfb2ZfdGFza3MSAhgCShsK
      FWNyZXdfbnVtYmVyX29mX2FnZW50cxICGAFKOgoQY3Jld19maW5nZXJwcmludBImCiRjOGI1ZDUx
      My0xNzY0LTQyYWQtOGVlYS0wYWU0MDAzZTRmNTRKOwobY3Jld19maW5nZXJwcmludF9jcmVhdGVk
      X2F0EhwKGjIwMjUtMDQtMTdUMTQ6MzM6NDMuNDk0ODMySs0CCgtjcmV3X2FnZW50cxK9Agq6Alt7
      ImtleSI6ICI5MmU3ZWIxOTE2NjRjOTM1Nzg1ZWQ3ZDQyNDBhMjk0ZCIsICJpZCI6ICIwZTg1MzFl
      YS05MmM4LTQzMGQtYmE0Yy1jMmIxYzkwZDBlMWQiLCAicm9sZSI6ICJTY29yZXIiLCAidmVyYm9z
      ZT8iOiBmYWxzZSwgIm1heF9pdGVyIjogMjUsICJtYXhfcnBtIjogbnVsbCwgImZ1bmN0aW9uX2Nh
      bGxpbmdfbGxtIjogIiIsICJsbG0iOiAiZ3B0LTRvLW1pbmkiLCAiZGVsZWdhdGlvbl9lbmFibGVk
      PyI6IGZhbHNlLCAiYWxsb3dfY29kZV9leGVjdXRpb24/IjogZmFsc2UsICJtYXhfcmV0cnlfbGlt
      aXQiOiAyLCAidG9vbHNfbmFtZXMiOiBbXX1dSuQDCgpjcmV3X3Rhc2tzEtUDCtIDW3sia2V5Ijog
      IjI3ZWYzOGNjOTlkYTRhOGRlZDcwZWQ0MDZlNDRhYjg2IiwgImlkIjogImI2NWQ1ZGUzLWY5MjAt
      NDVhZi1hOTgwLTA2NjBkMDU5YzdiZiIsICJhc3luY19leGVjdXRpb24/IjogZmFsc2UsICJodW1h
      bl9pbnB1dD8iOiBmYWxzZSwgImFnZW50X3JvbGUiOiAiU2NvcmVyIiwgImFnZW50X2tleSI6ICI5
      MmU3ZWIxOTE2NjRjOTM1Nzg1ZWQ3ZDQyNDBhMjk0ZCIsICJ0b29sc19uYW1lcyI6IFtdfSwgeyJr
      ZXkiOiAiYjBkMzRhNmY2MjFhN2IzNTgwZDVkMWY0ZTI2NjViOTIiLCAiaWQiOiAiZjk2MDU5NzMt
      YzYyMi00NzRjLWFhZjktYWJiOWMwZWZhYmQ0IiwgImFzeW5jX2V4ZWN1dGlvbj8iOiBmYWxzZSwg
      Imh1bWFuX2lucHV0PyI6IGZhbHNlLCAiYWdlbnRfcm9sZSI6ICJTY29yZXIiLCAiYWdlbnRfa2V5
      IjogIjkyZTdlYjE5MTY2NGM5MzU3ODVlZDdkNDI0MGEyOTRkIiwgInRvb2xzX25hbWVzIjogW119
      XXoCGAGFAQABAAASgAQKEGysuSY/RpRwwsmMtZmmkLgSCB3RCKVN5vMGKgxUYXNrIENyZWF0ZWQw
      ATmYyRpJgys3GEFwIxtJgys3GEouCghjcmV3X2tleRIiCiBhOTU0MGNkMGVhYTUzZjY3NTQzN2U5
      YmQ0ZmE1ZTQ0Y0oxCgdjcmV3X2lkEiYKJDFhMDA5NjZhLTcwYmQtNDc4OS1hZmQxLTY5NjgzMzZj
      Yjc2NkouCgh0YXNrX2tleRIiCiAyN2VmMzhjYzk5ZGE0YThkZWQ3MGVkNDA2ZTQ0YWI4NkoxCgd0
      YXNrX2lkEiYKJGI2NWQ1ZGUzLWY5MjAtNDVhZi1hOTgwLTA2NjBkMDU5YzdiZko6ChBjcmV3X2Zp
      bmdlcnByaW50EiYKJGM4YjVkNTEzLTE3NjQtNDJhZC04ZWVhLTBhZTQwMDNlNGY1NEo6ChB0YXNr
      X2ZpbmdlcnByaW50EiYKJDZjNDhlMTkxLTViMjEtNDBmNi1iNmQwLWRjMWY3ZmM2YWQzN0o7Cht0
      YXNrX2ZpbmdlcnByaW50X2NyZWF0ZWRfYXQSHAoaMjAyNS0wNC0xN1QxNDozMzo0My40OTQ3NzdK
      OwoRYWdlbnRfZmluZ2VycHJpbnQSJgokNTZmYTQ1MTYtM2RiNS00Mjk3LTg3NzUtOTI2MWVhNWI4
      OWI2egIYAYUBAAEAABKABAoQJRURFvAOz5/5e2bQNRT4ChIIpSiy2tnBCrsqDFRhc2sgQ3JlYXRl
      ZDABOdgreEmDKzcYQSCdeEmDKzcYSi4KCGNyZXdfa2V5EiIKIGE5NTQwY2QwZWFhNTNmNjc1NDM3
      ZTliZDRmYTVlNDRjSjEKB2NyZXdfaWQSJgokMWEwMDk2NmEtNzBiZC00Nzg5LWFmZDEtNjk2ODMz
      NmNiNzY2Si4KCHRhc2tfa2V5EiIKIGIwZDM0YTZmNjIxYTdiMzU4MGQ1ZDFmNGUyNjY1YjkySjEK
      B3Rhc2tfaWQSJgokZjk2MDU5NzMtYzYyMi00NzRjLWFhZjktYWJiOWMwZWZhYmQ0SjoKEGNyZXdf
      ZmluZ2VycHJpbnQSJgokYzhiNWQ1MTMtMTc2NC00MmFkLThlZWEtMGFlNDAwM2U0ZjU0SjoKEHRh
      c2tfZmluZ2VycHJpbnQSJgokMWI0YzI5NTYtMDZkOC00NWNjLWFmYWMtNmZhZDk0MzdkNTZmSjsK
      G3Rhc2tfZmluZ2VycHJpbnRfY3JlYXRlZF9hdBIcChoyMDI1LTA0LTE3VDE0OjMzOjQzLjQ5NDgw
      OEo7ChFhZ2VudF9maW5nZXJwcmludBImCiQ1NmZhNDUxNi0zZGI1LTQyOTctODc3NS05MjYxZWE1
      Yjg5YjZ6AhgBhQEAAQAAEpYIChCt1KD2VBrK6+JIjPGbSQYWEghyEaRdKejivSoMQ3JldyBDcmVh
      dGVkMAE54Fn7SYMrNxhBMPkBSoMrNxhKGwoOY3Jld2FpX3ZlcnNpb24SCQoHMC4xMTQuMEobCg5w
      eXRob25fdmVyc2lvbhIJCgczLjExLjEySi4KCGNyZXdfa2V5EiIKIDVlNmVmZmU2ODBhNWQ5N2Rj
      Mzg3M2IxNDgyNWNjZmEzSjEKB2NyZXdfaWQSJgokMDQ4ZDQ5MzctNmU3OC00OWFkLTllZDMtYzVi
      MjdlNDgyNThlShwKDGNyZXdfcHJvY2VzcxIMCgpzZXF1ZW50aWFsShEKC2NyZXdfbWVtb3J5EgIQ
      AEoaChRjcmV3X251bWJlcl9vZl90YXNrcxICGAFKGwoVY3Jld19udW1iZXJfb2ZfYWdlbnRzEgIY
      AUo6ChBjcmV3X2ZpbmdlcnByaW50EiYKJDIxYjViOWQ3LWVjNTktNDBhYi1iZjY1LTk1NzM2M2Fl
      ZDRkZEo7ChtjcmV3X2ZpbmdlcnByaW50X2NyZWF0ZWRfYXQSHAoaMjAyNS0wNC0xN1QxNDozMzo0
      My41MTA0NjZKzQIKC2NyZXdfYWdlbnRzEr0CCroCW3sia2V5IjogIjkyZTdlYjE5MTY2NGM5MzU3
      ODVlZDdkNDI0MGEyOTRkIiwgImlkIjogImFkN2ExMTJiLWY5NDQtNDViYy05YzE4LWJjOWQzMDE4
      NjE1OCIsICJyb2xlIjogIlNjb3JlciIsICJ2ZXJib3NlPyI6IGZhbHNlLCAibWF4X2l0ZXIiOiAy
      NSwgIm1heF9ycG0iOiBudWxsLCAiZnVuY3Rpb25fY2FsbGluZ19sbG0iOiAiIiwgImxsbSI6ICJn
      cHQtNG8tbWluaSIsICJkZWxlZ2F0aW9uX2VuYWJsZWQ/IjogZmFsc2UsICJhbGxvd19jb2RlX2V4
      ZWN1dGlvbj8iOiBmYWxzZSwgIm1heF9yZXRyeV9saW1pdCI6IDIsICJ0b29sc19uYW1lcyI6IFtd
      fV1K+wEKCmNyZXdfdGFza3MS7AEK6QFbeyJrZXkiOiAiMjdlZjM4Y2M5OWRhNGE4ZGVkNzBlZDQw
      NmU0NGFiODYiLCAiaWQiOiAiZTJmMDI3YzItZWQ2NC00MDU4LThkNzUtNjQ1OWMzYTllYWIwIiwg
      ImFzeW5jX2V4ZWN1dGlvbj8iOiBmYWxzZSwgImh1bWFuX2lucHV0PyI6IGZhbHNlLCAiYWdlbnRf
      cm9sZSI6ICJTY29yZXIiLCAiYWdlbnRfa2V5IjogIjkyZTdlYjE5MTY2NGM5MzU3ODVlZDdkNDI0
      MGEyOTRkIiwgInRvb2xzX25hbWVzIjogW119XXoCGAGFAQABAAASgAQKELdQRmZyaC0pfEDI4tAa
      vRsSCCE13pISM1wEKgxUYXNrIENyZWF0ZWQwATmwBwpKgys3GEG4WQpKgys3GEouCghjcmV3X2tl
      eRIiCiA1ZTZlZmZlNjgwYTVkOTdkYzM4NzNiMTQ4MjVjY2ZhM0oxCgdjcmV3X2lkEiYKJDA0OGQ0
      OTM3LTZlNzgtNDlhZC05ZWQzLWM1YjI3ZTQ4MjU4ZUouCgh0YXNrX2tleRIiCiAyN2VmMzhjYzk5
      ZGE0YThkZWQ3MGVkNDA2ZTQ0YWI4NkoxCgd0YXNrX2lkEiYKJGUyZjAyN2MyLWVkNjQtNDA1OC04
      ZDc1LTY0NTljM2E5ZWFiMEo6ChBjcmV3X2ZpbmdlcnByaW50EiYKJDIxYjViOWQ3LWVjNTktNDBh
      Yi1iZjY1LTk1NzM2M2FlZDRkZEo6ChB0YXNrX2ZpbmdlcnByaW50EiYKJDNmMDU4NDE1LTNkNzUt
      NDZhNi05OWFjLTIyZmM5OWM4OTBmM0o7Cht0YXNrX2ZpbmdlcnByaW50X2NyZWF0ZWRfYXQSHAoa
      MjAyNS0wNC0xN1QxNDozMzo0My41MTA0NDBKOwoRYWdlbnRfZmluZ2VycHJpbnQSJgokMjViMTgx
      NjYtYjk3My00ZDM3LWI0OTUtMTI4YmIyMzQyNjhmegIYAYUBAAEAABKWCAoQuKzLEfp7NclRHh6f
      Sm2/nxIIucJxIhUlkkwqDENyZXcgQ3JlYXRlZDABOfDOa0qDKzcYQfh/cUqDKzcYShsKDmNyZXdh
      aV92ZXJzaW9uEgkKBzAuMTE0LjBKGwoOcHl0aG9uX3ZlcnNpb24SCQoHMy4xMS4xMkouCghjcmV3
      X2tleRIiCiA1ZTZlZmZlNjgwYTVkOTdkYzM4NzNiMTQ4MjVjY2ZhM0oxCgdjcmV3X2lkEiYKJDhl
      YzE2ZWQ3LTNiYjItNDkxZC04YjYxLTI3MWYxNmZlOGFlMUocCgxjcmV3X3Byb2Nlc3MSDAoKc2Vx
      dWVudGlhbEoRCgtjcmV3X21lbW9yeRICEABKGgoUY3Jld19udW1iZXJfb2ZfdGFza3MSAhgBShsK
      FWNyZXdfbnVtYmVyX29mX2FnZW50cxICGAFKOgoQY3Jld19maW5nZXJwcmludBImCiQxN2MxN2Qz
      NC1mZWZkLTQ1ZTktYWM2NS00ODY2ZTBlNTgwYTBKOwobY3Jld19maW5nZXJwcmludF9jcmVhdGVk
      X2F0EhwKGjIwMjUtMDQtMTdUMTQ6MzM6NDMuNTE4MDI0Ss0CCgtjcmV3X2FnZW50cxK9Agq6Alt7
      ImtleSI6ICI5MmU3ZWIxOTE2NjRjOTM1Nzg1ZWQ3ZDQyNDBhMjk0ZCIsICJpZCI6ICJhODk2OTRm
      ZS1lNjE1LTQzOWItOTQ4MC02MmVmZTRiNGY4ODUiLCAicm9sZSI6ICJTY29yZXIiLCAidmVyYm9z
      ZT8iOiBmYWxzZSwgIm1heF9pdGVyIjogMjUsICJtYXhfcnBtIjogbnVsbCwgImZ1bmN0aW9uX2Nh
      bGxpbmdfbGxtIjogIiIsICJsbG0iOiAiZ3B0LTRvLW1pbmkiLCAiZGVsZWdhdGlvbl9lbmFibGVk
      PyI6IGZhbHNlLCAiYWxsb3dfY29kZV9leGVjdXRpb24/IjogZmFsc2UsICJtYXhfcmV0cnlfbGlt
      aXQiOiAyLCAidG9vbHNfbmFtZXMiOiBbXX1dSvsBCgpjcmV3X3Rhc2tzEuwBCukBW3sia2V5Ijog
      IjI3ZWYzOGNjOTlkYTRhOGRlZDcwZWQ0MDZlNDRhYjg2IiwgImlkIjogIjMwYjNmYmQ4LWM0MmMt
      NDhlYi1hNDdlLTAzNzFlOTFmYTAxYiIsICJhc3luY19leGVjdXRpb24/IjogZmFsc2UsICJodW1h
      bl9pbnB1dD8iOiBmYWxzZSwgImFnZW50X3JvbGUiOiAiU2NvcmVyIiwgImFnZW50X2tleSI6ICI5
      MmU3ZWIxOTE2NjRjOTM1Nzg1ZWQ3ZDQyNDBhMjk0ZCIsICJ0b29sc19uYW1lcyI6IFtdfV16AhgB
      hQEAAQAAEoAEChBSqrS4nl90w/7Z/EGzEvYpEgj54GNjf+XWQyoMVGFzayBDcmVhdGVkMAE5GJ55
      SoMrNxhBOOx5SoMrNxhKLgoIY3Jld19rZXkSIgogNWU2ZWZmZTY4MGE1ZDk3ZGMzODczYjE0ODI1
      Y2NmYTNKMQoHY3Jld19pZBImCiQ4ZWMxNmVkNy0zYmIyLTQ5MWQtOGI2MS0yNzFmMTZmZThhZTFK
      LgoIdGFza19rZXkSIgogMjdlZjM4Y2M5OWRhNGE4ZGVkNzBlZDQwNmU0NGFiODZKMQoHdGFza19p
      ZBImCiQzMGIzZmJkOC1jNDJjLTQ4ZWItYTQ3ZS0wMzcxZTkxZmEwMWJKOgoQY3Jld19maW5nZXJw
      cmludBImCiQxN2MxN2QzNC1mZWZkLTQ1ZTktYWM2NS00ODY2ZTBlNTgwYTBKOgoQdGFza19maW5n
      ZXJwcmludBImCiQ5MGYyODZhZC1lOWQ4LTRkOWUtYjA5MC05YTQyY2I3ZjYzNDhKOwobdGFza19m
      aW5nZXJwcmludF9jcmVhdGVkX2F0EhwKGjIwMjUtMDQtMTdUMTQ6MzM6NDMuNTE3OTk4SjsKEWFn
      ZW50X2ZpbmdlcnByaW50EiYKJDhhMTliOTQxLWI4MTgtNDU3MC05NmJjLWZlYWQ4MWNkMjk1NXoC
      GAGFAQABAAASlggKEFFBQgm1QyQTSqKMpRKbGjwSCCoFcjlB3aOLKgxDcmV3IENyZWF0ZWQwATlY
      /RVLgys3GEEwLR1Lgys3GEobCg5jcmV3YWlfdmVyc2lvbhIJCgcwLjExNC4wShsKDnB5dGhvbl92
      ZXJzaW9uEgkKBzMuMTEuMTJKLgoIY3Jld19rZXkSIgogNWU2ZWZmZTY4MGE1ZDk3ZGMzODczYjE0
      ODI1Y2NmYTNKMQoHY3Jld19pZBImCiQ5NDYyMGE5YS1jMjc1LTRjMjItYjk1OC0zZmZlYWRiOGFl
      ZGJKHAoMY3Jld19wcm9jZXNzEgwKCnNlcXVlbnRpYWxKEQoLY3Jld19tZW1vcnkSAhAAShoKFGNy
      ZXdfbnVtYmVyX29mX3Rhc2tzEgIYAUobChVjcmV3X251bWJlcl9vZl9hZ2VudHMSAhgBSjoKEGNy
      ZXdfZmluZ2VycHJpbnQSJgokNzQzZDgxNDYtZTM0My00Njk0LTljODUtMmVmZWRkMzZhYTlkSjsK
      G2NyZXdfZmluZ2VycHJpbnRfY3JlYXRlZF9hdBIcChoyMDI1LTA0LTE3VDE0OjMzOjQzLjUyOTAz
      MkrNAgoLY3Jld19hZ2VudHMSvQIKugJbeyJrZXkiOiAiOTJlN2ViMTkxNjY0YzkzNTc4NWVkN2Q0
      MjQwYTI5NGQiLCAiaWQiOiAiZWE5NTMyZDktZDczNy00ZTIyLWE3MjItMDg2ODQ4NGViMmY5Iiwg
      InJvbGUiOiAiU2NvcmVyIiwgInZlcmJvc2U/IjogZmFsc2UsICJtYXhfaXRlciI6IDI1LCAibWF4
      X3JwbSI6IG51bGwsICJmdW5jdGlvbl9jYWxsaW5nX2xsbSI6ICIiLCAibGxtIjogImdwdC00by1t
      aW5pIiwgImRlbGVnYXRpb25fZW5hYmxlZD8iOiBmYWxzZSwgImFsbG93X2NvZGVfZXhlY3V0aW9u
      PyI6IGZhbHNlLCAibWF4X3JldHJ5X2xpbWl0IjogMiwgInRvb2xzX25hbWVzIjogW119XUr7AQoK
      Y3Jld190YXNrcxLsAQrpAVt7ImtleSI6ICIyN2VmMzhjYzk5ZGE0YThkZWQ3MGVkNDA2ZTQ0YWI4
      NiIsICJpZCI6ICIyMmUyMjZiNC1kNmI5LTRiMzgtOTQwMi05NDY0NTBhOWExZjUiLCAiYXN5bmNf
      ZXhlY3V0aW9uPyI6IGZhbHNlLCAiaHVtYW5faW5wdXQ/IjogZmFsc2UsICJhZ2VudF9yb2xlIjog
      IlNjb3JlciIsICJhZ2VudF9rZXkiOiAiOTJlN2ViMTkxNjY0YzkzNTc4NWVkN2Q0MjQwYTI5NGQi
      LCAidG9vbHNfbmFtZXMiOiBbXX1degIYAYUBAAEAABKABAoQBNXlZK0H3tATZKP2Uw7bBxII0nvw
      9UluHcgqDFRhc2sgQ3JlYXRlZDABOXhiJ0uDKzcYQZiwJ0uDKzcYSi4KCGNyZXdfa2V5EiIKIDVl
      NmVmZmU2ODBhNWQ5N2RjMzg3M2IxNDgyNWNjZmEzSjEKB2NyZXdfaWQSJgokOTQ2MjBhOWEtYzI3
      NS00YzIyLWI5NTgtM2ZmZWFkYjhhZWRiSi4KCHRhc2tfa2V5EiIKIDI3ZWYzOGNjOTlkYTRhOGRl
      ZDcwZWQ0MDZlNDRhYjg2SjEKB3Rhc2tfaWQSJgokMjJlMjI2YjQtZDZiOS00YjM4LTk0MDItOTQ2
      NDUwYTlhMWY1SjoKEGNyZXdfZmluZ2VycHJpbnQSJgokNzQzZDgxNDYtZTM0My00Njk0LTljODUt
      MmVmZWRkMzZhYTlkSjoKEHRhc2tfZmluZ2VycHJpbnQSJgokYjI3M2Q1OTMtYzcxZC00ZWRmLWI0
      NmMtMTkyMmIxY2ZmZjY1SjsKG3Rhc2tfZmluZ2VycHJpbnRfY3JlYXRlZF9hdBIcChoyMDI1LTA0
      LTE3VDE0OjMzOjQzLjUyOTAwMko7ChFhZ2VudF9maW5nZXJwcmludBImCiQ5ZWYxZjc4Ny1lZTIz
      LTRlNGUtOGJkYi04NGJiNjkwZjlkZjV6AhgBhQEAAQAAEpYIChCLERZYyvqc04xX3gWXPO59EggU
      L4MaGDu+FyoMQ3JldyBDcmVhdGVkMAE54NLGS4MrNxhBeGbNS4MrNxhKGwoOY3Jld2FpX3ZlcnNp
      b24SCQoHMC4xMTQuMEobCg5weXRob25fdmVyc2lvbhIJCgczLjExLjEySi4KCGNyZXdfa2V5EiIK
      IDVlNmVmZmU2ODBhNWQ5N2RjMzg3M2IxNDgyNWNjZmEzSjEKB2NyZXdfaWQSJgokZWI4MDdlMzEt
      MGFiZS00NjQ0LWEwZjEtMDM4N2ZkZTYzYWQ1ShwKDGNyZXdfcHJvY2VzcxIMCgpzZXF1ZW50aWFs
      ShEKC2NyZXdfbWVtb3J5EgIQAEoaChRjcmV3X251bWJlcl9vZl90YXNrcxICGAFKGwoVY3Jld19u
      dW1iZXJfb2ZfYWdlbnRzEgIYAUo6ChBjcmV3X2ZpbmdlcnByaW50EiYKJDBkZDRmNDNlLTEzNzEt
      NDVlNS1iMGNmLWY2ZDE5YTNjNzZkOUo7ChtjcmV3X2ZpbmdlcnByaW50X2NyZWF0ZWRfYXQSHAoa
      MjAyNS0wNC0xN1QxNDozMzo0My41NDA2MzhKzQIKC2NyZXdfYWdlbnRzEr0CCroCW3sia2V5Ijog
      IjkyZTdlYjE5MTY2NGM5MzU3ODVlZDdkNDI0MGEyOTRkIiwgImlkIjogImY0ZDM5NzdlLTg4YjAt
      NDQ2Zi04YzQ5LThkMjc0NDgwOGQ4NiIsICJyb2xlIjogIlNjb3JlciIsICJ2ZXJib3NlPyI6IGZh
      bHNlLCAibWF4X2l0ZXIiOiAyNSwgIm1heF9ycG0iOiBudWxsLCAiZnVuY3Rpb25fY2FsbGluZ19s
      bG0iOiAiIiwgImxsbSI6ICJncHQtNG8tbWluaSIsICJkZWxlZ2F0aW9uX2VuYWJsZWQ/IjogZmFs
      c2UsICJhbGxvd19jb2RlX2V4ZWN1dGlvbj8iOiBmYWxzZSwgIm1heF9yZXRyeV9saW1pdCI6IDIs
      ICJ0b29sc19uYW1lcyI6IFtdfV1K+wEKCmNyZXdfdGFza3MS7AEK6QFbeyJrZXkiOiAiMjdlZjM4
      Y2M5OWRhNGE4ZGVkNzBlZDQwNmU0NGFiODYiLCAiaWQiOiAiYjA4MTZmY2QtYjcyMS00YTZkLTk0
      ODQtNDc4YWM4ZDdkYTg4IiwgImFzeW5jX2V4ZWN1dGlvbj8iOiBmYWxzZSwgImh1bWFuX2lucHV0
      PyI6IGZhbHNlLCAiYWdlbnRfcm9sZSI6ICJTY29yZXIiLCAiYWdlbnRfa2V5IjogIjkyZTdlYjE5
      MTY2NGM5MzU3ODVlZDdkNDI0MGEyOTRkIiwgInRvb2xzX25hbWVzIjogW119XXoCGAGFAQABAAAS
      gAQKEB0emJtWTE8969Cle2pbw8QSCFZbdjoOT655KgxUYXNrIENyZWF0ZWQwATlgt9VLgys3GEFo
      CdZLgys3GEouCghjcmV3X2tleRIiCiA1ZTZlZmZlNjgwYTVkOTdkYzM4NzNiMTQ4MjVjY2ZhM0ox
      CgdjcmV3X2lkEiYKJGViODA3ZTMxLTBhYmUtNDY0NC1hMGYxLTAzODdmZGU2M2FkNUouCgh0YXNr
      X2tleRIiCiAyN2VmMzhjYzk5ZGE0YThkZWQ3MGVkNDA2ZTQ0YWI4NkoxCgd0YXNrX2lkEiYKJGIw
      ODE2ZmNkLWI3MjEtNGE2ZC05NDg0LTQ3OGFjOGQ3ZGE4OEo6ChBjcmV3X2ZpbmdlcnByaW50EiYK
      JDBkZDRmNDNlLTEzNzEtNDVlNS1iMGNmLWY2ZDE5YTNjNzZkOUo6ChB0YXNrX2ZpbmdlcnByaW50
      EiYKJGM1ODIyZGM4LWIyYmYtNDUyZS04YjQ2LWRkNjAyMDNkNTA0Zko7Cht0YXNrX2ZpbmdlcnBy
      aW50X2NyZWF0ZWRfYXQSHAoaMjAyNS0wNC0xN1QxNDozMzo0My41NDA2MDlKOwoRYWdlbnRfZmlu
      Z2VycHJpbnQSJgokZjZhMjA0MDYtOTM0Yy00ZjVmLWI1MzMtMDYwMTQwMGUxMTM1egIYAYUBAAEA
      ABL4BwoQxJQoNY/stf/qihFVNGkp1hII9x5mkc7Cz5AqDENyZXcgQ3JlYXRlZDABOcB+REyDKzcY
      QYA7SkyDKzcYShsKDmNyZXdhaV92ZXJzaW9uEgkKBzAuMTE0LjBKGwoOcHl0aG9uX3ZlcnNpb24S
      CQoHMy4xMS4xMkouCghjcmV3X2tleRIiCiA1ZTZlZmZlNjgwYTVkOTdkYzM4NzNiMTQ4MjVjY2Zh
      M0oxCgdjcmV3X2lkEiYKJDY3NmIxNjlhLTI5YTYtNDVhOC05NmNjLTY4MjMyNzgzYmU3NUoeCgxj
      cmV3X3Byb2Nlc3MSDgoMaGllcmFyY2hpY2FsShEKC2NyZXdfbWVtb3J5EgIQAEoaChRjcmV3X251
      bWJlcl9vZl90YXNrcxICGAFKGwoVY3Jld19udW1iZXJfb2ZfYWdlbnRzEgIYAUo6ChBjcmV3X2Zp
      bmdlcnByaW50EiYKJDMzZDRkZWU1LWIxOTEtNDAzYy04OWEwLTYyNWJiNjVmMmYxOUo7ChtjcmV3
      X2ZpbmdlcnByaW50X2NyZWF0ZWRfYXQSHAoaMjAyNS0wNC0xN1QxNDozMzo0My41NDg4OThKzQIK
      C2NyZXdfYWdlbnRzEr0CCroCW3sia2V5IjogIjkyZTdlYjE5MTY2NGM5MzU3ODVlZDdkNDI0MGEy
      OTRkIiwgImlkIjogImE0Mzc1OWYyLTY0NDEtNDNiMy1hOGRjLWYxYmQzMTU3MDdlZiIsICJyb2xl
      IjogIlNjb3JlciIsICJ2ZXJib3NlPyI6IGZhbHNlLCAibWF4X2l0ZXIiOiAyNSwgIm1heF9ycG0i
      OiBudWxsLCAiZnVuY3Rpb25fY2FsbGluZ19sbG0iOiAiIiwgImxsbSI6ICJncHQtNG8tbWluaSIs
      ICJkZWxlZ2F0aW9uX2VuYWJsZWQ/IjogZmFsc2UsICJhbGxvd19jb2RlX2V4ZWN1dGlvbj8iOiBm
      YWxzZSwgIm1heF9yZXRyeV9saW1pdCI6IDIsICJ0b29sc19uYW1lcyI6IFtdfV1K2wEKCmNyZXdf
      dGFza3MSzAEKyQFbeyJrZXkiOiAiMjdlZjM4Y2M5OWRhNGE4ZGVkNzBlZDQwNmU0NGFiODYiLCAi
      aWQiOiAiMDVmYzk4OWItMDY5Mi00ODAzLTgyMTMtMWQ2ODY5YTYwMTBlIiwgImFzeW5jX2V4ZWN1
      dGlvbj8iOiBmYWxzZSwgImh1bWFuX2lucHV0PyI6IGZhbHNlLCAiYWdlbnRfcm9sZSI6ICJOb25l
      IiwgImFnZW50X2tleSI6IG51bGwsICJ0b29sc19uYW1lcyI6IFtdfV16AhgBhQEAAQAAEoAEChDT
      QOB/rAOBZumhFuP0yYFYEggS6cvMoHzBYioMVGFzayBDcmVhdGVkMAE5cB9dTIMrNxhBeHFdTIMr
      NxhKLgoIY3Jld19rZXkSIgogNWU2ZWZmZTY4MGE1ZDk3ZGMzODczYjE0ODI1Y2NmYTNKMQoHY3Jl
      d19pZBImCiQ2NzZiMTY5YS0yOWE2LTQ1YTgtOTZjYy02ODIzMjc4M2JlNzVKLgoIdGFza19rZXkS
      IgogMjdlZjM4Y2M5OWRhNGE4ZGVkNzBlZDQwNmU0NGFiODZKMQoHdGFza19pZBImCiQwNWZjOTg5
      Yi0wNjkyLTQ4MDMtODIxMy0xZDY4NjlhNjAxMGVKOgoQY3Jld19maW5nZXJwcmludBImCiQzM2Q0
      ZGVlNS1iMTkxLTQwM2MtODlhMC02MjViYjY1ZjJmMTlKOgoQdGFza19maW5nZXJwcmludBImCiQ2
      MGIzYTVkNi04MWUxLTQzOGYtOTE3Yy0yNjU4MzU4NjdmNzZKOwobdGFza19maW5nZXJwcmludF9j
      cmVhdGVkX2F0EhwKGjIwMjUtMDQtMTdUMTQ6MzM6NDMuNTQ4ODcwSjsKEWFnZW50X2ZpbmdlcnBy
      aW50EiYKJDgwZjQ2NDkxLWM0YjItNDExNy05MTM2LTZhOTQxZjI5ODBiZHoCGAGFAQABAAASnAEK
      EObnhSrs8UQiBg/0Bricu2cSCMFW2rX4WlIFKgpUb29sIFVzYWdlMAE5CJ26TIMrNxhBmE/DTIMr
      NxhKGwoOY3Jld2FpX3ZlcnNpb24SCQoHMC4xMTQuMEonCgl0b29sX25hbWUSGgoYQXNrIHF1ZXN0
      aW9uIHRvIGNvd29ya2VySg4KCGF0dGVtcHRzEgIYAXoCGAGFAQABAAAS0AoKEBk04hfXjSFQOWWK
      xJFMHB8SCLGFr6R51/OhKgxDcmV3IENyZWF0ZWQwATl4TzNNgys3GEFIMzlNgys3GEobCg5jcmV3
      YWlfdmVyc2lvbhIJCgcwLjExNC4wShsKDnB5dGhvbl92ZXJzaW9uEgkKBzMuMTEuMTJKLgoIY3Jl
      d19rZXkSIgogNzQyNzU3MzEyZWY3YmI0ZWUwYjA2NjJkMWMyZTIxNzlKMQoHY3Jld19pZBImCiQ2
      MWRiMTdjYi0xODQ3LTRmYzktOWNkMy1hMDY3ZjRkOGExMzRKHAoMY3Jld19wcm9jZXNzEgwKCnNl
      cXVlbnRpYWxKEQoLY3Jld19tZW1vcnkSAhAAShoKFGNyZXdfbnVtYmVyX29mX3Rhc2tzEgIYAUob
      ChVjcmV3X251bWJlcl9vZl9hZ2VudHMSAhgCSjoKEGNyZXdfZmluZ2VycHJpbnQSJgokNjQ3OGY2
      MjItZGVlOC00OWYyLTljMDktOGNiOTBjMjEwYzNjSjsKG2NyZXdfZmluZ2VycHJpbnRfY3JlYXRl
      ZF9hdBIcChoyMDI1LTA0LTE3VDE0OjMzOjQzLjU2NDI2NEqGBQoLY3Jld19hZ2VudHMS9gQK8wRb
      eyJrZXkiOiAiODljZjMxMWI0OGI1MjE2OWQ0MmYzOTI1YzViZTFjNWEiLCAiaWQiOiAiM2E3YjUx
      ZGItYzhhZC00ZDU3LWE1OGUtOGIzY2EyYzIxZTI2IiwgInJvbGUiOiAiTWFuYWdlciIsICJ2ZXJi
      b3NlPyI6IGZhbHNlLCAibWF4X2l0ZXIiOiAyNSwgIm1heF9ycG0iOiBudWxsLCAiZnVuY3Rpb25f
      Y2FsbGluZ19sbG0iOiAiIiwgImxsbSI6ICJncHQtNG8tbWluaSIsICJkZWxlZ2F0aW9uX2VuYWJs
      ZWQ/IjogdHJ1ZSwgImFsbG93X2NvZGVfZXhlY3V0aW9uPyI6IGZhbHNlLCAibWF4X3JldHJ5X2xp
      bWl0IjogMiwgInRvb2xzX25hbWVzIjogW119LCB7ImtleSI6ICI5MmU3ZWIxOTE2NjRjOTM1Nzg1
      ZWQ3ZDQyNDBhMjk0ZCIsICJpZCI6ICIxNWZiMjExOC0zOTE0LTQ2ZTctODRkZi05M2E5ZDA0ZWVj
      M2YiLCAicm9sZSI6ICJTY29yZXIiLCAidmVyYm9zZT8iOiBmYWxzZSwgIm1heF9pdGVyIjogMjUs
      ICJtYXhfcnBtIjogbnVsbCwgImZ1bmN0aW9uX2NhbGxpbmdfbGxtIjogIiIsICJsbG0iOiAiZ3B0
      LTRvLW1pbmkiLCAiZGVsZWdhdGlvbl9lbmFibGVkPyI6IHRydWUsICJhbGxvd19jb2RlX2V4ZWN1
      dGlvbj8iOiBmYWxzZSwgIm1heF9yZXRyeV9saW1pdCI6IDIsICJ0b29sc19uYW1lcyI6IFtdfV1K
      /AEKCmNyZXdfdGFza3MS7QEK6gFbeyJrZXkiOiAiMjdlZjM4Y2M5OWRhNGE4ZGVkNzBlZDQwNmU0
      NGFiODYiLCAiaWQiOiAiMzcwNTE2YzQtNWNhMC00NDBjLWE3YTctNWFhMjZiMDQ4MmFmIiwgImFz
      eW5jX2V4ZWN1dGlvbj8iOiBmYWxzZSwgImh1bWFuX2lucHV0PyI6IGZhbHNlLCAiYWdlbnRfcm9s
      ZSI6ICJNYW5hZ2VyIiwgImFnZW50X2tleSI6ICI4OWNmMzExYjQ4YjUyMTY5ZDQyZjM5MjVjNWJl
      MWM1YSIsICJ0b29sc19uYW1lcyI6IFtdfV16AhgBhQEAAQAAEoAEChBGCATWQjaQu6Zpx66QI8ik
      Egg05IWKCa/6oyoMVGFzayBDcmVhdGVkMAE54CVFTYMrNxhB0HtFTYMrNxhKLgoIY3Jld19rZXkS
      IgogNzQyNzU3MzEyZWY3YmI0ZWUwYjA2NjJkMWMyZTIxNzlKMQoHY3Jld19pZBImCiQ2MWRiMTdj
      Yi0xODQ3LTRmYzktOWNkMy1hMDY3ZjRkOGExMzRKLgoIdGFza19rZXkSIgogMjdlZjM4Y2M5OWRh
      NGE4ZGVkNzBlZDQwNmU0NGFiODZKMQoHdGFza19pZBImCiQzNzA1MTZjNC01Y2EwLTQ0MGMtYTdh
      Ny01YWEyNmIwNDgyYWZKOgoQY3Jld19maW5nZXJwcmludBImCiQ2NDc4ZjYyMi1kZWU4LTQ5ZjIt
      OWMwOS04Y2I5MGMyMTBjM2NKOgoQdGFza19maW5nZXJwcmludBImCiQxMWU1ZDRhNi04ODc5LTQx
      ZDktYWE3ZS04OTdhNDMzMDhlZWVKOwobdGFza19maW5nZXJwcmludF9jcmVhdGVkX2F0EhwKGjIw
      MjUtMDQtMTdUMTQ6MzM6NDMuNTY0MjMzSjsKEWFnZW50X2ZpbmdlcnByaW50EiYKJGUyMzRlYzA5
      LTk3MzktNGE3Zi04MDgxLTBhNjJkYzNlNzY1ZHoCGAGFAQABAAASnQEKEFjGvzVUde99Ey+6qDaD
      oG4SCFWLiXG8McaKKgpUb29sIFVzYWdlMAE5kI2lTYMrNxhBMG2tTYMrNxhKGwoOY3Jld2FpX3Zl
      cnNpb24SCQoHMC4xMTQuMEooCgl0b29sX25hbWUSGwoZRGVsZWdhdGUgd29yayB0byBjb3dvcmtl
      ckoOCghhdHRlbXB0cxICGAF6AhgBhQEAAQAAEooIChCB+QR8dk6jPImbeJiKh5UYEgj00bY0zkAX
      LSoMQ3JldyBDcmVhdGVkMAE5cAkrToMrNxhBKNM1ToMrNxhKGwoOY3Jld2FpX3ZlcnNpb24SCQoH
      MC4xMTQuMEobCg5weXRob25fdmVyc2lvbhIJCgczLjExLjEySi4KCGNyZXdfa2V5EiIKIDMwM2I4
      ZWRkNWIwMDg3MTBkNzZhYWY4MjVhNzA5ZTU1SjEKB2NyZXdfaWQSJgokNmUzM2ZiOTItNTZjMC00
      OTY3LTk1MjItZGQ3ZWFhOWY5NjFlSh4KDGNyZXdfcHJvY2VzcxIOCgxoaWVyYXJjaGljYWxKEQoL
      Y3Jld19tZW1vcnkSAhAAShoKFGNyZXdfbnVtYmVyX29mX3Rhc2tzEgIYAUobChVjcmV3X251bWJl
      cl9vZl9hZ2VudHMSAhgBSjoKEGNyZXdfZmluZ2VycHJpbnQSJgokNDY5MjUyODctOTY2OS00MTRl
      LWEwNzctZGVjNDE2ZTE3NDRlSjsKG2NyZXdfZmluZ2VycHJpbnRfY3JlYXRlZF9hdBIcChoyMDI1
      LTA0LTE3VDE0OjMzOjQzLjU4MDUyM0rfAgoLY3Jld19hZ2VudHMSzwIKzAJbeyJrZXkiOiAiOTJl
      N2ViMTkxNjY0YzkzNTc4NWVkN2Q0MjQwYTI5NGQiLCAiaWQiOiAiNzllZTU5MjktNjdlMy00NjNi
      LTljYjEtMjFlNTdjNjZlNGQ4IiwgInJvbGUiOiAiU2NvcmVyIiwgInZlcmJvc2U/IjogZmFsc2Us
      ICJtYXhfaXRlciI6IDI1LCAibWF4X3JwbSI6IG51bGwsICJmdW5jdGlvbl9jYWxsaW5nX2xsbSI6
      ICIiLCAibGxtIjogImdwdC00by1taW5pIiwgImRlbGVnYXRpb25fZW5hYmxlZD8iOiBmYWxzZSwg
      ImFsbG93X2NvZGVfZXhlY3V0aW9uPyI6IGZhbHNlLCAibWF4X3JldHJ5X2xpbWl0IjogMiwgInRv
      b2xzX25hbWVzIjogWyJzY29yaW5nX2V4YW1wbGVzIl19XUrbAQoKY3Jld190YXNrcxLMAQrJAVt7
      ImtleSI6ICJmMzU3NWIwMTNjMjI5NGI3Y2M4ZTgwMzE1NWM4YmE0NiIsICJpZCI6ICJhZTlmMjgz
      MC1hY2NhLTRiNmQtOGRjNy01MjMzZmZlYmJhM2QiLCAiYXN5bmNfZXhlY3V0aW9uPyI6IGZhbHNl
      LCAiaHVtYW5faW5wdXQ/IjogZmFsc2UsICJhZ2VudF9yb2xlIjogIk5vbmUiLCAiYWdlbnRfa2V5
      IjogbnVsbCwgInRvb2xzX25hbWVzIjogW119XXoCGAGFAQABAAASgAQKEHOf6gKzlTQXpW3qTaF+
      AlQSCIXd9+wt3hyiKgxUYXNrIENyZWF0ZWQwATkws0hOgys3GEE4BUlOgys3GEouCghjcmV3X2tl
      eRIiCiAzMDNiOGVkZDViMDA4NzEwZDc2YWFmODI1YTcwOWU1NUoxCgdjcmV3X2lkEiYKJDZlMzNm
      YjkyLTU2YzAtNDk2Ny05NTIyLWRkN2VhYTlmOTYxZUouCgh0YXNrX2tleRIiCiBmMzU3NWIwMTNj
      MjI5NGI3Y2M4ZTgwMzE1NWM4YmE0NkoxCgd0YXNrX2lkEiYKJGFlOWYyODMwLWFjY2EtNGI2ZC04
      ZGM3LTUyMzNmZmViYmEzZEo6ChBjcmV3X2ZpbmdlcnByaW50EiYKJDQ2OTI1Mjg3LTk2NjktNDE0
      ZS1hMDc3LWRlYzQxNmUxNzQ0ZUo6ChB0YXNrX2ZpbmdlcnByaW50EiYKJDU4ZGVjODNjLWFjZTQt
      NGI1Ni05YzhjLWQyZGQ1YjVlMWYyNUo7Cht0YXNrX2ZpbmdlcnByaW50X2NyZWF0ZWRfYXQSHAoa
      MjAyNS0wNC0xN1QxNDozMzo0My41ODA0OTNKOwoRYWdlbnRfZmluZ2VycHJpbnQSJgokMGVjNzAw
      NWEtOGMzOC00N2RlLWI5YzctNjc3ZmFkOTU2ZmMzegIYAYUBAAEAABJpChDdogUTH/ocvPeyU/F2
      dgcNEghEqOA/7aEsiyoQVG9vbCBVc2FnZSBFcnJvcjABOVBbt06DKzcYQajPvU6DKzcYShsKDmNy
      ZXdhaV92ZXJzaW9uEgkKBzAuMTE0LjB6AhgBhQEAAQAAEp0BChD7kRw1jX3K2nusXZXPvHyGEghl
      QFQp8mFhuCoKVG9vbCBVc2FnZTABORCS/E6DKzcYQXDPBE+DKzcYShsKDmNyZXdhaV92ZXJzaW9u
      EgkKBzAuMTE0LjBKKAoJdG9vbF9uYW1lEhsKGURlbGVnYXRlIHdvcmsgdG8gY293b3JrZXJKDgoI
      YXR0ZW1wdHMSAhgBegIYAYUBAAEAAA==
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
      Content-Length:
      - '27952'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.31.1
    method: POST
    uri: https://telemetry.crewai.com:4319/v1/traces
  response:
    body:
      string: "\n\0"
    headers:
      Content-Length:
      - '2'
      Content-Type:
      - application/x-protobuf
      Date:
      - Thu, 17 Apr 2025 17:33:47 GMT
    status:
      code: 200
      message: OK
version: 1
