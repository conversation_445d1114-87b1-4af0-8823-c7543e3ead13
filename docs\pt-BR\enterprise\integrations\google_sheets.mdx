---
title: Integração com Google Sheets
description: "Sincronização de dados de planilhas com a integração do Google Sheets para CrewAI."
icon: "google"
---

## Visão Geral

Permita que seus agentes gerenciem dados de planilhas por meio do Google Sheets. <PERSON><PERSON>, crie novos registros, atualize dados existentes e otimize os fluxos de trabalho de gerenciamento de dados com automação alimentada por IA. Perfeito para acompanhamento de dados, relatórios e gestão colaborativa de informações.

## Pré-requisitos

Antes de utilizar a integração com o Google Sheets, certifique-se de que você possui:

- Uma conta [CrewAI Enterprise](https://app.crewai.com) com assinatura ativa
- Uma conta Google com acesso ao Google Sheets
- Sua conta Google conectada pela [página de integrações](https://app.crewai.com/crewai_plus/connectors)
- Planilhas com cabeçalhos de coluna adequados para operações com dados

## Configurando a Integração com Google Sheets

### 1. Conecte sua Conta Google

1. Acesse [Integrações do CrewAI Enterprise](https://app.crewai.com/crewai_plus/connectors)
2. Localize **Google Sheets** na seção Integrações de Autenticação
3. Clique em **Conectar** e conclua o fluxo OAuth
4. Conceda as permissões necessárias para acesso à planilha
5. Copie seu Token Enterprise em [Configurações da Conta](https://app.crewai.com/crewai_plus/settings/account)

### 2. Instale o Pacote Necessário

```bash
uv add crewai-tools
```

## Ações Disponíveis

<AccordionGroup>
  <Accordion title="GOOGLE_SHEETS_GET_ROW">
    **Descrição:** Obtém linhas de uma planilha Google Sheets.

    **Parâmetros:**
    - `spreadsheetId` (string, obrigatório): Planilha - Use as Configurações de Workflow do Portal de Conexão para permitir ao usuário selecionar uma planilha. Por padrão, usa a primeira worksheet da planilha selecionada.
    - `limit` (string, opcional): Limite de linhas - Limita o número máximo de linhas retornadas.
  </Accordion>

  <Accordion title="GOOGLE_SHEETS_CREATE_ROW">
    **Descrição:** Cria uma nova linha em uma planilha Google Sheets.

    **Parâmetros:**
    - `spreadsheetId` (string, obrigatório): Planilha - Use as Configurações de Workflow do Portal de Conexão para permitir ao usuário selecionar uma planilha. Por padrão, usa a primeira worksheet da planilha selecionada.
    - `worksheet` (string, obrigatório): Worksheet - Sua worksheet deve conter cabeçalhos de coluna.
    - `additionalFields` (object, obrigatório): Campos - Inclua os campos para criar essa linha como um objeto, usando os nomes das colunas como chaves. Use as Configurações de Workflow do Portal de Conexão para permitir ao usuário selecionar um Mapeamento de Colunas.
      ```json
      {
        "columnName1": "columnValue1",
        "columnName2": "columnValue2",
        "columnName3": "columnValue3",
        "columnName4": "columnValue4"
      }
      ```
  </Accordion>

  <Accordion title="GOOGLE_SHEETS_UPDATE_ROW">
    **Descrição:** Atualiza linhas existentes em uma planilha Google Sheets.

    **Parâmetros:**
    - `spreadsheetId` (string, obrigatório): Planilha - Use as Configurações de Workflow do Portal de Conexão para permitir ao usuário selecionar uma planilha. Por padrão, usa a primeira worksheet da planilha selecionada.
    - `worksheet` (string, obrigatório): Worksheet - Sua worksheet deve conter cabeçalhos de coluna.
    - `filterFormula` (object, opcional): Filtro em forma normal disjuntiva - OU de grupos E (AND) de condições individuais para identificar quais linhas atualizar.
      ```json
      {
        "operator": "OR",
        "conditions": [
          {
            "operator": "AND",
            "conditions": [
              {
                "field": "status",
                "operator": "$stringExactlyMatches",
                "value": "pending"
              }
            ]
          }
        ]
      }
      ```
      Operadores disponíveis: `$stringContains`, `$stringDoesNotContain`, `$stringExactlyMatches`, `$stringDoesNotExactlyMatch`, `$stringStartsWith`, `$stringDoesNotStartWith`, `$stringEndsWith`, `$stringDoesNotEndWith`, `$numberGreaterThan`, `$numberLessThan`, `$numberEquals`, `$numberDoesNotEqual`, `$dateTimeAfter`, `$dateTimeBefore`, `$dateTimeEquals`, `$booleanTrue`, `$booleanFalse`, `$exists`, `$doesNotExist`
    - `additionalFields` (object, obrigatório): Campos - Inclua os campos a serem atualizados como objeto, usando os nomes das colunas como chaves. Use as Configurações de Workflow do Portal de Conexão para permitir ao usuário selecionar um Mapeamento de Colunas.
      ```json
      {
        "columnName1": "newValue1",
        "columnName2": "newValue2",
        "columnName3": "newValue3",
        "columnName4": "newValue4"
      }
      ```
  </Accordion>
</AccordionGroup>

## Exemplos de Uso

### Configuração Básica de um Agente Google Sheets

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

# Obtenha as ferramentas enterprise (ferramentas Google Sheets incluídas)
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

# Crie um agente com capacidades para Google Sheets
sheets_agent = Agent(
    role="Data Manager",
    goal="Gerenciar dados de planilha e rastrear informações de maneira eficiente",
    backstory="Um assistente de IA especializado em gestão de dados e operações em planilhas.",
    tools=[enterprise_tools]
)

# Tarefa para adicionar novos dados a uma planilha
data_entry_task = Task(
    description="Adicionar novo registro de cliente na planilha de banco de dados de clientes com nome, e-mail e data de cadastro",
    agent=sheets_agent,
    expected_output="Novo registro de cliente adicionado com sucesso à planilha"
)

# Execute a tarefa
crew = Crew(
    agents=[sheets_agent],
    tasks=[data_entry_task]
)

crew.kickoff()
```

### Filtrando Ferramentas Específicas do Google Sheets

```python
from crewai_tools import CrewaiEnterpriseTools

# Obtenha apenas ferramentas específicas do Google Sheets
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token",
    actions_list=["google_sheets_get_row", "google_sheets_create_row"]
)

data_collector = Agent(
    role="Data Collector",
    goal="Coletar e organizar dados em planilhas",
    backstory="Um assistente de IA dedicado à coleta e organização de dados.",
    tools=enterprise_tools
)

# Tarefa para coletar e organizar dados
data_collection = Task(
    description="Recuperar dados atuais de inventário e adicionar novos produtos à planilha de inventário",
    agent=data_collector,
    expected_output="Dados de inventário recuperados e novos produtos adicionados com sucesso"
)

crew = Crew(
    agents=[data_collector],
    tasks=[data_collection]
)

crew.kickoff()
```

### Análise de Dados e Geração de Relatórios

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

data_analyst = Agent(
    role="Data Analyst",
    goal="Analisar dados de planilhas e gerar insights",
    backstory="Um analista de dados experiente que extrai insights dos dados de planilhas.",
    tools=[enterprise_tools]
)

# Tarefa para analisar dados e criar relatórios
analysis_task = Task(
    description="""
    1. Recuperar todos os dados de vendas da planilha do mês atual
    2. Analisar os dados em busca de tendências e padrões
    3. Criar um relatório resumo em uma nova linha com os principais indicadores
    """,
    agent=data_analyst,
    expected_output="Dados de vendas analisados e relatório resumo criado com os principais insights"
)

crew = Crew(
    agents=[data_analyst],
    tasks=[analysis_task]
)

crew.kickoff()
```

### Atualizações Automatizadas de Dados

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

data_updater = Agent(
    role="Data Updater",
    goal="Atualizar e manter dados de planilhas automaticamente",
    backstory="Um assistente de IA que mantém a precisão dos dados e atualiza registros automaticamente.",
    tools=[enterprise_tools]
)

# Tarefa para atualizar dados com base em condições
update_task = Task(
    description="""
    1. Encontrar todos os pedidos pendentes na planilha de pedidos
    2. Atualizar o status para 'processing'
    3. Adicionar um registro de data/hora da atualização do status
    4. Registrar as alterações em uma planilha de acompanhamento separada
    """,
    agent=data_updater,
    expected_output="Todos os pedidos pendentes atualizados para o status processing com registros de data/hora"
)

crew = Crew(
    agents=[data_updater],
    tasks=[update_task]
)

crew.kickoff()
```

### Fluxo de Trabalho Complexo com Dados

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

workflow_manager = Agent(
    role="Data Workflow Manager",
    goal="Gerenciar fluxos de dados complexos entre várias planilhas",
    backstory="Um assistente de IA que orquestra operações complexas de dados entre várias planilhas.",
    tools=[enterprise_tools]
)

# Tarefa de workflow complexa
workflow_task = Task(
    description="""
    1. Obter todos os dados de clientes da planilha principal de clientes
    2. Criar registros de resumo mensal para clientes ativos
    3. Atualizar o status de clientes com base na atividade nos últimos 30 dias
    4. Gerar um relatório mensal com métricas dos clientes
    5. Arquivar registros de clientes inativos em uma planilha separada
    """,
    agent=workflow_manager,
    expected_output="Workflow mensal de clientes concluído com atualizações de status e relatórios gerados"
)

crew = Crew(
    agents=[workflow_manager],
    tasks=[workflow_task]
)

crew.kickoff()
```

## Solução de Problemas

### Problemas Comuns

**Erros de Permissão**
- Certifique-se de que sua conta Google tem acesso de edição às planilhas alvo
- Verifique se a conexão OAuth inclui os escopos necessários para a API do Google Sheets
- Confira se as planilhas estão compartilhadas com a conta autenticada

**Problemas de Estrutura da Planilha**
- Certifique-se de que as worksheets têm cabeçalhos de coluna antes de criar ou atualizar linhas
- Verifique se os nomes das colunas em `additionalFields` correspondem exatamente aos cabeçalhos
- Confirme que a worksheet especificada existe na planilha

**Problemas de Tipo e Formato de Dados**
- Garanta que os valores dos dados estejam no formato esperado para cada coluna
- Utilize formatos de data adequados nas colunas de data (recomenda-se ISO)
- Verifique se valores numéricos estão devidamente formatados para colunas numéricas

**Problemas com Fórmulas de Filtro**
- Certifique-se de que as fórmulas de filtro seguem a estrutura JSON correta para forma normal disjuntiva
- Use nomes de campos válidos, correspondendo exatamente aos cabeçalhos das colunas
- Teste filtros simples antes de criar consultas com múltiplas condições
- Verifique se os tipos de operadores correspondem aos tipos de dados das colunas

**Limites de Linhas e Performance**
- Fique atento aos limites de linhas ao usar `GOOGLE_SHEETS_GET_ROW`
- Considere paginação para grandes volumes de dados
- Use filtros específicos para reduzir a quantidade de dados processados

**Operações de Atualização**
- Certifique-se de que as condições de filtro identifiquem corretamente as linhas a serem atualizadas
- Teste condições de filtro com pequenos conjuntos de dados antes de grandes atualizações
- Verifique se todos os campos obrigatórios estão incluídos nas operações de atualização

### Obtendo Ajuda

<Card title="Precisa de Ajuda?" icon="headset" href="mailto:<EMAIL>">
  Entre em contato com nosso time de suporte para auxílio na configuração ou solução de problemas da integração com o Google Sheets.
</Card>