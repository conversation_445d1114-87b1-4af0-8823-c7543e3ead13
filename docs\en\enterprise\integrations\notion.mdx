---
title: Notion Integration
description: "Page and database management with Notion integration for CrewAI."
icon: "book"
---

## Overview

Enable your agents to manage pages, databases, and content through Notion. Create and update pages, manage content blocks, organize knowledge bases, and streamline your documentation workflows with AI-powered automation.

## Prerequisites

Before using the Notion integration, ensure you have:

- A [CrewAI Enterprise](https://app.crewai.com) account with an active subscription
- A Notion account with appropriate workspace permissions
- Connected your Notion account through the [Integrations page](https://app.crewai.com/crewai_plus/connectors)

## Setting Up Notion Integration

### 1. Connect Your Notion Account

1. Navigate to [CrewAI Enterprise Integrations](https://app.crewai.com/crewai_plus/connectors)
2. Find **Notion** in the Authentication Integrations section
3. Click **Connect** and complete the OAuth flow
4. Grant the necessary permissions for page and database management
5. Copy your Enterprise Token from [Account Settings](https://app.crewai.com/crewai_plus/settings/account)

### 2. Install Required Package

```bash
uv add crewai-tools
```

## Available Actions

<AccordionGroup>
  <Accordion title="NOTION_CREATE_PAGE">
    **Description:** Create a page in Notion.

    **Parameters:**
    - `parent` (object, required): Parent - The parent page or database where the new page is inserted, represented as a JSON object with a page_id or database_id key.
      ```json
      {
        "database_id": "DATABASE_ID"
      }
      ```
    - `properties` (object, required): Properties - The values of the page's properties. If the parent is a database, then the schema must match the parent database's properties.
      ```json
      {
        "title": [
          {
            "text": {
              "content": "My Page"
            }
          }
        ]
      }
      ```
    - `icon` (object, required): Icon - The page icon.
      ```json
      {
        "emoji": "🥬"
      }
      ```
    - `children` (object, optional): Children - Content blocks to add to the page.
      ```json
      [
        {
          "object": "block",
          "type": "heading_2",
          "heading_2": {
            "rich_text": [
              {
                "type": "text",
                "text": {
                  "content": "Lacinato kale"
                }
              }
            ]
          }
        }
      ]
      ```
    - `cover` (object, optional): Cover - The page cover image.
      ```json
      {
        "external": {
          "url": "https://upload.wikimedia.org/wikipedia/commons/6/62/Tuscankale.jpg"
        }
      }
      ```
  </Accordion>

  <Accordion title="NOTION_UPDATE_PAGE">
    **Description:** Update a page in Notion.

    **Parameters:**
    - `pageId` (string, required): Page ID - Specify the ID of the Page to Update. (example: "59833787-2cf9-4fdf-8782-e53db20768a5").
    - `icon` (object, required): Icon - The page icon.
      ```json
      {
        "emoji": "🥬"
      }
      ```
    - `archived` (boolean, optional): Archived - Whether the page is archived (deleted). Set to true to archive a page. Set to false to un-archive (restore) a page.
    - `properties` (object, optional): Properties - The property values to update for the page.
      ```json
      {
        "title": [
          {
            "text": {
              "content": "My Updated Page"
            }
          }
        ]
      }
      ```
    - `cover` (object, optional): Cover - The page cover image.
      ```json
      {
        "external": {
          "url": "https://upload.wikimedia.org/wikipedia/commons/6/62/Tuscankale.jpg"
        }
      }
      ```
  </Accordion>

  <Accordion title="NOTION_GET_PAGE_BY_ID">
    **Description:** Get a page by ID in Notion.

    **Parameters:**
    - `pageId` (string, required): Page ID - Specify the ID of the Page to Get. (example: "59833787-2cf9-4fdf-8782-e53db20768a5").
  </Accordion>

  <Accordion title="NOTION_ARCHIVE_PAGE">
    **Description:** Archive a page in Notion.

    **Parameters:**
    - `pageId` (string, required): Page ID - Specify the ID of the Page to Archive. (example: "59833787-2cf9-4fdf-8782-e53db20768a5").
  </Accordion>

  <Accordion title="NOTION_SEARCH_PAGES">
    **Description:** Search pages in Notion using filters.

    **Parameters:**
    - `searchByTitleFilterSearch` (object, optional): A filter in disjunctive normal form - OR of AND groups of single conditions.
      ```json
      {
        "operator": "OR",
        "conditions": [
          {
            "operator": "AND",
            "conditions": [
              {
                "field": "query",
                "operator": "$stringExactlyMatches",
                "value": "meeting notes"
              }
            ]
          }
        ]
      }
      ```
      Available fields: `query`, `filter.value`, `direction`, `page_size`
  </Accordion>

  <Accordion title="NOTION_GET_PAGE_CONTENT">
    **Description:** Get page content (blocks) in Notion.

    **Parameters:**
    - `blockId` (string, required): Page ID - Specify a Block or Page ID to receive all of its block's children in order. (example: "59833787-2cf9-4fdf-8782-e53db20768a5").
  </Accordion>

  <Accordion title="NOTION_UPDATE_BLOCK">
    **Description:** Update a block in Notion.

    **Parameters:**
    - `blockId` (string, required): Block ID - Specify the ID of the Block to Update. (example: "9bc30ad4-9373-46a5-84ab-0a7845ee52e6").
    - `archived` (boolean, optional): Archived - Set to true to archive (delete) a block. Set to false to un-archive (restore) a block.
    - `paragraph` (object, optional): Paragraph content.
      ```json
      {
        "rich_text": [
          {
            "type": "text",
            "text": {
              "content": "Lacinato kale",
              "link": null
            }
          }
        ],
        "color": "default"
      }
      ```
    - `image` (object, optional): Image block.
      ```json
      {
        "type": "external",
        "external": {
          "url": "https://website.domain/images/image.png"
        }
      }
      ```
    - `bookmark` (object, optional): Bookmark block.
      ```json
      {
        "caption": [],
        "url": "https://companywebsite.com"
      }
      ```
    - `code` (object, optional): Code block.
      ```json
      {
        "rich_text": [
          {
            "type": "text",
            "text": {
              "content": "const a = 3"
            }
          }
        ],
        "language": "javascript"
      }
      ```
    - `pdf` (object, optional): PDF block.
      ```json
      {
        "type": "external",
        "external": {
          "url": "https://website.domain/files/doc.pdf"
        }
      }
      ```
    - `table` (object, optional): Table block.
      ```json
      {
        "table_width": 2,
        "has_column_header": false,
        "has_row_header": false
      }
      ```
    - `tableOfContent` (object, optional): Table of Contents block.
      ```json
      {
        "color": "default"
      }
      ```
    - `additionalFields` (object, optional): Additional block types.
      ```json
      {
        "child_page": {
          "title": "Lacinato kale"
        },
        "child_database": {
          "title": "My database"
        }
      }
      ```
  </Accordion>

  <Accordion title="NOTION_GET_BLOCK_BY_ID">
    **Description:** Get a block by ID in Notion.

    **Parameters:**
    - `blockId` (string, required): Block ID - Specify the ID of the Block to Get. (example: "9bc30ad4-9373-46a5-84ab-0a7845ee52e6").
  </Accordion>

  <Accordion title="NOTION_DELETE_BLOCK">
    **Description:** Delete a block in Notion.

    **Parameters:**
    - `blockId` (string, required): Block ID - Specify the ID of the Block to Delete. (example: "9bc30ad4-9373-46a5-84ab-0a7845ee52e6").
  </Accordion>
</AccordionGroup>

## Usage Examples

### Basic Notion Agent Setup

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

# Get enterprise tools (Notion tools will be included)
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

# Create an agent with Notion capabilities
notion_agent = Agent(
    role="Documentation Manager",
    goal="Manage documentation and knowledge base in Notion efficiently",
    backstory="An AI assistant specialized in content management and documentation.",
    tools=[enterprise_tools]
)

# Task to create a meeting notes page
create_notes_task = Task(
    description="Create a new meeting notes page in the team database with today's date and agenda items",
    agent=notion_agent,
    expected_output="Meeting notes page created successfully with structured content"
)

# Run the task
crew = Crew(
    agents=[notion_agent],
    tasks=[create_notes_task]
)

crew.kickoff()
```

### Filtering Specific Notion Tools

```python
from crewai_tools import CrewaiEnterpriseTools

# Get only specific Notion tools
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token",
    actions_list=["notion_create_page", "notion_update_block", "notion_search_pages"]
)

content_manager = Agent(
    role="Content Manager",
    goal="Create and manage content pages efficiently",
    backstory="An AI assistant that focuses on content creation and management.",
    tools=enterprise_tools
)

# Task to manage content workflow
content_workflow = Task(
    description="Create a new project documentation page and add structured content blocks for requirements and specifications",
    agent=content_manager,
    expected_output="Project documentation created with organized content sections"
)

crew = Crew(
    agents=[content_manager],
    tasks=[content_workflow]
)

crew.kickoff()
```

### Knowledge Base Management

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

knowledge_curator = Agent(
    role="Knowledge Curator",
    goal="Curate and organize knowledge base content in Notion",
    backstory="An experienced knowledge manager who organizes and maintains comprehensive documentation.",
    tools=[enterprise_tools]
)

# Task to curate knowledge base
curation_task = Task(
    description="""
    1. Search for existing documentation pages related to our new product feature
    2. Create a comprehensive feature documentation page with proper structure
    3. Add code examples, images, and links to related resources
    4. Update existing pages with cross-references to the new documentation
    """,
    agent=knowledge_curator,
    expected_output="Feature documentation created and integrated with existing knowledge base"
)

crew = Crew(
    agents=[knowledge_curator],
    tasks=[curation_task]
)

crew.kickoff()
```

### Content Structure and Organization

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

content_organizer = Agent(
    role="Content Organizer",
    goal="Organize and structure content blocks for optimal readability",
    backstory="An AI assistant that specializes in content structure and user experience.",
    tools=[enterprise_tools]
)

# Task to organize content structure
organization_task = Task(
    description="""
    1. Get content from existing project pages
    2. Analyze the structure and identify improvement opportunities
    3. Update content blocks to use proper headings, tables, and formatting
    4. Add table of contents and improve navigation between related pages
    5. Create templates for future documentation consistency
    """,
    agent=content_organizer,
    expected_output="Content reorganized with improved structure and navigation"
)

crew = Crew(
    agents=[content_organizer],
    tasks=[organization_task]
)

crew.kickoff()
```

### Automated Documentation Workflows

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

doc_automator = Agent(
    role="Documentation Automator",
    goal="Automate documentation workflows and maintenance",
    backstory="An AI assistant that automates repetitive documentation tasks.",
    tools=[enterprise_tools]
)

# Complex documentation automation task
automation_task = Task(
    description="""
    1. Search for pages that haven't been updated in the last 30 days
    2. Review and update outdated content blocks
    3. Create weekly team update pages with consistent formatting
    4. Add status indicators and progress tracking to project pages
    5. Generate monthly documentation health reports
    6. Archive completed project pages and organize them in archive sections
    """,
    agent=doc_automator,
    expected_output="Documentation automated with updated content, weekly reports, and organized archives"
)

crew = Crew(
    agents=[doc_automator],
    tasks=[automation_task]
)

crew.kickoff()
```

## Troubleshooting

### Common Issues

**Permission Errors**
- Ensure your Notion account has edit access to the target workspace
- Verify that the OAuth connection includes required scopes for Notion API
- Check that pages and databases are shared with the authenticated integration

**Invalid Page and Block IDs**
- Double-check page IDs and block IDs for correct UUID format
- Ensure referenced pages and blocks exist and are accessible
- Verify that parent page or database IDs are valid when creating new pages

**Property Schema Issues**
- Ensure page properties match the database schema when creating pages in databases
- Verify that property names and types are correct for the target database
- Check that required properties are included when creating or updating pages

**Content Block Structure**
- Ensure block content follows Notion's rich text format specifications
- Verify that nested block structures are properly formatted
- Check that media URLs are accessible and properly formatted

**Search and Filter Issues**
- Ensure search queries are properly formatted and not empty
- Use valid field names in filter formulas: `query`, `filter.value`, `direction`, `page_size`
- Test simple searches before building complex filter conditions

**Parent-Child Relationships**
- Verify that parent page or database exists before creating child pages
- Ensure proper permissions exist for the parent container
- Check that database schemas allow the properties you're trying to set

**Rich Text and Media Content**
- Ensure URLs for external images, PDFs, and bookmarks are accessible
- Verify that rich text formatting follows Notion's API specifications
- Check that code block language types are supported by Notion

**Archive and Deletion Operations**
- Understand the difference between archiving (reversible) and deleting (permanent)
- Verify that you have permissions to archive or delete the target content
- Be cautious with bulk operations that might affect multiple pages or blocks

### Getting Help

<Card title="Need Help?" icon="headset" href="mailto:<EMAIL>">
  Contact our support team for assistance with Notion integration setup or troubleshooting.
</Card>
