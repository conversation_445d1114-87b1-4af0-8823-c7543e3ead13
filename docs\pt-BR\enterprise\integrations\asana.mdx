---
title: Integração com Asana
description: "Coordenação de tarefas e projetos em equipe com a integração Asana para CrewAI."
icon: "circle"
---

## Visão Geral

Permita que seus agentes gerenciem tarefas, projetos e a coordenação da equipe através do Asana. Crie tarefas, atualize o status de projetos, gerencie atribuições e otimize o fluxo de trabalho da sua equipe com automação baseada em IA.

## Pré-requisitos

Antes de usar a integração com o Asana, assegure-se de ter:

- Uma conta [CrewAI Enterprise](https://app.crewai.com) com assinatura ativa
- Uma conta Asana com as permissões apropriadas
- Sua conta Asana conectada através da [página de Integrações](https://app.crewai.com/crewai_plus/connectors)

## Configurando a Integração Asana

### 1. Conecte sua Conta Asana

1. Acesse [CrewAI Enterprise Integrações](https://app.crewai.com/crewai_plus/connectors)
2. Encontre **Asana** na seção Integrações de Autenticação
3. Clique em **Conectar** e complete o fluxo OAuth
4. Conceda as permissões necessárias para gerenciamento de tarefas e projetos
5. Copie seu Token Enterprise em [Configurações da Conta](https://app.crewai.com/crewai_plus/settings/account)

### 2. Instale o Pacote Necessário

```bash
uv add crewai-tools
```

## Ações Disponíveis

<AccordionGroup>
  <Accordion title="ASANA_CREATE_COMMENT">
    **Descrição:** Cria um comentário no Asana.

    **Parâmetros:**
    - `task` (string, obrigatório): ID da Tarefa - O ID da tarefa à qual o comentário será adicionado. O comentário será escrito pelo usuário atualmente autenticado.
    - `text` (string, obrigatório): Texto (exemplo: "Este é um comentário.").
  </Accordion>

  <Accordion title="ASANA_CREATE_PROJECT">
    **Descrição:** Cria um projeto no Asana.

    **Parâmetros:**
    - `name` (string, obrigatório): Nome (exemplo: "Itens para comprar").
    - `workspace` (string, obrigatório): Área de trabalho - Use as Configurações de Fluxo do Portal Connect para permitir que usuários escolham em qual área de trabalho criar projetos. Por padrão, será usada a primeira área de trabalho do usuário se deixado em branco.
    - `team` (string, opcional): Equipe - Use as Configurações de Fluxo do Portal Connect para permitir que usuários escolham com qual equipe compartilhar o projeto. Por padrão, será usada a primeira equipe do usuário se deixado em branco.
    - `notes` (string, opcional): Notas (exemplo: "Esses são itens que precisamos comprar.").
  </Accordion>

  <Accordion title="ASANA_GET_PROJECTS">
    **Descrição:** Obtém uma lista de projetos do Asana.

    **Parâmetros:**
    - `archived` (string, opcional): Arquivado - Escolha "true" para mostrar projetos arquivados, "false" para exibir apenas projetos ativos ou "default" para mostrar ambos.
      - Opções: `default`, `true`, `false`
  </Accordion>

  <Accordion title="ASANA_GET_PROJECT_BY_ID">
    **Descrição:** Obtém um projeto pelo ID no Asana.

    **Parâmetros:**
    - `projectFilterId` (string, obrigatório): ID do Projeto.
  </Accordion>

  <Accordion title="ASANA_CREATE_TASK">
    **Descrição:** Cria uma tarefa no Asana.

    **Parâmetros:**
    - `name` (string, obrigatório): Nome (exemplo: "Nome da tarefa").
    - `workspace` (string, opcional): Área de trabalho - Use as Configurações de Fluxo do Portal Connect para permitir que usuários escolham em qual área de trabalho criar tarefas. Por padrão, será usada a primeira área de trabalho do usuário se deixado em branco.
    - `project` (string, opcional): Projeto - Use as Configurações de Fluxo do Portal Connect para permitir que usuários escolham em qual projeto criar a tarefa.
    - `notes` (string, opcional): Notas.
    - `dueOnDate` (string, opcional): Data de Vencimento - A data em que esta tarefa deve ser concluída. Não pode ser usada em conjunto com Due At. (exemplo: "YYYY-MM-DD").
    - `dueAtDate` (string, opcional): Vence Em - A data e hora (timestamp ISO) em que esta tarefa deve ser concluída. Não pode ser usada em conjunto com Due On. (exemplo: "2019-09-15T02:06:58.147Z").
    - `assignee` (string, opcional): Responsável - O ID do usuário Asana a quem esta tarefa será atribuída. Use as Configurações de Fluxo do Portal Connect para permitir que usuários selecionem um responsável.
    - `gid` (string, opcional): ID Externo - Um ID da sua aplicação para associar esta tarefa. Você pode usar este ID para sincronizar atualizações com esta tarefa posteriormente.
  </Accordion>

  <Accordion title="ASANA_UPDATE_TASK">
    **Descrição:** Atualiza uma tarefa no Asana.

    **Parâmetros:**
    - `taskId` (string, obrigatório): ID da Tarefa - O ID da tarefa a ser atualizada.
    - `completeStatus` (string, opcional): Status de Conclusão.
      - Opções: `true`, `false`
    - `name` (string, opcional): Nome (exemplo: "Nome da Tarefa").
    - `notes` (string, opcional): Notas.
    - `dueOnDate` (string, opcional): Data de Vencimento - A data em que esta tarefa deve ser concluída. Não pode ser usada junto com Due At. (exemplo: "YYYY-MM-DD").
    - `dueAtDate` (string, opcional): Vence Em - A data e hora (timestamp ISO) em que esta tarefa deve ser concluída. Não pode ser usada junto com Due On. (exemplo: "2019-09-15T02:06:58.147Z").
    - `assignee` (string, opcional): Responsável - O ID do usuário Asana a quem esta tarefa será atribuída. Use as Configurações de Fluxo do Portal Connect para permitir que usuários selecionem o responsável.
    - `gid` (string, opcional): ID Externo - Um ID da sua aplicação para associar a tarefa. Você pode usar este ID para sincronizar atualizações posteriormente.
  </Accordion>

  <Accordion title="ASANA_GET_TASKS">
    **Descrição:** Obtém uma lista de tarefas no Asana.

    **Parâmetros:**
    - `workspace` (string, opcional): Área de trabalho - O ID da área de trabalho para filtrar tarefas. Use as Configurações de Fluxo do Portal Connect para permitir que usuários selecionem uma área de trabalho.
    - `project` (string, opcional): Projeto - O ID do projeto para filtrar as tarefas. Use as Configurações de Fluxo do Portal Connect para permitir que usuários selecionem um projeto.
    - `assignee` (string, opcional): Responsável - O ID do responsável para filtrar tarefas. Use as Configurações de Fluxo do Portal Connect para permitir que usuários selecionem um responsável.
    - `completedSince` (string, opcional): Concluída desde - Retorna apenas tarefas que estejam incompletas ou que tenham sido concluídas desde este horário (timestamp ISO ou Unix). (exemplo: "2014-04-25T16:15:47-04:00").
  </Accordion>

  <Accordion title="ASANA_GET_TASKS_BY_ID">
    **Descrição:** Obtém uma lista de tarefas pelo ID no Asana.

    **Parâmetros:**
    - `taskId` (string, obrigatório): ID da Tarefa.
  </Accordion>

  <Accordion title="ASANA_GET_TASK_BY_EXTERNAL_ID">
    **Descrição:** Obtém uma tarefa pelo ID externo no Asana.

    **Parâmetros:**
    - `gid` (string, obrigatório): ID Externo - O ID que esta tarefa está associada ou sincronizada, de sua aplicação.
  </Accordion>

  <Accordion title="ASANA_ADD_TASK_TO_SECTION">
    **Descrição:** Adiciona uma tarefa a uma seção no Asana.

    **Parâmetros:**
    - `sectionId` (string, obrigatório): ID da Seção - O ID da seção à qual a tarefa será adicionada.
    - `taskId` (string, obrigatório): ID da Tarefa - O ID da tarefa. (exemplo: "1204619611402340").
    - `beforeTaskId` (string, opcional): Antes da Tarefa - O ID de uma tarefa nesta seção antes da qual esta tarefa será inserida. Não pode ser usada junto com After Task ID. (exemplo: "1204619611402340").
    - `afterTaskId` (string, opcional): Após a Tarefa - O ID de uma tarefa nesta seção após a qual esta tarefa será inserida. Não pode ser usada junto com Before Task ID. (exemplo: "1204619611402340").
  </Accordion>

  <Accordion title="ASANA_GET_TEAMS">
    **Descrição:** Obtém uma lista de equipes no Asana.

    **Parâmetros:**
    - `workspace` (string, obrigatório): Área de trabalho - Retorna as equipes nesta área de trabalho visíveis para o usuário autorizado.
  </Accordion>

  <Accordion title="ASANA_GET_WORKSPACES">
    **Descrição:** Obtém uma lista de áreas de trabalho do Asana.

    **Parâmetros:** Nenhum obrigatório.
  </Accordion>
</AccordionGroup>

## Exemplos de Uso

### Configuração Básica do Agente Asana

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

# Get enterprise tools (Asana tools will be included)
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

# Create an agent with Asana capabilities
asana_agent = Agent(
    role="Project Manager",
    goal="Manage tasks and projects in Asana efficiently",
    backstory="An AI assistant specialized in project management and task coordination.",
    tools=[enterprise_tools]
)

# Task to create a new project
create_project_task = Task(
    description="Create a new project called 'Q1 Marketing Campaign' in the Marketing workspace",
    agent=asana_agent,
    expected_output="Confirmation that the project was created successfully with project ID"
)

# Run the task
crew = Crew(
    agents=[asana_agent],
    tasks=[create_project_task]
)

crew.kickoff()
```

### Filtrando Ferramentas Específicas do Asana

```python
from crewai_tools import CrewaiEnterpriseTools

# Get only specific Asana tools
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token",
    actions_list=["asana_create_task", "asana_update_task", "asana_get_tasks"]
)

task_manager_agent = Agent(
    role="Task Manager",
    goal="Create and manage tasks efficiently",
    backstory="An AI assistant that focuses on task creation and management.",
    tools=enterprise_tools
)

# Task to create and assign a task
task_management = Task(
    description="Create a task called 'Review quarterly reports' and assign it to the appropriate team member",
    agent=task_manager_agent,
    expected_output="Task created and assigned successfully"
)

crew = Crew(
    agents=[task_manager_agent],
    tasks=[task_management]
)

crew.kickoff()
```

### Gerenciamento Avançado de Projetos

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

project_coordinator = Agent(
    role="Project Coordinator",
    goal="Coordinate project activities and track progress",
    backstory="An experienced project coordinator who ensures projects run smoothly.",
    tools=[enterprise_tools]
)

# Complex task involving multiple Asana operations
coordination_task = Task(
    description="""
    1. Get all active projects in the workspace
    2. For each project, get the list of incomplete tasks
    3. Create a summary report task in the 'Management Reports' project
    4. Add comments to overdue tasks to request status updates
    """,
    agent=project_coordinator,
    expected_output="Summary report created and status update requests sent for overdue tasks"
)

crew = Crew(
    agents=[project_coordinator],
    tasks=[coordination_task]
)

crew.kickoff()
```