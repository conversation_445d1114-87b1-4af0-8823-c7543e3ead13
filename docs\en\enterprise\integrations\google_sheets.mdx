---
title: Google Sheets Integration
description: "Spreadsheet data synchronization with Google Sheets integration for CrewAI."
icon: "google"
---

## Overview

Enable your agents to manage spreadsheet data through Google Sheets. Read rows, create new entries, update existing data, and streamline your data management workflows with AI-powered automation. Perfect for data tracking, reporting, and collaborative data management.

## Prerequisites

Before using the Google Sheets integration, ensure you have:

- A [CrewAI Enterprise](https://app.crewai.com) account with an active subscription
- A Google account with Google Sheets access
- Connected your Google account through the [Integrations page](https://app.crewai.com/crewai_plus/connectors)
- Spreadsheets with proper column headers for data operations

## Setting Up Google Sheets Integration

### 1. Connect Your Google Account

1. Navigate to [CrewAI Enterprise Integrations](https://app.crewai.com/crewai_plus/connectors)
2. Find **Google Sheets** in the Authentication Integrations section
3. Click **Connect** and complete the OAuth flow
4. Grant the necessary permissions for spreadsheet access
5. Copy your Enterprise Token from [Account Settings](https://app.crewai.com/crewai_plus/settings/account)

### 2. Install Required Package

```bash
uv add crewai-tools
```

## Available Actions

<AccordionGroup>
  <Accordion title="GOOGLE_SHEETS_GET_ROW">
    **Description:** Get rows from a Google Sheets spreadsheet.

    **Parameters:**
    - `spreadsheetId` (string, required): Spreadsheet - Use Connect Portal Workflow Settings to allow users to select a spreadsheet. Defaults to using the first worksheet in the selected spreadsheet.
    - `limit` (string, optional): Limit rows - Limit the maximum number of rows to return.
  </Accordion>

  <Accordion title="GOOGLE_SHEETS_CREATE_ROW">
    **Description:** Create a new row in a Google Sheets spreadsheet.

    **Parameters:**
    - `spreadsheetId` (string, required): Spreadsheet - Use Connect Portal Workflow Settings to allow users to select a spreadsheet. Defaults to using the first worksheet in the selected spreadsheet..
    - `worksheet` (string, required): Worksheet - Your worksheet must have column headers.
    - `additionalFields` (object, required): Fields - Include fields to create this row with, as an object with keys of Column Names. Use Connect Portal Workflow Settings to allow users to select a Column Mapping.
      ```json
      {
        "columnName1": "columnValue1",
        "columnName2": "columnValue2",
        "columnName3": "columnValue3",
        "columnName4": "columnValue4"
      }
      ```
  </Accordion>

  <Accordion title="GOOGLE_SHEETS_UPDATE_ROW">
    **Description:** Update existing rows in a Google Sheets spreadsheet.

    **Parameters:**
    - `spreadsheetId` (string, required): Spreadsheet - Use Connect Portal Workflow Settings to allow users to select a spreadsheet. Defaults to using the first worksheet in the selected spreadsheet.
    - `worksheet` (string, required): Worksheet - Your worksheet must have column headers.
    - `filterFormula` (object, optional): A filter in disjunctive normal form - OR of AND groups of single conditions to identify which rows to update.
      ```json
      {
        "operator": "OR",
        "conditions": [
          {
            "operator": "AND",
            "conditions": [
              {
                "field": "status",
                "operator": "$stringExactlyMatches",
                "value": "pending"
              }
            ]
          }
        ]
      }
      ```
      Available operators: `$stringContains`, `$stringDoesNotContain`, `$stringExactlyMatches`, `$stringDoesNotExactlyMatch`, `$stringStartsWith`, `$stringDoesNotStartWith`, `$stringEndsWith`, `$stringDoesNotEndWith`, `$numberGreaterThan`, `$numberLessThan`, `$numberEquals`, `$numberDoesNotEqual`, `$dateTimeAfter`, `$dateTimeBefore`, `$dateTimeEquals`, `$booleanTrue`, `$booleanFalse`, `$exists`, `$doesNotExist`
    - `additionalFields` (object, required): Fields - Include fields to update, as an object with keys of Column Names. Use Connect Portal Workflow Settings to allow users to select a Column Mapping.
      ```json
      {
        "columnName1": "newValue1",
        "columnName2": "newValue2",
        "columnName3": "newValue3",
        "columnName4": "newValue4"
      }
      ```
  </Accordion>
</AccordionGroup>

## Usage Examples

### Basic Google Sheets Agent Setup

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

# Get enterprise tools (Google Sheets tools will be included)
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

# Create an agent with Google Sheets capabilities
sheets_agent = Agent(
    role="Data Manager",
    goal="Manage spreadsheet data and track information efficiently",
    backstory="An AI assistant specialized in data management and spreadsheet operations.",
    tools=[enterprise_tools]
)

# Task to add new data to a spreadsheet
data_entry_task = Task(
    description="Add a new customer record to the customer database spreadsheet with name, email, and signup date",
    agent=sheets_agent,
    expected_output="New customer record added successfully to the spreadsheet"
)

# Run the task
crew = Crew(
    agents=[sheets_agent],
    tasks=[data_entry_task]
)

crew.kickoff()
```

### Filtering Specific Google Sheets Tools

```python
from crewai_tools import CrewaiEnterpriseTools

# Get only specific Google Sheets tools
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token",
    actions_list=["google_sheets_get_row", "google_sheets_create_row"]
)

data_collector = Agent(
    role="Data Collector",
    goal="Collect and organize data in spreadsheets",
    backstory="An AI assistant that focuses on data collection and organization.",
    tools=enterprise_tools
)

# Task to collect and organize data
data_collection = Task(
    description="Retrieve current inventory data and add new product entries to the inventory spreadsheet",
    agent=data_collector,
    expected_output="Inventory data retrieved and new products added successfully"
)

crew = Crew(
    agents=[data_collector],
    tasks=[data_collection]
)

crew.kickoff()
```

### Data Analysis and Reporting

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

data_analyst = Agent(
    role="Data Analyst",
    goal="Analyze spreadsheet data and generate insights",
    backstory="An experienced data analyst who extracts insights from spreadsheet data.",
    tools=[enterprise_tools]
)

# Task to analyze data and create reports
analysis_task = Task(
    description="""
    1. Retrieve all sales data from the current month's spreadsheet
    2. Analyze the data for trends and patterns
    3. Create a summary report in a new row with key metrics
    """,
    agent=data_analyst,
    expected_output="Sales data analyzed and summary report created with key insights"
)

crew = Crew(
    agents=[data_analyst],
    tasks=[analysis_task]
)

crew.kickoff()
```

### Automated Data Updates

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

data_updater = Agent(
    role="Data Updater",
    goal="Automatically update and maintain spreadsheet data",
    backstory="An AI assistant that maintains data accuracy and updates records automatically.",
    tools=[enterprise_tools]
)

# Task to update data based on conditions
update_task = Task(
    description="""
    1. Find all pending orders in the orders spreadsheet
    2. Update their status to 'processing'
    3. Add a timestamp for when the status was updated
    4. Log the changes in a separate tracking sheet
    """,
    agent=data_updater,
    expected_output="All pending orders updated to processing status with timestamps logged"
)

crew = Crew(
    agents=[data_updater],
    tasks=[update_task]
)

crew.kickoff()
```

### Complex Data Management Workflow

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

workflow_manager = Agent(
    role="Data Workflow Manager",
    goal="Manage complex data workflows across multiple spreadsheets",
    backstory="An AI assistant that orchestrates complex data operations across multiple spreadsheets.",
    tools=[enterprise_tools]
)

# Complex workflow task
workflow_task = Task(
    description="""
    1. Get all customer data from the main customer spreadsheet
    2. Create monthly summary entries for active customers
    3. Update customer status based on activity in the last 30 days
    4. Generate a monthly report with customer metrics
    5. Archive inactive customer records to a separate sheet
    """,
    agent=workflow_manager,
    expected_output="Monthly customer workflow completed with updated statuses and generated reports"
)

crew = Crew(
    agents=[workflow_manager],
    tasks=[workflow_task]
)

crew.kickoff()
```

## Troubleshooting

### Common Issues

**Permission Errors**
- Ensure your Google account has edit access to the target spreadsheets
- Verify that the OAuth connection includes required scopes for Google Sheets API
- Check that spreadsheets are shared with the authenticated account

**Spreadsheet Structure Issues**
- Ensure worksheets have proper column headers before creating or updating rows
- Verify that column names in `additionalFields` match the actual column headers
- Check that the specified worksheet exists in the spreadsheet

**Data Type and Format Issues**
- Ensure data values match the expected format for each column
- Use proper date formats for date columns (ISO format recommended)
- Verify that numeric values are properly formatted for number columns

**Filter Formula Issues**
- Ensure filter formulas follow the correct JSON structure for disjunctive normal form
- Use valid field names that match actual column headers
- Test simple filters before building complex multi-condition queries
- Verify that operator types match the data types in the columns

**Row Limits and Performance**
- Be mindful of row limits when using `GOOGLE_SHEETS_GET_ROW`
- Consider pagination for large datasets
- Use specific filters to reduce the amount of data processed

**Update Operations**
- Ensure filter conditions properly identify the intended rows for updates
- Test filter conditions with small datasets before large updates
- Verify that all required fields are included in update operations

### Getting Help

<Card title="Need Help?" icon="headset" href="mailto:<EMAIL>">
  Contact our support team for assistance with Google Sheets integration setup or troubleshooting.
</Card>
