---
title: S3 Reader Tool
description: The `S3ReaderTool` enables CrewAI agents to read files from Amazon S3 buckets.
icon: aws
---

# `S3ReaderTool`

## Description

The `S3ReaderTool` is designed to read files from Amazon S3 buckets. This tool allows CrewAI agents to access and retrieve content stored in S3, making it ideal for workflows that require reading data, configuration files, or any other content stored in AWS S3 storage.

## Installation

To use this tool, you need to install the required dependencies:

```shell
uv add boto3
```

## Steps to Get Started

To effectively use the `S3ReaderTool`, follow these steps:

1. **Install Dependencies**: Install the required packages using the command above.
2. **Configure AWS Credentials**: Set up your AWS credentials as environment variables.
3. **Initialize the Tool**: Create an instance of the tool.
4. **Specify S3 Path**: Provide the S3 path to the file you want to read.

## Example

The following example demonstrates how to use the `S3ReaderTool` to read a file from an S3 bucket:

```python Code
from crewai import Agent, Task, Crew
from crewai_tools.aws.s3 import S3ReaderTool

# Initialize the tool
s3_reader_tool = S3ReaderTool()

# Define an agent that uses the tool
file_reader_agent = Agent(
    role="File Reader",
    goal="Read files from S3 buckets",
    backstory="An expert in retrieving and processing files from cloud storage.",
    tools=[s3_reader_tool],
    verbose=True,
)

# Example task to read a configuration file
read_task = Task(
    description="Read the configuration file from {my_bucket} and summarize its contents.",
    expected_output="A summary of the configuration file contents.",
    agent=file_reader_agent,
)

# Create and run the crew
crew = Crew(agents=[file_reader_agent], tasks=[read_task])
result = crew.kickoff(inputs={"my_bucket": "s3://my-bucket/config/app-config.json"})
```

## Parameters

The `S3ReaderTool` accepts the following parameter when used by an agent:

- **file_path**: Required. The S3 file path in the format `s3://bucket-name/file-name`.

## AWS Credentials

The tool requires AWS credentials to access S3 buckets. You can configure these credentials using environment variables:

- **CREW_AWS_REGION**: The AWS region where your S3 bucket is located. Default is `us-east-1`.
- **CREW_AWS_ACCESS_KEY_ID**: Your AWS access key ID.
- **CREW_AWS_SEC_ACCESS_KEY**: Your AWS secret access key.

## Usage

When using the `S3ReaderTool` with an agent, the agent will need to provide the S3 file path:

```python Code
# Example of using the tool with an agent
file_reader_agent = Agent(
    role="File Reader",
    goal="Read files from S3 buckets",
    backstory="An expert in retrieving and processing files from cloud storage.",
    tools=[s3_reader_tool],
    verbose=True,
)

# Create a task for the agent to read a specific file
read_config_task = Task(
    description="Read the application configuration file from {my_bucket} and extract the database connection settings.",
    expected_output="The database connection settings from the configuration file.",
    agent=file_reader_agent,
)

# Run the task
crew = Crew(agents=[file_reader_agent], tasks=[read_config_task])
result = crew.kickoff(inputs={"my_bucket": "s3://my-bucket/config/app-config.json"})
```

## Error Handling

The `S3ReaderTool` includes error handling for common S3 issues:

- Invalid S3 path format
- Missing or inaccessible files
- Permission issues
- AWS credential problems

When an error occurs, the tool will return an error message that includes details about the issue.

## Implementation Details

The `S3ReaderTool` uses the AWS SDK for Python (boto3) to interact with S3:

```python Code
class S3ReaderTool(BaseTool):
    name: str = "S3 Reader Tool"
    description: str = "Reads a file from Amazon S3 given an S3 file path"
    
    def _run(self, file_path: str) -> str:
        try:
            bucket_name, object_key = self._parse_s3_path(file_path)

            s3 = boto3.client(
                's3',
                region_name=os.getenv('CREW_AWS_REGION', 'us-east-1'),
                aws_access_key_id=os.getenv('CREW_AWS_ACCESS_KEY_ID'),
                aws_secret_access_key=os.getenv('CREW_AWS_SEC_ACCESS_KEY')
            )

            # Read file content from S3
            response = s3.get_object(Bucket=bucket_name, Key=object_key)
            file_content = response['Body'].read().decode('utf-8')

            return file_content
        except ClientError as e:
            return f"Error reading file from S3: {str(e)}"
```

## Conclusion

The `S3ReaderTool` provides a straightforward way to read files from Amazon S3 buckets. By enabling agents to access content stored in S3, it facilitates workflows that require cloud-based file access. This tool is particularly useful for data processing, configuration management, and any task that involves retrieving information from AWS S3 storage. 