---
title: CrewAI Examples
description: A collection of examples that show how to use CrewAI framework to automate workflows.
icon: rocket-launch
---

<CardGroup cols={3}>
  <Card
  title="Marketing Strategy"
  color="#F3A78B"
  href="https://github.com/crewAIInc/crewAI-examples/tree/main/marketing_strategy"
  icon="bullhorn"
    iconType="solid"
  >
    Automate marketing strategy creation with CrewAI. 
  </Card>
  <Card
    title="Surprise Trip"
    color="#F3A78B"
    href="https://github.com/crewAIInc/crewAI-examples/tree/main/surprise_trip"
    icon="plane"
      iconType="duotone"
    >
      Create a surprise trip itinerary with CrewAI.
    </Card>
    <Card
    title="Match Profile to Positions"
    color="#F3A78B"
    href="https://github.com/crewAIInc/crewAI-examples/tree/main/match_profile_to_positions"
    icon="linkedin"
      iconType="duotone"
    >
      Match a profile to jobpositions with CrewAI.
    </Card>
    <Card
    title="Create Job Posting"
    color="#F3A78B"
    href="https://github.com/crewAIInc/crewAI-examples/tree/main/job-posting"
    icon="newspaper"
      iconType="duotone"
    >
      Create a job posting with CrewAI.
    </Card>
    <Card
    title="Game Generator"
    color="#F3A78B"
    href="https://github.com/crewAIInc/crewAI-examples/tree/main/game-builder-crew"
    icon="gamepad"
      iconType="duotone"
    >
      Create a game with CrewAI.
    </Card>
    <Card
    title="Find Job Candidates"
    color="#F3A78B"
    href="https://github.com/crewAIInc/crewAI-examples/tree/main/recruitment"
    icon="user-group"
      iconType="duotone"
    >
      Find job candidates with CrewAI.
    </Card>
</CardGroup>
