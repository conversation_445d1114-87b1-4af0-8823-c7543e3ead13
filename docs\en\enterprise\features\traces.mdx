---
title: Traces
description: "Using Traces to monitor your Crews"
icon: "timeline"
---

## Overview

Traces provide comprehensive visibility into your crew executions, helping you monitor performance, debug issues, and optimize your AI agent workflows.

## What are Traces?

Traces in CrewAI Enterprise are detailed execution records that capture every aspect of your crew's operation, from initial inputs to final outputs. They record:

- Agent thoughts and reasoning
- Task execution details
- Tool usage and outputs
- Token consumption metrics
- Execution times
- Cost estimates

<Frame>
  ![Traces Overview](/images/enterprise/traces-overview.png)
</Frame>

## Accessing Traces

<Steps>
  <Step title="Navigate to the Traces Tab">
    Once in your CrewAI Enterprise dashboard, click on the **Traces** to view all execution records.
  </Step>
  
  <Step title="Select an Execution">
    You'll see a list of all crew executions, sorted by date. Click on any execution to view its detailed trace.
  </Step>
</Steps>

## Understanding the Trace Interface

The trace interface is divided into several sections, each providing different insights into your crew's execution:

### 1. Execution Summary

The top section displays high-level metrics about the execution:

- **Total Tokens**: Number of tokens consumed across all tasks
- **Prompt Tokens**: Tokens used in prompts to the LLM
- **Completion Tokens**: Tokens generated in LLM responses
- **Requests**: Number of API calls made
- **Execution Time**: Total duration of the crew run
- **Estimated Cost**: Approximate cost based on token usage

<Frame>
  ![Execution Summary](/images/enterprise/trace-summary.png)
</Frame>

### 2. Tasks & Agents

This section shows all tasks and agents that were part of the crew execution:

- Task name and agent assignment
- Agents and LLMs used for each task
- Status (completed/failed)
- Individual execution time of the task

<Frame>
  ![Task List](/images/enterprise/trace-tasks.png)
</Frame>

### 3. Final Output

Displays the final result produced by the crew after all tasks are completed.

<Frame>
  ![Final Output](/images/enterprise/final-output.png)
</Frame>

### 4. Execution Timeline

A visual representation of when each task started and ended, helping you identify bottlenecks or parallel execution patterns.

<Frame>
  ![Execution Timeline](/images/enterprise/trace-timeline.png)
</Frame>

### 5. Detailed Task View

When you click on a specific task in the timeline or task list, you'll see:

<Frame>
  ![Detailed Task View](/images/enterprise/trace-detailed-task.png)
</Frame>

- **Task Key**: Unique identifier for the task
- **Task ID**: Technical identifier in the system
- **Status**: Current state (completed/running/failed)
- **Agent**: Which agent performed the task
- **LLM**: Language model used for this task
- **Start/End Time**: When the task began and completed
- **Execution Time**: Duration of this specific task
- **Task Description**: What the agent was instructed to do
- **Expected Output**: What output format was requested
- **Input**: Any input provided to this task from previous tasks
- **Output**: The actual result produced by the agent


## Using Traces for Debugging

Traces are invaluable for troubleshooting issues with your crews:

<Steps>
  <Step title="Identify Failure Points">
    When a crew execution doesn't produce the expected results, examine the trace to find where things went wrong. Look for:
    
    - Failed tasks
    - Unexpected agent decisions
    - Tool usage errors
    - Misinterpreted instructions

    <Frame>
      ![Failure Points](/images/enterprise/failure.png)
    </Frame>
  </Step>
  
  <Step title="Optimize Performance">
    Use execution metrics to identify performance bottlenecks:
    
    - Tasks that took longer than expected
    - Excessive token usage
    - Redundant tool operations
    - Unnecessary API calls
  </Step>
  
  <Step title="Improve Cost Efficiency">
    Analyze token usage and cost estimates to optimize your crew's efficiency:
    
    - Consider using smaller models for simpler tasks
    - Refine prompts to be more concise
    - Cache frequently accessed information
    - Structure tasks to minimize redundant operations
  </Step>
</Steps>

<Card title="Need Help?" icon="headset" href="mailto:<EMAIL>">
  Contact our support team for assistance with trace analysis or any other CrewAI Enterprise features.
</Card>