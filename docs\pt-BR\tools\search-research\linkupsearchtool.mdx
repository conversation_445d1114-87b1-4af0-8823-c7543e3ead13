---
title: Linkup Search Tool
description: O `LinkupSearchTool` permite consultar a API do Linkup para obter informações contextuais.
icon: link
---

# `LinkupSearchTool`

## Descrição

O `LinkupSearchTool` fornece a capacidade de consultar a API do Linkup para obter informações contextuais e recuperar resultados estruturados. Esta ferramenta é ideal para enriquecer fluxos de trabalho com informações atualizadas e confiáveis do Linkup, permitindo que agentes acessem dados relevantes durante a execução de suas tarefas.

## Instalação

Para utilizar esta ferramenta, é necessário instalar o Linkup SDK:

```shell
uv add linkup-sdk
```

## Passos para começar

Para usar efetivamente o `LinkupSearchTool`, siga estes passos:

1. **Chave de API**: Obtenha uma chave de API do Linkup.
2. **Configuração do Ambiente**: Configure seu ambiente com a chave de API.
3. **Instalar SDK**: Instale o Linkup SDK usando o comando acima.

## Exemplo

O exemplo a seguir demonstra como inicializar a ferramenta e usá-la em um agente:

```python Code
from crewai_tools import LinkupSearchTool
from crewai import Agent
import os

# Inicialize a ferramenta com sua chave de API
linkup_ferramenta = LinkupSearchTool(api_key=os.getenv("LINKUP_API_KEY"))

# Defina um agente que usa a ferramenta
@agent
def pesquisador(self) -> Agent:
    '''
    Este agente usa o LinkupSearchTool para recuperar informações contextuais
    da API do Linkup.
    '''
    return Agent(
        config=self.agentes_config["pesquisador"],
        tools=[linkup_ferramenta]
    )
```