---
title: Integração com Gmail
description: "Gerenciamento de e-mails e contatos com a integração do Gmail para o CrewAI."
icon: "envelope"
---

## Visão Geral

Permita que seus agentes gerenciem e-mails, contatos e rascunhos através do Gmail. Envie e-mails, pesquise mensagens, gerencie contatos, crie rascunhos e otimize suas comunicações por e-mail com automação impulsionada por IA.

## Pré-requisitos

Antes de usar a integração com o Gmail, certifique-se de que você possui:

- Uma conta [CrewAI Enterprise](https://app.crewai.com) com assinatura ativa
- Uma conta do Gmail com as permissões adequadas
- Conectou sua conta do Gmail através da [página de Integrações](https://app.crewai.com/crewai_plus/connectors)

## Configurando a Integração com o Gmail

### 1. Conecte sua Conta do Gmail

1. Navegue até [Integrações CrewAI Enterprise](https://app.crewai.com/crewai_plus/connectors)
2. Encontre **Gmail** na seção de Integrações de Autenticação
3. Clique em **Conectar** e conclua o fluxo OAuth
4. Conceda as permissões necessárias para o gerenciamento de e-mail e contato
5. Copie seu Token Empresarial em [Configurações de Conta](https://app.crewai.com/crewai_plus/settings/account)

### 2. Instale o Pacote Necessário

```bash
uv add crewai-tools
```

## Ações Disponíveis

<AccordionGroup>
  <Accordion title="GMAIL_SEND_EMAIL">
    **Descrição:** Envia um e-mail pelo Gmail.

    **Parâmetros:**
    - `toRecipients` (array, obrigatório): Para - Especifique os destinatários como uma única string ou um array JSON.
      ```json
      [
        "<EMAIL>",
        "<EMAIL>"
      ]
      ```
    - `from` (string, obrigatório): De - Especifique o e-mail do remetente.
    - `subject` (string, obrigatório): Assunto - Especifique o assunto da mensagem.
    - `messageContent` (string, obrigatório): Conteúdo da Mensagem - Especifique o conteúdo do e-mail em texto simples ou HTML.
    - `attachments` (string, opcional): Anexos - Aceita um único objeto de arquivo ou um array JSON de objetos de arquivo.
    - `additionalHeaders` (object, opcional): Cabeçalhos Adicionais - Especifique quaisquer campos de cabeçalho adicionais aqui.
      ```json
      {
        "reply-to": "Nome do Remetente <<EMAIL>>"
      }
      ```
  </Accordion>

  <Accordion title="GMAIL_GET_EMAIL_BY_ID">
    **Descrição:** Obtém um e-mail pelo ID no Gmail.

    **Parâmetros:**
    - `userId` (string, obrigatório): ID do Usuário - Especifique o endereço de e-mail do usuário. (exemplo: "<EMAIL>").
    - `messageId` (string, obrigatório): ID da Mensagem - Especifique o ID da mensagem a ser recuperada.
  </Accordion>

  <Accordion title="GMAIL_SEARCH_FOR_EMAIL">
    **Descrição:** Pesquisa e-mails no Gmail usando filtros avançados.

    **Parâmetros:**
    - `emailFilterFormula` (object, opcional): Um filtro na forma normal disjuntiva - OU de grupos E de condições únicas.
      ```json
      {
        "operator": "OR",
        "conditions": [
          {
            "operator": "AND",
            "conditions": [
              {
                "field": "from",
                "operator": "$stringContains",
                "value": "<EMAIL>"
              }
            ]
          }
        ]
      }
      ```
      Campos disponíveis: `from`, `to`, `date`, `label`, `subject`, `cc`, `bcc`, `category`, `deliveredto:`, `size`, `filename`, `older_than`, `newer_than`, `list`, `is:important`, `is:unread`, `is:snoozed`, `is:starred`, `is:read`, `has:drive`, `has:document`, `has:spreadsheet`, `has:presentation`, `has:attachment`, `has:youtube`, `has:userlabels`
    - `paginationParameters` (object, opcional): Parâmetros de Paginação.
      ```json
      {
        "pageCursor": "page_cursor_string"
      }
      ```
  </Accordion>

  <Accordion title="GMAIL_DELETE_EMAIL">
    **Descrição:** Exclui um e-mail no Gmail.

    **Parâmetros:**
    - `userId` (string, obrigatório): ID do Usuário - Especifique o endereço de e-mail do usuário. (exemplo: "<EMAIL>").
    - `messageId` (string, obrigatório): ID da Mensagem - Especifique o ID da mensagem para enviar para a lixeira.
  </Accordion>

  <Accordion title="GMAIL_CREATE_A_CONTACT">
    **Descrição:** Cria um contato no Gmail.

    **Parâmetros:**
    - `givenName` (string, obrigatório): Primeiro Nome - Especifique o Primeiro Nome do contato a ser criado. (exemplo: "João").
    - `familyName` (string, obrigatório): Sobrenome - Especifique o Sobrenome do contato a ser criado. (exemplo: "Silva").
    - `email` (string, obrigatório): E-mail - Especifique o endereço de e-mail do contato a ser criado.
    - `additionalFields` (object, opcional): Campos Adicionais - Informações adicionais de contato.
      ```json
      {
        "addresses": [
          {
            "streetAddress": "1000 North St.",
            "city": "Los Angeles"
          }
        ]
      }
      ```
  </Accordion>

  <Accordion title="GMAIL_GET_CONTACT_BY_RESOURCE_NAME">
    **Descrição:** Obtém um contato pelo nome do recurso no Gmail.

    **Parâmetros:**
    - `resourceName` (string, obrigatório): Nome do Recurso - Especifique o nome do recurso do contato a ser buscado.
  </Accordion>

  <Accordion title="GMAIL_SEARCH_FOR_CONTACT">
    **Descrição:** Pesquisa um contato no Gmail.

    **Parâmetros:**
    - `searchTerm` (string, obrigatório): Termo - Especifique um termo para buscar correspondências aproximadas ou exatas nos campos nome, apelido, endereços de e-mail, números de telefone ou organizações do contato.
  </Accordion>

  <Accordion title="GMAIL_DELETE_CONTACT">
    **Descrição:** Exclui um contato no Gmail.

    **Parâmetros:**
    - `resourceName` (string, obrigatório): Nome do Recurso - Especifique o nome do recurso do contato a ser excluído.
  </Accordion>

  <Accordion title="GMAIL_CREATE_DRAFT">
    **Descrição:** Cria um rascunho no Gmail.

    **Parâmetros:**
    - `toRecipients` (array, opcional): Para - Especifique os destinatários como uma única string ou um array JSON.
      ```json
      [
        "<EMAIL>",
        "<EMAIL>"
      ]
      ```
    - `from` (string, opcional): De - Especifique o e-mail do remetente.
    - `subject` (string, opcional): Assunto - Especifique o assunto da mensagem.
    - `messageContent` (string, opcional): Conteúdo da Mensagem - Especifique o conteúdo do e-mail em texto simples ou HTML.
    - `attachments` (string, opcional): Anexos - Aceita um único objeto de arquivo ou um array JSON de objetos de arquivo.
    - `additionalHeaders` (object, opcional): Cabeçalhos Adicionais - Especifique quaisquer campos de cabeçalho adicionais aqui.
      ```json
      {
        "reply-to": "Nome do Remetente <<EMAIL>>"
      }
      ```
  </Accordion>
</AccordionGroup>

## Exemplos de Uso

### Configuração Básica de Agente Gmail

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

# Get enterprise tools (Gmail tools will be included)
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

# Create an agent with Gmail capabilities
gmail_agent = Agent(
    role="Email Manager",
    goal="Manage email communications and contacts efficiently",
    backstory="An AI assistant specialized in email management and communication.",
    tools=[enterprise_tools]
)

# Task to send a follow-up email
send_email_task = Task(
    description="Send a follow-up <NAME_EMAIL> about the project update meeting",
    agent=gmail_agent,
    expected_output="Email sent successfully with confirmation"
)

# Run the task
crew = Crew(
    agents=[gmail_agent],
    tasks=[send_email_task]
)

crew.kickoff()
```

### Filtrando Ferramentas Específicas do Gmail

```python
from crewai_tools import CrewaiEnterpriseTools

# Get only specific Gmail tools
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token",
    actions_list=["gmail_send_email", "gmail_search_for_email", "gmail_create_draft"]
)

email_coordinator = Agent(
    role="Email Coordinator",
    goal="Coordinate email communications and manage drafts",
    backstory="An AI assistant that focuses on email coordination and draft management.",
    tools=enterprise_tools
)

# Task to prepare and send emails
email_coordination = Task(
    description="Search for emails from the marketing team, create a summary draft, and send it to stakeholders",
    agent=email_coordinator,
    expected_output="Summary email sent to stakeholders"
)

crew = Crew(
    agents=[email_coordinator],
    tasks=[email_coordination]
)

crew.kickoff()
```

### Gerenciamento de Contatos

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

contact_manager = Agent(
    role="Contact Manager",
    goal="Manage and organize email contacts efficiently",
    backstory="An experienced contact manager who maintains organized contact databases.",
    tools=[enterprise_tools]
)

# Task to manage contacts
contact_task = Task(
    description="""
    1. Search for contacts from the 'example.com' domain
    2. Create new contacts for recent email senders not in the contact list
    3. Update contact information with recent interaction data
    """,
    agent=contact_manager,
    expected_output="Contact database updated with new contacts and recent interactions"
)

crew = Crew(
    agents=[contact_manager],
    tasks=[contact_task]
)

crew.kickoff()
```

### Pesquisa e Análise de E-mails

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

email_analyst = Agent(
    role="Email Analyst",
    goal="Analyze email patterns and provide insights",
    backstory="An AI assistant that analyzes email data to provide actionable insights.",
    tools=[enterprise_tools]
)

# Task to analyze email patterns
analysis_task = Task(
    description="""
    Search for all unread emails from the last 7 days,
    categorize them by sender domain,
    and create a summary report of communication patterns
    """,
    agent=email_analyst,
    expected_output="Email analysis report with communication patterns and recommendations"
)

crew = Crew(
    agents=[email_analyst],
    tasks=[analysis_task]
)

crew.kickoff()
```

### Fluxos de Trabalho Automatizados de E-mail

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

workflow_manager = Agent(
    role="Email Workflow Manager",
    goal="Automate email workflows and responses",
    backstory="An AI assistant that manages automated email workflows and responses.",
    tools=[enterprise_tools]
)

# Complex task involving multiple Gmail operations
workflow_task = Task(
    description="""
    1. Search for emails with 'urgent' in the subject from the last 24 hours
    2. Create draft responses for each urgent email
    3. Send automated acknowledgment emails to senders
    4. Create a summary report of urgent items requiring attention
    """,
    agent=workflow_manager,
    expected_output="Urgent emails processed with automated responses and summary report"
)

crew = Crew(
    agents=[workflow_manager],
    tasks=[workflow_task]
)

crew.kickoff()
```

### Precisa de Ajuda?

<Card title="Precisa de Ajuda?" icon="headset" href="mailto:<EMAIL>">
  Entre em contato com nosso time de suporte para obter assistência na configuração ou solução de problemas da integração Gmail.
</Card>