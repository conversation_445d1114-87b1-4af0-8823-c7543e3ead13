interactions:
- request:
    body: '{"model": "openai/gpt-4o-mini", "messages": [{"role": "system", "content":
      "Your goal is to rewrite the user query so that it is optimized for retrieval
      from a vector database. Consider how the query will be used to find relevant
      documents, and aim to make it more specific and context-aware. \n\n Do not include
      any other text than the rewritten query, especially any preamble or postamble
      and only add expected output format if its relevant to the rewritten query.
      \n\n Focus on the key words of the intended task and to retrieve the most relevant
      information. \n\n There will be some extra context provided that might need
      to be removed such as expected_output formats structured_outputs and other instructions."},
      {"role": "user", "content": "The original query is: What is Vidit''s favorite
      color?\n\nThis is the expected criteria for your final answer: Vidit''s favorclearite
      color.\nyou MUST return the actual complete content as the final answer, not
      a summary.."}], "stream": false, "stop": ["\nObservation:"]}'
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1017'
      content-type:
      - application/json
      host:
      - openrouter.ai
      http-referer:
      - https://litellm.ai
      user-agent:
      - litellm/1.68.0
      x-title:
      - liteLLM
    method: POST
    uri: https://openrouter.ai/api/v1/chat/completions
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAAwAAAP//4lKAAS4AAAAA//90kE1vE0EMhv9K9V64TGCbNGQ7N46gIg6IXhBaTWed
        Xbez49HYiaii/e9oqRKKBFf7/XjsE7iHx0B5db272W2uN++b3ep585k+jcmo/XqnYXvX5m/3cChV
        jtxThceXQvnDRzhM0lOChxTKgd8NxVY3spo4Mxzk4ZGiwSOOwd5GmUoiY8lwiJWCUQ9/qW0d4igc
        SeG/n5BkKFUeFD4fUnLYc2Ydu0pBJcNDTQoccjA+UvefLeeefsI3DhOphoHgT6iSCB5BldVCtoVG
        slFeSO+5Z3ujV/twlMpGV1GSVDhU2h80pDPOSxPn4WUwzz8c9FmNpoVloFoq/w7cl67Z3K7b9bq5
        beBwOGOUKlOxzuSJsi5/2C4c5xdd5lsHEwvpj7Bt3N/mricLnHRJjSGO1F/EzfyP0Nf6yx2vLPP8
        CwAA//8DAOHu/cIiAgAA
    headers:
      Access-Control-Allow-Origin:
      - '*'
      CF-RAY:
      - 9402c73df9d8859c-BOM
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 15 May 2025 12:53:27 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      Vary:
      - Accept-Encoding
      x-clerk-auth-message:
      - Invalid JWT form. A JWT consists of three parts separated by dots. (reason=token-invalid,
        token-carrier=header)
      x-clerk-auth-reason:
      - token-invalid
      x-clerk-auth-status:
      - signed-out
    status:
      code: 200
      message: OK
- request:
    body: '{"model": "openai/gpt-4o-mini", "messages": [{"role": "system", "content":
      "You are Information Agent. You have access to specific knowledge sources.\nYour
      personal goal is: Provide information based on knowledge sources\nTo give my
      best complete final answer to the task respond using the exact following format:\n\nThought:
      I now can give a great answer\nFinal Answer: Your final answer must be the great
      and the most complete as possible, it must be outcome described.\n\nI MUST use
      these formats, my job depends on it!"}, {"role": "user", "content": "\nCurrent
      Task: What is Vidit''s favorite color?\n\nThis is the expected criteria for
      your final answer: Vidit''s favorclearite color.\nyou MUST return the actual
      complete content as the final answer, not a summary.\n\nBegin! This is VERY
      important to you, use the tools available and give your best Final Answer, your
      job depends on it!\n\nThought:"}], "stream": false, "stop": ["\nObservation:"]}'
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '951'
      content-type:
      - application/json
      host:
      - openrouter.ai
      http-referer:
      - https://litellm.ai
      user-agent:
      - litellm/1.68.0
      x-title:
      - liteLLM
    method: POST
    uri: https://openrouter.ai/api/v1/chat/completions
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAAwAAAP//4lKAAS4AAAAA///iQjABAAAA//90kUGPEzEMhf+K5QuXdJmlpbvkthIg
        emFXQoIDoMpNPFNDJo6STLul6n9H09KyIDjmxc9+/rxH8Wix4zi5vpndTK+n8+Z2wo9vXj28fHff
        vW4+PNT5j1l6/wkNpqwb8ZzR4n3ieLdAg716DmhRE0eS512qk5lOeomCBnX1jV1Fi25N9cppnwJX
        0YgGXWaq7NH+HmvQrVUcF7Sf9xi0S1lXBW0cQjDYSpSyXmamohEtlqoJDUaqsuHlf34len5E2xjs
        uRTqGO0eswZGi1SKlEqxjmk0Vo5j0gVE3YKjCJ1sGAi6MShQLFvOAF/iW4kU4O74tvBRvNRnBVra
        aJbK4DRoBikQtcJWPIcdeHVDz7GyB4mQhlUQF3ZAG5JAq8BQdMiOi4GisBiHj+ZftIHA87hePeY5
        5cjcUfYSO1hLgZLYSSvurxRXaDBzOxQKZ4gnPhK7k3A4fDVYdqVyPxLsOKcsRwxtWvoVOZo3vm3Q
        4HCGl7L2qS6rfudYxus1I73zYS/69NZg1UrhorwYD/yHe+m5koQytnXk1uwvxc3hH12f1l8WeWI5
        HH4CAAD//wMAhZKqO+QCAAA=
    headers:
      Access-Control-Allow-Origin:
      - '*'
      CF-RAY:
      - 9402c7459f3f859c-BOM
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 15 May 2025 12:53:28 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      Vary:
      - Accept-Encoding
      x-clerk-auth-message:
      - Invalid JWT form. A JWT consists of three parts separated by dots. (reason=token-invalid,
        token-carrier=header)
      x-clerk-auth-reason:
      - token-invalid
      x-clerk-auth-status:
      - signed-out
    status:
      code: 200
      message: OK
version: 1
