---
title: Memória
description: Aproveitando sistemas de memória no framework CrewAI para aprimorar as capacidades dos agentes.
icon: database
---

## Visão Geral

O framework CrewAI oferece um sistema de memória sofisticado projetado para aprimorar significativamente as capacidades dos agentes de IA. O CrewAI disponibiliza **três abordagens distintas de memória** que atendem a diferentes casos de uso:

1. **Sistema Básico de Memória** - Memória de curto prazo, longo prazo e de entidades integradas
2. **Memória de Usuário** - Memória específica do usuário com integração ao Mem0 (abordagem legada)
3. **Memória Externa** - Provedores de memória externos autônomos (nova abordagem)

## Componentes do Sistema de Memória

| Componente             | Descrição                                                                                                                                             |
| :--------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Memória de Curto Prazo** | Armazena temporariamente interações e resultados recentes usando `RAG`, permitindo que os agentes recordem e utilizem informações relevantes ao contexto atual durante as execuções. |
| **Memória de Longo Prazo** | Preserva informações valiosas e aprendizados de execuções passadas, permitindo que os agentes construam e refinem seu conhecimento ao longo do tempo. |
| **Memória de Entidades**    | Captura e organiza informações sobre entidades (pessoas, lugares, conceitos) encontradas durante tarefas, facilitando um entendimento mais profundo e o mapeamento de relacionamentos. Utiliza `RAG` para armazenar informações de entidades. |
| **Memória Contextual**  | Mantém o contexto das interações combinando `ShortTermMemory`, `LongTermMemory` e `EntityMemory`, auxiliando na coerência e relevância das respostas dos agentes ao longo de uma sequência de tarefas ou conversas. |

## 1. Sistema Básico de Memória (Recomendado)

A abordagem mais simples e comum de uso. Ative a memória para sua crew com um único parâmetro:

### Início Rápido
```python
from crewai import Crew, Agent, Task, Process

# Habilitar o sistema básico de memória
crew = Crew(
    agents=[...],
    tasks=[...],
    process=Process.sequential,
    memory=True,  # Ativa memória de curto prazo, longo prazo e de entidades
    verbose=True
)
```

### Como Funciona
- **Memória de Curto Prazo**: Usa ChromaDB com RAG para o contexto atual
- **Memória de Longo Prazo**: Usa SQLite3 para armazenar resultados de tarefas entre sessões
- **Memória de Entidades**: Usa RAG para rastrear entidades (pessoas, lugares, conceitos)
- **Local de Armazenamento**: Localidade específica da plataforma via pacote `appdirs`
- **Diretório de Armazenamento Personalizado**: Defina a variável de ambiente `CREWAI_STORAGE_DIR`

## Transparência no Local de Armazenamento

<Info>
**Compreendendo os Locais de Armazenamento**: CrewAI utiliza diretórios específicos da plataforma para guardar arquivos de memória e conhecimento seguindo as convenções do sistema operacional. Conhecer esses locais ajuda na implantação em produção, backups e depuração.
</Info>

### Onde o CrewAI Armazena os Arquivos

Por padrão, o CrewAI usa a biblioteca `appdirs` para determinar os locais de armazenamento conforme a convenção da plataforma. Veja exatamente onde seus arquivos são armazenados:

#### Locais de Armazenamento Padrão por Plataforma

**macOS:**
```
~/Library/Application Support/CrewAI/{project_name}/
├── knowledge/           # Arquivos base de conhecimento ChromaDB
├── short_term_memory/   # Arquivos de memória de curto prazo ChromaDB  
├── long_term_memory/    # Arquivos de memória de longo prazo ChromaDB
├── entities/            # Arquivos de memória de entidades ChromaDB
└── long_term_memory_storage.db  # Banco de dados SQLite
```

**Linux:**
```
~/.local/share/CrewAI/{project_name}/
├── knowledge/
├── short_term_memory/
├── long_term_memory/
├── entities/
└── long_term_memory_storage.db
```

**Windows:**
```
C:\Users\<USER>\AppData\Local\CrewAI\{project_name}\
├── knowledge\
├── short_term_memory\
├── long_term_memory\
├── entities\
└── long_term_memory_storage.db
```

### Encontrando Seu Local de Armazenamento

Para ver exatamente onde o CrewAI está armazenando arquivos em seu sistema:

```python
from crewai.utilities.paths import db_storage_path
import os

# Obter o caminho base de armazenamento
storage_path = db_storage_path()
print(f"CrewAI storage location: {storage_path}")

# Listar todos os diretórios e arquivos do CrewAI
if os.path.exists(storage_path):
    print("\nStored files and directories:")
    for item in os.listdir(storage_path):
        item_path = os.path.join(storage_path, item)
        if os.path.isdir(item_path):
            print(f"📁 {item}/")
            # Exibir coleções ChromaDB
            if os.path.exists(item_path):
                for subitem in os.listdir(item_path):
                    print(f"   └── {subitem}")
        else:
            print(f"📄 {item}")
else:
    print("No CrewAI storage directory found yet.")
```

### Controlando Locais de Armazenamento

#### Opção 1: Variável de Ambiente (Recomendado)
```python
import os
from crewai import Crew

# Definir local de armazenamento personalizado
os.environ["CREWAI_STORAGE_DIR"] = "./my_project_storage"

# Toda a memória e conhecimento serão salvos em ./my_project_storage/
crew = Crew(
    agents=[...],
    tasks=[...],
    memory=True
)
```

#### Opção 2: Caminho de Armazenamento Personalizado
```python
import os
from crewai import Crew
from crewai.memory import LongTermMemory
from crewai.memory.storage.ltm_sqlite_storage import LTMSQLiteStorage

# Configurar local de armazenamento personalizado
custom_storage_path = "./storage"
os.makedirs(custom_storage_path, exist_ok=True)

crew = Crew(
    memory=True,
    long_term_memory=LongTermMemory(
        storage=LTMSQLiteStorage(
            db_path=f"{custom_storage_path}/memory.db"
        )
    )
)
```

#### Opção 3: Armazenamento Específico de Projeto
```python
import os
from pathlib import Path

# Armazenar no diretório do projeto
project_root = Path(__file__).parent
storage_dir = project_root / "crewai_storage"

os.environ["CREWAI_STORAGE_DIR"] = str(storage_dir)

# Todo o armazenamento ficará agora na pasta do projeto
```

### Padrão do Provedor de Embedding

<Info>
**Provedor de Embedding Padrão**: O CrewAI utiliza embeddings do OpenAI por padrão para garantir consistência e confiabilidade. Você pode facilmente customizar para combinar com seu provedor LLM ou utilizar embeddings locais.
</Info>

#### Compreendendo o Comportamento Padrão
```python
# Ao utilizar Claude como seu LLM...
from crewai import Agent, LLM

agent = Agent(
    role="Analyst",
    goal="Analyze data",
    backstory="Expert analyst",
    llm=LLM(provider="anthropic", model="claude-3-sonnet")  # Usando Claude
)

# O CrewAI usará embeddings OpenAI por padrão para garantir consistência
# Você pode customizar facilmente para combinar com seu provedor preferido
```

#### Personalizando Provedores de Embedding
```python
from crewai import Crew

# Opção 1: Combinar com seu provedor de LLM
crew = Crew(
    agents=[agent],
    tasks=[task],
    memory=True,
    embedder={
        "provider": "anthropic",  # Combine com seu provedor de LLM
        "config": {
            "api_key": "your-anthropic-key",
            "model": "text-embedding-3-small"
        }
    }
)

# Opção 2: Use embeddings locais (sem chamadas para API externa)
crew = Crew(
    agents=[agent],
    tasks=[task],
    memory=True,
    embedder={
        "provider": "ollama",
        "config": {"model": "mxbai-embed-large"}
    }
)
```

### Depuração de Problemas de Armazenamento

#### Verifique Permissões do Armazenamento
```python
import os
from crewai.utilities.paths import db_storage_path

storage_path = db_storage_path()
print(f"Storage path: {storage_path}")
print(f"Path exists: {os.path.exists(storage_path)}")
print(f"Is writable: {os.access(storage_path, os.W_OK) if os.path.exists(storage_path) else 'Path does not exist'}")

# Crie com permissões apropriadas
if not os.path.exists(storage_path):
    os.makedirs(storage_path, mode=0o755, exist_ok=True)
    print(f"Created storage directory: {storage_path}")
```

#### Inspecione Coleções do ChromaDB
```python
import chromadb
from crewai.utilities.paths import db_storage_path

# Conecte-se ao ChromaDB do CrewAI
storage_path = db_storage_path()
chroma_path = os.path.join(storage_path, "knowledge")

if os.path.exists(chroma_path):
    client = chromadb.PersistentClient(path=chroma_path)
    collections = client.list_collections()
    
    print("ChromaDB Collections:")
    for collection in collections:
        print(f"  - {collection.name}: {collection.count()} documentos")
else:
    print("No ChromaDB storage found")
```

#### Resetar Armazenamento (Depuração)
```python
from crewai import Crew

# Limpar todo o armazenamento de memória
crew = Crew(agents=[...], tasks=[...], memory=True)

# Limpar tipos específicos de memória
crew.reset_memories(command_type='short')     # Memória de curto prazo
crew.reset_memories(command_type='long')      # Memória de longo prazo  
crew.reset_memories(command_type='entity')    # Memória de entidades
crew.reset_memories(command_type='knowledge') # Armazenamento de conhecimento
```

### Melhores Práticas para Produção

1. **Defina o `CREWAI_STORAGE_DIR`** para um local conhecido em produção para maior controle
2. **Escolha explicitamente provedores de embeddings** para coincidir com seu setup de LLM
3. **Monitore o tamanho do diretório de armazenamento** em casos de grande escala
4. **Inclua diretórios de armazenamento** em sua política de backup
5. **Defina permissões apropriadas de arquivo** (0o755 para diretórios, 0o644 para arquivos)
6. **Use caminhos relativos ao projeto** para implantações containerizadas

### Problemas Comuns de Armazenamento

**Erros "ChromaDB permission denied":**
```bash
# Corrija permissões
chmod -R 755 ~/.local/share/CrewAI/
```

**Erros "Database is locked":**
```python
# Certifique-se que apenas uma instância CrewAI acesse o armazenamento
import fcntl
import os

storage_path = db_storage_path()
lock_file = os.path.join(storage_path, ".crewai.lock")

with open(lock_file, 'w') as f:
    fcntl.flock(f.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
    # Seu código CrewAI aqui
```

**Armazenamento não persiste entre execuções:**
```python
# Verifique se o local do armazenamento é consistente
import os
print("CREWAI_STORAGE_DIR:", os.getenv("CREWAI_STORAGE_DIR"))
print("Current working directory:", os.getcwd())
print("Computed storage path:", db_storage_path())
```

## Configuração Personalizada de Embedders

O CrewAI suporta múltiplos provedores de embeddings para oferecer flexibilidade na escolha da melhor opção para seu caso de uso. Aqui está um guia completo para configuração de diferentes provedores de embeddings para seu sistema de memória.

### Por que Escolher Diferentes Provedores de Embeddings?

- **Otimização de Custos**: Embeddings locais (Ollama) são gratuitos após configuração inicial
- **Privacidade**: Mantenha seus dados locais com Ollama ou use seu provedor preferido na nuvem
- **Desempenho**: Alguns modelos têm melhor desempenho para domínios ou idiomas específicos
- **Consistência**: Combine seu provedor de embedding com o de LLM
- **Conformidade**: Atenda a requisitos regulatórios ou organizacionais

### OpenAI Embeddings (Padrão)

A OpenAI oferece embeddings confiáveis e de alta qualidade para a maioria dos cenários.

```python
from crewai import Crew

# Configuração básica OpenAI (usa a variável de ambiente OPENAI_API_KEY)
crew = Crew(
    agents=[...],
    tasks=[...],
    memory=True,
    embedder={
        "provider": "openai",
        "config": {
            "model": "text-embedding-3-small"  # ou "text-embedding-3-large"
        }
    }
)

# Configuração avançada OpenAI
crew = Crew(
    memory=True,
    embedder={
        "provider": "openai",
        "config": {
            "api_key": "your-openai-api-key",  # Opcional: sobrescreve variável de ambiente
            "model": "text-embedding-3-large",
            "dimensions": 1536,  # Opcional: reduz as dimensões para armazenamento menor
            "organization_id": "your-org-id"  # Opcional: para contas organizacionais
        }
    }
)
```

### Azure OpenAI Embeddings

Para empresas que utilizam deploys Azure OpenAI.

```python
crew = Crew(
    memory=True,
    embedder={
        "provider": "openai",  # Use openai como provider para Azure
        "config": {
            "api_key": "your-azure-api-key",
            "api_base": "https://your-resource.openai.azure.com/",
            "api_type": "azure",
            "api_version": "2023-05-15",
            "model": "text-embedding-3-small",
            "deployment_id": "your-deployment-name"  # Nome do deploy Azure
        }
    }
)
```

### Google AI Embeddings

Use modelos de embeddings de texto do Google para integração com serviços do Google Cloud.

```python
crew = Crew(
    memory=True,
    embedder={
        "provider": "google",
        "config": {
            "api_key": "your-google-api-key",
            "model": "text-embedding-004"  # ou "text-embedding-preview-0409"
        }
    }
)
```

### Vertex AI Embeddings

Para usuários do Google Cloud com acesso ao Vertex AI.

```python
crew = Crew(
    memory=True,
    embedder={
        "provider": "vertexai",
        "config": {
            "project_id": "your-gcp-project-id",
            "region": "us-central1",  # ou sua região preferencial
            "api_key": "your-service-account-key",
            "model_name": "textembedding-gecko"
        }
    }
)
```

### Ollama Embeddings (Local)

Execute embeddings localmente para privacidade e economia.

```python
# Primeiro, instale e rode Ollama localmente, depois baixe um modelo de embedding:
# ollama pull mxbai-embed-large

crew = Crew(
    memory=True,
    embedder={
        "provider": "ollama",
        "config": {
            "model": "mxbai-embed-large",  # ou "nomic-embed-text"
            "url": "http://localhost:11434/api/embeddings"  # URL padrão do Ollama
        }
    }
)

# Para instalações personalizadas do Ollama
crew = Crew(
    memory=True,
    embedder={
        "provider": "ollama",
        "config": {
            "model": "mxbai-embed-large",
            "url": "http://your-ollama-server:11434/api/embeddings"
        }
    }
)
```

### Cohere Embeddings

Utilize os modelos de embedding da Cohere para suporte multilíngue.

```python
crew = Crew(
    memory=True,
    embedder={
        "provider": "cohere",
        "config": {
            "api_key": "your-cohere-api-key",
            "model": "embed-english-v3.0"  # ou "embed-multilingual-v3.0"
        }
    }
)
```

### VoyageAI Embeddings

Embeddings de alto desempenho otimizados para tarefas de recuperação.

```python
crew = Crew(
    memory=True,
    embedder={
        "provider": "voyageai",
        "config": {
            "api_key": "your-voyage-api-key",
            "model": "voyage-large-2",  # ou "voyage-code-2" para código
            "input_type": "document"  # ou "query"
        }
    }
)
```

### AWS Bedrock Embeddings

Para usuários AWS com acesso ao Bedrock.

```python
crew = Crew(
    memory=True,
    embedder={
        "provider": "bedrock",
        "config": {
            "aws_access_key_id": "your-access-key",
            "aws_secret_access_key": "your-secret-key",
            "region_name": "us-east-1",
            "model": "amazon.titan-embed-text-v1"
        }
    }
)
```

### Hugging Face Embeddings

Utilize modelos open-source do Hugging Face.

```python
crew = Crew(
    memory=True,
    embedder={
        "provider": "huggingface",
        "config": {
            "api_key": "your-hf-token",  # Opcional para modelos públicos
            "model": "sentence-transformers/all-MiniLM-L6-v2",
            "api_url": "https://api-inference.huggingface.co"  # ou seu endpoint customizado
        }
    }
)
```

### IBM Watson Embeddings

Para usuários do IBM Cloud.

```python
crew = Crew(
    memory=True,
    embedder={
        "provider": "watson",
        "config": {
            "api_key": "your-watson-api-key",
            "url": "your-watson-instance-url",
            "model": "ibm/slate-125m-english-rtrvr"
        }
    }
)
```

### Como Escolher o Provedor de Embedding Certo

| Provedor | Melhor Para | Prós | Contras |
|:---------|:----------|:------|:------|
| **OpenAI** | Uso geral, confiabilidade | Alta qualidade, bem testado | Custo, requer chave de API |
| **Ollama** | Privacidade, economia | Gratuito, local, privado | Requer configuração local |
| **Google AI** | Ecossistema Google | Bom desempenho | Requer conta Google |
| **Azure OpenAI** | Empresas, conformidade | Recursos corporativos | Configuração mais complexa |
| **Cohere** | Conteúdo multilíngue | Excelente suporte a idiomas | Uso especializado |
| **VoyageAI** | Tarefas de busca e recuperação | Otimizado para pesquisa | Provedor mais novo |

### Configuração via Variável de Ambiente

Para segurança, armazene chaves de API em variáveis de ambiente:

```python
import os

# Configurar variáveis de ambiente
os.environ["OPENAI_API_KEY"] = "your-openai-key"
os.environ["GOOGLE_API_KEY"] = "your-google-key"
os.environ["COHERE_API_KEY"] = "your-cohere-key"

# Use sem expor as chaves no código
crew = Crew(
    memory=True,
    embedder={
        "provider": "openai",
        "config": {
            "model": "text-embedding-3-small"
            # A chave de API será carregada automaticamente da variável de ambiente
        }
    }
)
```

### Testando Diferentes Provedores de Embedding

Compare provedores de embedding para o seu caso de uso específico:

```python
from crewai import Crew
from crewai.utilities.paths import db_storage_path

# Testar diferentes provedores com os mesmos dados
providers_to_test = [
    {
        "name": "OpenAI",
        "config": {
            "provider": "openai",
            "config": {"model": "text-embedding-3-small"}
        }
    },
    {
        "name": "Ollama",
        "config": {
            "provider": "ollama", 
            "config": {"model": "mxbai-embed-large"}
        }
    }
]

for provider in providers_to_test:
    print(f"\nTesting {provider['name']} embeddings...")
    
    # Criar crew com embedder específico
    crew = Crew(
        agents=[...],
        tasks=[...],
        memory=True,
        embedder=provider['config']
    )
    
    # Execute o teste e meça o desempenho
    result = crew.kickoff()
    print(f"{provider['name']} completed successfully")
```

### Solução de Problemas de Embeddings

**Erros de modelo não encontrado:**
```python
# Verifique disponibilidade do modelo
from crewai.rag.embeddings.configurator import EmbeddingConfigurator

configurator = EmbeddingConfigurator()
try:
    embedder = configurator.configure_embedder({
        "provider": "ollama",
        "config": {"model": "mxbai-embed-large"}
    })
    print("Embedder configured successfully")
except Exception as e:
    print(f"Configuration error: {e}")
```

**Problemas com chave de API:**
```python
import os

# Verifique se as chaves de API estão configuradas
required_keys = ["OPENAI_API_KEY", "GOOGLE_API_KEY", "COHERE_API_KEY"]
for key in required_keys:
    if os.getenv(key):
        print(f"✅ {key} is set")
    else:
        print(f"❌ {key} is not set")
```

**Comparação de desempenho:**
```python
import time

def test_embedding_performance(embedder_config, test_text="This is a test document"):
    start_time = time.time()
    
    crew = Crew(
        agents=[...],
        tasks=[...],
        memory=True,
        embedder=embedder_config
    )
    
    # Simula operação de memória
    crew.kickoff()
    
    end_time = time.time()
    return end_time - start_time

# Comparar desempenho
openai_time = test_embedding_performance({
    "provider": "openai",
    "config": {"model": "text-embedding-3-small"}
})

ollama_time = test_embedding_performance({
    "provider": "ollama", 
    "config": {"model": "mxbai-embed-large"}
})

print(f"OpenAI: {openai_time:.2f}s")
print(f"Ollama: {ollama_time:.2f}s")
```

## 2. Memória de Usuário com Mem0 (Legado)

<Warning>
**Abordagem Legada**: Embora totalmente funcional, esta abordagem é considerada legada. Para novos projetos que exijam memória específica do usuário, considere usar Memória Externa.
</Warning>

A Memória de Usuário se integra com o [Mem0](https://mem0.ai/) para fornecer memória específica do usuário que persiste entre sessões e se integra ao sistema de memória contextual da crew.

### Pré-requisitos
```bash
pip install mem0ai
```

### Configuração Mem0 na Nuvem
```python
import os
from crewai import Crew, Process

# Defina sua chave de API do Mem0
os.environ["MEM0_API_KEY"] = "m0-your-api-key"

crew = Crew(
    agents=[...],
    tasks=[...],
    memory=True,  # Necessário para integração com a memória contextual
    memory_config={
        "provider": "mem0",
        "config": {"user_id": "john"},
        "user_memory": {}  # Obrigatório - inicializa a memória de usuário
    },
    process=Process.sequential,
    verbose=True
)
```

### Configuração Avançada Mem0
```python
crew = Crew(
    agents=[...],
    tasks=[...],
    memory=True,
    memory_config={
        "provider": "mem0",
        "config": {
            "user_id": "john",
            "org_id": "my_org_id",        # Opcional
            "project_id": "my_project_id", # Opcional
            "api_key": "custom-api-key"    # Opcional - sobrescreve variável de ambiente
        },
        "user_memory": {}
    }
)
```

### Configuração Mem0 Local
```python
crew = Crew(
    agents=[...],
    tasks=[...],
    memory=True,
    memory_config={
        "provider": "mem0",
        "config": {
            "user_id": "john",
            "local_mem0_config": {
                "vector_store": {
                    "provider": "qdrant",
                    "config": {"host": "localhost", "port": 6333}
                },
                "llm": {
                    "provider": "openai",
                    "config": {"api_key": "your-api-key", "model": "gpt-4"}
                },
                "embedder": {
                    "provider": "openai",
                    "config": {"api_key": "your-api-key", "model": "text-embedding-3-small"}
                }
            }
        },
        "user_memory": {}
    }
)
```

## 3. Memória Externa (Nova Abordagem)

A Memória Externa fornece um sistema de memória autônomo que opera independentemente da memória interna da crew. Isso é ideal para provedores de memória especializados ou compartilhamento de memória entre aplicações.

### Memória Externa Básica com Mem0
```python
import os
from crewai import Agent, Crew, Process, Task
from crewai.memory.external.external_memory import ExternalMemory

os.environ["MEM0_API_KEY"] = "your-api-key"

# Criar instância de memória externa
external_memory = ExternalMemory(
    embedder_config={
        "provider": "mem0", 
        "config": {"user_id": "U-123"}
    }
)

crew = Crew(
    agents=[...],
    tasks=[...],
    external_memory=external_memory,  # Independente da memória básica
    process=Process.sequential,
    verbose=True
)
```

### Implementação Personalizada de Armazenamento
```python
from crewai.memory.external.external_memory import ExternalMemory
from crewai.memory.storage.interface import Storage

class CustomStorage(Storage):
    def __init__(self):
        self.memories = []

    def save(self, value, metadata=None, agent=None):
        self.memories.append({
            "value": value, 
            "metadata": metadata, 
            "agent": agent
        })

    def search(self, query, limit=10, score_threshold=0.5):
        # Implemente sua lógica de busca aqui
        return [m for m in self.memories if query.lower() in str(m["value"]).lower()]

    def reset(self):
        self.memories = []

# Usando armazenamento customizado
external_memory = ExternalMemory(storage=CustomStorage())

crew = Crew(
    agents=[...],
    tasks=[...],
    external_memory=external_memory
)
```

## Comparação dos Sistemas de Memória

| Recurso | Memória Básica | Memória de Usuário (Legado) | Memória Externa |
|---------|---------------|-----------------------------|----------------|
| **Complexidade de Setup** | Simples | Média | Média |
| **Integração**           | Contextual integrada         | Contextual + específica do usuário | Autônoma |
| **Armazenamento**        | Arquivos locais              | Mem0 Cloud/Local     | Customizada/Mem0 |
| **Multi-sessão**         | ✅                           | ✅                 | ✅ |
| **Especificidade do Usuário** | ❌                     | ✅                 | ✅ |
| **Provedores Customizados**   | Limitado               | Apenas Mem0         | Qualquer provedor |
| **Recomendado para**     | Maioria dos casos           | Projetos legados     | Necessidades especializadas |

## Provedores de Embedding Suportados

### OpenAI (Padrão)
```python
crew = Crew(
    memory=True,
    embedder={
        "provider": "openai",
        "config": {"model": "text-embedding-3-small"}
    }
)
```

### Ollama
```python
crew = Crew(
    memory=True,
    embedder={
        "provider": "ollama",
        "config": {"model": "mxbai-embed-large"}
    }
)
```

### Google AI
```python
crew = Crew(
    memory=True,
    embedder={
        "provider": "google",
        "config": {
            "api_key": "your-api-key",
            "model": "text-embedding-004"
        }
    }
)
```

### Azure OpenAI
```python
crew = Crew(
    memory=True,
    embedder={
        "provider": "openai",
        "config": {
            "api_key": "your-api-key",
            "api_base": "https://your-resource.openai.azure.com/",
            "api_version": "2023-05-15",
            "model_name": "text-embedding-3-small"
        }
    }
)
```

### Vertex AI
```python
crew = Crew(
    memory=True,
    embedder={
        "provider": "vertexai",
        "config": {
            "project_id": "your-project-id",
            "region": "your-region",
            "api_key": "your-api-key",
            "model_name": "textembedding-gecko"
        }
    }
)
```

## Melhores Práticas de Segurança

### Variáveis de Ambiente
```python
import os
from crewai import Crew

# Armazene dados sensíveis em variáveis de ambiente
crew = Crew(
    memory=True,
    embedder={
        "provider": "openai",
        "config": {
            "api_key": os.getenv("OPENAI_API_KEY"),
            "model": "text-embedding-3-small"
        }
    }
)
```

### Segurança no Armazenamento
```python
import os
from crewai import Crew
from crewai.memory import LongTermMemory
from crewai.memory.storage.ltm_sqlite_storage import LTMSQLiteStorage

# Use caminhos seguros para armazenamento
storage_path = os.getenv("CREWAI_STORAGE_DIR", "./storage")
os.makedirs(storage_path, mode=0o700, exist_ok=True)  # Permissões restritas

crew = Crew(
    memory=True,
    long_term_memory=LongTermMemory(
        storage=LTMSQLiteStorage(
            db_path=f"{storage_path}/memory.db"
        )
    )
)
```

## Solução de Problemas

### Problemas Comuns

**A memória não está persistindo entre sessões?**
- Verifique a variável de ambiente `CREWAI_STORAGE_DIR`
- Garanta permissões de escrita no diretório de armazenamento
- Certifique-se que a memória está ativada com `memory=True`

**Erros de autenticação no Mem0?**
- Verifique se a variável de ambiente `MEM0_API_KEY` está definida
- Confira permissões da chave de API no painel do Mem0
- Certifique-se de que o pacote `mem0ai` está instalado

**Alto uso de memória com grandes volumes de dados?**
- Considere usar Memória Externa com armazenamento personalizado
- Implemente paginação nos métodos de busca do armazenamento customizado
- Utilize modelos de embedding menores para menor consumo de memória

### Dicas de Desempenho

- Use `memory=True` para a maioria dos casos (mais simples e rápido)
- Só utilize Memória de Usuário se precisar de persistência específica por usuário
- Considere Memória Externa para necessidades de grande escala ou especializadas
- Prefira modelos de embedding menores para maior rapidez
- Defina limites apropriados de busca para controlar o tamanho da recuperação

## Benefícios do Sistema de Memória do CrewAI

- 🦾 **Aprendizado Adaptativo:** As crews tornam-se mais eficientes ao longo do tempo, adaptando-se a novas informações e refinando sua abordagem para tarefas.
- 🫡 **Personalização Avançada:** A memória permite que agentes lembrem preferências do usuário e interações passadas, proporcionando experiências personalizadas.
- 🧠 **Melhoria na Resolução de Problemas:** O acesso a um rico acervo de memória auxilia os agentes a tomar decisões mais informadas, recorrendo a aprendizados prévios e contextuais.

## Conclusão

Integrar o sistema de memória do CrewAI em seus projetos é simples. Ao aproveitar os componentes e configurações oferecidos, 
você rapidamente capacita seus agentes a lembrar, raciocinar e aprender com suas interações, desbloqueando novos níveis de inteligência e capacidade.