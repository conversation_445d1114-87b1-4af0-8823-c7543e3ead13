---
title: Reexecutar Tarefas a partir do Último Crew Kickoff
description: Reexecute tarefas a partir do último crew.kickoff(...)
icon: arrow-right
---

## Introdução

O CrewAI oferece a capacidade de reexecutar uma tarefa especificada a partir do último crew kickoff. Esse recurso é particularmente útil quando você concluiu um kickoff e deseja tentar novamente determinadas tarefas, ou não precisa buscar dados novamente porque seus agentes já possuem o contexto salvo da execução do kickoff, sendo necessário apenas reexecutar as tarefas desejadas.

<Note>
    Você deve executar `crew.kickoff()` antes de poder reexecutar uma tarefa. 
    Atualmente, apenas o kickoff mais recente é suportado, então se você utilizar `kickoff_for_each`, será possível reexecutar apenas a partir da execução de crew mais recente.
</Note>

Aqui está um exemplo de como reexecutar a partir de uma tarefa:

### Reexecutando a partir de uma Tarefa Específica Usando o CLI

Para utilizar o recurso de reexecução, siga estes passos:

<Steps>
   <Step title="Abra seu terminal ou prompt de comando."></Step>
   <Step title="Navegue até o diretório onde está localizado seu projeto CrewAI."></Step>
   <Step title="Execute os seguintes comandos:">
      Para visualizar os task_ids do último kickoff, utilize:

      ```shell
      crewai log-tasks-outputs
      ```

      Após identificar o `task_id` que deseja reexecutar, utilize:

      ```shell
      crewai replay -t <task_id>
      ```
   </Step>
</Steps>

<Note>
    Certifique-se de que o `crewai` está instalado e devidamente configurado no seu ambiente de desenvolvimento.
</Note>

### Reexecutando uma Tarefa Programaticamente

Para reexecutar uma tarefa programaticamente, siga os passos abaixo:

<Steps>
   <Step title="Especifique o `task_id` e os parâmetros de entrada para o processo de reexecução.">
      Especifique o `task_id` e os parâmetros de entrada para o processo de reexecução.
   </Step>
   <Step title="Execute o comando de reexecução dentro de um bloco try-except para lidar com possíveis erros.">
      Execute o comando de reexecução dentro de um bloco try-except para lidar com possíveis erros.
      <CodeGroup>
          ```python Code
            def replay():
            """
            Replay the crew execution from a specific task.
            """
            task_id = '<task_id>'
            inputs = {"topic": "CrewAI Training"}  # This is optional; you can pass in the inputs you want to replay; otherwise, it uses the previous kickoff's inputs.
            try:
                YourCrewName_Crew().crew().replay(task_id=task_id, inputs=inputs)

            except subprocess.CalledProcessError as e:
                raise Exception(f"An error occurred while replaying the crew: {e}")

            except Exception as e:
                raise Exception(f"An unexpected error occurred: {e}")
            ```
      </CodeGroup>
   </Step>
</Steps>

## Conclusão

Com as melhorias acima e funcionalidades detalhadas, a reexecução de tarefas específicas no CrewAI ficou mais eficiente e robusta. 
Certifique-se de seguir exatamente os comandos e passos para aproveitar ao máximo esses recursos.