{"type": "excalidraw", "version": 2, "source": "https://excalidraw.com", "elements": [{"type": "rectangle", "version": 93, "versionNonce": 2144691130, "isDeleted": false, "id": "X8lXjCPPYpZB7Y1FtbGzG", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 608.8013492062021, "y": 865.639005543389, "strokeColor": "#1971c2", "backgroundColor": "#ffffff", "width": 130.15044368419717, "height": 71.63924200302131, "seed": 289380200, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "PNh-RA9nmQjefHjs7oAfP"}, {"id": "XfYKhgJJS-7cPmw_h7HPf", "type": "arrow"}, {"id": "s1jPgUuVvC-bRBFWhjZft", "type": "arrow"}, {"id": "MGaWQZZpp-Sa9rrWsOE5d", "type": "arrow"}], "updated": 1699639456062, "link": null, "locked": false}, {"type": "text", "version": 61, "versionNonce": 493833446, "isDeleted": false, "id": "PNh-RA9nmQjefHjs7oAfP", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 644.5796960483007, "y": 889.4586265448996, "strokeColor": "#1971c2", "backgroundColor": "#ffffff", "width": 58.59375, "height": 24, "seed": 803683688, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "Agent", "textAlign": "center", "verticalAlign": "middle", "containerId": "X8lXjCPPYpZB7Y1FtbGzG", "originalText": "Agent", "lineHeight": 1.2, "baseline": 20}, {"type": "rectangle", "version": 206, "versionNonce": 765779066, "isDeleted": false, "id": "jII8doF-kDbxbeqnvIrIQ", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 775.0274462366549, "y": 865.2339350525253, "strokeColor": "#1971c2", "backgroundColor": "#ffffff", "width": 122.34713351815549, "height": 71.63924200302131, "seed": 859064424, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "jc25mlkgbKWnioVebre2R"}, {"id": "MGaWQZZpp-Sa9rrWsOE5d", "type": "arrow"}], "updated": 1699639456062, "link": null, "locked": false}, {"type": "text", "version": 133, "versionNonce": 285261862, "isDeleted": false, "id": "jc25mlkgbKWnioVebre2R", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 806.9041379957326, "y": 889.053556054036, "strokeColor": "#1971c2", "backgroundColor": "#ffffff", "width": 58.59375, "height": 24, "seed": 1096743784, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "Agent", "textAlign": "center", "verticalAlign": "middle", "containerId": "jII8doF-kDbxbeqnvIrIQ", "originalText": "Agent", "lineHeight": 1.2, "baseline": 20}, {"type": "rectangle", "version": 250, "versionNonce": 210189626, "isDeleted": false, "id": "mpg5srkWOqzwUrl7AyGYB", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 455.47908948323857, "y": 865.2339350525253, "strokeColor": "#1971c2", "backgroundColor": "#ffffff", "width": 124.14083922774466, "height": 71.63924200302131, "seed": 768880920, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "vNLQ9_r5QH2aANQlEv4Zb"}, {"id": "JfFs5njalTDTZwcPxwGqM", "type": "arrow"}, {"id": "s1jPgUuVvC-bRBFWhjZft", "type": "arrow"}, {"id": "e6vUdZBZKTBgQ_bSmk0Hl", "type": "arrow"}], "updated": 1699639456062, "link": null, "locked": false}, {"type": "text", "version": 210, "versionNonce": 1350683494, "isDeleted": false, "id": "vNLQ9_r5QH2aANQlEv4Zb", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 488.2526340971109, "y": 889.053556054036, "strokeColor": "#1971c2", "backgroundColor": "#ffffff", "width": 58.59375, "height": 24, "seed": 727600664, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "Agent", "textAlign": "center", "verticalAlign": "middle", "containerId": "mpg5srkWOqzwUrl7AyGYB", "originalText": "Agent", "lineHeight": 1.2, "baseline": 20}, {"type": "arrow", "version": 530, "versionNonce": 1239385594, "isDeleted": false, "id": "XfYKhgJJS-7cPmw_h7HPf", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 670.0066128316184, "y": 952.9011947913083, "strokeColor": "#1971c2", "backgroundColor": "#ffffff", "width": 41.60266729316356, "height": 46.12439015074642, "seed": 1194628200, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "startBinding": {"elementId": "X8lXjCPPYpZB7Y1FtbGzG", "gap": 15.622947244898, "focus": 0.0887464442520198}, "endBinding": {"elementId": "5paVZDChShp0xOo-X_Tcv", "focus": 0.0949975535445239, "gap": 22.13749534212934}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [1.8207857274198886, 46.12439015074642], [41.60266729316356, 43.23377434131771]]}, {"type": "text", "version": 316, "versionNonce": 1782541990, "isDeleted": false, "id": "5paVZDChShp0xOo-X_Tcv", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 733.7467754669113, "y": 958.1812387901072, "strokeColor": "#1971c2", "backgroundColor": "#ffffff", "width": 92.4921875, "height": 73.6, "seed": 1777161752, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [{"id": "XfYKhgJJS-7cPmw_h7HPf", "type": "arrow"}], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 16, "fontFamily": 2, "text": "Role\nBackstory\nOverall goal\nDe<PERSON><PERSON>", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Role\nBackstory\nOverall goal\nDe<PERSON><PERSON>", "lineHeight": 1.15, "baseline": 70}, {"type": "text", "version": 126, "versionNonce": 1857074874, "isDeleted": false, "id": "uQYAXYz8k5L6pnRWMgHAX", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 461.30089865224477, "y": 833.8998327198113, "strokeColor": "#1971c2", "backgroundColor": "#ffffff", "width": 163.6484375, "height": 18.4, "seed": 86315368, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 16, "fontFamily": 2, "text": "Role Playing AI Agents", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Role Playing AI Agents", "lineHeight": 1.15, "baseline": 15}, {"type": "rectangle", "version": 453, "versionNonce": 451324390, "isDeleted": false, "id": "B_VsW41U3DiStY0GmCviW", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 416.1635159471326, "y": 792.1129950334148, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 520.8461448729983, "height": 588.1557713914018, "seed": 579517208, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"id": "TaqfLl-ZIqkjWi3JnP54R", "type": "arrow"}], "updated": 1699639456062, "link": null, "locked": false}, {"type": "text", "version": 118, "versionNonce": 1805504378, "isDeleted": false, "id": "FNL8RN19CKkcCTpAzMKl6", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 435.8153779680391, "y": 748.1534008626188, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 98.4375, "height": 33.6, "seed": 1058226024, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 28, "fontFamily": 3, "text": "CrewAI", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "CrewAI", "lineHeight": 1.2, "baseline": 27}, {"type": "rectangle", "version": 367, "versionNonce": 25460006, "isDeleted": false, "id": "8YZhMN-Z-Y7c5jEEyhBx7", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 693.7375194068313, "y": 1268.1990017186035, "strokeColor": "#2f9e44", "backgroundColor": "#ffffff", "width": 116.50694738481775, "height": 71.63924200302131, "seed": 542375784, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "fe80TkQnpXlnHQRJQxOVL"}, {"id": "JfFs5njalTDTZwcPxwGqM", "type": "arrow"}], "updated": 1699639456062, "link": null, "locked": false}, {"type": "text", "version": 337, "versionNonce": 619302970, "isDeleted": false, "id": "fe80TkQnpXlnHQRJQxOVL", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 728.5534930992402, "y": 1292.0186227201143, "strokeColor": "#2f9e44", "backgroundColor": "#ffffff", "width": 46.875, "height": 24, "seed": 2138034792, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "Task", "textAlign": "center", "verticalAlign": "middle", "containerId": "8YZhMN-Z-Y7c5jEEyhBx7", "originalText": "Task", "lineHeight": 1.2, "baseline": 20}, {"type": "rectangle", "version": 587, "versionNonce": 1401284710, "isDeleted": false, "id": "QC8CiIQoc_dint6cI3wII", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 562.7407978591666, "y": 1267.79393122774, "strokeColor": "#2f9e44", "backgroundColor": "#ffffff", "width": 118.30295419217396, "height": 71.63924200302131, "seed": 2059733864, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "rsQF8EKDTG1sLDJtzNFpb"}, {"id": "W1M9sYw-5TSPLuerxHjVA", "type": "arrow"}, {"id": "TaqfLl-ZIqkjWi3JnP54R", "type": "arrow"}], "updated": 1699639456062, "link": null, "locked": false}, {"type": "text", "version": 577, "versionNonce": 2121775354, "isDeleted": false, "id": "rsQF8EKDTG1sLDJtzNFpb", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 598.4547749552536, "y": 1291.6135522292507, "strokeColor": "#2f9e44", "backgroundColor": "#ffffff", "width": 46.875, "height": 24, "seed": 2145345128, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "Task", "textAlign": "center", "verticalAlign": "middle", "containerId": "QC8CiIQoc_dint6cI3wII", "originalText": "Task", "lineHeight": 1.2, "baseline": 20}, {"type": "arrow", "version": 865, "versionNonce": 280488870, "isDeleted": false, "id": "W1M9sYw-5TSPLuerxHjVA", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 670.3027729279274, "y": 1256.652571908749, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "width": 40.927823712162876, "height": 62.0716808163445, "seed": 142351128, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "startBinding": {"elementId": "QC8CiIQoc_dint6cI3wII", "focus": 0.8343773250150892, "gap": 11.141359318991022}, "endBinding": {"elementId": "aHO4Qz97fQd9kBEWp2f18", "focus": -0.17790737779386612, "gap": 14.756350354554911}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-3.4324423247306868, -62.0716808163445], [-40.927823712162876, -61.42888584124239]]}, {"type": "text", "version": 602, "versionNonce": 1741326778, "isDeleted": false, "id": "aHO4Qz97fQd9kBEWp2f18", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 501.65766136120965, "y": 1173.9274256158294, "strokeColor": "#2f9e44", "backgroundColor": "#ffffff", "width": 112.9609375, "height": 55.199999999999996, "seed": 2142503704, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [{"id": "W1M9sYw-5TSPLuerxHjVA", "type": "arrow"}], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 16, "fontFamily": 2, "text": "Description\nTools (optional)\nAgent (optional)", "textAlign": "right", "verticalAlign": "top", "containerId": null, "originalText": "Description\nTools (optional)\nAgent (optional)", "lineHeight": 1.15, "baseline": 51}, {"type": "arrow", "version": 79, "versionNonce": 927434470, "isDeleted": false, "id": "JfFs5njalTDTZwcPxwGqM", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 0, "opacity": 100, "angle": 0, "x": 540.9208229511146, "y": 946.5781736280221, "strokeColor": "#1971c2", "backgroundColor": "#ffffff", "width": 219.85637271350515, "height": 314.79581944333904, "seed": 1546990872, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "startBinding": {"elementId": "mpg5srkWOqzwUrl7AyGYB", "focus": 0.09672501519482216, "gap": 9.704996572475466}, "endBinding": {"elementId": "8YZhMN-Z-Y7c5jEEyhBx7", "focus": 0.4631860889460089, "gap": 6.825008647242498}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "triangle", "points": [[0, 0], [219.85637271350515, 314.79581944333904]]}, {"type": "rectangle", "version": 267, "versionNonce": 1745175162, "isDeleted": false, "id": "-q1fbWPyskBjT0LkGKcKs", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 455.0422722717899, "y": 1064.3491760306265, "strokeColor": "#f08c00", "backgroundColor": "#ffffff", "width": 453.01208379157214, "height": 68.64897328291423, "seed": 1540246808, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "UEpy93NCiBqsGS-Em9PXJ"}, {"id": "_jI4fJuFnfb1qJyihR1kB", "type": "arrow"}], "updated": 1699639456062, "link": null, "locked": false}, {"type": "text", "version": 256, "versionNonce": 2009184806, "isDeleted": false, "id": "UEpy93NCiBqsGS-Em9PXJ", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 640.532689167576, "y": 1086.6736626720838, "strokeColor": "#f08c00", "backgroundColor": "#ffffff", "width": 82.03125, "height": 24, "seed": 1753531928, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "Process", "textAlign": "center", "verticalAlign": "middle", "containerId": "-q1fbWPyskBjT0LkGKcKs", "originalText": "Process", "lineHeight": 1.2, "baseline": 20}, {"type": "text", "version": 558, "versionNonce": 703259450, "isDeleted": false, "id": "kpViWtgkHN8s9uvxRdcUd", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 1062.2759216003155, "y": 790.232266089163, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 304.6875, "height": 48, "seed": 2074460440, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [{"id": "_jI4fJuFnfb1qJyihR1kB", "type": "arrow"}], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "Processes define how \nagents will work together:", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Processes define how \nagents will work together:", "lineHeight": 1.2, "baseline": 44}, {"type": "text", "version": 169, "versionNonce": 1762887014, "isDeleted": false, "id": "6EQu517C9-YzKjO5FBO1Y", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 1070.6909472892282, "y": 1076.6477630998681, "strokeColor": "#1971c2", "backgroundColor": "#ffffff", "width": 269.53125, "height": 24, "seed": 615835240, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "researcher = Agent(...)", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "researcher = Agent(...)", "lineHeight": 1.2, "baseline": 20}, {"type": "text", "version": 170, "versionNonce": 132017146, "isDeleted": false, "id": "0EO9yXTorl1vkm0xLVihC", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 1070.6909472892282, "y": 1110.7891195078867, "strokeColor": "#1971c2", "backgroundColor": "#ffffff", "width": 222.65625, "height": 24, "seed": 1514214424, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "writer = Agent(...)", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "writer = Agent(...)", "lineHeight": 1.2, "baseline": 20}, {"type": "text", "version": 201, "versionNonce": 1015670950, "isDeleted": false, "id": "XefLIs4Dd_f1BiGMZ37FA", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 1070.5456543736445, "y": 1147.5024701842847, "strokeColor": "#2f9e44", "backgroundColor": "#ffffff", "width": 351.5625, "height": 24, "seed": 891017576, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "tasks = [Task(...), Task(...)]", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "tasks = [Task(...), Task(...)]", "lineHeight": 1.2, "baseline": 20}, {"type": "text", "version": 201, "versionNonce": 400743610, "isDeleted": false, "id": "NorJjGpryuzvNFhiDj9pC", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 1070.6909472892282, "y": 1215.6477630998681, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 140.625, "height": 24, "seed": 506908952, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "crew = Crew(", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "crew = Crew(", "lineHeight": 1.2, "baseline": 20}, {"type": "text", "version": 250, "versionNonce": 1909580774, "isDeleted": false, "id": "f3tohiSRFHKeqN3jYYfmR", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 1106.6909472892282, "y": 1249.6477630998681, "strokeColor": "#1971c2", "backgroundColor": "#ffffff", "width": 328.125, "height": 24, "seed": 573336680, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "agents=[researcher, writer],", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "agents=[researcher, writer],", "lineHeight": 1.2, "baseline": 20}, {"type": "text", "version": 253, "versionNonce": 932918650, "isDeleted": false, "id": "mcynkGK_Ds7IAEmVkL5PZ", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 1106.6909472892282, "y": 1283.6477630998681, "strokeColor": "#2f9e44", "backgroundColor": "#ffffff", "width": 140.625, "height": 24, "seed": 913108504, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "tasks=tasks,", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "tasks=tasks,", "lineHeight": 1.2, "baseline": 20}, {"type": "text", "version": 250, "versionNonce": 184055590, "isDeleted": false, "id": "HAAVhT1Gy72ZYA305zQKK", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 1106.6909472892282, "y": 1317.6477630998681, "strokeColor": "#f08c00", "backgroundColor": "#ffffff", "width": 304.6875, "height": 24, "seed": 1706205032, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "process=Process.sequential", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "process=Process.sequential", "lineHeight": 1.2, "baseline": 20}, {"type": "text", "version": 201, "versionNonce": 407533114, "isDeleted": false, "id": "dzWll65DmRY1TVvgy-JEs", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 1070.6909472892282, "y": 1351.6477630998681, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 11.71875, "height": 24, "seed": 2077715224, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": ")", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": ")", "lineHeight": 1.2, "baseline": 20}, {"type": "arrow", "version": 376, "versionNonce": 1080730214, "isDeleted": false, "id": "_jI4fJuFnfb1qJyihR1kB", "fillStyle": "solid", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 910.2713918528236, "y": 1087.208151536097, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 129.9211871441621, "height": 266.8059274659122, "seed": 2013212520, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "startBinding": {"elementId": "-q1fbWPyskBjT0LkGKcKs", "focus": 0.6170476426453826, "gap": 2.2170357894615336}, "endBinding": {"elementId": "kpViWtgkHN8s9uvxRdcUd", "focus": 0.8849820845071233, "gap": 22.08334260332981}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "triangle", "points": [[0, 0], [73.30772563277344, -26.90207887202405], [78.92422691916329, -231.51287552607664], [129.9211871441621, -266.8059274659122]]}, {"type": "text", "version": 660, "versionNonce": 1255382778, "isDeleted": false, "id": "4BNPrebnV080D0q3JFf_W", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 1068.5459353332037, "y": 1025.0473569656765, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 161.16793823242188, "height": 35, "seed": 1074386280, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 28, "fontFamily": 1, "text": "PseudoCode", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "PseudoCode", "lineHeight": 1.25, "baseline": 25}, {"type": "arrow", "version": 309, "versionNonce": 888497574, "isDeleted": false, "id": "s1jPgUuVvC-bRBFWhjZft", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 0, "opacity": 100, "angle": 0, "x": 562.1289908234301, "y": 949.1235328679721, "strokeColor": "#1971c2", "backgroundColor": "#ffffff", "width": 70.97110805194939, "height": 16.65546900023071, "seed": 824142872, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "startBinding": {"elementId": "mpg5srkWOqzwUrl7AyGYB", "focus": 0.402682533868382, "gap": 12.250355812425482}, "endBinding": {"elementId": "X8lXjCPPYpZB7Y1FtbGzG", "focus": -0.45496604450179245, "gap": 10.840795950252755}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "triangle", "points": [[0, 0], [32.36350123439172, 15.650979628921618], [70.97110805194939, -1.0044893713090914]]}, {"id": "fyIW_LMHBOxG0N_iTIajG", "type": "line", "x": 1071.165519554974, "y": 1059.830041163249, "width": 163.2801513671875, "height": 0, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 214764454, "version": 135, "versionNonce": 1270460346, "isDeleted": false, "boundElements": null, "updated": 1699639456062, "link": null, "locked": false, "points": [[0, 0], [163.2801513671875, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"type": "text", "version": 380, "versionNonce": 700825830, "isDeleted": false, "id": "G_kkO3Vlq3m3dlt6D9sFE", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 542.3470991448178, "y": 757.7934109142257, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 356.25, "height": 19.2, "seed": 1664090554, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "- A role playing multi-agent framework", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "- A role playing multi-agent framework", "lineHeight": 1.2, "baseline": 15}, {"id": "e6vUdZBZKTBgQ_bSmk0Hl", "type": "arrow", "x": 454.47908948323857, "y": 907.3951164679082, "width": 80.04442286153426, "height": 0, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 429731578, "version": 753, "versionNonce": 253732986, "isDeleted": false, "boundElements": null, "updated": 1699639456062, "link": null, "locked": false, "points": [[0, 0], [-80.04442286153426, 0]], "lastCommittedPoint": null, "startBinding": {"elementId": "mpg5srkWOqzwUrl7AyGYB", "focus": -0.17704152742444654, "gap": 1}, "endBinding": {"elementId": "gQtdOFFC3_JDRkavniD94", "focus": 0.13796235978042484, "gap": 17.558919356494357}, "startArrowhead": null, "endArrowhead": "triangle"}, {"type": "text", "version": 907, "versionNonce": 845722662, "isDeleted": false, "id": "gQtdOFFC3_JDRkavniD94", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": -18.124252734790048, "y": 866.4284715158129, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 375, "height": 72, "seed": 95984742, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [{"id": "e6vUdZBZKTBgQ_bSmk0Hl", "type": "arrow"}], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "Agents have the innate ability of\nreach out to another to delegate\nwork or ask questions.", "textAlign": "right", "verticalAlign": "top", "containerId": null, "originalText": "Agents have the innate ability of\nreach out to another to delegate\nwork or ask questions.", "lineHeight": 1.2, "baseline": 68}, {"type": "arrow", "version": 1453, "versionNonce": 214468922, "isDeleted": false, "id": "TaqfLl-ZIqkjWi3JnP54R", "fillStyle": "solid", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 560.678775145108, "y": 1302.8194265847774, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 179.151520908134, "height": 1.0274351226576073, "seed": 671265338, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "startBinding": {"elementId": "QC8CiIQoc_dint6cI3wII", "focus": 0.03862108630386412, "gap": 2.062022714058571}, "endBinding": {"elementId": "DEM2FYp7T2M95M7GSR1ir", "focus": 0.015375670781584937, "gap": 17.156043913387236}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "triangle", "points": [[0, 0], [-179.151520908134, 1.0274351226576073]]}, {"type": "text", "version": 1402, "versionNonce": 1758150502, "isDeleted": false, "id": "DEM2FYp7T2M95M7GSR1ir", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": -10.628789676413135, "y": 1256.265999566267, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 375, "height": 96, "seed": 807688954, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [{"id": "TaqfLl-ZIqkjWi3JnP54R", "type": "arrow"}], "updated": 1699639456062, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "Tasks can override agent tool\nwith specific ones that should\nbe used and also have a specific\nagent tackle them.", "textAlign": "right", "verticalAlign": "top", "containerId": null, "originalText": "Tasks can override agent tool\nwith specific ones that should\nbe used and also have a specific\nagent tackle them.", "lineHeight": 1.2, "baseline": 92}, {"type": "arrow", "version": 580, "versionNonce": 1276596730, "isDeleted": false, "id": "MGaWQZZpp-Sa9rrWsOE5d", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 0, "opacity": 100, "angle": 0, "x": 719.5056010034991, "y": 859.4148045148963, "strokeColor": "#1971c2", "backgroundColor": "#ffffff", "width": 74.56833235791396, "height": 12.78176183866276, "seed": 826098234, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1699639456062, "link": null, "locked": false, "startBinding": {"elementId": "X8lXjCPPYpZB7Y1FtbGzG", "focus": -0.4363769342476624, "gap": 6.224201028492644}, "endBinding": {"elementId": "jII8doF-kDbxbeqnvIrIQ", "focus": 0.5483507138332064, "gap": 6.823619908938099}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "triangle", "points": [[0, 0], [35.82283270599521, -12.78176183866276], [74.56833235791396, -1.0044893713090914]]}, {"id": "Ja4a0Feupui4D6SJ3Ghzn", "type": "text", "x": 1084.6577409262457, "y": 857.6082707330752, "width": 457.03125, "height": 24, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 1624805030, "version": 85, "versionNonce": 557474470, "isDeleted": false, "boundElements": null, "updated": 1699639456062, "link": null, "locked": false, "text": "- How tasks will be assigned to Agents.", "fontSize": 20, "fontFamily": 3, "textAlign": "left", "verticalAlign": "top", "baseline": 20, "containerId": null, "originalText": "- How tasks will be assigned to Agents.", "lineHeight": 1.2}, {"id": "PsPmuXZ7gPGtvQ6Mk9K8-", "type": "text", "x": 1084.6577409262457, "y": 887.6082707330752, "width": 445.3125, "height": 24, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 565810874, "version": 86, "versionNonce": 1880031930, "isDeleted": false, "boundElements": null, "updated": 1699639456062, "link": null, "locked": false, "text": "- How agents interact with each other.", "fontSize": 20, "fontFamily": 3, "textAlign": "left", "verticalAlign": "top", "baseline": 20, "containerId": null, "originalText": "- How agents interact with each other.", "lineHeight": 1.2}, {"id": "6mQXPI86P1xKOBnrlWEcn", "type": "text", "x": 1084.6577409262457, "y": 917.6082707330752, "width": 386.71875, "height": 24, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 559663590, "version": 92, "versionNonce": 509812198, "isDeleted": false, "boundElements": null, "updated": 1699639456062, "link": null, "locked": false, "text": "- How agents perform their tasks.", "fontSize": 20, "fontFamily": 3, "textAlign": "left", "verticalAlign": "top", "baseline": 20, "containerId": null, "originalText": "- How agents perform their tasks.", "lineHeight": 1.2}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}, "files": {}}