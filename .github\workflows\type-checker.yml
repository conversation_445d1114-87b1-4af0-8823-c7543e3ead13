name: Run Type Checks

on: [pull_request]

permissions:
  contents: write

jobs:
  type-checker:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11.9"

      - name: Install Requirements
        run: |
          pip install mypy

      - name: Run type checks
        run: mypy src
