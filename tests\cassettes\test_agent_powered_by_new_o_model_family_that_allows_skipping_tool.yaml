interactions:
- request:
    body: '{"messages": [{"role": "user", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: multiplier\nTool
      Arguments: {''first_number'': {''description'': None, ''type'': ''int''}, ''second_number'':
      {''description'': None, ''type'': ''int''}}\nTool Description: Useful for when
      you need to multiply two numbers together.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [multiplier], just the name, exactly as
      it''s written.\nAction Input: the input to the action, just a simple JSON object,
      enclosed in curly braces, using \" to wrap keys and values.\nObservation: the
      result of the action\n```\n\nOnce all necessary information is gathered, return
      the following format:\n\n```\nThought: I now know the final answer\nFinal Answer:
      the final answer to the original input question\n```\nCurrent Task: What is
      3 times 4?\n\nThis is the expected criteria for your final answer: The result
      of the multiplication.\nyou MUST return the actual complete content as the final
      answer, not a summary.\n\nBegin! This is VERY important to you, use the tools
      available and give your best Final Answer, your job depends on it!\n\nThought:"}],
      "model": "o3-mini", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '1409'
      content-type:
      - application/json
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHIc6Eoq1bS5hOxvIXvHm8rvcS3Sg\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743462826,\n  \"model\": \"o3-mini-2025-01-31\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"```\\nThought: I need to multiply 3 by
      4 using the multiplier tool.\\nAction: multiplier\\nAction Input: {\\\"first_number\\\":
      3, \\\"second_number\\\": 4}\",\n        \"refusal\": null,\n        \"annotations\":
      []\n      },\n      \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n
      \   \"prompt_tokens\": 289,\n    \"completion_tokens\": 369,\n    \"total_tokens\":
      658,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\": 0,\n      \"audio_tokens\":
      0\n    },\n    \"completion_tokens_details\": {\n      \"reasoning_tokens\":
      320,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\": 0,\n
      \     \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\": \"default\",\n
      \ \"system_fingerprint\": \"fp_617f206dd9\"\n}\n"
    headers:
      CF-RAY:
      - 92938a09c9a47ac2-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:13:50 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=57u6EtH_gSxgjHZShVlFLmvT2llY2pxEvawPcGWN0xM-1743462830-*******-8YjbI_1pxIPv3qB9xO7RckBpDDlGwv7AhsthHf450Nt8IzpLPd.RcEp0.kv8tfgpjeUfqUzksJIbw97Da06HFXJaBC.G0OOd27SqDAx4z2w;
        path=/; expires=Mon, 31-Mar-25 23:43:50 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=Gr1EyX0LLsKtl8de8dQsqXR2qCChTYrfTow05mWQBqs-1743462830990-*******-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '4384'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999677'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_2308de6953e2cfcb6ab7566dbf115c11
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "user", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: multiplier\nTool
      Arguments: {''first_number'': {''description'': None, ''type'': ''int''}, ''second_number'':
      {''description'': None, ''type'': ''int''}}\nTool Description: Useful for when
      you need to multiply two numbers together.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [multiplier], just the name, exactly as
      it''s written.\nAction Input: the input to the action, just a simple JSON object,
      enclosed in curly braces, using \" to wrap keys and values.\nObservation: the
      result of the action\n```\n\nOnce all necessary information is gathered, return
      the following format:\n\n```\nThought: I now know the final answer\nFinal Answer:
      the final answer to the original input question\n```\nCurrent Task: What is
      3 times 4?\n\nThis is the expected criteria for your final answer: The result
      of the multiplication.\nyou MUST return the actual complete content as the final
      answer, not a summary.\n\nBegin! This is VERY important to you, use the tools
      available and give your best Final Answer, your job depends on it!\n\nThought:"},
      {"role": "assistant", "content": "12"}, {"role": "assistant", "content": "```\nThought:
      I need to multiply 3 by 4 using the multiplier tool.\nAction: multiplier\nAction
      Input: {\"first_number\": 3, \"second_number\": 4}\nObservation: 12"}], "model":
      "o3-mini", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '1649'
      content-type:
      - application/json
      cookie:
      - __cf_bm=57u6EtH_gSxgjHZShVlFLmvT2llY2pxEvawPcGWN0xM-1743462830-*******-8YjbI_1pxIPv3qB9xO7RckBpDDlGwv7AhsthHf450Nt8IzpLPd.RcEp0.kv8tfgpjeUfqUzksJIbw97Da06HFXJaBC.G0OOd27SqDAx4z2w;
        _cfuvid=Gr1EyX0LLsKtl8de8dQsqXR2qCChTYrfTow05mWQBqs-1743462830990-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHIcBrSyMUt4ujKNww9ZR2m0FJgPj\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743462831,\n  \"model\": \"o3-mini-2025-01-31\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"```\\nThought: I now know the final answer\\nFinal
      Answer: 12\\n```\",\n        \"refusal\": null,\n        \"annotations\": []\n
      \     },\n      \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      341,\n    \"completion_tokens\": 29,\n    \"total_tokens\": 370,\n    \"prompt_tokens_details\":
      {\n      \"cached_tokens\": 0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_617f206dd9\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 92938a25ec087ac2-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:13:52 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '1818'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999636'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_01bee1028234ea669dc8ab805d877b7e
    http_version: HTTP/1.1
    status_code: 200
version: 1
