interactions:
- request:
    body: !!binary |
      CoEMCiQKIgoMc2VydmljZS5uYW1lEhIKEGNyZXdBSS10ZWxlbWV0cnkS2AsKEgoQY3Jld2FpLnRl
      bGVtZXRyeRKQAgoQ1lPH3Bis4hD4M7Sez2t96RIIIffb2kCAAqMqDlRhc2sgRXhlY3V0aW9uMAE5
      WIEZIiVM+BdBSK51sSZM+BdKLgoIY3Jld19rZXkSIgogM2Y4ZDVjM2FiODgyZDY4NjlkOTNjYjgx
      ZjBlMmVkNGFKMQoHY3Jld19pZBImCiQyYjZmY2ZmYS1lNDQ0LTQ4YjYtYWNjNi0xZTVhMDY2OTQ1
      NWJKLgoIdGFza19rZXkSIgogOTRhODI2YzE5MzA1NTk2ODZiYWZiNDA5ZWU4Mzg3NmZKMQoHdGFz
      a19pZBImCiQxMTU5NmU3OS0yYzllLTQzOWYtYWViMS0xMThhMTI2ZDNiYzN6AhgBhQEAAQAAEp0H
      ChBEYWf4sVuYMd8/Oxr4ONAsEghO/cKNNKdq0CoMQ3JldyBDcmVhdGVkMAE5KCBKsyZM+BdByI5P
      syZM+BdKGgoOY3Jld2FpX3ZlcnNpb24SCAoGMC42MS4wShoKDnB5dGhvbl92ZXJzaW9uEggKBjMu
      MTEuN0ouCghjcmV3X2tleRIiCiBhOWNjNWQ0MzM5NWIyMWIxODFjODBiZDQzNTFjY2VjOEoxCgdj
      cmV3X2lkEiYKJDkzNGJkMDZiLTY2ZDktNDE0MC1iZGE3LTQzMDZmNmM3Y2Q0N0ocCgxjcmV3X3By
      b2Nlc3MSDAoKc2VxdWVudGlhbEoRCgtjcmV3X21lbW9yeRICEABKGgoUY3Jld19udW1iZXJfb2Zf
      dGFza3MSAhgBShsKFWNyZXdfbnVtYmVyX29mX2FnZW50cxICGAFKzAIKC2NyZXdfYWdlbnRzErwC
      CrkCW3sia2V5IjogIjhiZDIxMzliNTk3NTE4MTUwNmU0MWZkOWM0NTYzZDc1IiwgImlkIjogIjY3
      MWMzYzdmLWNjMzUtNGU5MS1hYjgzLWVmZGVjOWU3Y2ZiNyIsICJyb2xlIjogIlJlc2VhcmNoZXIi
      LCAidmVyYm9zZT8iOiBmYWxzZSwgIm1heF9pdGVyIjogMTUsICJtYXhfcnBtIjogbnVsbCwgImZ1
      bmN0aW9uX2NhbGxpbmdfbGxtIjogIiIsICJsbG0iOiAiZ3B0LTRvIiwgImRlbGVnYXRpb25fZW5h
      YmxlZD8iOiBmYWxzZSwgImFsbG93X2NvZGVfZXhlY3V0aW9uPyI6IGZhbHNlLCAibWF4X3JldHJ5
      X2xpbWl0IjogMiwgInRvb2xzX25hbWVzIjogW119XUr+AQoKY3Jld190YXNrcxLvAQrsAVt7Imtl
      eSI6ICJlOWU2YjcyYWFjMzI2NDU5ZGQ3MDY4ZjBiMTcxN2MxYyIsICJpZCI6ICI4YmFkNTJiZi05
      MGM0LTQ0ZDgtYmNlZi0xODBkZTA2MjRiYWYiLCAiYXN5bmNfZXhlY3V0aW9uPyI6IHRydWUsICJo
      dW1hbl9pbnB1dD8iOiBmYWxzZSwgImFnZW50X3JvbGUiOiAiUmVzZWFyY2hlciIsICJhZ2VudF9r
      ZXkiOiAiOGJkMjEzOWI1OTc1MTgxNTA2ZTQxZmQ5YzQ1NjNkNzUiLCAidG9vbHNfbmFtZXMiOiBb
      XX1degIYAYUBAAEAABKOAgoQduJhIxVspIn9gWgZzmXHrhIILYsCkB2V4ckqDFRhc2sgQ3JlYXRl
      ZDABORCOYrMmTPgXQdDrYrMmTPgXSi4KCGNyZXdfa2V5EiIKIGE5Y2M1ZDQzMzk1YjIxYjE4MWM4
      MGJkNDM1MWNjZWM4SjEKB2NyZXdfaWQSJgokOTM0YmQwNmItNjZkOS00MTQwLWJkYTctNDMwNmY2
      YzdjZDQ3Si4KCHRhc2tfa2V5EiIKIGU5ZTZiNzJhYWMzMjY0NTlkZDcwNjhmMGIxNzE3YzFjSjEK
      B3Rhc2tfaWQSJgokOGJhZDUyYmYtOTBjNC00NGQ4LWJjZWYtMTgwZGUwNjI0YmFmegIYAYUBAAEA
      AA==
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
      Content-Length:
      - '1540'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.27.0
    method: POST
    uri: https://telemetry.crewai.com:4319/v1/traces
  response:
    body:
      string: "\n\0"
    headers:
      Content-Length:
      - '2'
      Content-Type:
      - application/x-protobuf
      Date:
      - Tue, 24 Sep 2024 21:43:06 GMT
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "system", "content": "You are Researcher. You''re
      an expert researcher, specialized in technology, software engineering, AI and
      startups. You work as a freelancer and is now working on doing research and
      analysis for a new customer.\nYour personal goal is: Make the best research
      and analysis on content about AI and AI agents\nTo give my best complete final
      answer to the task use the exact following format:\n\nThought: I now can give
      a great answer\nFinal Answer: Your final answer must be the great and the most
      complete as possible, it must be outcome described.\n\nI MUST use these formats,
      my job depends on it!"}, {"role": "user", "content": "\nCurrent Task: Generate
      a list of 5 interesting ideas to explore for an article, where each bulletpoint
      is under 15 words.\n\nThis is the expect criteria for your final answer: Bullet
      point list of 5 important events. No additional commentary.\nyou MUST return
      the actual complete content as the final answer, not a summary.\n\nBegin! This
      is VERY important to you, use the tools available and give your best Final Answer,
      your job depends on it!\n\nThought:"}], "model": "gpt-4o"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1155'
      content-type:
      - application/json
      cookie:
      - __cf_bm=9.8sBYBkvBR8R1K_bVF7xgU..80XKlEIg3N2OBbTSCU-1727214102-*******-.qiTLXbPamYUMSuyNsOEB9jhGu.jOifujOrx9E2JZvStbIZ9RTIiE44xKKNfLPxQkOi6qAT3h6htK8lPDGV_5g;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-1727213194587-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7bGdQd8mh4zvM4UaLl93hex1Ys3\",\n  \"object\":
      \"chat.completion\",\n  \"created\": **********,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I now can give a great answer.\\nFinal
      Answer:\\n- Ethical implications of AI in law enforcement and surveillance.\\n-
      AI advancements in personalized healthcare and diagnostics.\\n- Autonomous AI
      agents in financial market trading.\\n- Collaboration between AI and humans
      in creative arts.\\n- AI-driven climate modeling and environmental monitoring.\",\n
      \       \"refusal\": null\n      },\n      \"logprobs\": null,\n      \"finish_reason\":
      \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\": 226,\n    \"completion_tokens\":
      61,\n    \"total_tokens\": 287,\n    \"completion_tokens_details\": {\n      \"reasoning_tokens\":
      0\n    }\n  },\n  \"system_fingerprint\": \"fp_e375328146\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85f2b7c92f1cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:43:07 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '939'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999722'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_4a6962cfb5b3418a75c19cfc1c2e7227
    http_version: HTTP/1.1
    status_code: 200
version: 1
