interactions:
- request:
    body: '{"messages": [{"role": "user", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: comapny_customer_data\nTool
      Arguments: {}\nTool Description: Useful for getting customer related data.\n\nIMPORTANT:
      Use the following format in your response:\n\n```\nThought: you should always
      think about what to do\nAction: the action to take, only one name of [comapny_customer_data],
      just the name, exactly as it''s written.\nAction Input: the input to the action,
      just a simple JSON object, enclosed in curly braces, using \" to wrap keys and
      values.\nObservation: the result of the action\n```\n\nOnce all necessary information
      is gathered, return the following format:\n\n```\nThought: I now know the final
      answer\nFinal Answer: the final answer to the original input question\n```\nCurrent
      Task: How many customers does the company have?\n\nThis is the expected criteria
      for your final answer: The number of customers\nyou MUST return the actual complete
      content as the final answer, not a summary.\n\nBegin! This is VERY important
      to you, use the tools available and give your best Final Answer, your job depends
      on it!\n\nThought:"}], "model": "o3-mini", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '1320'
      content-type:
      - application/json
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHIeRex66NqQZhbzOTR7yLSo0WdT3\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743462971,\n  \"model\": \"o3-mini-2025-01-31\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"```\\nThought: I need to retrieve the
      total number of customers from the company's customer data.\\nAction: comapny_customer_data\\nAction
      Input: {\\\"query\\\": \\\"number_of_customers\\\"}\",\n        \"refusal\":
      null,\n        \"annotations\": []\n      },\n      \"finish_reason\": \"stop\"\n
      \   }\n  ],\n  \"usage\": {\n    \"prompt_tokens\": 262,\n    \"completion_tokens\":
      881,\n    \"total_tokens\": 1143,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 832,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_617f206dd9\"\n}\n"
    headers:
      CF-RAY:
      - 92938d93ac687ad0-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:16:18 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=6UQzmWTcRP41vYXI_O2QOTeLXRU1peuWHLs8Xx91dHs-1743462978-*******-ya2L0NSRc8YM5HkGsa2a72pzXIyFbLgXTayEqJgJ_EuXEgb5g0yI1i3JmLHDhZabRHE0TzP2DWXXCXkPB7egM3PdGeG4ruCLzDJPprH4yDI;
        path=/; expires=Mon, 31-Mar-25 23:46:18 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=q.iizOITNrDEsHjJlXIQF1mWa43E47tEWJWPJjPcpy4-1743462978067-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '6491'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999699'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_7602c287ab6ee69cfa02e28121ddee2c
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: !!binary |
      CtkBCiQKIgoMc2VydmljZS5uYW1lEhIKEGNyZXdBSS10ZWxlbWV0cnkSsAEKEgoQY3Jld2FpLnRl
      bGVtZXRyeRKZAQoQg7AgPgPg0GtIDX72FpP+ZRIIvm5yzhS5CUcqClRvb2wgVXNhZ2UwATlwAZNi
      VwYyGEF4XqZiVwYyGEobCg5jcmV3YWlfdmVyc2lvbhIJCgcwLjEwOC4wSiQKCXRvb2xfbmFtZRIX
      ChVjb21hcG55X2N1c3RvbWVyX2RhdGFKDgoIYXR0ZW1wdHMSAhgBegIYAYUBAAEAAA==
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate, zstd
      Connection:
      - keep-alive
      Content-Length:
      - '220'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.31.1
    method: POST
    uri: https://telemetry.crewai.com:4319/v1/traces
  response:
    body:
      string: "\n\0"
    headers:
      Content-Length:
      - '2'
      Content-Type:
      - application/x-protobuf
      Date:
      - Mon, 31 Mar 2025 23:16:19 GMT
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "user", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: comapny_customer_data\nTool
      Arguments: {}\nTool Description: Useful for getting customer related data.\n\nIMPORTANT:
      Use the following format in your response:\n\n```\nThought: you should always
      think about what to do\nAction: the action to take, only one name of [comapny_customer_data],
      just the name, exactly as it''s written.\nAction Input: the input to the action,
      just a simple JSON object, enclosed in curly braces, using \" to wrap keys and
      values.\nObservation: the result of the action\n```\n\nOnce all necessary information
      is gathered, return the following format:\n\n```\nThought: I now know the final
      answer\nFinal Answer: the final answer to the original input question\n```\nCurrent
      Task: How many customers does the company have?\n\nThis is the expected criteria
      for your final answer: The number of customers\nyou MUST return the actual complete
      content as the final answer, not a summary.\n\nBegin! This is VERY important
      to you, use the tools available and give your best Final Answer, your job depends
      on it!\n\nThought:"}, {"role": "assistant", "content": "The company has 42 customers"},
      {"role": "assistant", "content": "```\nThought: I need to retrieve the total
      number of customers from the company''s customer data.\nAction: comapny_customer_data\nAction
      Input: {\"query\": \"number_of_customers\"}\nObservation: The company has 42
      customers"}], "model": "o3-mini", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '1646'
      content-type:
      - application/json
      cookie:
      - __cf_bm=6UQzmWTcRP41vYXI_O2QOTeLXRU1peuWHLs8Xx91dHs-1743462978-*******-ya2L0NSRc8YM5HkGsa2a72pzXIyFbLgXTayEqJgJ_EuXEgb5g0yI1i3JmLHDhZabRHE0TzP2DWXXCXkPB7egM3PdGeG4ruCLzDJPprH4yDI;
        _cfuvid=q.iizOITNrDEsHjJlXIQF1mWa43E47tEWJWPJjPcpy4-1743462978067-*******-*********
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHIeYiyOID6u9eviBPAKBkV1z1OYn\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743462978,\n  \"model\": \"o3-mini-2025-01-31\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"```\\nThought: I retrieved the number
      of customers from the company data and confirmed it.\\nFinal Answer: 42\\n```\",\n
      \       \"refusal\": null,\n        \"annotations\": []\n      },\n      \"finish_reason\":
      \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\": 323,\n    \"completion_tokens\":
      164,\n    \"total_tokens\": 487,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 128,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_617f206dd9\"\n}\n"
    headers:
      CF-RAY:
      - 92938dbdb99b7ad0-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:16:20 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '2085'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999636'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_94e4598735cab3011d351991446daa0f
    http_version: HTTP/1.1
    status_code: 200
version: 1
