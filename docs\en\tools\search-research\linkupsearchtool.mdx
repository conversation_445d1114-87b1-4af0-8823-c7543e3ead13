---
title: Linkup Search Tool
description: The `LinkupSearchTool` enables querying the Linkup API for contextual information.
icon: link
---

# `LinkupSearchTool`

## Description

The `LinkupSearchTool` provides the ability to query the Linkup API for contextual information and retrieve structured results. This tool is ideal for enriching workflows with up-to-date and reliable information from Linkup, allowing agents to access relevant data during their tasks.

## Installation

To use this tool, you need to install the Linkup SDK:

```shell
uv add linkup-sdk
```

## Steps to Get Started

To effectively use the `LinkupSearchTool`, follow these steps:

1. **API Key**: Obtain a Linkup API key.
2. **Environment Setup**: Set up your environment with the API key.
3. **Install SDK**: Install the Linkup SDK using the command above.

## Example

The following example demonstrates how to initialize the tool and use it in an agent:

```python Code
from crewai_tools import LinkupSearchTool
from crewai import Agent
import os

# Initialize the tool with your API key
linkup_tool = LinkupSearchTool(api_key=os.getenv("LINKUP_API_KEY"))

# Define an agent that uses the tool
@agent
def researcher(self) -> Agent:
    '''
    This agent uses the LinkupSearchTool to retrieve contextual information
    from the Linkup API.
    '''
    return Agent(
        config=self.agents_config["researcher"],
        tools=[linkup_tool]
    )
```

## Parameters

The `LinkupSearchTool` accepts the following parameters:

### Constructor Parameters
- **api_key**: Required. Your Linkup API key.

### Run Parameters
- **query**: Required. The search term or phrase.
- **depth**: Optional. The search depth. Default is "standard".
- **output_type**: Optional. The type of output. Default is "searchResults".

## Advanced Usage

You can customize the search parameters for more specific results:

```python Code
# Perform a search with custom parameters
results = linkup_tool.run(
    query="Women Nobel Prize Physics",
    depth="deep",
    output_type="searchResults"
)
```

## Return Format

The tool returns results in the following format:

```json
{
  "success": true,
  "results": [
    {
      "name": "Result Title",
      "url": "https://example.com/result",
      "content": "Content of the result..."
    },
    // Additional results...
  ]
}
```

If an error occurs, the response will be:

```json
{
  "success": false,
  "error": "Error message"
}
```

## Error Handling

The tool gracefully handles API errors and provides structured feedback. If the API request fails, the tool will return a dictionary with `success: false` and an error message.

## Conclusion

The `LinkupSearchTool` provides a seamless way to integrate Linkup's contextual information retrieval capabilities into your CrewAI agents. By leveraging this tool, agents can access relevant and up-to-date information to enhance their decision-making and task execution. 