---
title: Integração com Slack
description: "Comunicação e colaboração em equipe com a integração Slack para CrewAI."
icon: "slack"
---

## Visão Geral

Permita que seus agentes gerenciem a comunicação da equipe pelo Slack. Envie mensagens, pesquise conversas, gerencie canais e coordene as atividades do time para otimizar os fluxos de colaboração com automação impulsionada por IA.

## Pré-requisitos

Antes de usar a integração com o Slack, certifique-se de que você tenha:

- Uma conta [CrewAI Enterprise](https://app.crewai.com) com assinatura ativa
- Um workspace do Slack com permissões apropriadas
- Seu workspace do Slack conectado por meio da [página de Integrações](https://app.crewai.com/integrations)

## Ferramentas Disponíveis

### **Gerenciamento de Usuários**

<AccordionGroup>
  <Accordion title="SLACK_LIST_MEMBERS">
    **Descrição:** Lista todos os membros de um canal do Slack.

    **Parâmetros:**
    - Nenhum parâmetro necessário – recupera todos os membros do canal
  </Accordion>

  <Accordion title="SLACK_GET_USER_BY_EMAIL">
    **Descrição:** Encontre um usuário no seu workspace do Slack pelo endereço de e-mail.

    **Parâmetros:**
    - `email` (string, obrigatório): O endereço de e-mail de um usuário do workspace
  </Accordion>

  <Accordion title="SLACK_GET_USERS_BY_NAME">
    **Descrição:** Pesquise usuários pelo nome ou nome de exibição.

    **Parâmetros:**
    - `name` (string, obrigatório): Nome real do usuário para a pesquisa
    - `displayName` (string, obrigatório): Nome de exibição do usuário para a pesquisa
    - `paginationParameters` (object, opcional): Configurações de paginação
      - `pageCursor` (string, opcional): Cursor de página para paginação
  </Accordion>
</AccordionGroup>

### **Gerenciamento de Canais**

<AccordionGroup>
  <Accordion title="SLACK_LIST_CHANNELS">
    **Descrição:** Lista todos os canais do seu workspace no Slack.

    **Parâmetros:**
    - Nenhum parâmetro necessário – recupera todos os canais acessíveis
  </Accordion>
</AccordionGroup>

### **Mensagens**

<AccordionGroup>
  <Accordion title="SLACK_SEND_MESSAGE">
    **Descrição:** Envie uma mensagem para um canal do Slack.

    **Parâmetros:**
    - `channel` (string, obrigatório): Nome ou ID do canal – Use as Configurações de Workflow do Connect Portal para que usuários selecionem o canal, ou insira o nome do canal para criar um novo
    - `message` (string, obrigatório): Texto da mensagem a ser enviada
    - `botName` (string, obrigatório): Nome do bot que enviará a mensagem
    - `botIcon` (string, obrigatório): Ícone do bot – Pode ser uma URL de imagem ou um emoji (ex.: ":dog:")
    - `blocks` (object, opcional): JSON do Slack Block Kit para mensagens ricas com anexos e elementos interativos
    - `authenticatedUser` (boolean, opcional): Se verdadeiro, a mensagem aparecerá como enviada pelo seu usuário autenticado do Slack ao invés do aplicativo (por padrão é falso)
  </Accordion>

  <Accordion title="SLACK_SEND_DIRECT_MESSAGE">
    **Descrição:** Envie uma mensagem direta para um usuário específico no Slack.

    **Parâmetros:**
    - `memberId` (string, obrigatório): ID do usuário destinatário – Use as Configurações de Workflow do Connect Portal para que usuários selecionem um membro
    - `message` (string, obrigatório): Texto da mensagem a ser enviada
    - `botName` (string, obrigatório): Nome do bot que enviará a mensagem
    - `botIcon` (string, obrigatório): Ícone do bot – Pode ser uma URL de imagem ou um emoji (ex.: ":dog:")
    - `blocks` (object, opcional): JSON do Slack Block Kit para formatação rica com anexos e elementos interativos
    - `authenticatedUser` (boolean, opcional): Se verdadeiro, a mensagem aparecerá como enviada pelo seu usuário autenticado do Slack (padrão é falso)
  </Accordion>
</AccordionGroup>

### **Pesquisa & Descoberta**

<AccordionGroup>
  <Accordion title="SLACK_SEARCH_MESSAGES">
    **Descrição:** Procure por mensagens em todo o seu workspace do Slack.

    **Parâmetros:**
    - `query` (string, obrigatório): Consulta de pesquisa usando a sintaxe do Slack para encontrar mensagens que correspondam aos critérios especificados

    **Exemplos de Consultas de Pesquisa:**
    - `"project update"` – Busca mensagens contendo "project update"
    - `from:@john in:#general` – Busca mensagens do John no canal #general
    - `has:link after:2023-01-01` – Busca mensagens com links após 1º de janeiro de 2023
    - `in:@channel before:yesterday` – Busca mensagens em um canal específico antes de ontem
  </Accordion>
</AccordionGroup>

## Integração com Block Kit

O Block Kit do Slack permite criar mensagens ricas e interativas. Veja alguns exemplos de como usar o parâmetro `blocks`:

### Texto Simples com Anexo
```json
[
  {
    "text": "I am a test message",
    "attachments": [
      {
        "text": "And here's an attachment!"
      }
    ]
  }
]
```

### Formatação Rica com Seções
```json
[
  {
    "type": "section",
    "text": {
      "type": "mrkdwn",
      "text": "*Project Update*\nStatus: ✅ Complete"
    }
  },
  {
    "type": "divider"
  },
  {
    "type": "section",
    "text": {
      "type": "plain_text",
      "text": "All tasks have been completed successfully."
    }
  }
]
```

## Exemplos de Uso

### Configuração Básica de Agente Slack

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

# Get enterprise tools (Slack tools will be included)
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

# Create an agent with Slack capabilities
slack_agent = Agent(
    role="Team Communication Manager",
    goal="Facilitate team communication and coordinate collaboration efficiently",
    backstory="An AI assistant specialized in team communication and workspace coordination.",
    tools=[enterprise_tools]
)

# Task to send project updates
update_task = Task(
    description="Send a project status update to the #general channel with current progress",
    agent=slack_agent,
    expected_output="Project update message sent successfully to team channel"
)

# Run the task
crew = Crew(
    agents=[slack_agent],
    tasks=[update_task]
)

crew.kickoff()
```

### Filtrando Ferramentas Específicas do Slack

```python
from crewai_tools import CrewaiEnterpriseTools

# Get only specific Slack tools
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token",
    actions_list=["slack_send_message", "slack_send_direct_message", "slack_search_messages"]
)

communication_manager = Agent(
    role="Communication Coordinator",
    goal="Manage team communications and ensure important messages reach the right people",
    backstory="An experienced communication coordinator who handles team messaging and notifications.",
    tools=enterprise_tools
)

# Task to coordinate team communication
coordination_task = Task(
    description="Send task completion notifications to team members and update project channels",
    agent=communication_manager,
    expected_output="Team notifications sent and project channels updated successfully"
)

crew = Crew(
    agents=[communication_manager],
    tasks=[coordination_task]
)

crew.kickoff()
```

### Mensagens Avançadas com Block Kit

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

notification_agent = Agent(
    role="Notification Manager",
    goal="Create rich, interactive notifications and manage workspace communication",
    backstory="An AI assistant that specializes in creating engaging team notifications and updates.",
    tools=[enterprise_tools]
)

# Task to send rich notifications
notification_task = Task(
    description="""
    1. Send a formatted project completion message to #general with progress charts
    2. Send direct messages to team leads with task summaries
    3. Create interactive notification with action buttons for team feedback
    """,
    agent=notification_agent,
    expected_output="Rich notifications sent with interactive elements and formatted content"
)

crew = Crew(
    agents=[notification_agent],
    tasks=[notification_task]
)

crew.kickoff()
```

### Pesquisa de Mensagens e Análises

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

analytics_agent = Agent(
    role="Communication Analyst",
    goal="Analyze team communication patterns and extract insights from conversations",
    backstory="An analytical AI that excels at understanding team dynamics through communication data.",
    tools=[enterprise_tools]
)

# Complex task involving search and analysis
analysis_task = Task(
    description="""
    1. Search for recent project-related messages across all channels
    2. Find users by email to identify team members
    3. Analyze communication patterns and response times
    4. Generate weekly team communication summary
    """,
    agent=analytics_agent,
    expected_output="Comprehensive communication analysis with team insights and recommendations"
)

crew = Crew(
    agents=[analytics_agent],
    tasks=[analysis_task]
)

crew.kickoff()
```

## Fale com o Suporte

<Card title="Precisa de Ajuda?" icon="headset" href="mailto:<EMAIL>">
  Entre em contato com nossa equipe de suporte para obter ajuda na configuração ou solução de problemas da integração com o Slack.
</Card>