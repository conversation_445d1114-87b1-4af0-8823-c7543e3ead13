---
title: "HubSpot Trigger"
description: "Trigger CrewAI crews directly from HubSpot Workflows"
icon: "hubspot"
---

This guide provides a step-by-step process to set up HubSpot triggers for CrewAI Enterprise, enabling you to initiate crews directly from HubSpot Workflows.

## Prerequisites

- A CrewAI Enterprise account
- A HubSpot account with the [HubSpot Workflows](https://knowledge.hubspot.com/workflows/create-workflows) feature

## Setup Steps

<Steps>
    <Step title="Connect your HubSpot account with CrewAI Enterprise">
        - Log in to your `CrewAI Enterprise account > Triggers`
        - Select `HubSpot` from the list of available triggers
        - Choose the HubSpot account you want to connect with CrewAI Enterprise
        - Follow the on-screen prompts to authorize CrewAI Enterprise access to your HubSpot account
        - A confirmation message will appear once HubSpot is successfully connected with CrewAI Enterprise
    </Step>
    <Step title="Create a HubSpot Workflow">
        - Log in to your `HubSpot account > Automations > Workflows > New workflow`
        - Select the workflow type that fits your needs (e.g., Start from scratch)
        - In the workflow builder, click the Plus (+) icon to add a new action.
        - Choose `Integrated apps > CrewAI > Kickoff a Crew`.
        - Select the Crew you want to initiate.
        - Click `Save` to add the action to your workflow
        <Frame>
            <img src="/images/enterprise/hubspot-workflow-1.png" alt="HubSpot Workflow 1" />
        </Frame>
    </Step>
    <Step title="Use Crew results with other actions">
        - After the Kickoff a Crew step, click the Plus (+) icon to add a new action.
        - For example, to send an internal email notification, choose `Communications > Send internal email notification`
        - In the Body field, click `Insert data`, select `View properties or action outputs from > Action outputs > Crew Result` to include Crew data in the email
            <Frame>
                <img src="/images/enterprise/hubspot-workflow-2.png" alt="HubSpot Workflow 2" />
            </Frame>
        - Configure any additional actions as needed
        - Review your workflow steps to ensure everything is set up correctly
        - Activate the workflow
            <Frame>
                <img src="/images/enterprise/hubspot-workflow-3.png" alt="HubSpot Workflow 3" />
            </Frame>
    </Step>
</Steps>

## Additional Resources

For more detailed information on available actions and customization options, refer to the [HubSpot Workflows Documentation](https://knowledge.hubspot.com/workflows/create-workflows). 