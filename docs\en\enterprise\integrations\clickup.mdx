---
title: ClickUp Integration
description: "Task and productivity management with ClickUp integration for CrewAI."
icon: "list-check"
---

## Overview

Enable your agents to manage tasks, projects, and productivity workflows through ClickUp. Create and update tasks, organize projects, manage team assignments, and streamline your productivity management with AI-powered automation.

## Prerequisites

Before using the ClickUp integration, ensure you have:

- A [CrewAI Enterprise](https://app.crewai.com) account with an active subscription
- A ClickUp account with appropriate permissions
- Connected your ClickUp account through the [Integrations page](https://app.crewai.com/crewai_plus/connectors)

## Setting Up ClickUp Integration

### 1. Connect Your ClickUp Account

1. Navigate to [CrewAI Enterprise Integrations](https://app.crewai.com/crewai_plus/connectors)
2. Find **ClickUp** in the Authentication Integrations section
3. Click **Connect** and complete the OAuth flow
4. Grant the necessary permissions for task and project management
5. Copy your Enterprise Token from [Account Settings](https://app.crewai.com/crewai_plus/settings/account)

### 2. Install Required Package

```bash
uv add crewai-tools
```

## Available Actions

<AccordionGroup>
  <Accordion title="CLICKUP_SEARCH_TASKS">
    **Description:** Search for tasks in ClickUp using advanced filters.

    **Parameters:**
    - `taskFilterFormula` (object, optional): A filter in disjunctive normal form - OR of AND groups of single conditions.
      ```json
      {
        "operator": "OR",
        "conditions": [
          {
            "operator": "AND",
            "conditions": [
              {
                "field": "statuses%5B%5D",
                "operator": "$stringExactlyMatches",
                "value": "open"
              }
            ]
          }
        ]
      }
      ```
      Available fields: `space_ids%5B%5D`, `project_ids%5B%5D`, `list_ids%5B%5D`, `statuses%5B%5D`, `include_closed`, `assignees%5B%5D`, `tags%5B%5D`, `due_date_gt`, `due_date_lt`, `date_created_gt`, `date_created_lt`, `date_updated_gt`, `date_updated_lt`
  </Accordion>

  <Accordion title="CLICKUP_GET_TASK_IN_LIST">
    **Description:** Get tasks in a specific list in ClickUp.

    **Parameters:**
    - `listId` (string, required): List - Select a List to get tasks from. Use Connect Portal User Settings to allow users to select a ClickUp List.
    - `taskFilterFormula` (string, optional): Search for tasks that match specified filters. For example: name=task1.
  </Accordion>

  <Accordion title="CLICKUP_CREATE_TASK">
    **Description:** Create a task in ClickUp.

    **Parameters:**
    - `listId` (string, required): List - Select a List to create this task in. Use Connect Portal User Settings to allow users to select a ClickUp List.
    - `name` (string, required): Name - The task name.
    - `description` (string, optional): Description - Task description.
    - `status` (string, optional): Status - Select a Status for this task. Use Connect Portal User Settings to allow users to select a ClickUp Status.
    - `assignees` (string, optional): Assignees - Select a Member (or an array of member IDs) to be assigned to this task. Use Connect Portal User Settings to allow users to select a ClickUp Member.
    - `dueDate` (string, optional): Due Date - Specify a date for this task to be due on.
    - `additionalFields` (string, optional): Additional Fields - Specify additional fields to include on this task as JSON.
  </Accordion>

  <Accordion title="CLICKUP_UPDATE_TASK">
    **Description:** Update a task in ClickUp.

    **Parameters:**
    - `taskId` (string, required): Task ID - The ID of the task to update.
    - `listId` (string, required): List - Select a List to create this task in. Use Connect Portal User Settings to allow users to select a ClickUp List.
    - `name` (string, optional): Name - The task name.
    - `description` (string, optional): Description - Task description.
    - `status` (string, optional): Status - Select a Status for this task. Use Connect Portal User Settings to allow users to select a ClickUp Status.
    - `assignees` (string, optional): Assignees - Select a Member (or an array of member IDs) to be assigned to this task. Use Connect Portal User Settings to allow users to select a ClickUp Member.
    - `dueDate` (string, optional): Due Date - Specify a date for this task to be due on.
    - `additionalFields` (string, optional): Additional Fields - Specify additional fields to include on this task as JSON.
  </Accordion>

  <Accordion title="CLICKUP_DELETE_TASK">
    **Description:** Delete a task in ClickUp.

    **Parameters:**
    - `taskId` (string, required): Task ID - The ID of the task to delete.
  </Accordion>

  <Accordion title="CLICKUP_GET_LIST">
    **Description:** Get List information in ClickUp.

    **Parameters:**
    - `spaceId` (string, required): Space ID - The ID of the space containing the lists.
  </Accordion>

  <Accordion title="CLICKUP_GET_CUSTOM_FIELDS_IN_LIST">
    **Description:** Get Custom Fields in a List in ClickUp.

    **Parameters:**
    - `listId` (string, required): List ID - The ID of the list to get custom fields from.
  </Accordion>

  <Accordion title="CLICKUP_GET_ALL_FIELDS_IN_LIST">
    **Description:** Get All Fields in a List in ClickUp.

    **Parameters:**
    - `listId` (string, required): List ID - The ID of the list to get all fields from.
  </Accordion>

  <Accordion title="CLICKUP_GET_SPACE">
    **Description:** Get Space information in ClickUp.

    **Parameters:**
    - `spaceId` (string, optional): Space ID - The ID of the space to retrieve.
  </Accordion>

  <Accordion title="CLICKUP_GET_FOLDERS">
    **Description:** Get Folders in ClickUp.

    **Parameters:**
    - `spaceId` (string, required): Space ID - The ID of the space containing the folders.
  </Accordion>

  <Accordion title="CLICKUP_GET_MEMBER">
    **Description:** Get Member information in ClickUp.

    **Parameters:** None required.
  </Accordion>
</AccordionGroup>

## Usage Examples

### Basic ClickUp Agent Setup

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

# Get enterprise tools (ClickUp tools will be included)
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

# Create an agent with ClickUp capabilities
clickup_agent = Agent(
    role="Task Manager",
    goal="Manage tasks and projects in ClickUp efficiently",
    backstory="An AI assistant specialized in task management and productivity coordination.",
    tools=[enterprise_tools]
)

# Task to create a new task
create_task = Task(
    description="Create a task called 'Review Q1 Reports' in the Marketing list with high priority",
    agent=clickup_agent,
    expected_output="Task created successfully with task ID"
)

# Run the task
crew = Crew(
    agents=[clickup_agent],
    tasks=[create_task]
)

crew.kickoff()
```

### Filtering Specific ClickUp Tools

```python
from crewai_tools import CrewaiEnterpriseTools

# Get only specific ClickUp tools
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token",
    actions_list=["clickup_create_task", "clickup_update_task", "clickup_search_tasks"]
)

task_coordinator = Agent(
    role="Task Coordinator",
    goal="Create and manage tasks efficiently",
    backstory="An AI assistant that focuses on task creation and status management.",
    tools=enterprise_tools
)

# Task to manage task workflow
task_workflow = Task(
    description="Create a task for project planning and assign it to the development team",
    agent=task_coordinator,
    expected_output="Task created and assigned successfully"
)

crew = Crew(
    agents=[task_coordinator],
    tasks=[task_workflow]
)

crew.kickoff()
```

### Advanced Project Management

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

project_manager = Agent(
    role="Project Manager",
    goal="Coordinate project activities and track team productivity",
    backstory="An experienced project manager who ensures projects are delivered on time.",
    tools=[enterprise_tools]
)

# Complex task involving multiple ClickUp operations
project_coordination = Task(
    description="""
    1. Get all open tasks in the current space
    2. Identify overdue tasks and update their status
    3. Create a weekly report task summarizing project progress
    4. Assign the report task to the team lead
    """,
    agent=project_manager,
    expected_output="Project status updated and weekly report task created and assigned"
)

crew = Crew(
    agents=[project_manager],
    tasks=[project_coordination]
)

crew.kickoff()
```

### Task Search and Management

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

task_analyst = Agent(
    role="Task Analyst",
    goal="Analyze task patterns and optimize team productivity",
    backstory="An AI assistant that analyzes task data to improve team efficiency.",
    tools=[enterprise_tools]
)

# Task to analyze and optimize task distribution
task_analysis = Task(
    description="""
    Search for all tasks assigned to team members in the last 30 days,
    analyze completion patterns, and create optimization recommendations
    """,
    agent=task_analyst,
    expected_output="Task analysis report with optimization recommendations"
)

crew = Crew(
    agents=[task_analyst],
    tasks=[task_analysis]
)

crew.kickoff()
```

### Getting Help

<Card title="Need Help?" icon="headset" href="mailto:<EMAIL>">
  Contact our support team for assistance with ClickUp integration setup or troubleshooting.
</Card>
