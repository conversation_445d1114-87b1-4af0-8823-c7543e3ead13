interactions:
- request:
    body: '{"messages": [{"role": "system", "content": "You are Crew Manager. You
      are a seasoned manager with a knack for getting the best out of your team.\nYou
      are also known for your ability to delegate work to the right people, and to
      ask the right questions to get the best out of your team.\nEven though you don''t
      perform tasks by yourself, you have a lot of experience in the field, which
      allows you to properly evaluate the work of your team members.\nYour personal
      goal is: Manage the team to complete the task in the best way possible.\nYou
      ONLY have access to the following tools, and should NEVER make up tools that
      are not listed here:\n\nTool Name: Delegate work to coworker(task: str, context:
      str, coworker: Optional[str] = None, **kwargs)\nTool Description: Delegate a
      specific task to one of the following coworkers: Scorer\nThe input to this tool
      should be the coworker, the task you want them to do, and ALL necessary context
      to execute the task, they know nothing about the task, so share absolute everything
      you know, don''t reference things but instead explain them.\nTool Arguments:
      {''task'': {''title'': ''Task'', ''type'': ''string''}, ''context'': {''title'':
      ''Context'', ''type'': ''string''}, ''coworker'': {''title'': ''Coworker'',
      ''type'': ''string''}, ''kwargs'': {''title'': ''Kwargs'', ''type'': ''object''}}\nTool
      Name: Ask question to coworker(question: str, context: str, coworker: Optional[str]
      = None, **kwargs)\nTool Description: Ask a specific question to one of the following
      coworkers: Scorer\nThe input to this tool should be the coworker, the question
      you have for them, and ALL necessary context to ask the question properly, they
      know nothing about the question, so share absolute everything you know, don''t
      reference things but instead explain them.\nTool Arguments: {''question'': {''title'':
      ''Question'', ''type'': ''string''}, ''context'': {''title'': ''Context'', ''type'':
      ''string''}, ''coworker'': {''title'': ''Coworker'', ''type'': ''string''},
      ''kwargs'': {''title'': ''Kwargs'', ''type'': ''object''}}\n\nUse the following
      format:\n\nThought: you should always think about what to do\nAction: the action
      to take, only one name of [Delegate work to coworker, Ask question to coworker],
      just the name, exactly as it''s written.\nAction Input: the input to the action,
      just a simple python dictionary, enclosed in curly braces, using \" to wrap
      keys and values.\nObservation: the result of the action\n\nOnce all necessary
      information is gathered:\n\nThought: I now know the final answer\nFinal Answer:
      the final answer to the original input question\n"}, {"role": "user", "content":
      "\nCurrent Task: Give me an integer score between 1-5 for the following title:
      ''The impact of AI in the future of work''\n\nThis is the expect criteria for
      your final answer: The score of the title.\nyou MUST return the actual complete
      content as the final answer, not a summary.\n\nBegin! This is VERY important
      to you, use the tools available and give your best Final Answer, your job depends
      on it!\n\nThought:"}], "model": "gpt-4o"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '2986'
      content-type:
      - application/json
      cookie:
      - __cf_bm=9.8sBYBkvBR8R1K_bVF7xgU..80XKlEIg3N2OBbTSCU-**********-*******-.qiTLXbPamYUMSuyNsOEB9jhGu.jOifujOrx9E2JZvStbIZ9RTIiE44xKKNfLPxQkOi6qAT3h6htK8lPDGV_5g;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-**********587-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7fzflt7Fhs1oZsbsvf8UH2540zr\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727214479,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"To provide the score for the title \\\"The
      impact of AI in the future of work,\\\" I need to ask Scorer to evaluate this
      based on the criteria they use. \\n\\nAction: Ask question to coworker\\nAction
      Input: {\\\"question\\\": \\\"Can you provide an integer score between 1-5 for
      the title 'The impact of AI in the future of work'?\\\", \\\"context\\\": \\\"We
      need to evaluate this title based on your criteria for scoring titles. The context
      for the scoring revolves entirely around the perceived impact of AI on the future
      of work.\\\", \\\"coworker\\\": \\\"Scorer\\\"}\",\n        \"refusal\": null\n
      \     },\n      \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n
      \ ],\n  \"usage\": {\n    \"prompt_tokens\": 664,\n    \"completion_tokens\":
      122,\n    \"total_tokens\": 786,\n    \"completion_tokens_details\": {\n      \"reasoning_tokens\":
      0\n    }\n  },\n  \"system_fingerprint\": \"fp_e375328146\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85f9de4b8c1cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:48:01 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '1846'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999269'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 1ms
      x-request-id:
      - req_11f38fee4ec6e3f51a3324984c025e8a
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: !!binary |
      CrkXCiQKIgoMc2VydmljZS5uYW1lEhIKEGNyZXdBSS10ZWxlbWV0cnkSkBcKEgoQY3Jld2FpLnRl
      bGVtZXRyeRKQAgoQ1tibm5O29ydlYfnOavm8RBII2SJ7HUWzRw0qDlRhc2sgRXhlY3V0aW9uMAE5
      MJ81e2lM+BdB2DorkWpM+BdKLgoIY3Jld19rZXkSIgogNWU2ZWZmZTY4MGE1ZDk3ZGMzODczYjE0
      ODI1Y2NmYTNKMQoHY3Jld19pZBImCiQwODczNWFkNy1iZTFhLTQ0ZDMtOTU3Ny1hMzM0ZjI2NTNm
      NjFKLgoIdGFza19rZXkSIgogMjdlZjM4Y2M5OWRhNGE4ZGVkNzBlZDQwNmU0NGFiODZKMQoHdGFz
      a19pZBImCiQ5YjE2YjE3YS0wZTg5LTRmMGItODc3NC1mZjkxODU3NGYzNjZ6AhgBhQEAAQAAEpYH
      ChBxyWIltrFtco3B5ZvJkfAeEgh/kPnzKKzEGioMQ3JldyBDcmVhdGVkMAE5SKsrkmpM+BdBqIMv
      kmpM+BdKGgoOY3Jld2FpX3ZlcnNpb24SCAoGMC42MS4wShoKDnB5dGhvbl92ZXJzaW9uEggKBjMu
      MTEuN0ouCghjcmV3X2tleRIiCiA1ZTZlZmZlNjgwYTVkOTdkYzM4NzNiMTQ4MjVjY2ZhM0oxCgdj
      cmV3X2lkEiYKJDU3OWM3OGE3LTg2MjEtNGQ2Ni04NDQyLWRjY2NkNjY2YTk3ZkocCgxjcmV3X3By
      b2Nlc3MSDAoKc2VxdWVudGlhbEoRCgtjcmV3X21lbW9yeRICEABKGgoUY3Jld19udW1iZXJfb2Zf
      dGFza3MSAhgBShsKFWNyZXdfbnVtYmVyX29mX2FnZW50cxICGAFKyAIKC2NyZXdfYWdlbnRzErgC
      CrUCW3sia2V5IjogIjkyZTdlYjE5MTY2NGM5MzU3ODVlZDdkNDI0MGEyOTRkIiwgImlkIjogIjVm
      ODRlMTRiLWMzOTEtNDM4Mi1iNzM2LWNiYzlhZjE1M2I0OSIsICJyb2xlIjogIlNjb3JlciIsICJ2
      ZXJib3NlPyI6IGZhbHNlLCAibWF4X2l0ZXIiOiAxNSwgIm1heF9ycG0iOiBudWxsLCAiZnVuY3Rp
      b25fY2FsbGluZ19sbG0iOiAiIiwgImxsbSI6ICJncHQtNG8iLCAiZGVsZWdhdGlvbl9lbmFibGVk
      PyI6IGZhbHNlLCAiYWxsb3dfY29kZV9leGVjdXRpb24/IjogZmFsc2UsICJtYXhfcmV0cnlfbGlt
      aXQiOiAyLCAidG9vbHNfbmFtZXMiOiBbXX1dSvsBCgpjcmV3X3Rhc2tzEuwBCukBW3sia2V5Ijog
      IjI3ZWYzOGNjOTlkYTRhOGRlZDcwZWQ0MDZlNDRhYjg2IiwgImlkIjogImJlODc3NWQyLTU1OTQt
      NGIyMy1hZDIwLTcxMTdlZTcyMDcyYiIsICJhc3luY19leGVjdXRpb24/IjogZmFsc2UsICJodW1h
      bl9pbnB1dD8iOiBmYWxzZSwgImFnZW50X3JvbGUiOiAiU2NvcmVyIiwgImFnZW50X2tleSI6ICI5
      MmU3ZWIxOTE2NjRjOTM1Nzg1ZWQ3ZDQyNDBhMjk0ZCIsICJ0b29sc19uYW1lcyI6IFtdfV16AhgB
      hQEAAQAAEo4CChADNfQkyWpTxYTb4wV4Xi1zEgjhZIssXkYhYyoMVGFzayBDcmVhdGVkMAE54LRI
      kmpM+BdBkGhJkmpM+BdKLgoIY3Jld19rZXkSIgogNWU2ZWZmZTY4MGE1ZDk3ZGMzODczYjE0ODI1
      Y2NmYTNKMQoHY3Jld19pZBImCiQ1NzljNzhhNy04NjIxLTRkNjYtODQ0Mi1kY2NjZDY2NmE5N2ZK
      LgoIdGFza19rZXkSIgogMjdlZjM4Y2M5OWRhNGE4ZGVkNzBlZDQwNmU0NGFiODZKMQoHdGFza19p
      ZBImCiRiZTg3NzVkMi01NTk0LTRiMjMtYWQyMC03MTE3ZWU3MjA3MmJ6AhgBhQEAAQAAEpACChDW
      xf6NEFlSgigrF1Uyj4hIEgiPFfsrt0iWwyoOVGFzayBFeGVjdXRpb24wATmYukmSakz4F0FgOfji
      akz4F0ouCghjcmV3X2tleRIiCiA1ZTZlZmZlNjgwYTVkOTdkYzM4NzNiMTQ4MjVjY2ZhM0oxCgdj
      cmV3X2lkEiYKJDU3OWM3OGE3LTg2MjEtNGQ2Ni04NDQyLWRjY2NkNjY2YTk3ZkouCgh0YXNrX2tl
      eRIiCiAyN2VmMzhjYzk5ZGE0YThkZWQ3MGVkNDA2ZTQ0YWI4NkoxCgd0YXNrX2lkEiYKJGJlODc3
      NWQyLTU1OTQtNGIyMy1hZDIwLTcxMTdlZTcyMDcyYnoCGAGFAQABAAASmAcKEPcwxZwxkQKMAj/Q
      Ue+krp4SCK8Ly14L/2MwKgxDcmV3IENyZWF0ZWQwATkwT8bjakz4F0FIsMrjakz4F0oaCg5jcmV3
      YWlfdmVyc2lvbhIICgYwLjYxLjBKGgoOcHl0aG9uX3ZlcnNpb24SCAoGMy4xMS43Si4KCGNyZXdf
      a2V5EiIKIDVlNmVmZmU2ODBhNWQ5N2RjMzg3M2IxNDgyNWNjZmEzSjEKB2NyZXdfaWQSJgokN2Q5
      ODViMTAtZjJlYy00NTI0LTg4ZGItMWI0Yzk4MDViZGYzSh4KDGNyZXdfcHJvY2VzcxIOCgxoaWVy
      YXJjaGljYWxKEQoLY3Jld19tZW1vcnkSAhAAShoKFGNyZXdfbnVtYmVyX29mX3Rhc2tzEgIYAUob
      ChVjcmV3X251bWJlcl9vZl9hZ2VudHMSAhgBSsgCCgtjcmV3X2FnZW50cxK4Agq1Alt7ImtleSI6
      ICI5MmU3ZWIxOTE2NjRjOTM1Nzg1ZWQ3ZDQyNDBhMjk0ZCIsICJpZCI6ICI1YjA5Y2YyNC0xYjg4
      LTQ4YzUtYjNhZi1jMDRjMTUyZDRmM2MiLCAicm9sZSI6ICJTY29yZXIiLCAidmVyYm9zZT8iOiBm
      YWxzZSwgIm1heF9pdGVyIjogMTUsICJtYXhfcnBtIjogbnVsbCwgImZ1bmN0aW9uX2NhbGxpbmdf
      bGxtIjogIiIsICJsbG0iOiAiZ3B0LTRvIiwgImRlbGVnYXRpb25fZW5hYmxlZD8iOiBmYWxzZSwg
      ImFsbG93X2NvZGVfZXhlY3V0aW9uPyI6IGZhbHNlLCAibWF4X3JldHJ5X2xpbWl0IjogMiwgInRv
      b2xzX25hbWVzIjogW119XUr7AQoKY3Jld190YXNrcxLsAQrpAVt7ImtleSI6ICIyN2VmMzhjYzk5
      ZGE0YThkZWQ3MGVkNDA2ZTQ0YWI4NiIsICJpZCI6ICJiNmE2ZDhiNS1iMWRkLTQxYTctOTJlOS1j
      YzIxNzAwODJmMTciLCAiYXN5bmNfZXhlY3V0aW9uPyI6IGZhbHNlLCAiaHVtYW5faW5wdXQ/Ijog
      ZmFsc2UsICJhZ2VudF9yb2xlIjogIlNjb3JlciIsICJhZ2VudF9rZXkiOiAiOTJlN2ViMTkxNjY0
      YzkzNTc4NWVkN2Q0MjQwYTI5NGQiLCAidG9vbHNfbmFtZXMiOiBbXX1degIYAYUBAAEAABKOAgoQ
      XyhkBtLg+uYDB+G/D3snoBII1hx0numRabUqDFRhc2sgQ3JlYXRlZDABObCgJeVqTPgXQXB7JuVq
      TPgXSi4KCGNyZXdfa2V5EiIKIDVlNmVmZmU2ODBhNWQ5N2RjMzg3M2IxNDgyNWNjZmEzSjEKB2Ny
      ZXdfaWQSJgokN2Q5ODViMTAtZjJlYy00NTI0LTg4ZGItMWI0Yzk4MDViZGYzSi4KCHRhc2tfa2V5
      EiIKIDI3ZWYzOGNjOTlkYTRhOGRlZDcwZWQ0MDZlNDRhYjg2SjEKB3Rhc2tfaWQSJgokYjZhNmQ4
      YjUtYjFkZC00MWE3LTkyZTktY2MyMTcwMDgyZjE3egIYAYUBAAEAAA==
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
      Content-Length:
      - '3004'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.27.0
    method: POST
    uri: https://telemetry.crewai.com:4319/v1/traces
  response:
    body:
      string: "\n\0"
    headers:
      Content-Length:
      - '2'
      Content-Type:
      - application/x-protobuf
      Date:
      - Tue, 24 Sep 2024 21:48:02 GMT
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "system", "content": "You are Scorer. You''re an
      expert scorer, specialized in scoring titles.\nYour personal goal is: Score
      the title\nTo give my best complete final answer to the task use the exact following
      format:\n\nThought: I now can give a great answer\nFinal Answer: Your final
      answer must be the great and the most complete as possible, it must be outcome
      described.\n\nI MUST use these formats, my job depends on it!"}, {"role": "user",
      "content": "\nCurrent Task: Can you provide an integer score between 1-5 for
      the title ''The impact of AI in the future of work''?\n\nThis is the expect
      criteria for your final answer: Your best answer to your coworker asking you
      this, accounting for the context shared.\nyou MUST return the actual complete
      content as the final answer, not a summary.\n\nThis is the context you''re working
      with:\nWe need to evaluate this title based on your criteria for scoring titles.
      The context for the scoring revolves entirely around the perceived impact of
      AI on the future of work.\n\nBegin! This is VERY important to you, use the tools
      available and give your best Final Answer, your job depends on it!\n\nThought:"}],
      "model": "gpt-4o"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1197'
      content-type:
      - application/json
      cookie:
      - __cf_bm=9.8sBYBkvBR8R1K_bVF7xgU..80XKlEIg3N2OBbTSCU-**********-*******-.qiTLXbPamYUMSuyNsOEB9jhGu.jOifujOrx9E2JZvStbIZ9RTIiE44xKKNfLPxQkOi6qAT3h6htK8lPDGV_5g;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-**********587-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7g1mAHJE9QnUWqbrKUvROBJKSYb\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727214481,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I now can give a great answer\\nFinal
      Answer: 4 - The title 'The impact of AI in the future of work' is clear and
      directly addresses a timely and significant topic that is highly relevant in
      today's context. However, it could be made even more specific to immediately
      hook the reader or audience.\",\n        \"refusal\": null\n      },\n      \"logprobs\":
      null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      238,\n    \"completion_tokens\": 64,\n    \"total_tokens\": 302,\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0\n    }\n  },\n  \"system_fingerprint\": \"fp_e375328146\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85f9ec685c1cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:48:02 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '802'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999712'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_a7c8c6c2bd1951dd5d067148bc6ceba8
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are Crew Manager. You
      are a seasoned manager with a knack for getting the best out of your team.\nYou
      are also known for your ability to delegate work to the right people, and to
      ask the right questions to get the best out of your team.\nEven though you don''t
      perform tasks by yourself, you have a lot of experience in the field, which
      allows you to properly evaluate the work of your team members.\nYour personal
      goal is: Manage the team to complete the task in the best way possible.\nYou
      ONLY have access to the following tools, and should NEVER make up tools that
      are not listed here:\n\nTool Name: Delegate work to coworker(task: str, context:
      str, coworker: Optional[str] = None, **kwargs)\nTool Description: Delegate a
      specific task to one of the following coworkers: Scorer\nThe input to this tool
      should be the coworker, the task you want them to do, and ALL necessary context
      to execute the task, they know nothing about the task, so share absolute everything
      you know, don''t reference things but instead explain them.\nTool Arguments:
      {''task'': {''title'': ''Task'', ''type'': ''string''}, ''context'': {''title'':
      ''Context'', ''type'': ''string''}, ''coworker'': {''title'': ''Coworker'',
      ''type'': ''string''}, ''kwargs'': {''title'': ''Kwargs'', ''type'': ''object''}}\nTool
      Name: Ask question to coworker(question: str, context: str, coworker: Optional[str]
      = None, **kwargs)\nTool Description: Ask a specific question to one of the following
      coworkers: Scorer\nThe input to this tool should be the coworker, the question
      you have for them, and ALL necessary context to ask the question properly, they
      know nothing about the question, so share absolute everything you know, don''t
      reference things but instead explain them.\nTool Arguments: {''question'': {''title'':
      ''Question'', ''type'': ''string''}, ''context'': {''title'': ''Context'', ''type'':
      ''string''}, ''coworker'': {''title'': ''Coworker'', ''type'': ''string''},
      ''kwargs'': {''title'': ''Kwargs'', ''type'': ''object''}}\n\nUse the following
      format:\n\nThought: you should always think about what to do\nAction: the action
      to take, only one name of [Delegate work to coworker, Ask question to coworker],
      just the name, exactly as it''s written.\nAction Input: the input to the action,
      just a simple python dictionary, enclosed in curly braces, using \" to wrap
      keys and values.\nObservation: the result of the action\n\nOnce all necessary
      information is gathered:\n\nThought: I now know the final answer\nFinal Answer:
      the final answer to the original input question\n"}, {"role": "user", "content":
      "\nCurrent Task: Give me an integer score between 1-5 for the following title:
      ''The impact of AI in the future of work''\n\nThis is the expect criteria for
      your final answer: The score of the title.\nyou MUST return the actual complete
      content as the final answer, not a summary.\n\nBegin! This is VERY important
      to you, use the tools available and give your best Final Answer, your job depends
      on it!\n\nThought:"}, {"role": "assistant", "content": "To provide the score
      for the title \"The impact of AI in the future of work,\" I need to ask Scorer
      to evaluate this based on the criteria they use. \n\nAction: Ask question to
      coworker\nAction Input: {\"question\": \"Can you provide an integer score between
      1-5 for the title ''The impact of AI in the future of work''?\", \"context\":
      \"We need to evaluate this title based on your criteria for scoring titles.
      The context for the scoring revolves entirely around the perceived impact of
      AI on the future of work.\", \"coworker\": \"Scorer\"}\nObservation: 4 - The
      title ''The impact of AI in the future of work'' is clear and directly addresses
      a timely and significant topic that is highly relevant in today''s context.
      However, it could be made even more specific to immediately hook the reader
      or audience."}], "model": "gpt-4o"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '3831'
      content-type:
      - application/json
      cookie:
      - __cf_bm=9.8sBYBkvBR8R1K_bVF7xgU..80XKlEIg3N2OBbTSCU-**********-*******-.qiTLXbPamYUMSuyNsOEB9jhGu.jOifujOrx9E2JZvStbIZ9RTIiE44xKKNfLPxQkOi6qAT3h6htK8lPDGV_5g;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-**********587-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7g2oKgp7qGfodgXRIUm5FpBtpNp\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727214482,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I have received the score from
      Scorer.\\n\\nFinal Answer: 4 - The title 'The impact of AI in the future of
      work' is clear and directly addresses a timely and significant topic that is
      highly relevant in today's context. However, it could be made even more specific
      to immediately hook the reader or audience.\",\n        \"refusal\": null\n
      \     },\n      \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n
      \ ],\n  \"usage\": {\n    \"prompt_tokens\": 843,\n    \"completion_tokens\":
      65,\n    \"total_tokens\": 908,\n    \"completion_tokens_details\": {\n      \"reasoning_tokens\":
      0\n    }\n  },\n  \"system_fingerprint\": \"fp_e375328146\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85f9f338e01cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:48:03 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '1078'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999071'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 1ms
      x-request-id:
      - req_4bee152d111ba79c2f73025a64db943f
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "user", "content": "4 - The title ''The impact of
      AI in the future of work'' is clear and directly addresses a timely and significant
      topic that is highly relevant in today''s context. However, it could be made
      even more specific to immediately hook the reader or audience."}, {"role": "system",
      "content": "I''m gonna convert this raw text into valid JSON.\n\nThe json should
      have the following structure, with the following keys:\n{\n    score: int\n}"}],
      "model": "gpt-4o", "tool_choice": {"type": "function", "function": {"name":
      "ScoreOutput"}}, "tools": [{"type": "function", "function": {"name": "ScoreOutput",
      "description": "Correctly extracted `ScoreOutput` with all the required parameters
      with correct types", "parameters": {"properties": {"score": {"title": "Score",
      "type": "integer"}}, "required": ["score"], "type": "object"}}}]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '864'
      content-type:
      - application/json
      cookie:
      - __cf_bm=9.8sBYBkvBR8R1K_bVF7xgU..80XKlEIg3N2OBbTSCU-**********-*******-.qiTLXbPamYUMSuyNsOEB9jhGu.jOifujOrx9E2JZvStbIZ9RTIiE44xKKNfLPxQkOi6qAT3h6htK8lPDGV_5g;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-**********587-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7g4iZixQjuGxE0RxbSIixLAsEGf\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727214484,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": null,\n        \"tool_calls\": [\n          {\n
      \           \"id\": \"call_iIgRFzMLI0NHZwyJM2CwzJPL\",\n            \"type\":
      \"function\",\n            \"function\": {\n              \"name\": \"ScoreOutput\",\n
      \             \"arguments\": \"{\\\"score\\\":4}\"\n            }\n          }\n
      \       ],\n        \"refusal\": null\n      },\n      \"logprobs\": null,\n
      \     \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      149,\n    \"completion_tokens\": 5,\n    \"total_tokens\": 154,\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0\n    }\n  },\n  \"system_fingerprint\": \"fp_9f2bfdaa89\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85f9fc5e991cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:48:04 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '141'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999884'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_dd38f9b6e9e6ac64ebbb827111e31414
    http_version: HTTP/1.1
    status_code: 200
version: 1
