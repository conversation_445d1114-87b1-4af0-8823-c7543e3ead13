{"$schema": "https://mintlify.com/docs.json", "theme": "mint", "name": "CrewAI", "colors": {"primary": "#EB6658", "light": "#F3A78B", "dark": "#C94C3C"}, "favicon": "/images/favicon.svg", "contextual": {"options": ["copy", "view", "chatgpt", "claude"]}, "navigation": {"languages": [{"language": "en", "global": {"anchors": [{"anchor": "Website", "href": "https://crewai.com", "icon": "globe"}, {"anchor": "Forum", "href": "https://community.crewai.com", "icon": "discourse"}, {"anchor": "Crew GPT", "href": "https://chatgpt.com/g/g-qqTuUWsBY-<PERSON><PERSON>-assistant", "icon": "robot"}, {"anchor": "Releases", "href": "https://github.com/crewAIInc/crewAI/releases", "icon": "tag"}]}, "tabs": [{"tab": "Documentation", "groups": [{"group": "Get Started", "pages": ["en/introduction", "en/installation", "en/quickstart"]}, {"group": "Guides", "pages": [{"group": "Strategy", "pages": ["en/guides/concepts/evaluating-use-cases"]}, {"group": "Agents", "pages": ["en/guides/agents/crafting-effective-agents"]}, {"group": "Crews", "pages": ["en/guides/crews/first-crew"]}, {"group": "Flows", "pages": ["en/guides/flows/first-flow", "en/guides/flows/mastering-flow-state"]}, {"group": "Advanced", "pages": ["en/guides/advanced/customizing-prompts", "en/guides/advanced/fingerprinting"]}]}, {"group": "Core Concepts", "pages": ["en/concepts/agents", "en/concepts/tasks", "en/concepts/crews", "en/concepts/flows", "en/concepts/knowledge", "en/concepts/llms", "en/concepts/processes", "en/concepts/collaboration", "en/concepts/training", "en/concepts/memory", "en/concepts/reasoning", "en/concepts/planning", "en/concepts/testing", "en/concepts/cli", "en/concepts/tools", "en/concepts/event-listener"]}, {"group": "MCP Integration", "pages": ["en/mcp/overview", "en/mcp/stdio", "en/mcp/sse", "en/mcp/streamable-http", "en/mcp/multiple-servers", "en/mcp/security"]}, {"group": "Tools", "pages": ["en/tools/overview", {"group": "File & Document", "pages": ["en/tools/file-document/overview", "en/tools/file-document/filereadtool", "en/tools/file-document/filewritetool", "en/tools/file-document/pdfsearchtool", "en/tools/file-document/docxsearchtool", "en/tools/file-document/mdxsearchtool", "en/tools/file-document/xmlsearchtool", "en/tools/file-document/txtsearchtool", "en/tools/file-document/jsonsearchtool", "en/tools/file-document/csvsearchtool", "en/tools/file-document/directorysearchtool", "en/tools/file-document/directoryreadtool"]}, {"group": "Web Scraping & Browsing", "pages": ["en/tools/web-scraping/overview", "en/tools/web-scraping/scrapewebsitetool", "en/tools/web-scraping/scrapeelementfromwebsitetool", "en/tools/web-scraping/scrapflyscrapetool", "en/tools/web-scraping/seleniumscrapingtool", "en/tools/web-scraping/scrapegraphscrapetool", "en/tools/web-scraping/spidertool", "en/tools/web-scraping/browserbaseloadtool", "en/tools/web-scraping/hyperbrowserloadtool", "en/tools/web-scraping/stagehandtool", "en/tools/web-scraping/firecrawlcrawlwebsitetool", "en/tools/web-scraping/firecrawlscrapewebsitetool", "en/tools/web-scraping/oxylabsscraperstool"]}, {"group": "Search & Research", "pages": ["en/tools/search-research/overview", "en/tools/search-research/serperdevtool", "en/tools/search-research/bravesearchtool", "en/tools/search-research/exasearchtool", "en/tools/search-research/linkupsearchtool", "en/tools/search-research/githubsearchtool", "en/tools/search-research/websitesearchtool", "en/tools/search-research/codedocssearchtool", "en/tools/search-research/youtubechannelsearchtool", "en/tools/search-research/youtubevideosearchtool", "en/tools/search-research/tavilysearchtool", "en/tools/search-research/tavilyextractortool"]}, {"group": "Database & Data", "pages": ["en/tools/database-data/overview", "en/tools/database-data/mysqltool", "en/tools/database-data/pgsearchtool", "en/tools/database-data/snowflakesearchtool", "en/tools/database-data/nl2sqltool", "en/tools/database-data/qdrantvectorsearchtool", "en/tools/database-data/weaviatevectorsearchtool"]}, {"group": "AI & Machine Learning", "pages": ["en/tools/ai-ml/overview", "en/tools/ai-ml/dalletool", "en/tools/ai-ml/visiontool", "en/tools/ai-ml/aimindtool", "en/tools/ai-ml/llamaindextool", "en/tools/ai-ml/langchaintool", "en/tools/ai-ml/ragtool", "en/tools/ai-ml/codeinterpretertool"]}, {"group": "Cloud & Storage", "pages": ["en/tools/cloud-storage/overview", "en/tools/cloud-storage/s3readertool", "en/tools/cloud-storage/s3writertool", "en/tools/cloud-storage/bedrockinvokeagenttool", "en/tools/cloud-storage/bedrockkbretriever"]}, {"group": "Automation & Integration", "pages": ["en/tools/automation/overview", "en/tools/automation/apifyactorstool", "en/tools/automation/composiotool", "en/tools/automation/multiontool"]}]}, {"group": "Observability", "pages": ["en/observability/overview", "en/observability/agentops", "en/observability/arize-phoenix", "en/observability/langfuse", "en/observability/langtrace", "en/observability/maxim", "en/observability/mlflow", "en/observability/neatlogs", "en/observability/openlit", "en/observability/opik", "en/observability/patronus-evaluation", "en/observability/portkey", "en/observability/weave"]}, {"group": "Learn", "pages": ["en/learn/overview", "en/learn/llm-selection-guide", "en/learn/conditional-tasks", "en/learn/coding-agents", "en/learn/create-custom-tools", "en/learn/custom-llm", "en/learn/custom-manager-agent", "en/learn/customizing-agents", "en/learn/dalle-image-generation", "en/learn/force-tool-output-as-result", "en/learn/hierarchical-process", "en/learn/human-input-on-execution", "en/learn/kickoff-async", "en/learn/kickoff-for-each", "en/learn/llm-connections", "en/learn/multimodal-agents", "en/learn/replay-tasks-from-latest-crew-kickoff", "en/learn/sequential-process", "en/learn/using-annotations"]}, {"group": "Telemetry", "pages": ["en/telemetry"]}]}, {"tab": "Enterprise", "groups": [{"group": "Getting Started", "pages": ["en/enterprise/introduction"]}, {"group": "Features", "pages": ["en/enterprise/features/tool-repository", "en/enterprise/features/webhook-streaming", "en/enterprise/features/traces", "en/enterprise/features/hallucination-guardrail", "en/enterprise/features/integrations", "en/enterprise/features/agent-repositories"]}, {"group": "Integration Docs", "pages": ["en/enterprise/integrations/asana", "en/enterprise/integrations/box", "en/enterprise/integrations/clickup", "en/enterprise/integrations/github", "en/enterprise/integrations/gmail", "en/enterprise/integrations/google_calendar", "en/enterprise/integrations/google_sheets", "en/enterprise/integrations/hubspot", "en/enterprise/integrations/jira", "en/enterprise/integrations/linear", "en/enterprise/integrations/notion", "en/enterprise/integrations/salesforce", "en/enterprise/integrations/shopify", "en/enterprise/integrations/slack", "en/enterprise/integrations/stripe", "en/enterprise/integrations/zendesk"]}, {"group": "How-To Guides", "pages": ["en/enterprise/guides/build-crew", "en/enterprise/guides/deploy-crew", "en/enterprise/guides/kickoff-crew", "en/enterprise/guides/update-crew", "en/enterprise/guides/enable-crew-studio", "en/enterprise/guides/azure-openai-setup", "en/enterprise/guides/hubspot-trigger", "en/enterprise/guides/react-component-export", "en/enterprise/guides/salesforce-trigger", "en/enterprise/guides/slack-trigger", "en/enterprise/guides/team-management", "en/enterprise/guides/webhook-automation", "en/enterprise/guides/human-in-the-loop", "en/enterprise/guides/zapier-trigger"]}, {"group": "Resources", "pages": ["en/enterprise/resources/frequently-asked-questions"]}]}, {"tab": "API Reference", "groups": [{"group": "Getting Started", "pages": ["en/api-reference/introduction"]}, {"group": "Endpoints", "openapi": "enterprise-api.yaml"}]}, {"tab": "Examples", "groups": [{"group": "Examples", "pages": ["en/examples/example"]}]}]}, {"language": "pt-BR", "global": {"anchors": [{"anchor": "Website", "href": "https://crewai.com", "icon": "globe"}, {"anchor": "Fórum", "href": "https://community.crewai.com", "icon": "discourse"}, {"anchor": "Crew GPT", "href": "https://chatgpt.com/g/g-qqTuUWsBY-<PERSON><PERSON>-assistant", "icon": "robot"}, {"anchor": "Lançamentos", "href": "https://github.com/crewAIInc/crewAI/releases", "icon": "tag"}]}, "tabs": [{"tab": "Documentação", "groups": [{"group": "Começando", "pages": ["pt-BR/introduction", "pt-BR/installation", "pt-BR/quickstart"]}, {"group": "<PERSON><PERSON><PERSON>", "pages": [{"group": "Estratégia", "pages": ["pt-BR/guides/concepts/evaluating-use-cases"]}, {"group": "<PERSON><PERSON>", "pages": ["pt-BR/guides/agents/crafting-effective-agents"]}, {"group": "Crews", "pages": ["pt-BR/guides/crews/first-crew"]}, {"group": "Flows", "pages": ["pt-BR/guides/flows/first-flow", "pt-BR/guides/flows/mastering-flow-state"]}, {"group": "Avançado", "pages": ["pt-BR/guides/advanced/customizing-prompts", "pt-BR/guides/advanced/fingerprinting"]}]}, {"group": "Conceitos-Chave", "pages": ["pt-BR/concepts/agents", "pt-BR/concepts/tasks", "pt-BR/concepts/crews", "pt-BR/concepts/flows", "pt-BR/concepts/knowledge", "pt-BR/concepts/llms", "pt-BR/concepts/processes", "pt-BR/concepts/collaboration", "pt-BR/concepts/training", "pt-BR/concepts/memory", "pt-BR/concepts/reasoning", "pt-BR/concepts/planning", "pt-BR/concepts/testing", "pt-BR/concepts/cli", "pt-BR/concepts/tools", "pt-BR/concepts/event-listener"]}, {"group": "Integração MCP", "pages": ["pt-BR/mcp/overview", "pt-BR/mcp/stdio", "pt-BR/mcp/sse", "pt-BR/mcp/streamable-http", "pt-BR/mcp/multiple-servers", "pt-BR/mcp/security"]}, {"group": "Ferramentas", "pages": ["pt-BR/tools/overview", {"group": "Arquivo & Documento", "pages": ["pt-BR/tools/file-document/overview", "pt-BR/tools/file-document/filereadtool", "pt-BR/tools/file-document/filewritetool", "pt-BR/tools/file-document/pdfsearchtool", "pt-BR/tools/file-document/docxsearchtool", "pt-BR/tools/file-document/mdxsearchtool", "pt-BR/tools/file-document/xmlsearchtool", "pt-BR/tools/file-document/txtsearchtool", "pt-BR/tools/file-document/jsonsearchtool", "pt-BR/tools/file-document/csvsearchtool", "pt-BR/tools/file-document/directorysearchtool", "pt-BR/tools/file-document/directoryreadtool"]}, {"group": "Web Scraping & Navegação", "pages": ["pt-BR/tools/web-scraping/overview", "pt-BR/tools/web-scraping/scrapewebsitetool", "pt-BR/tools/web-scraping/scrapeelementfromwebsitetool", "pt-BR/tools/web-scraping/scrapflyscrapetool", "pt-BR/tools/web-scraping/seleniumscrapingtool", "pt-BR/tools/web-scraping/scrapegraphscrapetool", "pt-BR/tools/web-scraping/spidertool", "pt-BR/tools/web-scraping/browserbaseloadtool", "pt-BR/tools/web-scraping/hyperbrowserloadtool", "pt-BR/tools/web-scraping/stagehandtool", "pt-BR/tools/web-scraping/firecrawlcrawlwebsitetool", "pt-BR/tools/web-scraping/firecrawlscrapewebsitetool", "pt-BR/tools/web-scraping/oxylabsscraperstool"]}, {"group": "Pesquisa", "pages": ["pt-BR/tools/search-research/overview", "pt-BR/tools/search-research/serperdevtool", "pt-BR/tools/search-research/bravesearchtool", "pt-BR/tools/search-research/exasearchtool", "pt-BR/tools/search-research/linkupsearchtool", "pt-BR/tools/search-research/githubsearchtool", "pt-BR/tools/search-research/websitesearchtool", "pt-BR/tools/search-research/codedocssearchtool", "pt-BR/tools/search-research/youtubechannelsearchtool", "pt-BR/tools/search-research/youtubevideosearchtool"]}, {"group": "<PERSON><PERSON>", "pages": ["pt-BR/tools/database-data/overview", "pt-BR/tools/database-data/mysqltool", "pt-BR/tools/database-data/pgsearchtool", "pt-BR/tools/database-data/snowflakesearchtool", "pt-BR/tools/database-data/nl2sqltool", "pt-BR/tools/database-data/qdrantvectorsearchtool", "pt-BR/tools/database-data/weaviatevectorsearchtool"]}, {"group": "IA & Machine Learning", "pages": ["pt-BR/tools/ai-ml/overview", "pt-BR/tools/ai-ml/dalletool", "pt-BR/tools/ai-ml/visiontool", "pt-BR/tools/ai-ml/aimindtool", "pt-BR/tools/ai-ml/llamaindextool", "pt-BR/tools/ai-ml/langchaintool", "pt-BR/tools/ai-ml/ragtool", "pt-BR/tools/ai-ml/codeinterpretertool"]}, {"group": "Cloud & Armazenamento", "pages": ["pt-BR/tools/cloud-storage/overview", "pt-BR/tools/cloud-storage/s3readertool", "pt-BR/tools/cloud-storage/s3writertool", "pt-BR/tools/cloud-storage/bedrockinvokeagenttool", "pt-BR/tools/cloud-storage/bedrockkbretriever"]}, {"group": "Automação & Integração", "pages": ["pt-BR/tools/automation/overview", "pt-BR/tools/automation/apifyactorstool", "pt-BR/tools/automation/composiotool", "pt-BR/tools/automation/multiontool"]}]}, {"group": "Observabilidade", "pages": ["pt-BR/observability/overview", "pt-BR/observability/agentops", "pt-BR/observability/arize-phoenix", "pt-BR/observability/langfuse", "pt-BR/observability/langtrace", "pt-BR/observability/maxim", "pt-BR/observability/mlflow", "pt-BR/observability/openlit", "pt-BR/observability/opik", "pt-BR/observability/patronus-evaluation", "pt-BR/observability/portkey", "pt-BR/observability/weave"]}, {"group": "Aprenda", "pages": ["pt-BR/learn/overview", "pt-BR/learn/llm-selection-guide", "pt-BR/learn/conditional-tasks", "pt-BR/learn/coding-agents", "pt-BR/learn/create-custom-tools", "pt-BR/learn/custom-llm", "pt-BR/learn/custom-manager-agent", "pt-BR/learn/customizing-agents", "pt-BR/learn/dalle-image-generation", "pt-BR/learn/force-tool-output-as-result", "pt-BR/learn/hierarchical-process", "pt-BR/learn/human-input-on-execution", "pt-BR/learn/kickoff-async", "pt-BR/learn/kickoff-for-each", "pt-BR/learn/llm-connections", "pt-BR/learn/multimodal-agents", "pt-BR/learn/replay-tasks-from-latest-crew-kickoff", "pt-BR/learn/sequential-process", "pt-BR/learn/using-annotations"]}, {"group": "Telemetria", "pages": ["pt-BR/telemetry"]}]}, {"tab": "Enterprise", "groups": [{"group": "Começando", "pages": ["pt-BR/enterprise/introduction"]}, {"group": "Funcionalidades", "pages": ["pt-BR/enterprise/features/tool-repository", "pt-BR/enterprise/features/webhook-streaming", "pt-BR/enterprise/features/traces", "pt-BR/enterprise/features/hallucination-guardrail", "pt-BR/enterprise/features/integrations"]}, {"group": "Documentação de Integração", "pages": ["pt-BR/enterprise/integrations/asana", "pt-BR/enterprise/integrations/box", "pt-BR/enterprise/integrations/clickup", "pt-BR/enterprise/integrations/github", "pt-BR/enterprise/integrations/gmail", "pt-BR/enterprise/integrations/google_calendar", "pt-BR/enterprise/integrations/google_sheets", "pt-BR/enterprise/integrations/hubspot", "pt-BR/enterprise/integrations/jira", "pt-BR/enterprise/integrations/linear", "pt-BR/enterprise/integrations/notion", "pt-BR/enterprise/integrations/salesforce", "pt-BR/enterprise/integrations/shopify", "pt-BR/enterprise/integrations/slack", "pt-BR/enterprise/integrations/stripe", "pt-BR/enterprise/integrations/zendesk"]}, {"group": "<PERSON><PERSON><PERSON>", "pages": ["pt-BR/enterprise/guides/build-crew", "pt-BR/enterprise/guides/deploy-crew", "pt-BR/enterprise/guides/kickoff-crew", "pt-BR/enterprise/guides/update-crew", "pt-BR/enterprise/guides/enable-crew-studio", "pt-BR/enterprise/guides/azure-openai-setup", "pt-BR/enterprise/guides/hubspot-trigger", "pt-BR/enterprise/guides/react-component-export", "pt-BR/enterprise/guides/salesforce-trigger", "pt-BR/enterprise/guides/slack-trigger", "pt-BR/enterprise/guides/team-management", "pt-BR/enterprise/guides/webhook-automation", "pt-BR/enterprise/guides/human-in-the-loop", "pt-BR/enterprise/guides/zapier-trigger"]}, {"group": "Recursos", "pages": ["pt-BR/enterprise/resources/frequently-asked-questions"]}]}, {"tab": "Referência da API", "groups": [{"group": "Começando", "pages": ["pt-BR/api-reference/introduction"]}, {"group": "Endpoints", "openapi": "enterprise-api.yaml"}]}, {"tab": "Exemplos", "groups": [{"group": "Exemplos", "pages": ["pt-BR/examples/example"]}]}]}]}, "logo": {"light": "/images/crew_only_logo.png", "dark": "/images/crew_only_logo.png"}, "appearance": {"default": "dark", "strict": false}, "navbar": {"links": [{"label": "Start Cloud Trial", "href": "https://app.crewai.com"}], "primary": {"type": "github", "href": "https://github.com/crewAIInc/crewAI"}}, "api": {"baseUrl": "https://your-actual-crew-name.crewai.com", "auth": {"method": "bearer", "name": "Authorization"}, "playground": {"mode": "simple"}}, "seo": {"indexing": "all"}, "redirects": [{"source": "/introduction", "destination": "/en/introduction"}, {"source": "/installation", "destination": "/en/installation"}, {"source": "/quickstart", "destination": "/en/quickstart"}, {"source": "/changelog", "destination": "https://github.com/crewAIInc/crewAI/releases"}, {"source": "/telemetry", "destination": "/en/telemetry"}, {"source": "/concepts/:path*", "destination": "/en/concepts/:path*"}, {"source": "/guides/:path*", "destination": "/en/guides/:path*"}, {"source": "/tools/:path*", "destination": "/en/tools/:path*"}, {"source": "/learn/:path*", "destination": "/en/learn/:path*"}, {"source": "/mcp/:path*", "destination": "/en/mcp/:path*"}, {"source": "/observability/:path*", "destination": "/en/observability/:path*"}, {"source": "/enterprise/:path*", "destination": "/en/enterprise/:path*"}, {"source": "/api-reference/:path*", "destination": "/en/api-reference/:path*"}, {"source": "/examples/:path*", "destination": "/en/examples/:path*"}], "errors": {"404": {"redirect": true}}, "footer": {"socials": {"website": "https://crewai.com", "x": "https://x.com/crewAIInc", "github": "https://github.com/crewAIInc/crewAI", "linkedin": "https://www.linkedin.com/company/crewai-inc", "youtube": "https://youtube.com/@crewAIInc", "reddit": "https://www.reddit.com/r/crewAIInc/"}}}