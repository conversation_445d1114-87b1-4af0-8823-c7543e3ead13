---
title: Integração com o Notion
description: "Gerenciamento de páginas e bancos de dados com integração do Notion para o CrewAI."
icon: "book"
---

## Visão Geral

Permita que seus agentes gerenciem páginas, bancos de dados e conteúdos através do Notion. Crie e atualize páginas, gerencie blocos de conteúdo, organize bases de conhecimento e otimize seus fluxos de documentação com automação alimentada por IA.

## Pré-requisitos

Antes de usar a integração com o Notion, certifique-se de que você tem:

- Uma conta [CrewAI Enterprise](https://app.crewai.com) com assinatura ativa
- Uma conta Notion com permissões adequadas no workspace
- Sua conta Notion conectada através da [página de Integrações](https://app.crewai.com/crewai_plus/connectors)

## Configurando a Integração com o Notion

### 1. Conecte sua Conta Notion

1. Acesse [Integrações do CrewAI Enterprise](https://app.crewai.com/crewai_plus/connectors)
2. Procure por **Notion** na seção de Integrações de Autenticação
3. Clique em **Conectar** e complete o fluxo de OAuth
4. Conceda as permissões necessárias para gerenciamento de páginas e bancos de dados
5. Copie seu Token Enterprise em [Configurações da Conta](https://app.crewai.com/crewai_plus/settings/account)

### 2. Instale o Pacote Necessário

```bash
uv add crewai-tools
```

## Ações Disponíveis

<AccordionGroup>
  <Accordion title="NOTION_CREATE_PAGE">
    **Descrição:** Cria uma página no Notion.

    **Parâmetros:**
    - `parent` (object, obrigatório): Parent - A página ou banco de dados pai onde a nova página será inserida, representado como um objeto JSON com uma chave page_id ou database_id.
      ```json
      {
        "database_id": "DATABASE_ID"
      }
      ```
    - `properties` (object, obrigatório): Properties - Os valores das propriedades da página. Se o pai for um banco de dados, o schema deve corresponder às propriedades do banco de dados.
      ```json
      {
        "title": [
          {
            "text": {
              "content": "My Page"
            }
          }
        ]
      }
      ```
    - `icon` (object, obrigatório): Icon - O ícone da página.
      ```json
      {
        "emoji": "🥬"
      }
      ```
    - `children` (object, opcional): Children - Blocos de conteúdo a serem adicionados à página.
      ```json
      [
        {
          "object": "block",
          "type": "heading_2",
          "heading_2": {
            "rich_text": [
              {
                "type": "text",
                "text": {
                  "content": "Lacinato kale"
                }
              }
            ]
          }
        }
      ]
      ```
    - `cover` (object, opcional): Cover - A imagem de capa da página.
      ```json
      {
        "external": {
          "url": "https://upload.wikimedia.org/wikipedia/commons/6/62/Tuscankale.jpg"
        }
      }
      ```
  </Accordion>

  <Accordion title="NOTION_UPDATE_PAGE">
    **Descrição:** Atualiza uma página no Notion.

    **Parâmetros:**
    - `pageId` (string, obrigatório): Page ID - Especifique o ID da Página a ser atualizada. (exemplo: "59833787-2cf9-4fdf-8782-e53db20768a5").
    - `icon` (object, obrigatório): Icon - O ícone da página.
      ```json
      {
        "emoji": "🥬"
      }
      ```
    - `archived` (boolean, opcional): Archived - Indica se a página está arquivada (excluída). Defina como true para arquivar a página. Defina como false para restaurar.
    - `properties` (object, opcional): Properties - Os valores das propriedades a serem atualizados na página.
      ```json
      {
        "title": [
          {
            "text": {
              "content": "My Updated Page"
            }
          }
        ]
      }
      ```
    - `cover` (object, opcional): Cover - A imagem de capa da página.
      ```json
      {
        "external": {
          "url": "https://upload.wikimedia.org/wikipedia/commons/6/62/Tuscankale.jpg"
        }
      }
      ```
  </Accordion>

  <Accordion title="NOTION_GET_PAGE_BY_ID">
    **Descrição:** Busca uma página pelo ID no Notion.

    **Parâmetros:**
    - `pageId` (string, obrigatório): Page ID - Especifique o ID da Página a ser buscada. (exemplo: "59833787-2cf9-4fdf-8782-e53db20768a5").
  </Accordion>

  <Accordion title="NOTION_ARCHIVE_PAGE">
    **Descrição:** Arquiva uma página no Notion.

    **Parâmetros:**
    - `pageId` (string, obrigatório): Page ID - Especifique o ID da Página a ser arquivada. (exemplo: "59833787-2cf9-4fdf-8782-e53db20768a5").
  </Accordion>

  <Accordion title="NOTION_SEARCH_PAGES">
    **Descrição:** Pesquisa páginas no Notion utilizando filtros.

    **Parâmetros:**
    - `searchByTitleFilterSearch` (object, opcional): Um filtro na forma normal disjuntiva - OU de grupos E de condições simples.
      ```json
      {
        "operator": "OR",
        "conditions": [
          {
            "operator": "AND",
            "conditions": [
              {
                "field": "query",
                "operator": "$stringExactlyMatches",
                "value": "meeting notes"
              }
            ]
          }
        ]
      }
      ```
      Campos disponíveis: `query`, `filter.value`, `direction`, `page_size`
  </Accordion>

  <Accordion title="NOTION_GET_PAGE_CONTENT">
    **Descrição:** Obtém o conteúdo (blocos) de uma página no Notion.

    **Parâmetros:**
    - `blockId` (string, obrigatório): Page ID - Especifique o ID de um Bloco ou Página para receber todos os seus blocos filhos na ordem correta. (exemplo: "59833787-2cf9-4fdf-8782-e53db20768a5").
  </Accordion>

  <Accordion title="NOTION_UPDATE_BLOCK">
    **Descrição:** Atualiza um bloco no Notion.

    **Parâmetros:**
    - `blockId` (string, obrigatório): Block ID - Especifique o ID do Bloco a ser atualizado. (exemplo: "9bc30ad4-9373-46a5-84ab-0a7845ee52e6").
    - `archived` (boolean, opcional): Archived - Defina como true para arquivar (excluir) um bloco. Defina como false para restaurar um bloco.
    - `paragraph` (object, opcional): Conteúdo do parágrafo.
      ```json
      {
        "rich_text": [
          {
            "type": "text",
            "text": {
              "content": "Lacinato kale",
              "link": null
            }
          }
        ],
        "color": "default"
      }
      ```
    - `image` (object, opcional): Bloco de imagem.
      ```json
      {
        "type": "external",
        "external": {
          "url": "https://website.domain/images/image.png"
        }
      }
      ```
    - `bookmark` (object, opcional): Bloco de bookmark.
      ```json
      {
        "caption": [],
        "url": "https://companywebsite.com"
      }
      ```
    - `code` (object, opcional): Bloco de código.
      ```json
      {
        "rich_text": [
          {
            "type": "text",
            "text": {
              "content": "const a = 3"
            }
          }
        ],
        "language": "javascript"
      }
      ```
    - `pdf` (object, opcional): Bloco de PDF.
      ```json
      {
        "type": "external",
        "external": {
          "url": "https://website.domain/files/doc.pdf"
        }
      }
      ```
    - `table` (object, opcional): Bloco de Tabela.
      ```json
      {
        "table_width": 2,
        "has_column_header": false,
        "has_row_header": false
      }
      ```
    - `tableOfContent` (object, opcional): Bloco de Sumário.
      ```json
      {
        "color": "default"
      }
      ```
    - `additionalFields` (object, opcional): Blocos adicionais.
      ```json
      {
        "child_page": {
          "title": "Lacinato kale"
        },
        "child_database": {
          "title": "My database"
        }
      }
      ```
  </Accordion>

  <Accordion title="NOTION_GET_BLOCK_BY_ID">
    **Descrição:** Busca um bloco pelo ID no Notion.

    **Parâmetros:**
    - `blockId` (string, obrigatório): Block ID - Especifique o ID do Bloco a ser buscado. (exemplo: "9bc30ad4-9373-46a5-84ab-0a7845ee52e6").
  </Accordion>

  <Accordion title="NOTION_DELETE_BLOCK">
    **Descrição:** Exclui um bloco no Notion.

    **Parâmetros:**
    - `blockId` (string, obrigatório): Block ID - Especifique o ID do Bloco a ser excluído. (exemplo: "9bc30ad4-9373-46a5-84ab-0a7845ee52e6").
  </Accordion>
</AccordionGroup>

## Exemplos de Uso

### Configuração Básica do Agente Notion

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

# Get enterprise tools (Notion tools will be included)
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

# Create an agent with Notion capabilities
notion_agent = Agent(
    role="Documentation Manager",
    goal="Manage documentation and knowledge base in Notion efficiently",
    backstory="An AI assistant specialized in content management and documentation.",
    tools=[enterprise_tools]
)

# Task to create a meeting notes page
create_notes_task = Task(
    description="Create a new meeting notes page in the team database with today's date and agenda items",
    agent=notion_agent,
    expected_output="Meeting notes page created successfully with structured content"
)

# Run the task
crew = Crew(
    agents=[notion_agent],
    tasks=[create_notes_task]
)

crew.kickoff()
```

### Filtrando Ferramentas Específicas do Notion

```python
from crewai_tools import CrewaiEnterpriseTools

# Get only specific Notion tools
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token",
    actions_list=["notion_create_page", "notion_update_block", "notion_search_pages"]
)

content_manager = Agent(
    role="Content Manager",
    goal="Create and manage content pages efficiently",
    backstory="An AI assistant that focuses on content creation and management.",
    tools=enterprise_tools
)

# Task to manage content workflow
content_workflow = Task(
    description="Create a new project documentation page and add structured content blocks for requirements and specifications",
    agent=content_manager,
    expected_output="Project documentation created with organized content sections"
)

crew = Crew(
    agents=[content_manager],
    tasks=[content_workflow]
)

crew.kickoff()
```

### Gerenciamento de Base de Conhecimento

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

knowledge_curator = Agent(
    role="Knowledge Curator",
    goal="Curate and organize knowledge base content in Notion",
    backstory="An experienced knowledge manager who organizes and maintains comprehensive documentation.",
    tools=[enterprise_tools]
)

# Task to curate knowledge base
curation_task = Task(
    description="""
    1. Search for existing documentation pages related to our new product feature
    2. Create a comprehensive feature documentation page with proper structure
    3. Add code examples, images, and links to related resources
    4. Update existing pages with cross-references to the new documentation
    """,
    agent=knowledge_curator,
    expected_output="Feature documentation created and integrated with existing knowledge base"
)

crew = Crew(
    agents=[knowledge_curator],
    tasks=[curation_task]
)

crew.kickoff()
```

### Estrutura e Organização de Conteúdo

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

content_organizer = Agent(
    role="Content Organizer",
    goal="Organize and structure content blocks for optimal readability",
    backstory="An AI assistant that specializes in content structure and user experience.",
    tools=[enterprise_tools]
)

# Task to organize content structure
organization_task = Task(
    description="""
    1. Get content from existing project pages
    2. Analyze the structure and identify improvement opportunities
    3. Update content blocks to use proper headings, tables, and formatting
    4. Add table of contents and improve navigation between related pages
    5. Create templates for future documentation consistency
    """,
    agent=content_organizer,
    expected_output="Content reorganized with improved structure and navigation"
)

crew = Crew(
    agents=[content_organizer],
    tasks=[organization_task]
)

crew.kickoff()
```

### Fluxos de Trabalho de Documentação Automatizados

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

doc_automator = Agent(
    role="Documentation Automator",
    goal="Automate documentation workflows and maintenance",
    backstory="An AI assistant that automates repetitive documentation tasks.",
    tools=[enterprise_tools]
)

# Complex documentation automation task
automation_task = Task(
    description="""
    1. Search for pages that haven't been updated in the last 30 days
    2. Review and update outdated content blocks
    3. Create weekly team update pages with consistent formatting
    4. Add status indicators and progress tracking to project pages
    5. Generate monthly documentation health reports
    6. Archive completed project pages and organize them in archive sections
    """,
    agent=doc_automator,
    expected_output="Documentation automated with updated content, weekly reports, and organized archives"
)

crew = Crew(
    agents=[doc_automator],
    tasks=[automation_task]
)

crew.kickoff()
```

## Solução de Problemas

### Problemas Comuns

**Erros de Permissão**
- Certifique-se de que sua conta Notion possui acesso de edição ao workspace desejado
- Verifique se a conexão OAuth inclui os escopos necessários para a API do Notion
- Confira se as páginas e bancos de dados estão compartilhados com a integração autenticada

**IDs de Página e Bloco Inválidos**
- Revise os IDs de página e bloco para garantir que estejam no formato UUID correto
- Garanta que as páginas e blocos referenciados existem e são acessíveis
- Verifique se os IDs da página ou banco de dados pai são válidos ao criar novas páginas

**Problemas com Schema de Propriedades**
- Assegure que as propriedades da página correspondem ao schema do banco de dados ao criar páginas em bancos de dados
- Verifique se os nomes e tipos das propriedades estão corretos para o banco de dados alvo
- Confirme que as propriedades obrigatórias estão incluídas ao criar ou atualizar páginas

**Estrutura dos Blocos de Conteúdo**
- Assegure que o conteúdo dos blocos segue as especificações de rich text do Notion
- Verifique se estruturas aninhadas de blocos estão devidamente formatadas
- Confira se URLs de mídias são acessíveis e estão corretamente formatadas

**Problemas de Pesquisa e Filtros**
- Certifique-se de que as queries de pesquisa estão devidamente formatadas e não estão vazias
- Use nomes de campos válidos em fórmulas de filtro: `query`, `filter.value`, `direction`, `page_size`
- Teste pesquisas simples antes de construir condições de filtro mais complexas

**Relacionamentos Pai-Filho**
- Verifique se a página ou banco de dados pai existe antes de criar páginas filhas
- Assegure que existam permissões apropriadas para o container pai
- Confirme que os schemas do banco permitem definir as propriedades desejadas

**Rich Text e Conteúdo de Mídia**
- Assegure que URLs para imagens externas, PDFs e bookmarks sejam acessíveis
- Verifique se a formatação rich text segue as especificações da API do Notion
- Confira se os tipos de linguagem nos blocos de código são suportados pelo Notion

**Operações de Arquivamento e Exclusão**
- Entenda a diferença entre arquivar (reversível) e excluir (permanente)
- Certifique-se de ter permissões para arquivar ou excluir o conteúdo desejado
- Tenha cuidado com operações em massa que possam afetar múltiplas páginas ou blocos

### Obtendo Ajuda

<Card title="Precisa de ajuda?" icon="headset" href="mailto:<EMAIL>">
  Entre em contato com nosso time de suporte para auxílio na configuração ou solução de problemas com a integração Notion.
</Card>