---
title: Kickoff Crew para Cada
description: Kickoff Crew para Cada Item em uma Lista
icon: at
---

## Introdução

A CrewAI oferece a capacidade de iniciar um crew para cada item em uma lista, permitindo que você execute o crew para cada item da lista. 
Esse recurso é particularmente útil quando é necessário realizar o mesmo conjunto de tarefas para vários itens.

## Iniciando um Crew para Cada Item

Para iniciar um crew para cada item em uma lista, utilize o método `kickoff_for_each()`. 
Esse método executa o crew para cada item da lista, permitindo o processamento eficiente de múltiplos itens.

Veja um exemplo de como iniciar um crew para cada item em uma lista:

```python Code
from crewai import Crew, Agent, Task

# Create an agent with code execution enabled
coding_agent = Agent(
    role="Python Data Analyst",
    goal="Analyze data and provide insights using Python",
    backstory="You are an experienced data analyst with strong Python skills.",
    allow_code_execution=True
)

# Create a task that requires code execution
data_analysis_task = Task(
    description="Analyze the given dataset and calculate the average age of participants. Ages: {ages}",
    agent=coding_agent,
    expected_output="The average age calculated from the dataset"
)

# Create a crew and add the task
analysis_crew = Crew(
    agents=[coding_agent],
    tasks=[data_analysis_task],
    verbose=True,
    memory=False
)

datasets = [
  { "ages": [25, 30, 35, 40, 45] },
  { "ages": [20, 25, 30, 35, 40] },
  { "ages": [30, 35, 40, 45, 50] }
]

# Execute the crew
result = analysis_crew.kickoff_for_each(inputs=datasets)
```