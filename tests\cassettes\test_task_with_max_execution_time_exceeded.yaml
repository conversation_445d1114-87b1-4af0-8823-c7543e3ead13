interactions:
- request:
    body: '{"contents": [{"role": "user", "parts": [{"text": "\nCurrent Task: Give
      me a list of 5 interesting ideas to explore for an article, what makes them
      unique and interesting.\n\nThis is the expected criteria for your final answer:
      Bullet point list of 5 interesting ideas.\nyou MUST return the actual complete
      content as the final answer, not a summary.\n\nBegin! This is VERY important
      to you, use the tools available and give your best Final Answer, your job depends
      on it!\n\nThought:"}]}], "system_instruction": {"parts": [{"text": "You are
      Researcher. You''re an expert researcher, specialized in technology, software
      engineering, AI and startups. You work as a freelancer and are now working on
      doing research and analysis for a new customer.\nYour personal goal is: Make
      the best research and analysis on content about AI and AI agents. Use the tool
      provided to you.\nYou ONLY have access to the following tools, and should NEVER
      make up tools that are not listed here:\n\nTool Name: what amazing tool\nTool
      Arguments: {}\nTool Description: My tool\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [what amazing tool], just the name, exactly
      as it''s written.\nAction Input: the input to the action, just a simple JSON
      object, enclosed in curly braces, using \" to wrap keys and values.\nObservation:
      the result of the action\n```\n\nOnce all necessary information is gathered,
      return the following format:\n\n```\nThought: I now know the final answer\nFinal
      Answer: the final answer to the original input question\n```"}]}, "generationConfig":
      {"temperature": 0.7, "stop_sequences": ["\nObservation:"]}}'
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1718'
      content-type:
      - application/json
      host:
      - generativelanguage.googleapis.com
      user-agent:
      - litellm/1.60.2
    method: POST
    uri: https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-latest:generateContent
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAC/61STWvbQBC961cMe+klFnJUy8S30JZiaGloTQlEOaylibRktavujtKkxv+9s3Lk
        rE2PFUha9r2Z9+ZjlwCISppa1ZLQixXc8Q3AbvwGzBpCQwxMV3zZS0dv3MOzi85MIXwOQWLT2qFp
        aQVrMIg1kIUGDTpWA1Wj9PBgHUgDnFJVGkFu7UBwvea7evwxnXwKsH6nNQwegVp+rdUhV4u6hw5h
        66Qynqzr0tKU5roiZc0KfreSQHbyjzLNGDNBsDb9wK52pfg1oHsp2WspPk/OFqC4bIeeQuA/feJz
        r60L8LnXC6ZWgw8QC40WOvmIHlBW7ZgMBqNYdgyLhNJS7EXUxv3xfH/x1nxnNYbOdrZGPdH3E0E8
        KKN8+50dWxNoPzbfbsQRlU/NF9v0zm7D/GZZml1dLoti8X4+X+RFvszzPJmkR1ExeK7qK5LkBZHH
        NRCcoutpYx/RfLDDuCB5nh10ooU6IRTLV5wsSX0aezVhUWL/kWWVjjctWkKuX2pFL+OWfbrdiKhH
        dOZr6lISNfPc5X9SK5anYsnrcA7z+onOq8NgGux4VLN5uphxzbMsuxTJPvkLk2fgU5EDAAA=
    headers:
      Alt-Svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Thu, 17 Apr 2025 19:00:14 GMT
      Server:
      - scaffolding on HTTPServer2
      Server-Timing:
      - gfet4t7; dur=1972
      Transfer-Encoding:
      - chunked
      Vary:
      - Origin
      - X-Origin
      - Referer
      X-Content-Type-Options:
      - nosniff
      X-Frame-Options:
      - SAMEORIGIN
      X-XSS-Protection:
      - '0'
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "system", "content": "You are Researcher. You''re
      an expert researcher, specialized in technology, software engineering, AI and
      startups. You work as a freelancer and are now working on doing research and
      analysis for a new customer.\nYour personal goal is: Make the best research
      and analysis on content about AI and AI agents. Use the tool provided to you.\nYou
      ONLY have access to the following tools, and should NEVER make up tools that
      are not listed here:\n\nTool Name: what amazing tool\nTool Arguments: {}\nTool
      Description: My tool\n\nIMPORTANT: Use the following format in your response:\n\n```\nThought:
      you should always think about what to do\nAction: the action to take, only one
      name of [what amazing tool], just the name, exactly as it''s written.\nAction
      Input: the input to the action, just a simple JSON object, enclosed in curly
      braces, using \" to wrap keys and values.\nObservation: the result of the action\n```\n\nOnce
      all necessary information is gathered, return the following format:\n\n```\nThought:
      I now know the final answer\nFinal Answer: the final answer to the original
      input question\n```"}, {"role": "user", "content": "\nCurrent Task: Give me
      a list of 5 interesting ideas to explore for an article, what makes them unique
      and interesting.\n\nThis is the expected criteria for your final answer: Bullet
      point list of 5 interesting ideas.\nyou MUST return the actual complete content
      as the final answer, not a summary.\n\nBegin! This is VERY important to you,
      use the tools available and give your best Final Answer, your job depends on
      it!\n\nThought:"}], "model": "gpt-4o-mini", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1666'
      content-type:
      - application/json
      cookie:
      - _cfuvid=CW_cKQGYWY3cL.S6Xo5z0cmkmWHy5Q50OA_KjPEijNk-1735926034530-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAAwAAAP//jJNPb9swDMXv+RSELrskRZukSZNbBxRbUWwYdhmwtTAYiba5ypQn0f2z
        It+9kNPW6dYBu9iAfnzP5BP9MAIw7MwajK1RbdP6yfvvG7r79PnDxYK/LPni5uO35uxstfp6fTvz
        1oyzImx+ktVn1YENTetJOcgO20iolF2PlvPlyfR4NZ/1oAmOfJZVrU7mYdKw8GR6OJ1PDpeTo5Mn
        dR3YUjJr+DECAHjon7lPcXRn1nA4fj5pKCWsyKxfigBMDD6fGEyJk6KoGQ/QBlGSvvVzECIHGqAi
        oYhKgOA5KYQSWJQiJWWpAKOy9QTsCBNE8nm4rDs9BxTXvyoSTWMog+1S1gQBrYkjdMK/OhJKqa+N
        5OkGxRKwgAaH9+8SKNlagg8VW/TgUVyy2NLBpVzKqc25ruG2RgVs8Hd21xA8wDOEc2k7XcPDFmB/
        1khllzDnLZ33ewBFgmKW9ilfPZHtS64+VG0Mm/SH1JQsnOoiEqYgOcOkoTU93Y4Arvr7615diWlj
        aFotNFxT/7nZdLHzM8PaDHR+9AQ1KPo91WI5fsOvcKTIPu1tgLFoa3KDdFgX7ByHPTDam/rvbt7y
        3k3OUv2P/QCspVbJFW0kx/b1xENZpPxX/avsJeW+YZMo3rClQplivglHJXZ+t+sm3SelpihZKopt
        5N3Cl21xPKfNfOMWq5kZbUePAAAA//8DALOFcXD+AwAA
    headers:
      CF-RAY:
      - 9433a372ec1069e6-LAS
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Wed, 21 May 2025 11:12:24 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=SC.7rKr584CqggyyZVMEQ5_zFD.g4Q5djrKS1Kg2ifs-1747825944-*******-M3vY0AX_JtRWZBGWsq8v4VWUTYLoQRB5_X2WbagS7emC73L80mIv3OUlMwPOwh7ag8LdkVtbkQ_hpAdM9kVJ_wyV7mhTNCoCPLE._sZWMeI;
        path=/; expires=Wed, 21-May-25 11:42:24 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=LMbhtXYRu2foKMlmDSxZF0LlpAWtafPdjq_4PWulGz0-1747825944424-*******-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '819'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-envoy-upstream-service-time:
      - '827'
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999620'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_93e3b862779b855ab166d171911fa9e0
    status:
      code: 200
      message: OK
version: 1
