[project]
name = "crewai"
dynamic = ["version"]
description = "Cutting-edge framework for orchestrating role-playing, autonomous AI agents. By fostering collaborative intelligence, CrewAI empowers agents to work together seamlessly, tackling complex tasks."
readme = "README.md"
requires-python = ">=3.10,<3.14"
authors = [
    { name = "<PERSON><PERSON>", email = "<EMAIL>" }
]
dependencies = [
    # Core Dependencies
    "pydantic>=2.4.2",
    "openai>=1.13.3",
    "litellm==1.74.3",
    "instructor>=1.3.3",
    # Text Processing
    "pdfplumber>=0.11.4",
    "regex>=2024.9.11",
    # Telemetry and Monitoring
    "opentelemetry-api>=1.30.0",
    "opentelemetry-sdk>=1.30.0",
    "opentelemetry-exporter-otlp-proto-http>=1.30.0",
    # Data Handling
    "chromadb>=0.5.23",
    "tokenizers>=0.20.3",
    "onnxruntime==1.22.0",
    "openpyxl>=3.1.5",
    "pyvis>=0.3.2",
    # Authentication and Security
    "python-dotenv>=1.0.0",
    "pyjwt>=2.9.0",
    # Configuration and Utils
    "click>=8.1.7",
    "appdirs>=1.4.4",
    "jsonref>=1.1.0",
    "json-repair==0.25.2",
    "uv>=0.4.25",
    "tomli-w>=1.1.0",
    "tomli>=2.0.2",
    "blinker>=1.9.0",
    "json5>=0.10.0",
    "portalocker==2.7.0",
]

[project.urls]
Homepage = "https://crewai.com"
Documentation = "https://docs.crewai.com"
Repository = "https://github.com/crewAIInc/crewAI"

[project.optional-dependencies]
tools = ["crewai-tools~=0.59.0"]
embeddings = [
    "tiktoken~=0.8.0"
]
agentops = ["agentops==0.3.18"]
pdfplumber = [
    "pdfplumber>=0.11.4",
]
pandas = [
    "pandas>=2.2.3",
]
openpyxl = [
    "openpyxl>=3.1.5",
]
mem0 = ["mem0ai>=0.1.94"]
docling = [
    "docling>=2.12.0",
]
aisuite = [
    "aisuite>=0.1.10",
]

[tool.uv]
dev-dependencies = [
    "ruff>=0.8.2",
    "mypy>=1.10.0",
    "pre-commit>=3.6.0",
    "pillow>=10.2.0",
    "cairosvg>=2.7.1",
    "pytest>=8.0.0",
    "python-dotenv>=1.0.0",
    "pytest-asyncio>=0.23.7",
    "pytest-subprocess>=1.5.2",
    "pytest-recording>=0.13.2",
    "pytest-randomly>=3.16.0",
    "pytest-timeout>=2.3.1",
    "pytest-xdist>=3.6.1",
    "pytest-split>=0.9.0",
]

[project.scripts]
crewai = "crewai.cli.cli:crewai"

[tool.mypy]
ignore_missing_imports = true
disable_error_code = 'import-untyped'
exclude = ["cli/templates"]

[tool.bandit]
exclude_dirs = ["src/crewai/cli/templates"]

# PyTorch index configuration, since torch 2.5.0 is not compatible with python 3.13
[[tool.uv.index]]
name = "pytorch-nightly"
url = "https://download.pytorch.org/whl/nightly/cpu"
explicit = true

[[tool.uv.index]]
name = "pytorch"
url = "https://download.pytorch.org/whl/cpu"
explicit = true

[tool.uv.sources]
torch = [
  { index = "pytorch-nightly", marker = "python_version >= '3.13'" },
  { index = "pytorch", marker = "python_version < '3.13'" },
]
torchvision = [
  { index = "pytorch-nightly", marker = "python_version >= '3.13'" },
  { index = "pytorch", marker = "python_version < '3.13'" },
]

[tool.hatch.version]
path = "src/crewai/__init__.py"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
exclude = [
"docs/**",
"docs/",
]

[tool.hatch.build.targets.sdist]
exclude = [
"docs/**",
"docs/",
]
