---
title: "Overview"
description: "Perform web searches, find repositories, and research information across the internet"
icon: "face-smile"
---

These tools enable your agents to search the web, research topics, and find information across various platforms including search engines, GitHub, and YouTube.

## **Available Tools**

<CardGroup cols={2}>
  <Card title="Serper Dev Tool" icon="google" href="/en/tools/search-research/serperdevtool">
    Google search API integration for comprehensive web search capabilities.
  </Card>

  <Card title="Brave Search Tool" icon="shield" href="/en/tools/search-research/bravesearchtool">
    Privacy-focused search with Brave's independent search index.
  </Card>

  <Card title="Exa Search Tool" icon="magnifying-glass" href="/en/tools/search-research/exasearchtool">
    AI-powered search for finding specific and relevant content.
  </Card>

  <Card title="LinkUp Search Tool" icon="link" href="/en/tools/search-research/linkupsearchtool">
    Real-time web search with fresh content indexing.
  </Card>

  <Card title="GitHub Search Tool" icon="github" href="/en/tools/search-research/githubsearchtool">
    Search GitHub repositories, code, issues, and documentation.
  </Card>

  <Card title="Website Search Tool" icon="globe" href="/en/tools/search-research/websitesearchtool">
    Search within specific websites and domains.
  </Card>

  <Card title="Code Docs Search Tool" icon="code" href="/en/tools/search-research/codedocssearchtool">
    Search through code documentation and technical resources.
  </Card>

  <Card title="YouTube Channel Search" icon="youtube" href="/en/tools/search-research/youtubechannelsearchtool">
    Search YouTube channels for specific content and creators.
  </Card>

  <Card title="YouTube Video Search" icon="play" href="/en/tools/search-research/youtubevideosearchtool">
    Find and analyze YouTube videos by topic, keyword, or criteria.
  </Card>

  <Card title="Tavily Search Tool" icon="magnifying-glass" href="/en/tools/search-research/tavilysearchtool">
    Comprehensive web search using Tavily's AI-powered search API.
  </Card>

  <Card title="Tavily Extractor Tool" icon="file-text" href="/en/tools/search-research/tavilyextractortool">
    Extract structured content from web pages using the Tavily API.
  </Card>
</CardGroup>

## **Common Use Cases**

- **Market Research**: Search for industry trends and competitor analysis
- **Content Discovery**: Find relevant articles, videos, and resources
- **Code Research**: Search repositories and documentation for solutions
- **Lead Generation**: Research companies and individuals
- **Academic Research**: Find scholarly articles and technical papers

```python
from crewai_tools import SerperDevTool, GitHubSearchTool, YoutubeVideoSearchTool, TavilySearchTool, TavilyExtractorTool

# Create research tools
web_search = SerperDevTool()
code_search = GitHubSearchTool()
video_research = YoutubeVideoSearchTool()
tavily_search = TavilySearchTool()
content_extractor = TavilyExtractorTool()

# Add to your agent
agent = Agent(
    role="Research Analyst",
    tools=[web_search, code_search, video_research, tavily_search, content_extractor],
    goal="Gather comprehensive information on any topic"
)
```
