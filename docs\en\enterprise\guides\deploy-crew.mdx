---
title: "Deploy Crew"
description: "Deploying a Crew on CrewAI Enterprise"
icon: "rocket"
---

<Note>
After creating a crew locally or through Crew Studio, the next step is deploying it to the CrewAI Enterprise platform. This guide covers multiple deployment methods to help you choose the best approach for your workflow.
</Note>

## Prerequisites

<CardGroup cols={2}>
  <Card title="Crew Ready for Deployment" icon="users">
    You should have a working crew either built locally or created through Crew Studio
  </Card>
  <Card title="GitHub Repository" icon="github">
    Your crew code should be in a GitHub repository (for GitHub integration method)
  </Card>
</CardGroup>

## Option 1: Deploy Using CrewAI CLI

The CLI provides the fastest way to deploy locally developed crews to the Enterprise platform.

<Steps>
  <Step title="Install CrewAI CLI">
    If you haven't already, install the CrewAI CLI:

    ```bash
    pip install crewai[tools]
    ```

    <Tip>
    The CLI comes with the main CrewAI package, but the `[tools]` extra ensures you have all deployment dependencies.
    </Tip>

  </Step>

  <Step title="Authenticate with the Enterprise Platform">
    First, you need to authenticate your CLI with the CrewAI Enterprise platform:

    ```bash
    # If you already have a CrewAI Enterprise account, or want to create one:
    crewai login
    ```

    When you run either command, the CLI will:
    1. Display a URL and a unique device code
    2. Open your browser to the authentication page
    3. Prompt you to confirm the device
    4. Complete the authentication process

    Upon successful authentication, you'll see a confirmation message in your terminal!

  </Step>

  <Step title="Create a Deployment">

    From your project directory, run:

    ```bash
    crewai deploy create
    ```

    This command will:
    1. Detect your GitHub repository information
    2. Identify environment variables in your local `.env` file
    3. Securely transfer these variables to the Enterprise platform
    4. Create a new deployment with a unique identifier

    On successful creation, you'll see a message like:
    ```shell
    Deployment created successfully!
    Name: your_project_name
    Deployment ID: ********-89ab-cdef-0123-456789abcdef
    Current Status: Deploy Enqueued
    ```

  </Step>

  <Step title="Monitor Deployment Progress">

    Track the deployment status with:

    ```bash
    crewai deploy status
    ```

    For detailed logs of the build process:

    ```bash
    crewai deploy logs
    ```

    <Tip>
    The first deployment typically takes 10-15 minutes as it builds the container images. Subsequent deployments are much faster.
    </Tip>

  </Step>
</Steps>

## Additional CLI Commands

The CrewAI CLI offers several commands to manage your deployments:

  ```bash
  # List all your deployments
  crewai deploy list

  # Get the status of your deployment
  crewai deploy status

  # View the logs of your deployment
  crewai deploy logs

  # Push updates after code changes
  crewai deploy push

  # Remove a deployment
  crewai deploy remove <deployment_id>
  ```

## Option 2: Deploy Directly via Web Interface

You can also deploy your crews directly through the CrewAI Enterprise web interface by connecting your GitHub account. This approach doesn't require using the CLI on your local machine.

<Steps>

  <Step title="Pushing to GitHub">

  You need to push your crew to a GitHub repository. If you haven't created a crew yet, you can [follow this tutorial](/en/quickstart).

  </Step>

  <Step title="Connecting GitHub to CrewAI Enterprise">

    1. Log in to [CrewAI Enterprise](https://app.crewai.com)
    2. Click on the button "Connect GitHub"

    <Frame>
      ![Connect GitHub Button](/images/enterprise/connect-github.png)
    </Frame>

  </Step>

  <Step title="Select the Repository">

    After connecting your GitHub account, you'll be able to select which repository to deploy:

    <Frame>
      ![Select Repository](/images/enterprise/select-repo.png)
    </Frame>

  </Step>

  <Step title="Set Environment Variables">

    Before deploying, you'll need to set up your environment variables to connect to your LLM provider or other services:

    1. You can add variables individually or in bulk
    2. Enter your environment variables in `KEY=VALUE` format (one per line)

    <Frame>
      ![Set Environment Variables](/images/enterprise/set-env-variables.png)
    </Frame>

  </Step>

  <Step title="Deploy Your Crew">

    1. Click the "Deploy" button to start the deployment process
    2. You can monitor the progress through the progress bar
    3. The first deployment typically takes around 10-15 minutes; subsequent deployments will be faster

    <Frame>
      ![Deploy Progress](/images/enterprise/deploy-progress.png)
    </Frame>

    Once deployment is complete, you'll see:
    - Your crew's unique URL
    - A Bearer token to protect your crew API
    - A "Delete" button if you need to remove the deployment

  </Step>

</Steps>

## ⚠️ Environment Variable Security Requirements

<Warning>
**Important**: CrewAI Enterprise has security restrictions on environment variable names that can cause deployment failures if not followed.
</Warning>

### Blocked Environment Variable Patterns

For security reasons, the following environment variable naming patterns are **automatically filtered** and will cause deployment issues:

**Blocked Patterns:**
- Variables ending with `_TOKEN` (e.g., `MY_API_TOKEN`)
- Variables ending with `_PASSWORD` (e.g., `DB_PASSWORD`)
- Variables ending with `_SECRET` (e.g., `API_SECRET`)
- Variables ending with `_KEY` in certain contexts

**Specific Blocked Variables:**
- `GITHUB_USER`, `GITHUB_TOKEN`
- `AWS_REGION`, `AWS_DEFAULT_REGION`
- Various internal CrewAI system variables

### Allowed Exceptions

Some variables are explicitly allowed despite matching blocked patterns:
- `AZURE_AD_TOKEN`
- `AZURE_OPENAI_AD_TOKEN`
- `ENTERPRISE_ACTION_TOKEN`
- `CREWAI_ENTEPRISE_TOOLS_TOKEN`

### How to Fix Naming Issues

If your deployment fails due to environment variable restrictions:

```bash
# ❌ These will cause deployment failures
OPENAI_TOKEN=sk-...
DATABASE_PASSWORD=mypassword
API_SECRET=secret123

# ✅ Use these naming patterns instead
OPENAI_API_KEY=sk-...
DATABASE_CREDENTIALS=mypassword
API_CONFIG=secret123
```

### Best Practices

1. **Use standard naming conventions**: `PROVIDER_API_KEY` instead of `PROVIDER_TOKEN`
2. **Test locally first**: Ensure your crew works with the renamed variables
3. **Update your code**: Change any references to the old variable names
4. **Document changes**: Keep track of renamed variables for your team

<Tip>
If you encounter deployment failures with cryptic environment variable errors, check your variable names against these patterns first.
</Tip>

### Interact with Your Deployed Crew

Once deployment is complete, you can access your crew through:

1. **REST API**: The platform generates a unique HTTPS endpoint with these key routes:
   - `/inputs`: Lists the required input parameters
   - `/kickoff`: Initiates an execution with provided inputs
   - `/status/{kickoff_id}`: Checks the execution status

2. **Web Interface**: Visit [app.crewai.com](https://app.crewai.com) to access:
   - **Status tab**: View deployment information, API endpoint details, and authentication token
   - **Run tab**: Visual representation of your crew's structure
   - **Executions tab**: History of all executions
   - **Metrics tab**: Performance analytics
   - **Traces tab**: Detailed execution insights

### Trigger an Execution

From the Enterprise dashboard, you can:

1. Click on your crew's name to open its details
2. Select "Trigger Crew" from the management interface
3. Enter the required inputs in the modal that appears
4. Monitor progress as the execution moves through the pipeline

### Monitoring and Analytics

The Enterprise platform provides comprehensive observability features:

- **Execution Management**: Track active and completed runs
- **Traces**: Detailed breakdowns of each execution
- **Metrics**: Token usage, execution times, and costs
- **Timeline View**: Visual representation of task sequences

### Advanced Features

The Enterprise platform also offers:

- **Environment Variables Management**: Securely store and manage API keys
- **LLM Connections**: Configure integrations with various LLM providers
- **Custom Tools Repository**: Create, share, and install tools
- **Crew Studio**: Build crews through a chat interface without writing code

<Card title="Need Help?" icon="headset" href="mailto:<EMAIL>">
  Contact our support team for assistance with deployment issues or questions about the Enterprise platform.
</Card>
