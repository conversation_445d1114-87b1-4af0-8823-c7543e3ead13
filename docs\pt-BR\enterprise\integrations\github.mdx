---
title: Integração com GitHub
description: "Gerenciamento de repositórios e issues com a integração do GitHub para CrewAI."
icon: "github"
---

## Visão Geral

Permita que seus agentes gerenciem repositórios, issues e releases através do GitHub. Crie e atualize issues, gerencie releases, acompanhe o desenvolvimento do projeto e otimize seu fluxo de trabalho de desenvolvimento de software com automação alimentada por IA.

## Pré-requisitos

Antes de usar a integração do GitHub, assegure-se de ter:

- Uma conta [CrewAI Enterprise](https://app.crewai.com) com assinatura ativa
- Uma conta GitHub com permissões adequadas no repositório
- Conta do GitHub conectada através da [página de Integrações](https://app.crewai.com/crewai_plus/connectors)

## Configurando a Integração com GitHub

### 1. Conecte sua conta GitHub

1. Acesse [Integrações CrewAI Enterprise](https://app.crewai.com/crewai_plus/connectors)
2. Encontre **GitHub** na seção de Integrações de Autenticação
3. Clique em **Conectar** e complete o fluxo OAuth
4. Conceda as permissões necessárias para gerenciamento de repositório e issues
5. Copie seu Token Enterprise nas [Configurações de Conta](https://app.crewai.com/crewai_plus/settings/account)

### 2. Instale o pacote necessário

```bash
uv add crewai-tools
```

## Ações Disponíveis

<AccordionGroup>
  <Accordion title="GITHUB_CREATE_ISSUE">
    **Descrição:** Cria uma issue no GitHub.

    **Parâmetros:**
    - `owner` (string, obrigatório): Proprietário - Especifique o nome do proprietário da conta do repositório associado a esta Issue. (exemplo: "abc").
    - `repo` (string, obrigatório): Repositório - Especifique o nome do repositório associado a esta Issue.
    - `title` (string, obrigatório): Título da Issue - Especifique o título da issue a ser criada.
    - `body` (string, opcional): Corpo da Issue - Especifique o conteúdo do corpo da issue a ser criada.
    - `assignees` (string, opcional): Responsáveis - Especifique o login dos responsáveis no GitHub como um array de strings para esta issue. (exemplo: `["octocat"]`).
  </Accordion>

  <Accordion title="GITHUB_UPDATE_ISSUE">
    **Descrição:** Atualiza uma issue no GitHub.

    **Parâmetros:**
    - `owner` (string, obrigatório): Proprietário - Especifique o nome do proprietário da conta do repositório associado a esta Issue. (exemplo: "abc").
    - `repo` (string, obrigatório): Repositório - Especifique o nome do repositório associado a esta Issue.
    - `issue_number` (string, obrigatório): Número da Issue - Especifique o número da issue a ser atualizada.
    - `title` (string, obrigatório): Título da Issue - Especifique o título da issue a ser atualizada.
    - `body` (string, opcional): Corpo da Issue - Especifique o conteúdo do corpo da issue a ser atualizada.
    - `assignees` (string, opcional): Responsáveis - Especifique o login dos responsáveis no GitHub como um array de strings para esta issue. (exemplo: `["octocat"]`).
    - `state` (string, opcional): Estado - Especifique o estado atualizado da issue.
      - Opções: `open`, `closed`
  </Accordion>

  <Accordion title="GITHUB_GET_ISSUE_BY_NUMBER">
    **Descrição:** Obtém uma issue pelo número no GitHub.

    **Parâmetros:**
    - `owner` (string, obrigatório): Proprietário - Especifique o nome do proprietário da conta do repositório associado a esta Issue. (exemplo: "abc").
    - `repo` (string, obrigatório): Repositório - Especifique o nome do repositório associado a esta Issue.
    - `issue_number` (string, obrigatório): Número da Issue - Especifique o número da issue a ser buscada.
  </Accordion>

  <Accordion title="GITHUB_LOCK_ISSUE">
    **Descrição:** Bloqueia uma issue no GitHub.

    **Parâmetros:**
    - `owner` (string, obrigatório): Proprietário - Especifique o nome do proprietário da conta do repositório associado a esta Issue. (exemplo: "abc").
    - `repo` (string, obrigatório): Repositório - Especifique o nome do repositório associado a esta Issue.
    - `issue_number` (string, obrigatório): Número da Issue - Especifique o número da issue a ser bloqueada.
    - `lock_reason` (string, obrigatório): Motivo do Bloqueio - Especifique um motivo para bloquear a discussão da issue ou pull request.
      - Opções: `off-topic`, `too heated`, `resolved`, `spam`
  </Accordion>

  <Accordion title="GITHUB_SEARCH_ISSUE">
    **Descrição:** Busca por issues no GitHub.

    **Parâmetros:**
    - `owner` (string, obrigatório): Proprietário - Especifique o nome do proprietário da conta do repositório associado a esta Issue. (exemplo: "abc").
    - `repo` (string, obrigatório): Repositório - Especifique o nome do repositório associado a esta Issue.
    - `filter` (object, obrigatório): Um filtro em forma normal disjuntiva - OU de grupos E de condições simples.
      ```json
      {
        "operator": "OR",
        "conditions": [
          {
            "operator": "AND",
            "conditions": [
              {
                "field": "assignee",
                "operator": "$stringExactlyMatches",
                "value": "octocat"
              }
            ]
          }
        ]
      }
      ```
      Campos disponíveis: `assignee`, `creator`, `mentioned`, `labels`
  </Accordion>

  <Accordion title="GITHUB_CREATE_RELEASE">
    **Descrição:** Cria um release no GitHub.

    **Parâmetros:**
    - `owner` (string, obrigatório): Proprietário - Especifique o nome do proprietário da conta do repositório associado a este Release. (exemplo: "abc").
    - `repo` (string, obrigatório): Repositório - Especifique o nome do repositório associado a este Release.
    - `tag_name` (string, obrigatório): Nome - Especifique o nome da tag do release a ser criada. (exemplo: "v1.0.0").
    - `target_commitish` (string, opcional): Destino - Especifique o destino do release. Pode ser o nome de um branch ou o SHA de um commit. Padrão é o branch principal. (exemplo: "master").
    - `body` (string, opcional): Descrição - Especifique uma descrição para este release.
    - `draft` (string, opcional): Rascunho - Especifique se o release criado deve ser um rascunho (não publicado).
      - Opções: `true`, `false`
    - `prerelease` (string, opcional): Pré-lançamento - Especifique se o release criado deve ser um pré-lançamento.
      - Opções: `true`, `false`
    - `discussion_category_name` (string, opcional): Nome da Categoria de Discussão - Se especificado, uma discussão da categoria indicada é criada e vinculada ao release. O valor deve ser uma categoria já existente no repositório.
    - `generate_release_notes` (string, opcional): Notas de Release - Especifique se o release criado deve criar automaticamente notas de release usando o nome e a descrição fornecidos.
      - Opções: `true`, `false`
  </Accordion>

  <Accordion title="GITHUB_UPDATE_RELEASE">
    **Descrição:** Atualiza um release no GitHub.

    **Parâmetros:**
    - `owner` (string, obrigatório): Proprietário - Especifique o nome do proprietário da conta do repositório associado a este Release. (exemplo: "abc").
    - `repo` (string, obrigatório): Repositório - Especifique o nome do repositório associado a este Release.
    - `id` (string, obrigatório): ID do Release - Especifique o ID do release a ser atualizado.
    - `tag_name` (string, opcional): Nome - Especifique o nome da tag do release a ser atualizado. (exemplo: "v1.0.0").
    - `target_commitish` (string, opcional): Destino - Especifique o destino do release. Pode ser o nome de um branch ou o SHA de um commit. Padrão é o branch principal. (exemplo: "master").
    - `body` (string, opcional): Descrição - Especifique uma descrição para este release.
    - `draft` (string, opcional): Rascunho - Especifique se o release criado deve ser um rascunho (não publicado).
      - Opções: `true`, `false`
    - `prerelease` (string, opcional): Pré-lançamento - Especifique se o release criado deve ser um pré-lançamento.
      - Opções: `true`, `false`
    - `discussion_category_name` (string, opcional): Nome da Categoria de Discussão - Se especificado, uma discussão da categoria indicada é criada e vinculada ao release. O valor deve ser uma categoria já existente no repositório.
    - `generate_release_notes` (string, opcional): Notas de Release - Especifique se o release criado deve criar automaticamente notas de release usando o nome e a descrição fornecidos.
      - Opções: `true`, `false`
  </Accordion>

  <Accordion title="GITHUB_GET_RELEASE_BY_ID">
    **Descrição:** Obtém um release por ID no GitHub.

    **Parâmetros:**
    - `owner` (string, obrigatório): Proprietário - Especifique o nome do proprietário da conta do repositório associado a este Release. (exemplo: "abc").
    - `repo` (string, obrigatório): Repositório - Especifique o nome do repositório associado a este Release.
    - `id` (string, obrigatório): ID do Release - Especifique o ID do release a ser recuperado.
  </Accordion>

  <Accordion title="GITHUB_GET_RELEASE_BY_TAG_NAME">
    **Descrição:** Obtém um release pelo nome da tag no GitHub.

    **Parâmetros:**
    - `owner` (string, obrigatório): Proprietário - Especifique o nome do proprietário da conta do repositório associado a este Release. (exemplo: "abc").
    - `repo` (string, obrigatório): Repositório - Especifique o nome do repositório associado a este Release.
    - `tag_name` (string, obrigatório): Nome - Especifique o nome da tag do release a ser recuperado. (exemplo: "v1.0.0").
  </Accordion>

  <Accordion title="GITHUB_DELETE_RELEASE">
    **Descrição:** Exclui um release no GitHub.

    **Parâmetros:**
    - `owner` (string, obrigatório): Proprietário - Especifique o nome do proprietário da conta do repositório associado a este Release. (exemplo: "abc").
    - `repo` (string, obrigatório): Repositório - Especifique o nome do repositório associado a este Release.
    - `id` (string, obrigatório): ID do Release - Especifique o ID do release a ser excluído.
  </Accordion>
</AccordionGroup>

## Exemplos de Uso

### Configuração Básica de Agente GitHub

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

# Get enterprise tools (GitHub tools will be included)
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

# Create an agent with GitHub capabilities
github_agent = Agent(
    role="Repository Manager",
    goal="Manage GitHub repositories, issues, and releases efficiently",
    backstory="An AI assistant specialized in repository management and issue tracking.",
    tools=[enterprise_tools]
)

# Task to create a new issue
create_issue_task = Task(
    description="Create a bug report issue for the login functionality in the main repository",
    agent=github_agent,
    expected_output="Issue created successfully with issue number"
)

# Run the task
crew = Crew(
    agents=[github_agent],
    tasks=[create_issue_task]
)

crew.kickoff()
```

### Filtrando Ferramentas GitHub Específicas

```python
from crewai_tools import CrewaiEnterpriseTools

# Get only specific GitHub tools
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token",
    actions_list=["github_create_issue", "github_update_issue", "github_search_issue"]
)

issue_manager = Agent(
    role="Issue Manager",
    goal="Create and manage GitHub issues efficiently",
    backstory="An AI assistant that focuses on issue tracking and management.",
    tools=enterprise_tools
)

# Task to manage issue workflow
issue_workflow = Task(
    description="Create a feature request issue and assign it to the development team",
    agent=issue_manager,
    expected_output="Feature request issue created and assigned successfully"
)

crew = Crew(
    agents=[issue_manager],
    tasks=[issue_workflow]
)

crew.kickoff()
```

### Gerenciamento de Releases

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

release_manager = Agent(
    role="Release Manager",
    goal="Manage software releases and versioning",
    backstory="An experienced release manager who handles version control and release processes.",
    tools=[enterprise_tools]
)

# Task to create a new release
release_task = Task(
    description="""
    Create a new release v2.1.0 for the project with:
    - Auto-generated release notes
    - Target the main branch
    - Include a description of new features and bug fixes
    """,
    agent=release_manager,
    expected_output="Release v2.1.0 created successfully with release notes"
)

crew = Crew(
    agents=[release_manager],
    tasks=[release_task]
)

crew.kickoff()
```

### Acompanhamento e Gerenciamento de Issues

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

project_coordinator = Agent(
    role="Project Coordinator",
    goal="Track and coordinate project issues and development progress",
    backstory="An AI assistant that helps coordinate development work and track project progress.",
    tools=[enterprise_tools]
)

# Complex task involving multiple GitHub operations
coordination_task = Task(
    description="""
    1. Search for all open issues assigned to the current milestone
    2. Identify overdue issues and update their priority labels
    3. Create a weekly progress report issue
    4. Lock resolved issues that have been inactive for 30 days
    """,
    agent=project_coordinator,
    expected_output="Project coordination completed with progress report and issue management"
)

crew = Crew(
    agents=[project_coordinator],
    tasks=[coordination_task]
)

crew.kickoff()
```

### Obtendo Ajuda

<Card title="Precisa de Ajuda?" icon="headset" href="mailto:<EMAIL>">
  Entre em contato com nossa equipe de suporte para auxílio na configuração ou solução de problemas com a integração do GitHub.
</Card>