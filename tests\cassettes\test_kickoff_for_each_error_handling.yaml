interactions:
- request:
    body: !!binary |
      Cv1YCiQKIgoMc2VydmljZS5uYW1lEhIKEGNyZXdBSS10ZWxlbWV0cnkS1FgKEgoQY3Jld2FpLnRl
      bGVtZXRyeRLADQoQ5TzgW9QzcBbzMl1hJozLcxIIl3adf7U81wwqDENyZXcgQ3JlYXRlZDABOaAJ
      txhffgkYQfiVuRhffgkYShoKDmNyZXdhaV92ZXJzaW9uEggKBjAuODAuMEoaCg5weXRob25fdmVy
      c2lvbhIICgYzLjEyLjVKLgoIY3Jld19rZXkSIgogM2Y4ZDVjM2FiODgyZDY4NjlkOTNjYjgxZjBl
      MmVkNGFKMQoHY3Jld19pZBImCiRjYjRiY2Q1Zi0xYWJkLTQyYmYtOGQ1OC02ZmEzMDU3ZDFjOTZK
      HAoMY3Jld19wcm9jZXNzEgwKCnNlcXVlbnRpYWxKEQoLY3Jld19tZW1vcnkSAhAAShoKFGNyZXdf
      bnVtYmVyX29mX3Rhc2tzEgIYA0obChVjcmV3X251bWJlcl9vZl9hZ2VudHMSAhgCSpIFCgtjcmV3
      X2FnZW50cxKCBQr/BFt7ImtleSI6ICI4YmQyMTM5YjU5NzUxODE1MDZlNDFmZDljNDU2M2Q3NSIs
      ICJpZCI6ICI1ZThjNTM1MS1jNWVlLTRhZGUtODY5MC1kM2RhOWI1NzI5YzciLCAicm9sZSI6ICJS
      ZXNlYXJjaGVyIiwgInZlcmJvc2U/IjogZmFsc2UsICJtYXhfaXRlciI6IDIwLCAibWF4X3JwbSI6
      IG51bGwsICJmdW5jdGlvbl9jYWxsaW5nX2xsbSI6ICIiLCAibGxtIjogImdwdC00by1taW5pIiwg
      ImRlbGVnYXRpb25fZW5hYmxlZD8iOiBmYWxzZSwgImFsbG93X2NvZGVfZXhlY3V0aW9uPyI6IGZh
      bHNlLCAibWF4X3JldHJ5X2xpbWl0IjogMiwgInRvb2xzX25hbWVzIjogW119LCB7ImtleSI6ICI5
      YTUwMTVlZjQ4OTVkYzYyNzhkNTQ4MThiYTQ0NmFmNyIsICJpZCI6ICJhMTcwODczOC0yYWE2LTRk
      ZmYtODFlNy00OGFkMDNjNWFjY2QiLCAicm9sZSI6ICJTZW5pb3IgV3JpdGVyIiwgInZlcmJvc2U/
      IjogZmFsc2UsICJtYXhfaXRlciI6IDIwLCAibWF4X3JwbSI6IG51bGwsICJmdW5jdGlvbl9jYWxs
      aW5nX2xsbSI6ICIiLCAibGxtIjogImdwdC00by1taW5pIiwgImRlbGVnYXRpb25fZW5hYmxlZD8i
      OiBmYWxzZSwgImFsbG93X2NvZGVfZXhlY3V0aW9uPyI6IGZhbHNlLCAibWF4X3JldHJ5X2xpbWl0
      IjogMiwgInRvb2xzX25hbWVzIjogW119XUrbBQoKY3Jld190YXNrcxLMBQrJBVt7ImtleSI6ICI2
      Nzg0OWZmNzE3ZGJhZGFiYTFiOTVkNWYyZGZjZWVhMSIsICJpZCI6ICIyNzkxNTMxMy0wNDBhLTRk
      ZWItOTVkMy1mNWVmMzg2Mjk3NTEiLCAiYXN5bmNfZXhlY3V0aW9uPyI6IHRydWUsICJodW1hbl9p
      bnB1dD8iOiBmYWxzZSwgImFnZW50X3JvbGUiOiAiUmVzZWFyY2hlciIsICJhZ2VudF9rZXkiOiAi
      OGJkMjEzOWI1OTc1MTgxNTA2ZTQxZmQ5YzQ1NjNkNzUiLCAidG9vbHNfbmFtZXMiOiBbXX0sIHsi
      a2V5IjogImZjNTZkZWEzOGM5OTc0YjZmNTVhMmUyOGMxNDk5ODg2IiwgImlkIjogIjc3NzQ3MmVl
      LWYzNzAtNDQyZS05NWMyLWVlMGVkYzZiMTgyZiIsICJhc3luY19leGVjdXRpb24/IjogZmFsc2Us
      ICJodW1hbl9pbnB1dD8iOiBmYWxzZSwgImFnZW50X3JvbGUiOiAiUmVzZWFyY2hlciIsICJhZ2Vu
      dF9rZXkiOiAiOGJkMjEzOWI1OTc1MTgxNTA2ZTQxZmQ5YzQ1NjNkNzUiLCAidG9vbHNfbmFtZXMi
      OiBbXX0sIHsia2V5IjogIjk0YTgyNmMxOTMwNTU5Njg2YmFmYjQwOWVlODM4NzZmIiwgImlkIjog
      ImM4OWEzODA2LTg5MDItNGQ2My1iYzA0LTdjMzRhZTJmM2UxNyIsICJhc3luY19leGVjdXRpb24/
      IjogZmFsc2UsICJodW1hbl9pbnB1dD8iOiBmYWxzZSwgImFnZW50X3JvbGUiOiAiU2VuaW9yIFdy
      aXRlciIsICJhZ2VudF9rZXkiOiAiOWE1MDE1ZWY0ODk1ZGM2Mjc4ZDU0ODE4YmE0NDZhZjciLCAi
      dG9vbHNfbmFtZXMiOiBbXX1degIYAYUBAAEAABKOAgoQSqupTllrk2mxgu2AqenZUhIIspWxig2+
      1M0qDFRhc2sgQ3JlYXRlZDABOcCj1BhffgkYQfhq1RhffgkYSi4KCGNyZXdfa2V5EiIKIDNmOGQ1
      YzNhYjg4MmQ2ODY5ZDkzY2I4MWYwZTJlZDRhSjEKB2NyZXdfaWQSJgokY2I0YmNkNWYtMWFiZC00
      MmJmLThkNTgtNmZhMzA1N2QxYzk2Si4KCHRhc2tfa2V5EiIKIDY3ODQ5ZmY3MTdkYmFkYWJhMWI5
      NWQ1ZjJkZmNlZWExSjEKB3Rhc2tfaWQSJgokMjc5MTUzMTMtMDQwYS00ZGViLTk1ZDMtZjVlZjM4
      NjI5NzUxegIYAYUBAAEAABKOAgoQ3dJesXQA5ISCqVgmwvBMgRIIdrWBiVQuihcqDFRhc2sgQ3Jl
      YXRlZDABOdjAch1ffgkYQVh8cx1ffgkYSi4KCGNyZXdfa2V5EiIKIDNmOGQ1YzNhYjg4MmQ2ODY5
      ZDkzY2I4MWYwZTJlZDRhSjEKB2NyZXdfaWQSJgokY2I0YmNkNWYtMWFiZC00MmJmLThkNTgtNmZh
      MzA1N2QxYzk2Si4KCHRhc2tfa2V5EiIKIGZjNTZkZWEzOGM5OTc0YjZmNTVhMmUyOGMxNDk5ODg2
      SjEKB3Rhc2tfaWQSJgokNzc3NDcyZWUtZjM3MC00NDJlLTk1YzItZWUwZWRjNmIxODJmegIYAYUB
      AAEAABKOAgoQCBmV+4VbArZNiL5MaefbahII1fRxaC46KKgqDFRhc2sgQ3JlYXRlZDABOaDs4SNf
      fgkYQai74iNffgkYSi4KCGNyZXdfa2V5EiIKIDNmOGQ1YzNhYjg4MmQ2ODY5ZDkzY2I4MWYwZTJl
      ZDRhSjEKB2NyZXdfaWQSJgokY2I0YmNkNWYtMWFiZC00MmJmLThkNTgtNmZhMzA1N2QxYzk2Si4K
      CHRhc2tfa2V5EiIKIDk0YTgyNmMxOTMwNTU5Njg2YmFmYjQwOWVlODM4NzZmSjEKB3Rhc2tfaWQS
      JgokYzg5YTM4MDYtODkwMi00ZDYzLWJjMDQtN2MzNGFlMmYzZTE3egIYAYUBAAEAABKiBwoQhITI
      U8q3JLgneRv1MZQY8RIIF2CpEmiZsP4qDENyZXcgQ3JlYXRlZDABOZDBCytffgkYQTDFDStffgkY
      ShoKDmNyZXdhaV92ZXJzaW9uEggKBjAuODAuMEoaCg5weXRob25fdmVyc2lvbhIICgYzLjEyLjVK
      LgoIY3Jld19rZXkSIgogYTljYzVkNDMzOTViMjFiMTgxYzgwYmQ0MzUxY2NlYzhKMQoHY3Jld19p
      ZBImCiQ2MTMwMWVmYS0yOGQ4LTQyNTItOWVjNi1iM2JmZDcyMWM0MzVKHAoMY3Jld19wcm9jZXNz
      EgwKCnNlcXVlbnRpYWxKEQoLY3Jld19tZW1vcnkSAhAAShoKFGNyZXdfbnVtYmVyX29mX3Rhc2tz
      EgIYAUobChVjcmV3X251bWJlcl9vZl9hZ2VudHMSAhgBStECCgtjcmV3X2FnZW50cxLBAgq+Alt7
      ImtleSI6ICI4YmQyMTM5YjU5NzUxODE1MDZlNDFmZDljNDU2M2Q3NSIsICJpZCI6ICI3NWRjOTUw
      OS02MjQ4LTQ0YWQtYTExZC1iZjdlZWVhOWI0NTQiLCAicm9sZSI6ICJSZXNlYXJjaGVyIiwgInZl
      cmJvc2U/IjogZmFsc2UsICJtYXhfaXRlciI6IDIwLCAibWF4X3JwbSI6IG51bGwsICJmdW5jdGlv
      bl9jYWxsaW5nX2xsbSI6ICIiLCAibGxtIjogImdwdC00by1taW5pIiwgImRlbGVnYXRpb25fZW5h
      YmxlZD8iOiBmYWxzZSwgImFsbG93X2NvZGVfZXhlY3V0aW9uPyI6IGZhbHNlLCAibWF4X3JldHJ5
      X2xpbWl0IjogMiwgInRvb2xzX25hbWVzIjogW119XUr+AQoKY3Jld190YXNrcxLvAQrsAVt7Imtl
      eSI6ICJlOWU2YjcyYWFjMzI2NDU5ZGQ3MDY4ZjBiMTcxN2MxYyIsICJpZCI6ICIxOTBlMGQ1Zi0y
      NDg1LTQ3N2ItYWIxNC1kMTlmNDE5YTFlYjQiLCAiYXN5bmNfZXhlY3V0aW9uPyI6IHRydWUsICJo
      dW1hbl9pbnB1dD8iOiBmYWxzZSwgImFnZW50X3JvbGUiOiAiUmVzZWFyY2hlciIsICJhZ2VudF9r
      ZXkiOiAiOGJkMjEzOWI1OTc1MTgxNTA2ZTQxZmQ5YzQ1NjNkNzUiLCAidG9vbHNfbmFtZXMiOiBb
      XX1degIYAYUBAAEAABKOAgoQxgDNe1lQGKnixKPk3O1TDBIISyqKkjcA7OYqDFRhc2sgQ3JlYXRl
      ZDABOfCYJCtffgkYQZAlJStffgkYSi4KCGNyZXdfa2V5EiIKIGE5Y2M1ZDQzMzk1YjIxYjE4MWM4
      MGJkNDM1MWNjZWM4SjEKB2NyZXdfaWQSJgokNjEzMDFlZmEtMjhkOC00MjUyLTllYzYtYjNiZmQ3
      MjFjNDM1Si4KCHRhc2tfa2V5EiIKIGU5ZTZiNzJhYWMzMjY0NTlkZDcwNjhmMGIxNzE3YzFjSjEK
      B3Rhc2tfaWQSJgokMTkwZTBkNWYtMjQ4NS00NzdiLWFiMTQtZDE5ZjQxOWExZWI0egIYAYUBAAEA
      ABK/DQoQwwR54Z8nOGgj2VSb63WRwhIIonLT+7Mwj00qDENyZXcgQ3JlYXRlZDABObDfvzhffgkY
      QfBvwjhffgkYShoKDmNyZXdhaV92ZXJzaW9uEggKBjAuODAuMEoaCg5weXRob25fdmVyc2lvbhII
      CgYzLjEyLjVKLgoIY3Jld19rZXkSIgogNjZhOTYwZGM2OWZmZjU3OGIyNmM2MWQ0ZjdjNWE5ZmVK
      MQoHY3Jld19pZBImCiQxNThhMTkzOS01OWUzLTRlODgtYTRkYi04M2IzN2U5MjgxZWVKHAoMY3Jl
      d19wcm9jZXNzEgwKCnNlcXVlbnRpYWxKEQoLY3Jld19tZW1vcnkSAhAAShoKFGNyZXdfbnVtYmVy
      X29mX3Rhc2tzEgIYA0obChVjcmV3X251bWJlcl9vZl9hZ2VudHMSAhgCSpIFCgtjcmV3X2FnZW50
      cxKCBQr/BFt7ImtleSI6ICI4YmQyMTM5YjU5NzUxODE1MDZlNDFmZDljNDU2M2Q3NSIsICJpZCI6
      ICI1ZThjNTM1MS1jNWVlLTRhZGUtODY5MC1kM2RhOWI1NzI5YzciLCAicm9sZSI6ICJSZXNlYXJj
      aGVyIiwgInZlcmJvc2U/IjogZmFsc2UsICJtYXhfaXRlciI6IDIwLCAibWF4X3JwbSI6IG51bGws
      ICJmdW5jdGlvbl9jYWxsaW5nX2xsbSI6ICIiLCAibGxtIjogImdwdC00by1taW5pIiwgImRlbGVn
      YXRpb25fZW5hYmxlZD8iOiBmYWxzZSwgImFsbG93X2NvZGVfZXhlY3V0aW9uPyI6IGZhbHNlLCAi
      bWF4X3JldHJ5X2xpbWl0IjogMiwgInRvb2xzX25hbWVzIjogW119LCB7ImtleSI6ICI5YTUwMTVl
      ZjQ4OTVkYzYyNzhkNTQ4MThiYTQ0NmFmNyIsICJpZCI6ICJhMTcwODczOC0yYWE2LTRkZmYtODFl
      Ny00OGFkMDNjNWFjY2QiLCAicm9sZSI6ICJTZW5pb3IgV3JpdGVyIiwgInZlcmJvc2U/IjogZmFs
      c2UsICJtYXhfaXRlciI6IDIwLCAibWF4X3JwbSI6IG51bGwsICJmdW5jdGlvbl9jYWxsaW5nX2xs
      bSI6ICIiLCAibGxtIjogImdwdC00by1taW5pIiwgImRlbGVnYXRpb25fZW5hYmxlZD8iOiBmYWxz
      ZSwgImFsbG93X2NvZGVfZXhlY3V0aW9uPyI6IGZhbHNlLCAibWF4X3JldHJ5X2xpbWl0IjogMiwg
      InRvb2xzX25hbWVzIjogW119XUraBQoKY3Jld190YXNrcxLLBQrIBVt7ImtleSI6ICI5NDRhZWYw
      YmFjODQwZjFjMjdiZDgzYTkzN2JjMzYxYiIsICJpZCI6ICIzN2FkNzI5MC04Yjg5LTRjNWEtYmNl
      Zi03YzY0ZWJhMWM5NjciLCAiYXN5bmNfZXhlY3V0aW9uPyI6IHRydWUsICJodW1hbl9pbnB1dD8i
      OiBmYWxzZSwgImFnZW50X3JvbGUiOiAiUmVzZWFyY2hlciIsICJhZ2VudF9rZXkiOiAiOGJkMjEz
      OWI1OTc1MTgxNTA2ZTQxZmQ5YzQ1NjNkNzUiLCAidG9vbHNfbmFtZXMiOiBbXX0sIHsia2V5Ijog
      ImZjNTZkZWEzOGM5OTc0YjZmNTVhMmUyOGMxNDk5ODg2IiwgImlkIjogIjZhMmViMGY2LTgwZTIt
      NDkxOC05Zjk3LWVhNDY3OTNkMjI2YyIsICJhc3luY19leGVjdXRpb24/IjogdHJ1ZSwgImh1bWFu
      X2lucHV0PyI6IGZhbHNlLCAiYWdlbnRfcm9sZSI6ICJSZXNlYXJjaGVyIiwgImFnZW50X2tleSI6
      ICI4YmQyMTM5YjU5NzUxODE1MDZlNDFmZDljNDU2M2Q3NSIsICJ0b29sc19uYW1lcyI6IFtdfSwg
      eyJrZXkiOiAiOTRhODI2YzE5MzA1NTk2ODZiYWZiNDA5ZWU4Mzg3NmYiLCAiaWQiOiAiZGQ2Yzkz
      NzAtOGYwNC00ZDFmLThjODMtMmFiM2IyYzIwYWI3IiwgImFzeW5jX2V4ZWN1dGlvbj8iOiBmYWxz
      ZSwgImh1bWFuX2lucHV0PyI6IGZhbHNlLCAiYWdlbnRfcm9sZSI6ICJTZW5pb3IgV3JpdGVyIiwg
      ImFnZW50X2tleSI6ICI5YTUwMTVlZjQ4OTVkYzYyNzhkNTQ4MThiYTQ0NmFmNyIsICJ0b29sc19u
      YW1lcyI6IFtdfV16AhgBhQEAAQAAErMHChBV+1WNQzpVlY6l4C/mUgHzEgi3vWQXjOQJ5CoMQ3Jl
      dyBDcmVhdGVkMAE5sH1kPF9+CRhBaH1mPF9+CRhKGgoOY3Jld2FpX3ZlcnNpb24SCAoGMC44MC4w
      ShoKDnB5dGhvbl92ZXJzaW9uEggKBjMuMTIuNUouCghjcmV3X2tleRIiCiBlZTY3NDVkN2M4YWU4
      MmUwMGRmOTRkZTBmN2Y4NzExOEoxCgdjcmV3X2lkEiYKJDAwOThmODNmLTdkNTAtNGI2Mi1hYmIy
      LTJlNTc0N2ZlMWE4OUocCgxjcmV3X3Byb2Nlc3MSDAoKc2VxdWVudGlhbEoRCgtjcmV3X21lbW9y
      eRICEABKGgoUY3Jld19udW1iZXJfb2ZfdGFza3MSAhgBShsKFWNyZXdfbnVtYmVyX29mX2FnZW50
      cxICGAFK2QIKC2NyZXdfYWdlbnRzEskCCsYCW3sia2V5IjogImYzMzg2ZjZkOGRhNzVhYTQxNmE2
      ZTMxMDA1M2Y3Njk4IiwgImlkIjogIjEzODI4ZDViLWIyOWMtNDllMy05NWVhLTkyOGQ2ZmZhY2I0
      NSIsICJyb2xlIjogInt0b3BpY30gUmVzZWFyY2hlciIsICJ2ZXJib3NlPyI6IGZhbHNlLCAibWF4
      X2l0ZXIiOiAyMCwgIm1heF9ycG0iOiBudWxsLCAiZnVuY3Rpb25fY2FsbGluZ19sbG0iOiAiIiwg
      ImxsbSI6ICJncHQtNG8tbWluaSIsICJkZWxlZ2F0aW9uX2VuYWJsZWQ/IjogZmFsc2UsICJhbGxv
      d19jb2RlX2V4ZWN1dGlvbj8iOiBmYWxzZSwgIm1heF9yZXRyeV9saW1pdCI6IDIsICJ0b29sc19u
      YW1lcyI6IFtdfV1KhwIKCmNyZXdfdGFza3MS+AEK9QFbeyJrZXkiOiAiMDZhNzMyMjBmNDE0OGE0
      YmJkNWJhY2IwZDBiNDRmY2UiLCAiaWQiOiAiNWM4MjM1ZmYtNWVjNy00NzFhLWI4NWEtNWFkZjk3
      YzJkYzI3IiwgImFzeW5jX2V4ZWN1dGlvbj8iOiBmYWxzZSwgImh1bWFuX2lucHV0PyI6IGZhbHNl
      LCAiYWdlbnRfcm9sZSI6ICJ7dG9waWN9IFJlc2VhcmNoZXIiLCAiYWdlbnRfa2V5IjogImYzMzg2
      ZjZkOGRhNzVhYTQxNmE2ZTMxMDA1M2Y3Njk4IiwgInRvb2xzX25hbWVzIjogW119XXoCGAGFAQAB
      AAASjgIKEHOZ/a+LQwTLSkMO0sPwg9gSCL1SwQw1+0iKKgxUYXNrIENyZWF0ZWQwATl4kX48X34J
      GEFIFn88X34JGEouCghjcmV3X2tleRIiCiBlZTY3NDVkN2M4YWU4MmUwMGRmOTRkZTBmN2Y4NzEx
      OEoxCgdjcmV3X2lkEiYKJDAwOThmODNmLTdkNTAtNGI2Mi1hYmIyLTJlNTc0N2ZlMWE4OUouCgh0
      YXNrX2tleRIiCiAwNmE3MzIyMGY0MTQ4YTRiYmQ1YmFjYjBkMGI0NGZjZUoxCgd0YXNrX2lkEiYK
      JDVjODIzNWZmLTVlYzctNDcxYS1iODVhLTVhZGY5N2MyZGMyN3oCGAGFAQABAAASswcKEHZQCRd7
      z4ZBCh4+06qs1r4SCNzrNsw+dn2zKgxDcmV3IENyZWF0ZWQwATlgWdtDX34JGEGIcN1DX34JGEoa
      Cg5jcmV3YWlfdmVyc2lvbhIICgYwLjgwLjBKGgoOcHl0aG9uX3ZlcnNpb24SCAoGMy4xMi41Si4K
      CGNyZXdfa2V5EiIKIGVlNjc0NWQ3YzhhZTgyZTAwZGY5NGRlMGY3Zjg3MTE4SjEKB2NyZXdfaWQS
      JgokZTgzMTdjNzEtNmZiZS00MjI5LWE3MzctYTkxM2I0ZmU0ZTU0ShwKDGNyZXdfcHJvY2VzcxIM
      CgpzZXF1ZW50aWFsShEKC2NyZXdfbWVtb3J5EgIQAEoaChRjcmV3X251bWJlcl9vZl90YXNrcxIC
      GAFKGwoVY3Jld19udW1iZXJfb2ZfYWdlbnRzEgIYAUrZAgoLY3Jld19hZ2VudHMSyQIKxgJbeyJr
      ZXkiOiAiZjMzODZmNmQ4ZGE3NWFhNDE2YTZlMzEwMDUzZjc2OTgiLCAiaWQiOiAiNjAwMzU5OTYt
      ZWU1ZS00YmZhLThmODctMGM1ZTY0OTBlMmE4IiwgInJvbGUiOiAie3RvcGljfSBSZXNlYXJjaGVy
      IiwgInZlcmJvc2U/IjogZmFsc2UsICJtYXhfaXRlciI6IDIwLCAibWF4X3JwbSI6IG51bGwsICJm
      dW5jdGlvbl9jYWxsaW5nX2xsbSI6ICIiLCAibGxtIjogImdwdC00by1taW5pIiwgImRlbGVnYXRp
      b25fZW5hYmxlZD8iOiBmYWxzZSwgImFsbG93X2NvZGVfZXhlY3V0aW9uPyI6IGZhbHNlLCAibWF4
      X3JldHJ5X2xpbWl0IjogMiwgInRvb2xzX25hbWVzIjogW119XUqHAgoKY3Jld190YXNrcxL4AQr1
      AVt7ImtleSI6ICIwNmE3MzIyMGY0MTQ4YTRiYmQ1YmFjYjBkMGI0NGZjZSIsICJpZCI6ICI4MDc3
      MDhjNS0yN2RkLTQ4ZDEtYTU0ZC1lZTRkNTZmMzBiZTQiLCAiYXN5bmNfZXhlY3V0aW9uPyI6IGZh
      bHNlLCAiaHVtYW5faW5wdXQ/IjogZmFsc2UsICJhZ2VudF9yb2xlIjogInt0b3BpY30gUmVzZWFy
      Y2hlciIsICJhZ2VudF9rZXkiOiAiZjMzODZmNmQ4ZGE3NWFhNDE2YTZlMzEwMDUzZjc2OTgiLCAi
      dG9vbHNfbmFtZXMiOiBbXX1degIYAYUBAAEAABKOAgoQtqHg5uy2kZsnJlJTYgmZoxIIlgUHkQ7m
      LugqDFRhc2sgQ3JlYXRlZDABOTi470NffgkYQQg98ENffgkYSi4KCGNyZXdfa2V5EiIKIGVlNjc0
      NWQ3YzhhZTgyZTAwZGY5NGRlMGY3Zjg3MTE4SjEKB2NyZXdfaWQSJgokZTgzMTdjNzEtNmZiZS00
      MjI5LWE3MzctYTkxM2I0ZmU0ZTU0Si4KCHRhc2tfa2V5EiIKIDA2YTczMjIwZjQxNDhhNGJiZDVi
      YWNiMGQwYjQ0ZmNlSjEKB3Rhc2tfaWQSJgokODA3NzA4YzUtMjdkZC00OGQxLWE1NGQtZWU0ZDU2
      ZjMwYmU0egIYAYUBAAEAABKzBwoQpfKhpM9cCoiT5Mun1aoNQhII4HhX0QHHc/0qDENyZXcgQ3Jl
      YXRlZDABORiH20lffgkYQcix3UlffgkYShoKDmNyZXdhaV92ZXJzaW9uEggKBjAuODAuMEoaCg5w
      eXRob25fdmVyc2lvbhIICgYzLjEyLjVKLgoIY3Jld19rZXkSIgogZWU2NzQ1ZDdjOGFlODJlMDBk
      Zjk0ZGUwZjdmODcxMThKMQoHY3Jld19pZBImCiRmZDk2MmQwMi0wNGY0LTQ3NDUtODc5YS02NTFm
      MzFmMmZhOTZKHAoMY3Jld19wcm9jZXNzEgwKCnNlcXVlbnRpYWxKEQoLY3Jld19tZW1vcnkSAhAA
      ShoKFGNyZXdfbnVtYmVyX29mX3Rhc2tzEgIYAUobChVjcmV3X251bWJlcl9vZl9hZ2VudHMSAhgB
      StkCCgtjcmV3X2FnZW50cxLJAgrGAlt7ImtleSI6ICJmMzM4NmY2ZDhkYTc1YWE0MTZhNmUzMTAw
      NTNmNzY5OCIsICJpZCI6ICIzNmZhMTEyZS02ZDVlLTRhMzgtODk0Yy01M2M5YjAzNTI5ODUiLCAi
      cm9sZSI6ICJ7dG9waWN9IFJlc2VhcmNoZXIiLCAidmVyYm9zZT8iOiBmYWxzZSwgIm1heF9pdGVy
      IjogMjAsICJtYXhfcnBtIjogbnVsbCwgImZ1bmN0aW9uX2NhbGxpbmdfbGxtIjogIiIsICJsbG0i
      OiAiZ3B0LTRvLW1pbmkiLCAiZGVsZWdhdGlvbl9lbmFibGVkPyI6IGZhbHNlLCAiYWxsb3dfY29k
      ZV9leGVjdXRpb24/IjogZmFsc2UsICJtYXhfcmV0cnlfbGltaXQiOiAyLCAidG9vbHNfbmFtZXMi
      OiBbXX1dSocCCgpjcmV3X3Rhc2tzEvgBCvUBW3sia2V5IjogIjA2YTczMjIwZjQxNDhhNGJiZDVi
      YWNiMGQwYjQ0ZmNlIiwgImlkIjogIjY3NTE3ZjY1LThhYzMtNDIyZi1hMmJhLTM4NDcyZDRkYmZl
      NSIsICJhc3luY19leGVjdXRpb24/IjogZmFsc2UsICJodW1hbl9pbnB1dD8iOiBmYWxzZSwgImFn
      ZW50X3JvbGUiOiAie3RvcGljfSBSZXNlYXJjaGVyIiwgImFnZW50X2tleSI6ICJmMzM4NmY2ZDhk
      YTc1YWE0MTZhNmUzMTAwNTNmNzY5OCIsICJ0b29sc19uYW1lcyI6IFtdfV16AhgBhQEAAQAAEo4C
      ChAzGUzQMDZOgJ090im3887lEgik7+/nVnqntioMVGFzayBDcmVhdGVkMAE5UO7rSV9+CRhBWEDs
      SV9+CRhKLgoIY3Jld19rZXkSIgogZWU2NzQ1ZDdjOGFlODJlMDBkZjk0ZGUwZjdmODcxMThKMQoH
      Y3Jld19pZBImCiRmZDk2MmQwMi0wNGY0LTQ3NDUtODc5YS02NTFmMzFmMmZhOTZKLgoIdGFza19r
      ZXkSIgogMDZhNzMyMjBmNDE0OGE0YmJkNWJhY2IwZDBiNDRmY2VKMQoHdGFza19pZBImCiQ2NzUx
      N2Y2NS04YWMzLTQyMmYtYTJiYS0zODQ3MmQ0ZGJmZTV6AhgBhQEAAQAAErMHChCB1TPvVbWX62DF
      102NfOHLEghdZ/LjI40W8SoMQ3JldyBDcmVhdGVkMAE5sJDUT19+CRhBEHXWT19+CRhKGgoOY3Jl
      d2FpX3ZlcnNpb24SCAoGMC44MC4wShoKDnB5dGhvbl92ZXJzaW9uEggKBjMuMTIuNUouCghjcmV3
      X2tleRIiCiBlZTY3NDVkN2M4YWU4MmUwMGRmOTRkZTBmN2Y4NzExOEoxCgdjcmV3X2lkEiYKJDUx
      YmI1NGQ0LWM4MTAtNDA0Yy04MTQzLWVmNTgwMTlhN2Q2OEocCgxjcmV3X3Byb2Nlc3MSDAoKc2Vx
      dWVudGlhbEoRCgtjcmV3X21lbW9yeRICEABKGgoUY3Jld19udW1iZXJfb2ZfdGFza3MSAhgBShsK
      FWNyZXdfbnVtYmVyX29mX2FnZW50cxICGAFK2QIKC2NyZXdfYWdlbnRzEskCCsYCW3sia2V5Ijog
      ImYzMzg2ZjZkOGRhNzVhYTQxNmE2ZTMxMDA1M2Y3Njk4IiwgImlkIjogIjRlNTExYTRhLTM1Yzkt
      NDA0NC1iMzBlLWM4OGZjZTJiMzc5YiIsICJyb2xlIjogInt0b3BpY30gUmVzZWFyY2hlciIsICJ2
      ZXJib3NlPyI6IGZhbHNlLCAibWF4X2l0ZXIiOiAyMCwgIm1heF9ycG0iOiBudWxsLCAiZnVuY3Rp
      b25fY2FsbGluZ19sbG0iOiAiIiwgImxsbSI6ICJncHQtNG8tbWluaSIsICJkZWxlZ2F0aW9uX2Vu
      YWJsZWQ/IjogZmFsc2UsICJhbGxvd19jb2RlX2V4ZWN1dGlvbj8iOiBmYWxzZSwgIm1heF9yZXRy
      eV9saW1pdCI6IDIsICJ0b29sc19uYW1lcyI6IFtdfV1KhwIKCmNyZXdfdGFza3MS+AEK9QFbeyJr
      ZXkiOiAiMDZhNzMyMjBmNDE0OGE0YmJkNWJhY2IwZDBiNDRmY2UiLCAiaWQiOiAiOTIzMWJmZjIt
      ODFmOS00MjU4LTgyMDktMjkzMjUyOWI1ZjlmIiwgImFzeW5jX2V4ZWN1dGlvbj8iOiBmYWxzZSwg
      Imh1bWFuX2lucHV0PyI6IGZhbHNlLCAiYWdlbnRfcm9sZSI6ICJ7dG9waWN9IFJlc2VhcmNoZXIi
      LCAiYWdlbnRfa2V5IjogImYzMzg2ZjZkOGRhNzVhYTQxNmE2ZTMxMDA1M2Y3Njk4IiwgInRvb2xz
      X25hbWVzIjogW119XXoCGAGFAQABAAASjgIKECTO19lNFYzBivlrqiZfSxASCIAH8VhjiPfQKgxU
      YXNrIENyZWF0ZWQwATnIr+NPX34JGEHQAeRPX34JGEouCghjcmV3X2tleRIiCiBlZTY3NDVkN2M4
      YWU4MmUwMGRmOTRkZTBmN2Y4NzExOEoxCgdjcmV3X2lkEiYKJDUxYmI1NGQ0LWM4MTAtNDA0Yy04
      MTQzLWVmNTgwMTlhN2Q2OEouCgh0YXNrX2tleRIiCiAwNmE3MzIyMGY0MTQ4YTRiYmQ1YmFjYjBk
      MGI0NGZjZUoxCgd0YXNrX2lkEiYKJDkyMzFiZmYyLTgxZjktNDI1OC04MjA5LTI5MzI1MjliNWY5
      ZnoCGAGFAQABAAASswcKEHGb7KITfOkYfQT7CRjWfUcSCIn6YlQJ1QVbKgxDcmV3IENyZWF0ZWQw
      ATmYH/BYX34JGEEgJ/JYX34JGEoaCg5jcmV3YWlfdmVyc2lvbhIICgYwLjgwLjBKGgoOcHl0aG9u
      X3ZlcnNpb24SCAoGMy4xMi41Si4KCGNyZXdfa2V5EiIKIGVlNjc0NWQ3YzhhZTgyZTAwZGY5NGRl
      MGY3Zjg3MTE4SjEKB2NyZXdfaWQSJgokZDc5Y2UyMWUtYmU1Ny00NTdiLWExMzEtNjZkMDFmZjQx
      ZTI2ShwKDGNyZXdfcHJvY2VzcxIMCgpzZXF1ZW50aWFsShEKC2NyZXdfbWVtb3J5EgIQAEoaChRj
      cmV3X251bWJlcl9vZl90YXNrcxICGAFKGwoVY3Jld19udW1iZXJfb2ZfYWdlbnRzEgIYAUrZAgoL
      Y3Jld19hZ2VudHMSyQIKxgJbeyJrZXkiOiAiZjMzODZmNmQ4ZGE3NWFhNDE2YTZlMzEwMDUzZjc2
      OTgiLCAiaWQiOiAiNzRhNDUxNzgtNmExOS00N2RjLThlZjktZDdhZmQ5YzUwMDQ0IiwgInJvbGUi
      OiAie3RvcGljfSBSZXNlYXJjaGVyIiwgInZlcmJvc2U/IjogZmFsc2UsICJtYXhfaXRlciI6IDIw
      LCAibWF4X3JwbSI6IG51bGwsICJmdW5jdGlvbl9jYWxsaW5nX2xsbSI6ICIiLCAibGxtIjogImdw
      dC00by1taW5pIiwgImRlbGVnYXRpb25fZW5hYmxlZD8iOiBmYWxzZSwgImFsbG93X2NvZGVfZXhl
      Y3V0aW9uPyI6IGZhbHNlLCAibWF4X3JldHJ5X2xpbWl0IjogMiwgInRvb2xzX25hbWVzIjogW119
      XUqHAgoKY3Jld190YXNrcxL4AQr1AVt7ImtleSI6ICIwNmE3MzIyMGY0MTQ4YTRiYmQ1YmFjYjBk
      MGI0NGZjZSIsICJpZCI6ICJjZWZiYjE1ZS01Y2M4LTQwZTctYTViMS03ODkzYjJlZGFkYmQiLCAi
      YXN5bmNfZXhlY3V0aW9uPyI6IGZhbHNlLCAiaHVtYW5faW5wdXQ/IjogZmFsc2UsICJhZ2VudF9y
      b2xlIjogInt0b3BpY30gUmVzZWFyY2hlciIsICJhZ2VudF9rZXkiOiAiZjMzODZmNmQ4ZGE3NWFh
      NDE2YTZlMzEwMDUzZjc2OTgiLCAidG9vbHNfbmFtZXMiOiBbXX1degIYAYUBAAEAAA==
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
      Content-Length:
      - '11392'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.27.0
    method: POST
    uri: https://telemetry.crewai.com:4319/v1/traces
  response:
    body:
      string: "\n\0"
    headers:
      Content-Length:
      - '2'
      Content-Type:
      - application/x-protobuf
      Date:
      - Tue, 19 Nov 2024 22:14:39 GMT
    status:
      code: 200
      message: OK
version: 1
