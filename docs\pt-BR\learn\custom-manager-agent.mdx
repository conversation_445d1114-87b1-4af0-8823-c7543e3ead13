---
title: Agente Gerente Personalizado
description: Saiba como definir um agente personalizado como gerente no CrewAI, proporcionando mais controle sobre o gerenciamento e a coordenação das tarefas.
icon: user-shield
---

# Definindo um Agente Específico como Gerente no CrewAI

O CrewAI permite que usuários definam um agente específico como gerente da crew, oferecendo mais controle sobre o gerenciamento e a coordenação das tarefas.
Esse recurso possibilita a personalização do papel gerencial para se adequar melhor às necessidades do seu projeto.

## Utilizando o Atributo `manager_agent`

### Agente Gerente Personalizado

O atributo `manager_agent` permite que você defina um agente personalizado para gerenciar a crew. Este agente supervisionará todo o processo, garantindo que as tarefas sejam concluídas de forma eficiente e com o mais alto padrão de qualidade.

### Exemplo

```python Code
import os
from crewai import Agent, Task, Crew, Process

# Define your agents
researcher = Agent(
    role="Researcher",
    goal="Conduct thorough research and analysis on AI and AI agents",
    backstory="You're an expert researcher, specialized in technology, software engineering, AI, and startups. You work as a freelancer and are currently researching for a new client.",
    allow_delegation=False,
)

writer = Agent(
    role="Senior Writer",
    goal="Create compelling content about AI and AI agents",
    backstory="You're a senior writer, specialized in technology, software engineering, AI, and startups. You work as a freelancer and are currently writing content for a new client.",
    allow_delegation=False,
)

# Define your task
task = Task(
    description="Generate a list of 5 interesting ideas for an article, then write one captivating paragraph for each idea that showcases the potential of a full article on this topic. Return the list of ideas with their paragraphs and your notes.",
    expected_output="5 bullet points, each with a paragraph and accompanying notes.",
)

# Define the manager agent
manager = Agent(
    role="Project Manager",
    goal="Efficiently manage the crew and ensure high-quality task completion",
    backstory="You're an experienced project manager, skilled in overseeing complex projects and guiding teams to success. Your role is to coordinate the efforts of the crew members, ensuring that each task is completed on time and to the highest standard.",
    allow_delegation=True,
)

# Instantiate your crew with a custom manager
crew = Crew(
    agents=[researcher, writer],
    tasks=[task],
    manager_agent=manager,
    process=Process.hierarchical,
)

# Start the crew's work
result = crew.kickoff()
```

## Benefícios de um Agente Gerente Personalizado

- **Controle aprimorado**: Adapte a abordagem de gerenciamento para atender às necessidades específicas do seu projeto.
- **Coordenação melhorada**: Assegure uma coordenação e gestão eficiente das tarefas por um agente experiente.
- **Gestão personalizável**: Defina funções e responsabilidades gerenciais que estejam alinhadas aos objetivos do seu projeto.

## Definindo um Manager LLM

Se você estiver utilizando o processo hierarchical e não quiser definir um agente gerente personalizado, é possível especificar o modelo de linguagem para o gerente:

```python Code
from crewai import LLM

manager_llm = LLM(model="gpt-4o")

crew = Crew(
    agents=[researcher, writer],
    tasks=[task],
    process=Process.hierarchical,
    manager_llm=manager_llm
)
```

<Note>
É necessário definir `manager_agent` ou `manager_llm` ao utilizar o processo hierarchical.
</Note>