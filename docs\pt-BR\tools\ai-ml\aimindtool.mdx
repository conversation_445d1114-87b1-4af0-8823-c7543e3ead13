---
title: AI Mind Tool
description: O `AIMindTool` foi desenvolvido para consultar fontes de dados em linguagem natural.
icon: brain
---

# `AIMindTool`

## Descrição

O `AIMindTool` é um wrapper em torno do [AI-Minds](https://mindsdb.com/minds) fornecido pela [MindsDB](https://mindsdb.com/). Ele permite que você consulte fontes de dados em linguagem natural, bastando configurar os parâmetros de conexão. Essa ferramenta é útil quando você precisa de respostas para perguntas utilizando dados armazenados em diversas fontes, incluindo PostgreSQL, MySQL, MariaDB, ClickHouse, Snowflake e Google BigQuery.

Minds são sistemas de IA que funcionam de forma similar aos grandes modelos de linguagem (LLMs), mas vão além ao responder qualquer pergunta sobre qualquer dado. Isso é realizado por meio de:
- Sele<PERSON> dos dados mais relevantes para a resposta utilizando busca paramétrica
- Compreensão do significado e fornecimento de respostas dentro do contexto correto através de busca semântica
- Entrega de respostas precisas ao analisar dados e utilizar modelos de machine learning (ML)

## Instalação

Para incorporar esta ferramenta ao seu projeto, é necessário instalar o Minds SDK:

```shell
uv add minds-sdk
```

## Passos para Começar

Para utilizar o `AIMindTool` de maneira eficaz, siga estes passos:

1. **Instalação de Pacotes**: Verifique se os pacotes `crewai[tools]` e `minds-sdk` estão instalados no seu ambiente Python.
2. **Obtenção da Chave de API**: Cadastre-se para uma conta Minds [aqui](https://mdb.ai/register) e obtenha uma chave de API.
3. **Configuração do Ambiente**: Armazene sua chave de API obtida em uma variável de ambiente chamada `MINDS_API_KEY` para facilitar seu uso pela ferramenta.

## Exemplo

O exemplo a seguir demonstra como inicializar a ferramenta e executar uma consulta:

```python Code
from crewai_tools import AIMindTool

# Initialize the AIMindTool
aimind_tool = AIMindTool(
    datasources=[
        {
            "description": "house sales data",
            "engine": "postgres",
            "connection_data": {
                "user": "demo_user",
                "password": "demo_password",
                "host": "samples.mindsdb.com",
                "port": 5432,
                "database": "demo",
                "schema": "demo_data"
            },
            "tables": ["house_sales"]
        }
    ]
)

# Run a natural language query
result = aimind_tool.run("How many 3 bedroom houses were sold in 2008?")
print(result)
```

## Parâmetros

O `AIMindTool` aceita os seguintes parâmetros:

- **api_key**: Opcional. Sua chave de API da Minds. Se não for fornecida, será lida da variável de ambiente `MINDS_API_KEY`.
- **datasources**: Uma lista de dicionários, cada um contendo as seguintes chaves:
  - **description**: Uma descrição dos dados contidos na fonte de dados.
  - **engine**: O engine (ou tipo) da fonte de dados.
  - **connection_data**: Um dicionário contendo os parâmetros de conexão da fonte de dados.
  - **tables**: Uma lista de tabelas que a fonte de dados irá utilizar. Isso é opcional e pode ser omitido caso todas as tabelas da fonte de dados devam ser utilizadas.

Uma lista das fontes de dados suportadas e seus parâmetros de conexão pode ser encontrada [aqui](https://docs.mdb.ai/docs/data_sources).

## Exemplo de Integração com Agente

Veja como integrar o `AIMindTool` com um agente CrewAI:

```python Code
from crewai import Agent
from crewai.project import agent
from crewai_tools import AIMindTool

# Initialize the tool
aimind_tool = AIMindTool(
    datasources=[
        {
            "description": "sales data",
            "engine": "postgres",
            "connection_data": {
                "user": "your_user",
                "password": "your_password",
                "host": "your_host",
                "port": 5432,
                "database": "your_db",
                "schema": "your_schema"
            },
            "tables": ["sales"]
        }
    ]
)

# Define an agent with the AIMindTool
@agent
def data_analyst(self) -> Agent:
    return Agent(
        config=self.agents_config["data_analyst"],
        allow_delegation=False,
        tools=[aimind_tool]
    )
```

## Conclusão

O `AIMindTool` oferece uma forma poderosa de consultar suas fontes de dados utilizando linguagem natural, facilitando a extração de insights sem a necessidade de escrever consultas SQL complexas. Ao conectar diversas fontes de dados e aproveitar a tecnologia AI-Minds, essa ferramenta permite que agentes acessem e analisem dados de maneira eficiente.