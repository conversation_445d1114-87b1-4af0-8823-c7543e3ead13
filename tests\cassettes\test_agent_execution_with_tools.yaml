interactions:
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: multiplier(*args:
      Any, **kwargs: Any) -> Any\nTool Description: multiplier(first_number: ''integer'',
      second_number: ''integer'') - Useful for when you need to multiply two numbers
      together. \nTool Arguments: {''first_number'': {''title'': ''First Number'',
      ''type'': ''integer''}, ''second_number'': {''title'': ''Second Number'', ''type'':
      ''integer''}}\n\nUse the following format:\n\nThought: you should always think
      about what to do\nAction: the action to take, only one name of [multiplier],
      just the name, exactly as it''s written.\nAction Input: the input to the action,
      just a simple python dictionary, enclosed in curly braces, using \" to wrap
      keys and values.\nObservation: the result of the action\n\nOnce all necessary
      information is gathered:\n\nThought: I now know the final answer\nFinal Answer:
      the final answer to the original input question\n"}, {"role": "user", "content":
      "\nCurrent Task: What is 3 times 4?\n\nThis is the expect criteria for your
      final answer: The result of the multiplication.\nyou MUST return the actual
      complete content as the final answer, not a summary.\n\nBegin! This is VERY
      important to you, use the tools available and give your best Final Answer, your
      job depends on it!\n\nThought:"}], "model": "gpt-4o"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1460'
      content-type:
      - application/json
      cookie:
      - __cf_bm=rb61BZH2ejzD5YPmLaEJqI7km71QqyNJGTVdNxBq6qk-1727213194-*******-pJ49onmgX9IugEMuYQMralzD7oj_6W.CHbSu4Su1z3NyjTGYg.rhgJZWng8feFYah._oSnoYlkTjpK1Wd2C9FA;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-1727213194587-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7LIYQkWZFFTpqgYl6wMZtTEQLpO\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727213196,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"I need to multiply 3 by 4 to get the
      final answer.\\n\\nAction: multiplier\\nAction Input: {\\\"first_number\\\":
      3, \\\"second_number\\\": 4}\",\n        \"refusal\": null\n      },\n      \"logprobs\":
      null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      309,\n    \"completion_tokens\": 36,\n    \"total_tokens\": 345,\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0\n    }\n  },\n  \"system_fingerprint\": \"fp_e375328146\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85da8abe6c1cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:26:36 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '525'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999648'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_4245fe9eede1d3ea650f7e97a63dcdbb
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: multiplier(*args:
      Any, **kwargs: Any) -> Any\nTool Description: multiplier(first_number: ''integer'',
      second_number: ''integer'') - Useful for when you need to multiply two numbers
      together. \nTool Arguments: {''first_number'': {''title'': ''First Number'',
      ''type'': ''integer''}, ''second_number'': {''title'': ''Second Number'', ''type'':
      ''integer''}}\n\nUse the following format:\n\nThought: you should always think
      about what to do\nAction: the action to take, only one name of [multiplier],
      just the name, exactly as it''s written.\nAction Input: the input to the action,
      just a simple python dictionary, enclosed in curly braces, using \" to wrap
      keys and values.\nObservation: the result of the action\n\nOnce all necessary
      information is gathered:\n\nThought: I now know the final answer\nFinal Answer:
      the final answer to the original input question\n"}, {"role": "user", "content":
      "\nCurrent Task: What is 3 times 4?\n\nThis is the expect criteria for your
      final answer: The result of the multiplication.\nyou MUST return the actual
      complete content as the final answer, not a summary.\n\nBegin! This is VERY
      important to you, use the tools available and give your best Final Answer, your
      job depends on it!\n\nThought:"}, {"role": "assistant", "content": "I need to
      multiply 3 by 4 to get the final answer.\n\nAction: multiplier\nAction Input:
      {\"first_number\": 3, \"second_number\": 4}\nObservation: 12"}], "model": "gpt-4o"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1646'
      content-type:
      - application/json
      cookie:
      - __cf_bm=rb61BZH2ejzD5YPmLaEJqI7km71QqyNJGTVdNxBq6qk-1727213194-*******-pJ49onmgX9IugEMuYQMralzD7oj_6W.CHbSu4Su1z3NyjTGYg.rhgJZWng8feFYah._oSnoYlkTjpK1Wd2C9FA;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-1727213194587-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7LIRK2yiJiNebQLyiMT7fAo73Ac\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727213196,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I now know the final answer.\\nFinal
      Answer: The result of the multiplication is 12.\",\n        \"refusal\": null\n
      \     },\n      \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n
      \ ],\n  \"usage\": {\n    \"prompt_tokens\": 353,\n    \"completion_tokens\":
      21,\n    \"total_tokens\": 374,\n    \"completion_tokens_details\": {\n      \"reasoning_tokens\":
      0\n    }\n  },\n  \"system_fingerprint\": \"fp_e375328146\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85da8fcce81cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:26:37 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '398'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999613'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_7a2c1a8d417b75e8dfafe586a1089504
    http_version: HTTP/1.1
    status_code: 200
version: 1
