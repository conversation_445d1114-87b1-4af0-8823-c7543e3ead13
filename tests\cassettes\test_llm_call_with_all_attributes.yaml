interactions:
- request:
    body: '{"messages": [{"role": "user", "content": "Say ''Hello, <PERSON>!'' and then
      say STOP"}], "model": "gpt-3.5-turbo", "frequency_penalty": 0.1, "max_tokens":
      50, "presence_penalty": 0.1, "stop": ["STOP"], "temperature": 0.7}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '217'
      content-type:
      - application/json
      cookie:
      - __cf_bm=rb61BZH2ejzD5YPmLaEJqI7km71QqyNJGTVdNxBq6qk-1727213194-*******-pJ49onmgX9IugEMuYQMralzD7oj_6W.CHbSu4Su1z3NyjTGYg.rhgJZWng8feFYah._oSnoYlkTjpK1Wd2C9FA;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-1727213194587-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7WQiKhiq2NMRarJHdddTbE4gjqJ\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727213886,\n  \"model\": \"gpt-3.5-turbo-0125\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Hello, World!\\n\",\n        \"refusal\":
      null\n      },\n      \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n
      \   }\n  ],\n  \"usage\": {\n    \"prompt_tokens\": 17,\n    \"completion_tokens\":
      4,\n    \"total_tokens\": 21,\n    \"completion_tokens_details\": {\n      \"reasoning_tokens\":
      0\n    }\n  },\n  \"system_fingerprint\": null\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85eb66bacf1cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:38:07 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '244'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '50000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '49999938'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_bd4c4ada379bf9bd5d37279b5ef7a6c7
    http_version: HTTP/1.1
    status_code: 200
version: 1
