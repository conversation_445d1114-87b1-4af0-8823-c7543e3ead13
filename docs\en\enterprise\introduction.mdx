---
title: "CrewAI Enterprise"
description: "Deploy, monitor, and scale your AI agent workflows"
icon: "globe"
---

## Introduction

CrewAI Enterprise provides a platform for deploying, monitoring, and scaling your crews and agents in a production environment.

<Frame>
  <img src="/images/enterprise/crewai-enterprise-dashboard.png" alt="CrewAI Enterprise Dashboard" />
</Frame>

CrewAI Enterprise extends the power of the open-source framework with features designed for production deployments, collaboration, and scalability. Deploy your crews to a managed infrastructure and monitor their execution in real-time.

## Key Features

<CardGroup cols={2}>
  <Card title="Crew Deployments" icon="rocket">
    Deploy your crews to a managed infrastructure with a few clicks
  </Card>
  <Card title="API Access" icon="code">
    Access your deployed crews via REST API for integration with existing systems
  </Card>
  <Card title="Observability" icon="chart-line">
    Monitor your crews with detailed execution traces and logs
  </Card>
  <Card title="Tool Repository" icon="toolbox">
    Publish and install tools to enhance your crews' capabilities
  </Card>
  <Card title="Webhook Streaming" icon="webhook">
    Stream real-time events and updates to your systems
  </Card>
  <Card title="Crew Studio" icon="paintbrush">
    Create and customize crews using a no-code/low-code interface
  </Card>
</CardGroup>

## Deployment Options

<CardGroup cols={3}>
  <Card title="GitHub Integration" icon="github">
    Connect directly to your GitHub repositories to deploy code
  </Card>
  <Card title="Crew Studio" icon="palette">
    Deploy crews created through the no-code Crew Studio interface
  </Card>
  <Card title="CLI Deployment" icon="terminal">
    Use the CrewAI CLI for more advanced deployment workflows
  </Card>
</CardGroup>

## Getting Started

<Steps>
  <Step title="Sign up for an account">
    Create your account at [app.crewai.com](https://app.crewai.com)
    <Card
      title="Sign Up"
      icon="user"
      href="https://app.crewai.com/signup"
    >
      Sign Up
    </Card>
  </Step>
  <Step title="Build your first crew">
    Use code or Crew Studio to build your crew
    <Card
      title="Build Crew"
      icon="paintbrush"
      href="/en/enterprise/guides/build-crew"
    >
      Build Crew
    </Card>
  </Step>
  <Step title="Deploy your crew">
    Deploy your crew to the Enterprise platform
    <Card
      title="Deploy Crew"
      icon="rocket"
      href="/en/enterprise/guides/deploy-crew"
    >
      Deploy Crew
    </Card>
  </Step>
  <Step title="Access your crew">
    Integrate with your crew via the generated API endpoints
    <Card
      title="API Access"
      icon="code"
      href="/en/enterprise/guides/kickoff-crew"
    >
      Use the Crew API
    </Card>
  </Step>
</Steps>

For detailed instructions, check out our [deployment guide](/en/enterprise/guides/deploy-crew) or click the button below to get started.
