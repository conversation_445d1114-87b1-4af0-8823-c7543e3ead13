interactions:
- request:
    body: '{"model": "meta-llama/llama-3.2-3b-instruct", "messages": [{"role": "system",
      "content": "Please convert the following text into valid JSON.\n\nOutput ONLY
      the valid JSON and nothing else.\n\nThe JSON must follow this format exactly:\n{\n  \"name\":
      str,\n  \"age\": int\n}"}, {"role": "user", "content": "Name: <PERSON> Llama, Age:
      30"}], "stream": false, "stop": []}'
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '365'
      content-type:
      - application/json
      host:
      - openrouter.ai
      http-referer:
      - https://litellm.ai
      user-agent:
      - litellm/1.68.0
      x-title:
      - liteLLM
    method: POST
    uri: https://openrouter.ai/api/v1/chat/completions
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAAwAAAP//dJDBTsMwEER/pZqzU1ICKfKNMwgQEieCKsfZJgbHG9mbghTl31FatZy4
        7GHm8N7OBNdAo6WQbbY327zMi81t9vwizVv5sB9f22/DdOg730FhiHxwDUVoPFHtxgSFnhvy0OhJ
        TOa96c3V8WbF+jor6syFJHG0AgWuP8kKNGxnZG25HzyJ4wAFG8kINdB/Egq2Y2cpQb9P8NwOkesE
        HUbvFfYuuNTtIpnEARpJeIBCMOIOtPundaGhH+hcoaeUTEvQEyJ7goZJySUxYRG1HITCYjpVCKan
        CnpV4d47S6vH5bsKalXBtMemyGcoRNqPyfiz4IntQnsK5vlDYTwzh8j9IDvhLwoJuiwX5nmOS7xs
        ICzGX5K7zTz/AgAA//8DAIXfumyyAQAA
    headers:
      Access-Control-Allow-Origin:
      - '*'
      CF-RAY:
      - 93ea9f59596559fa-DEL
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 12 May 2025 14:31:55 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      Vary:
      - Accept-Encoding
      x-clerk-auth-message:
      - Invalid JWT form. A JWT consists of three parts separated by dots. (reason=token-invalid,
        token-carrier=header)
      x-clerk-auth-reason:
      - token-invalid
      x-clerk-auth-status:
      - signed-out
    status:
      code: 200
      message: OK
version: 1
