interactions:
- request:
    body: !!binary |
      Cv4MCiQKIgoMc2VydmljZS5uYW1lEhIKEGNyZXdBSS10ZWxlbWV0cnkS1QwKEgoQY3Jld2FpLnRl
      bGVtZXRyeRK7CAoQoZHzwzzqT//MOge9CaeNnhIIPhrIWGCJs1IqDENyZXcgQ3JlYXRlZDABOXAF
      wn/PBjIYQeDOzn/PBjIYShsKDmNyZXdhaV92ZXJzaW9uEgkKBzAuMTA4LjBKGgoOcHl0aG9uX3Zl
      cnNpb24SCAoGMy4xMi44Si4KCGNyZXdfa2V5EiIKIDQ5NGYzNjU3MjM3YWQ4YTMwMzViMmYxYmVl
      Y2RjNjc3SjEKB2NyZXdfaWQSJgokZjc5OWM3ZGUtOTkzOC00N2ZlLWJjZDMtOWJkY2FiZjNkZjlh
      ShwKDGNyZXdfcHJvY2VzcxIMCgpzZXF1ZW50aWFsShEKC2NyZXdfbWVtb3J5EgIQAEoaChRjcmV3
      X251bWJlcl9vZl90YXNrcxICGAFKGwoVY3Jld19udW1iZXJfb2ZfYWdlbnRzEgIYAUo6ChBjcmV3
      X2ZpbmdlcnByaW50EiYKJDY4NzBhYjc3LWE5MmQtNGVmMy1hYjU2LWRlNTFlZGM3MDY2MUo7Chtj
      cmV3X2ZpbmdlcnByaW50X2NyZWF0ZWRfYXQSHAoaMjAyNS0wMy0zMVQxNjoyNDo1My43NDUzNzRK
      4AIKC2NyZXdfYWdlbnRzEtACCs0CW3sia2V5IjogImUxNDhlNTMyMDI5MzQ5OWY4Y2ViZWE4MjZl
      NzI1ODJiIiwgImlkIjogIjUyZTk4MWIyLTBmNWUtNDQwZC1iMjc3LWQwYzlhOWQzZjg1ZCIsICJy
      b2xlIjogInRlc3Qgcm9sZSIsICJ2ZXJib3NlPyI6IGZhbHNlLCAibWF4X2l0ZXIiOiAyLCAibWF4
      X3JwbSI6IG51bGwsICJmdW5jdGlvbl9jYWxsaW5nX2xsbSI6ICJncHQtNG8iLCAibGxtIjogImdw
      dC00byIsICJkZWxlZ2F0aW9uX2VuYWJsZWQ/IjogZmFsc2UsICJhbGxvd19jb2RlX2V4ZWN1dGlv
      bj8iOiBmYWxzZSwgIm1heF9yZXRyeV9saW1pdCI6IDIsICJ0b29sc19uYW1lcyI6IFsibGVhcm5f
      YWJvdXRfYWkiXX1dSo4CCgpjcmV3X3Rhc2tzEv8BCvwBW3sia2V5IjogImYyNTk3Yzc4NjdmYmUz
      MjRkYzY1ZGMwOGRmZGJmYzZjIiwgImlkIjogImMxYzFmNWZkLTM3Y2ItNDdjNC04NmY0LWUzYTJh
      MTQyOGY4OSIsICJhc3luY19leGVjdXRpb24/IjogZmFsc2UsICJodW1hbl9pbnB1dD8iOiBmYWxz
      ZSwgImFnZW50X3JvbGUiOiAidGVzdCByb2xlIiwgImFnZW50X2tleSI6ICJlMTQ4ZTUzMjAyOTM0
      OTlmOGNlYmVhODI2ZTcyNTgyYiIsICJ0b29sc19uYW1lcyI6IFsibGVhcm5fYWJvdXRfYWkiXX1d
      egIYAYUBAAEAABKABAoQOqy1VdqH3blm7jGGk44O8hIIXVB00yaxmDcqDFRhc2sgQ3JlYXRlZDAB
      OaAr5H/PBjIYQbDP5H/PBjIYSi4KCGNyZXdfa2V5EiIKIDQ5NGYzNjU3MjM3YWQ4YTMwMzViMmYx
      YmVlY2RjNjc3SjEKB2NyZXdfaWQSJgokZjc5OWM3ZGUtOTkzOC00N2ZlLWJjZDMtOWJkY2FiZjNk
      ZjlhSi4KCHRhc2tfa2V5EiIKIGYyNTk3Yzc4NjdmYmUzMjRkYzY1ZGMwOGRmZGJmYzZjSjEKB3Rh
      c2tfaWQSJgokYzFjMWY1ZmQtMzdjYi00N2M0LTg2ZjQtZTNhMmExNDI4Zjg5SjoKEGNyZXdfZmlu
      Z2VycHJpbnQSJgokNjg3MGFiNzctYTkyZC00ZWYzLWFiNTYtZGU1MWVkYzcwNjYxSjoKEHRhc2tf
      ZmluZ2VycHJpbnQSJgokOWM3MDIxY2UtNjU2OC00OGY2LWI4ZGMtNmNlY2M5ODcwMDhkSjsKG3Rh
      c2tfZmluZ2VycHJpbnRfY3JlYXRlZF9hdBIcChoyMDI1LTAzLTMxVDE2OjI0OjUzLjc0NTMzMUo7
      ChFhZ2VudF9maW5nZXJwcmludBImCiRhYjY1ZDE5Yi0yNmIwLTRiMGMtYTg0My01ZjU3MThkZjdi
      Y2Z6AhgBhQEAAQAA
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate, zstd
      Connection:
      - keep-alive
      Content-Length:
      - '1665'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.31.1
    method: POST
    uri: https://telemetry.crewai.com:4319/v1/traces
  response:
    body:
      string: "\n\0"
    headers:
      Content-Length:
      - '2'
      Content-Type:
      - application/x-protobuf
      Date:
      - Mon, 31 Mar 2025 23:24:57 GMT
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: learn_about_AI\nTool
      Arguments: {}\nTool Description: Useful for when you need to learn about AI
      to write an paragraph about it.\n\nIMPORTANT: Use the following format in your
      response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [learn_about_AI], just the name, exactly
      as it''s written.\nAction Input: the input to the action, just a simple JSON
      object, enclosed in curly braces, using \" to wrap keys and values.\nObservation:
      the result of the action\n```\n\nOnce all necessary information is gathered,
      return the following format:\n\n```\nThought: I now know the final answer\nFinal
      Answer: the final answer to the original input question\n```"}, {"role": "user",
      "content": "\nCurrent Task: Write and then review an small paragraph on AI until
      it''s AMAZING\n\nThis is the expected criteria for your final answer: The final
      paragraph.\nyou MUST return the actual complete content as the final answer,
      not a summary.\n\nBegin! This is VERY important to you, use the tools available
      and give your best Final Answer, your job depends on it!\n\nThought:"}], "model":
      "gpt-4o", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '1394'
      content-type:
      - application/json
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHImuG3FAgbOcTLxgpZthhEmVg7hf\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743463496,\n  \"model\": \"gpt-4o-2024-08-06\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"```\\nThought: To write an amazing paragraph
      on AI, I need to gather detailed information about it first.\\nAction: learn_about_AI\\nAction
      Input: {}\",\n        \"refusal\": null,\n        \"annotations\": []\n      },\n
      \     \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n
      \ \"usage\": {\n    \"prompt_tokens\": 276,\n    \"completion_tokens\": 32,\n
      \   \"total_tokens\": 308,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_6dd05565ef\"\n}\n"
    headers:
      CF-RAY:
      - 92939a567c9a67c4-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:24:58 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=wwI79dE5g__fUSqelLdMoCMOwubFvm.hJGS3Ewpb3uw-1743463498-*******-xvVXLCgoJPzbAg4AmSjLnM1YbzRk5qmuEPsRgzfid0J39zmNxiLOXAFeAz_4VHmYpT5tUBxfComgXCPkg9MCrMZr7aGLOuoPu4pj_dvah0o;
        path=/; expires=Mon, 31-Mar-25 23:54:58 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=wu1mwFBixM_Cn8wLLh.nRacWi8OMVBrEyBNuF_Htz6I-1743463498282-*******-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '1700'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '50000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '49999'
      x-ratelimit-remaining-tokens:
      - '149999688'
      x-ratelimit-reset-requests:
      - 1ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_944eb951995f00b65dfc691a0e529c0c
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "user", "content": "Only tools available:\n###\nTool
      Name: learn_about_AI\nTool Arguments: {}\nTool Description: Useful for when
      you need to learn about AI to write an paragraph about it.\n\nReturn a valid
      schema for the tool, the tool name must be exactly equal one of the options,
      use this text to inform the valid output schema:\n\n### TEXT \n```\nThought:
      To write an amazing paragraph on AI, I need to gather detailed information about
      it first.\nAction: learn_about_AI\nAction Input: {}"}], "model": "gpt-4o", "tool_choice":
      {"type": "function", "function": {"name": "InstructorToolCalling"}}, "tools":
      [{"type": "function", "function": {"name": "InstructorToolCalling", "description":
      "Correctly extracted `InstructorToolCalling` with all the required parameters
      with correct types", "parameters": {"properties": {"tool_name": {"description":
      "The name of the tool to be called.", "title": "Tool Name", "type": "string"},
      "arguments": {"anyOf": [{"type": "object"}, {"type": "null"}], "description":
      "A dictionary of arguments to be passed to the tool.", "title": "Arguments"}},
      "required": ["arguments", "tool_name"], "type": "object"}}}]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '1170'
      content-type:
      - application/json
      cookie:
      - __cf_bm=wwI79dE5g__fUSqelLdMoCMOwubFvm.hJGS3Ewpb3uw-1743463498-*******-xvVXLCgoJPzbAg4AmSjLnM1YbzRk5qmuEPsRgzfid0J39zmNxiLOXAFeAz_4VHmYpT5tUBxfComgXCPkg9MCrMZr7aGLOuoPu4pj_dvah0o;
        _cfuvid=wu1mwFBixM_Cn8wLLh.nRacWi8OMVBrEyBNuF_Htz6I-1743463498282-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHImw7lLFFPaIqe3NQubFNJDgghnU\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743463498,\n  \"model\": \"gpt-4o-2024-08-06\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": null,\n        \"tool_calls\": [\n          {\n
      \           \"id\": \"call_NIY8OTJapOBOwYmnfHo6SigC\",\n            \"type\":
      \"function\",\n            \"function\": {\n              \"name\": \"InstructorToolCalling\",\n
      \             \"arguments\": \"{\\\"tool_name\\\":\\\"learn_about_AI\\\",\\\"arguments\\\":null}\"\n
      \           }\n          }\n        ],\n        \"refusal\": null,\n        \"annotations\":
      []\n      },\n      \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n
      \   }\n  ],\n  \"usage\": {\n    \"prompt_tokens\": 199,\n    \"completion_tokens\":
      13,\n    \"total_tokens\": 212,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_898ac29719\"\n}\n"
    headers:
      CF-RAY:
      - 92939a70fda567c4-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:24:59 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '533'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '50000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '49999'
      x-ratelimit-remaining-tokens:
      - '149999882'
      x-ratelimit-reset-requests:
      - 1ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_6c3a0db9bc035c18e8f7fee439a28668
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: learn_about_AI\nTool
      Arguments: {}\nTool Description: Useful for when you need to learn about AI
      to write an paragraph about it.\n\nIMPORTANT: Use the following format in your
      response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [learn_about_AI], just the name, exactly
      as it''s written.\nAction Input: the input to the action, just a simple JSON
      object, enclosed in curly braces, using \" to wrap keys and values.\nObservation:
      the result of the action\n```\n\nOnce all necessary information is gathered,
      return the following format:\n\n```\nThought: I now know the final answer\nFinal
      Answer: the final answer to the original input question\n```"}, {"role": "user",
      "content": "\nCurrent Task: Write and then review an small paragraph on AI until
      it''s AMAZING\n\nThis is the expected criteria for your final answer: The final
      paragraph.\nyou MUST return the actual complete content as the final answer,
      not a summary.\n\nBegin! This is VERY important to you, use the tools available
      and give your best Final Answer, your job depends on it!\n\nThought:"}, {"role":
      "assistant", "content": "AI is a very broad field."}, {"role": "assistant",
      "content": "```\nThought: To write an amazing paragraph on AI, I need to gather
      detailed information about it first.\nAction: learn_about_AI\nAction Input:
      {}\nObservation: AI is a very broad field."}], "model": "gpt-4o", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '1681'
      content-type:
      - application/json
      cookie:
      - __cf_bm=wwI79dE5g__fUSqelLdMoCMOwubFvm.hJGS3Ewpb3uw-1743463498-*******-xvVXLCgoJPzbAg4AmSjLnM1YbzRk5qmuEPsRgzfid0J39zmNxiLOXAFeAz_4VHmYpT5tUBxfComgXCPkg9MCrMZr7aGLOuoPu4pj_dvah0o;
        _cfuvid=wu1mwFBixM_Cn8wLLh.nRacWi8OMVBrEyBNuF_Htz6I-1743463498282-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHImxQG4CPqO2OFhN7ZIwXtotTwwP\",\n  \"object\":
      \"chat.completion\",\n  \"created\": **********,\n  \"model\": \"gpt-4o-2024-08-06\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"```\\nThought: I now have the necessary
      information to craft a comprehensive and compelling paragraph about AI.\\nFinal
      Answer: Artificial Intelligence (AI) is a transformative force in today's world,
      dramatically reshaping industries from healthcare to automotive. By leveraging
      complex algorithms and large datasets, AI systems can perform tasks that typically
      require human intelligence, such as understanding natural language, recognizing
      patterns, and making decisions. The potential of AI extends beyond automation;
      it is a catalyst for innovation, enabling breakthroughs in personalized medicine,
      autonomous vehicles, and more. As AI continues to evolve, it promises to enhance
      efficiency, drive economic growth, and unlock new levels of problem-solving
      capabilities, cementing its role as a cornerstone of technological progress.\\n```\",\n
      \       \"refusal\": null,\n        \"annotations\": []\n      },\n      \"logprobs\":
      null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      332,\n    \"completion_tokens\": 142,\n    \"total_tokens\": 474,\n    \"prompt_tokens_details\":
      {\n      \"cached_tokens\": 0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_6dd05565ef\"\n}\n"
    headers:
      CF-RAY:
      - 92939a75b95d67c4-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:25:01 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '1869'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '50000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '49999'
      x-ratelimit-remaining-tokens:
      - '149999633'
      x-ratelimit-reset-requests:
      - 1ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_3f7dc3979b7fa55a9002ef66916059f5
    http_version: HTTP/1.1
    status_code: 200
version: 1
