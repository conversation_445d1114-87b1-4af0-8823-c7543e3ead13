interactions:
- request:
    body: '{"messages": [{"role": "system", "content": "You are Data Scientist. You
      work with data and AI\nYour personal goal is: Product amazing resports on AI\nYou
      ONLY have access to the following tools, and should NEVER make up tools that
      are not listed here:\n\nTool Name: Get Greetings() -> str\nTool Description:
      Get Greetings() - Get a random greeting back \nTool Arguments: {}\n\nUse the
      following format:\n\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [Get Greetings], just the name, exactly
      as it''s written.\nAction Input: the input to the action, just a simple python
      dictionary, enclosed in curly braces, using \" to wrap keys and values.\nObservation:
      the result of the action\n\nOnce all necessary information is gathered:\n\nThought:
      I now know the final answer\nFinal Answer: the final answer to the original
      input question\n"}, {"role": "user", "content": "\nCurrent Task: Write and then
      review an small paragraph on AI until it''s AMAZING. But first use the `Get
      Greetings` tool to get a greeting.\n\nThis is the expect criteria for your final
      answer: The final paragraph with the full review on AI and no greeting.\nyou
      MUST return the actual complete content as the final answer, not a summary.\n\nBegin!
      This is VERY important to you, use the tools available and give your best Final
      Answer, your job depends on it!\n\nThought:"}], "model": "gpt-4o"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1412'
      content-type:
      - application/json
      cookie:
      - __cf_bm=rb61BZH2ejzD5YPmLaEJqI7km71QqyNJGTVdNxBq6qk-1727213194-*******-pJ49onmgX9IugEMuYQMralzD7oj_6W.CHbSu4Su1z3NyjTGYg.rhgJZWng8feFYah._oSnoYlkTjpK1Wd2C9FA;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-1727213194587-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7WF40CXhA3hdWFV0w1Py8m9X6E5\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727213875,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I should start by using the
      Get Greetings tool to get a random greeting.\\n\\nAction: Get Greetings\\nAction
      Input: {}\",\n        \"refusal\": null\n      },\n      \"logprobs\": null,\n
      \     \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      284,\n    \"completion_tokens\": 26,\n    \"total_tokens\": 310,\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0\n    }\n  },\n  \"system_fingerprint\": \"fp_e375328146\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85eb23cd701cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:37:56 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '521'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999661'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_d0737c9b1f07bfb15487d0d04284f260
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: !!binary |
      CqgMCiQKIgoMc2VydmljZS5uYW1lEhIKEGNyZXdBSS10ZWxlbWV0cnkS/wsKEgoQY3Jld2FpLnRl
      bGVtZXRyeRKQAgoQEm+HPIG0bZY/UytlpKjeURIIzbGEu+Oo+tMqDlRhc2sgRXhlY3V0aW9uMAE5
      eJfTNW1L+BdBeD5dZt5L+BdKLgoIY3Jld19rZXkSIgogNDk0ZjM2NTcyMzdhZDhhMzAzNWIyZjFi
      ZWVjZGM2NzdKMQoHY3Jld19pZBImCiQ0Mzg4MzMyNS1hYjEwLTQxYjYtODI1Ny1lODVjYTk1ZWNj
      NzJKLgoIdGFza19rZXkSIgogZjI1OTdjNzg2N2ZiZTMyNGRjNjVkYzA4ZGZkYmZjNmNKMQoHdGFz
      a19pZBImCiRjYTFmMzYwNy0wZjg0LTRlYjUtYTQ4Yy1lYmFjMzAxMGM2ZWJ6AhgBhQEAAQAAEsQH
      ChC3lq21iz10UM0bo/m4yEnHEgjfYn8uhj036ioMQ3JldyBDcmVhdGVkMAE5uLY5bd5L+BdBINs+
      bd5L+BdKGgoOY3Jld2FpX3ZlcnNpb24SCAoGMC42MS4wShoKDnB5dGhvbl92ZXJzaW9uEggKBjMu
      MTEuN0ouCghjcmV3X2tleRIiCiA3ZTY2MDg5ODk4NTlhNjdlZWM4OGVlZjdmY2U4NTIyNUoxCgdj
      cmV3X2lkEiYKJDFhMjIxMWNmLTAyOWQtNDUwMy1hMDIxLTQzMjVmOTQ5OWQ3YkocCgxjcmV3X3By
      b2Nlc3MSDAoKc2VxdWVudGlhbEoRCgtjcmV3X21lbW9yeRICEABKGgoUY3Jld19udW1iZXJfb2Zf
      dGFza3MSAhgBShsKFWNyZXdfbnVtYmVyX29mX2FnZW50cxICGAFK3wIKC2NyZXdfYWdlbnRzEs8C
      CswCW3sia2V5IjogIjIyYWNkNjExZTQ0ZWY1ZmFjMDViNTMzZDc1ZTg4OTNiIiwgImlkIjogImIx
      ZGQ3MDRjLTczMDAtNGVhZS04ZmYzLTIyYzE0NTc5NzQ2ZSIsICJyb2xlIjogIkRhdGEgU2NpZW50
      aXN0IiwgInZlcmJvc2U/IjogZmFsc2UsICJtYXhfaXRlciI6IDE1LCAibWF4X3JwbSI6IG51bGws
      ICJmdW5jdGlvbl9jYWxsaW5nX2xsbSI6ICIiLCAibGxtIjogImdwdC00byIsICJkZWxlZ2F0aW9u
      X2VuYWJsZWQ/IjogZmFsc2UsICJhbGxvd19jb2RlX2V4ZWN1dGlvbj8iOiBmYWxzZSwgIm1heF9y
      ZXRyeV9saW1pdCI6IDIsICJ0b29sc19uYW1lcyI6IFsiZ2V0IGdyZWV0aW5ncyJdfV1KkgIKCmNy
      ZXdfdGFza3MSgwIKgAJbeyJrZXkiOiAiYTI3N2IzNGIyYzE0NmYwYzU2YzVlMTM1NmU4ZjhhNTci
      LCAiaWQiOiAiZDc5OTFlOGQtNzI0Zi00ZGQ1LWI1ZWUtNDAxNGVmMmEyMDgxIiwgImFzeW5jX2V4
      ZWN1dGlvbj8iOiBmYWxzZSwgImh1bWFuX2lucHV0PyI6IGZhbHNlLCAiYWdlbnRfcm9sZSI6ICJE
      YXRhIFNjaWVudGlzdCIsICJhZ2VudF9rZXkiOiAiMjJhY2Q2MTFlNDRlZjVmYWMwNWI1MzNkNzVl
      ODg5M2IiLCAidG9vbHNfbmFtZXMiOiBbImdldCBncmVldGluZ3MiXX1degIYAYUBAAEAABKOAgoQ
      Ipnb+wuVyO/6K2F+fu2ZARIIXxLM6dFgtCMqDFRhc2sgQ3JlYXRlZDABOXAaVW3eS/gXQdCHVW3e
      S/gXSi4KCGNyZXdfa2V5EiIKIDdlNjYwODk4OTg1OWE2N2VlYzg4ZWVmN2ZjZTg1MjI1SjEKB2Ny
      ZXdfaWQSJgokMWEyMjExY2YtMDI5ZC00NTAzLWEwMjEtNDMyNWY5NDk5ZDdiSi4KCHRhc2tfa2V5
      EiIKIGEyNzdiMzRiMmMxNDZmMGM1NmM1ZTEzNTZlOGY4YTU3SjEKB3Rhc2tfaWQSJgokZDc5OTFl
      OGQtNzI0Zi00ZGQ1LWI1ZWUtNDAxNGVmMmEyMDgxegIYAYUBAAEAAA==
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
      Content-Length:
      - '1579'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.27.0
    method: POST
    uri: https://telemetry.crewai.com:4319/v1/traces
  response:
    body:
      string: "\n\0"
    headers:
      Content-Length:
      - '2'
      Content-Type:
      - application/x-protobuf
      Date:
      - Tue, 24 Sep 2024 21:37:56 GMT
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "system", "content": "You are Data Scientist. You
      work with data and AI\nYour personal goal is: Product amazing resports on AI\nYou
      ONLY have access to the following tools, and should NEVER make up tools that
      are not listed here:\n\nTool Name: Get Greetings() -> str\nTool Description:
      Get Greetings() - Get a random greeting back \nTool Arguments: {}\n\nUse the
      following format:\n\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [Get Greetings], just the name, exactly
      as it''s written.\nAction Input: the input to the action, just a simple python
      dictionary, enclosed in curly braces, using \" to wrap keys and values.\nObservation:
      the result of the action\n\nOnce all necessary information is gathered:\n\nThought:
      I now know the final answer\nFinal Answer: the final answer to the original
      input question\n"}, {"role": "user", "content": "\nCurrent Task: Write and then
      review an small paragraph on AI until it''s AMAZING. But first use the `Get
      Greetings` tool to get a greeting.\n\nThis is the expect criteria for your final
      answer: The final paragraph with the full review on AI and no greeting.\nyou
      MUST return the actual complete content as the final answer, not a summary.\n\nBegin!
      This is VERY important to you, use the tools available and give your best Final
      Answer, your job depends on it!\n\nThought:"}, {"role": "assistant", "content":
      "Thought: I should start by using the Get Greetings tool to get a random greeting.\n\nAction:
      Get Greetings\nAction Input: {}\nObservation: Howdy!"}], "model": "gpt-4o"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1595'
      content-type:
      - application/json
      cookie:
      - __cf_bm=rb61BZH2ejzD5YPmLaEJqI7km71QqyNJGTVdNxBq6qk-1727213194-*******-pJ49onmgX9IugEMuYQMralzD7oj_6W.CHbSu4Su1z3NyjTGYg.rhgJZWng8feFYah._oSnoYlkTjpK1Wd2C9FA;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-1727213194587-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7WGcsBd3iW1colmI280lUeERSVO\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727213876,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I now have a greeting and can
      focus on writing and reviewing a small paragraph about AI until it is amazing.\\n\\nArtificial
      Intelligence (AI) represents one of the most transformative technologies of
      our time, promising to redefine industries and enhance human capabilities in
      unprecedented ways. From machine learning algorithms that predict consumer behavior
      to autonomous systems that drive cars and diagnose diseases, the potential applications
      of AI are vast and varied. By enabling machines to learn from data, make decisions,
      and even improve over time, AI is not only increasing efficiency but also opening
      new avenues for innovation. However, the ethical considerations and challenges
      associated with AI, such as ensuring privacy and preventing bias, underscore
      the importance of responsible development and deployment. In embracing AI\u2019s
      potential, we stand on the brink of a new era where intelligent systems enrich
      our lives while fostering sustainable progress.\\n\\nFinal Answer: Artificial
      Intelligence (AI) represents one of the most transformative technologies of
      our time, promising to redefine industries and enhance human capabilities in
      unprecedented ways. From machine learning algorithms that predict consumer behavior
      to autonomous systems that drive cars and diagnose diseases, the potential applications
      of AI are vast and varied. By enabling machines to learn from data, make decisions,
      and even improve over time, AI is not only increasing efficiency but also opening
      new avenues for innovation. However, the ethical considerations and challenges
      associated with AI, such as ensuring privacy and preventing bias, underscore
      the importance of responsible development and deployment. In embracing AI\u2019s
      potential, we stand on the brink of a new era where intelligent systems enrich
      our lives while fostering sustainable progress.\",\n        \"refusal\": null\n
      \     },\n      \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n
      \ ],\n  \"usage\": {\n    \"prompt_tokens\": 319,\n    \"completion_tokens\":
      309,\n    \"total_tokens\": 628,\n    \"completion_tokens_details\": {\n      \"reasoning_tokens\":
      0\n    }\n  },\n  \"system_fingerprint\": \"fp_e375328146\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85eb28db581cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:38:01 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '4477'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999625'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_bbfe512aa3a05220da4bd4537796bc59
    http_version: HTTP/1.1
    status_code: 200
version: 1
