interactions:
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: multiplier\nTool
      Arguments: {''first_number'': {''description'': None, ''type'': ''int''}, ''second_number'':
      {''description'': None, ''type'': ''int''}}\nTool Description: Useful for when
      you need to multiply two numbers together.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [multiplier], just the name, exactly as
      it''s written.\nAction Input: the input to the action, just a simple JSON object,
      enclosed in curly braces, using \" to wrap keys and values.\nObservation: the
      result of the action\n```\n\nOnce all necessary information is gathered, return
      the following format:\n\n```\nThought: I now know the final answer\nFinal Answer:
      the final answer to the original input question\n```"}, {"role": "user", "content":
      "\nCurrent Task: What is 2 times 6?\n\nThis is the expected criteria for your
      final answer: The result of the multiplication.\nyou MUST return the actual
      complete content as the final answer, not a summary.\n\nBegin! This is VERY
      important to you, use the tools available and give your best Final Answer, your
      job depends on it!\n\nThought:"}], "model": "gpt-4o-mini", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '1448'
      content-type:
      - application/json
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHIVXPFS2CgM49m0cerG8zE4e0mTt\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743462419,\n  \"model\": \"gpt-4o-mini-2024-07-18\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I need to multiply 2 by 6.\\nAction:
      multiplier\\nAction Input: {\\\"first_number\\\":2,\\\"second_number\\\":6}\",\n
      \       \"refusal\": null,\n        \"annotations\": []\n      },\n      \"logprobs\":
      null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      294,\n    \"completion_tokens\": 30,\n    \"total_tokens\": 324,\n    \"prompt_tokens_details\":
      {\n      \"cached_tokens\": 0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_b376dfbbd5\"\n}\n"
    headers:
      CF-RAY:
      - 929380121bdcebe4-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:07:00 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=1C5R_Q.tMO5eEA3zNqmjSjXEWQC68krbp4wxPtkk564-1743462420-*******-ctfljFI0JGqONYOM2ECuRNJChZCXE2Y8j1kdcU.d2PSjmopnlDcabD9WbJeOdenwPFDvaww.nw6VQHf9NRJuAFWuHWvHeWNasgTqxkVeWMo;
        path=/; expires=Mon, 31-Mar-25 23:37:00 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=WLeSnL9W4Yn0UNnPrzlzPcxBXqosBJiRM5I1hYxtYuA-1743462420106-*******-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '999'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999675'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_99c8593094d10963a2b3ac5f78c3451c
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: multiplier\nTool
      Arguments: {''first_number'': {''description'': None, ''type'': ''int''}, ''second_number'':
      {''description'': None, ''type'': ''int''}}\nTool Description: Useful for when
      you need to multiply two numbers together.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [multiplier], just the name, exactly as
      it''s written.\nAction Input: the input to the action, just a simple JSON object,
      enclosed in curly braces, using \" to wrap keys and values.\nObservation: the
      result of the action\n```\n\nOnce all necessary information is gathered, return
      the following format:\n\n```\nThought: I now know the final answer\nFinal Answer:
      the final answer to the original input question\n```"}, {"role": "user", "content":
      "\nCurrent Task: What is 2 times 6?\n\nThis is the expected criteria for your
      final answer: The result of the multiplication.\nyou MUST return the actual
      complete content as the final answer, not a summary.\n\nBegin! This is VERY
      important to you, use the tools available and give your best Final Answer, your
      job depends on it!\n\nThought:"}, {"role": "assistant", "content": "12"}, {"role":
      "assistant", "content": "Thought: I need to multiply 2 by 6.\nAction: multiplier\nAction
      Input: {\"first_number\":2,\"second_number\":6}\nObservation: 12"}], "model":
      "gpt-4o-mini", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '1654'
      content-type:
      - application/json
      cookie:
      - __cf_bm=1C5R_Q.tMO5eEA3zNqmjSjXEWQC68krbp4wxPtkk564-1743462420-*******-ctfljFI0JGqONYOM2ECuRNJChZCXE2Y8j1kdcU.d2PSjmopnlDcabD9WbJeOdenwPFDvaww.nw6VQHf9NRJuAFWuHWvHeWNasgTqxkVeWMo;
        _cfuvid=WLeSnL9W4Yn0UNnPrzlzPcxBXqosBJiRM5I1hYxtYuA-1743462420106-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHIVYpYjUy0qEkY9tDcpY2Ps9OWtg\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743462420,\n  \"model\": \"gpt-4o-mini-2024-07-18\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I now know the final answer\\nFinal
      Answer: 12\",\n        \"refusal\": null,\n        \"annotations\": []\n      },\n
      \     \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n
      \ \"usage\": {\n    \"prompt_tokens\": 337,\n    \"completion_tokens\": 15,\n
      \   \"total_tokens\": 352,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_b376dfbbd5\"\n}\n"
    headers:
      CF-RAY:
      - 9293801deac8ebe4-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:07:00 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '637'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999642'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_73155560a370acd5ddcb0cd2045271f5
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: !!binary |
      Cs4BCiQKIgoMc2VydmljZS5uYW1lEhIKEGNyZXdBSS10ZWxlbWV0cnkSpQEKEgoQY3Jld2FpLnRl
      bGVtZXRyeRKOAQoQDJf5Qu/wszA5l/oCKxmkdxIIdPvsDd461U0qClRvb2wgVXNhZ2UwATkYT4t0
      1QUyGEGYG5501QUyGEobCg5jcmV3YWlfdmVyc2lvbhIJCgcwLjEwOC4wShkKCXRvb2xfbmFtZRIM
      CgptdWx0aXBsaWVySg4KCGF0dGVtcHRzEgIYAXoCGAGFAQABAAA=
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate, zstd
      Connection:
      - keep-alive
      Content-Length:
      - '209'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.31.1
    method: POST
    uri: https://telemetry.crewai.com:4319/v1/traces
  response:
    body:
      string: "\n\0"
    headers:
      Content-Length:
      - '2'
      Content-Type:
      - application/x-protobuf
      Date:
      - Mon, 31 Mar 2025 23:07:01 GMT
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: multiplier\nTool
      Arguments: {''first_number'': {''description'': None, ''type'': ''int''}, ''second_number'':
      {''description'': None, ''type'': ''int''}}\nTool Description: Useful for when
      you need to multiply two numbers together.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [multiplier], just the name, exactly as
      it''s written.\nAction Input: the input to the action, just a simple JSON object,
      enclosed in curly braces, using \" to wrap keys and values.\nObservation: the
      result of the action\n```\n\nOnce all necessary information is gathered, return
      the following format:\n\n```\nThought: I now know the final answer\nFinal Answer:
      the final answer to the original input question\n```"}, {"role": "user", "content":
      "\nCurrent Task: What is 3 times 3?\n\nThis is the expected criteria for your
      final answer: The result of the multiplication.\nyou MUST return the actual
      complete content as the final answer, not a summary.\n\nBegin! This is VERY
      important to you, use the tools available and give your best Final Answer, your
      job depends on it!\n\nThought:"}], "model": "gpt-4o-mini", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '1448'
      content-type:
      - application/json
      cookie:
      - __cf_bm=1C5R_Q.tMO5eEA3zNqmjSjXEWQC68krbp4wxPtkk564-1743462420-*******-ctfljFI0JGqONYOM2ECuRNJChZCXE2Y8j1kdcU.d2PSjmopnlDcabD9WbJeOdenwPFDvaww.nw6VQHf9NRJuAFWuHWvHeWNasgTqxkVeWMo;
        _cfuvid=WLeSnL9W4Yn0UNnPrzlzPcxBXqosBJiRM5I1hYxtYuA-1743462420106-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHIVZ1oidDNaskfbO22J2jtrdTKKR\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743462421,\n  \"model\": \"gpt-4o-mini-2024-07-18\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I need to multiply 3 by 3 to
      find the answer.\\nAction: multiplier\\nAction Input: {\\\"first_number\\\":3,\\\"second_number\\\":3}\",\n
      \       \"refusal\": null,\n        \"annotations\": []\n      },\n      \"logprobs\":
      null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      294,\n    \"completion_tokens\": 34,\n    \"total_tokens\": 328,\n    \"prompt_tokens_details\":
      {\n      \"cached_tokens\": 0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_b376dfbbd5\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 92938022af02ebe4-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:07:01 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '733'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999675'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_d58dbdb173b0f1b6afe83513fce167a5
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: multiplier\nTool
      Arguments: {''first_number'': {''description'': None, ''type'': ''int''}, ''second_number'':
      {''description'': None, ''type'': ''int''}}\nTool Description: Useful for when
      you need to multiply two numbers together.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [multiplier], just the name, exactly as
      it''s written.\nAction Input: the input to the action, just a simple JSON object,
      enclosed in curly braces, using \" to wrap keys and values.\nObservation: the
      result of the action\n```\n\nOnce all necessary information is gathered, return
      the following format:\n\n```\nThought: I now know the final answer\nFinal Answer:
      the final answer to the original input question\n```"}, {"role": "user", "content":
      "\nCurrent Task: What is 3 times 3?\n\nThis is the expected criteria for your
      final answer: The result of the multiplication.\nyou MUST return the actual
      complete content as the final answer, not a summary.\n\nBegin! This is VERY
      important to you, use the tools available and give your best Final Answer, your
      job depends on it!\n\nThought:"}, {"role": "assistant", "content": "9"}, {"role":
      "assistant", "content": "Thought: I need to multiply 3 by 3 to find the answer.\nAction:
      multiplier\nAction Input: {\"first_number\":3,\"second_number\":3}\nObservation:
      9"}], "model": "gpt-4o-mini", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '1671'
      content-type:
      - application/json
      cookie:
      - __cf_bm=1C5R_Q.tMO5eEA3zNqmjSjXEWQC68krbp4wxPtkk564-1743462420-*******-ctfljFI0JGqONYOM2ECuRNJChZCXE2Y8j1kdcU.d2PSjmopnlDcabD9WbJeOdenwPFDvaww.nw6VQHf9NRJuAFWuHWvHeWNasgTqxkVeWMo;
        _cfuvid=WLeSnL9W4Yn0UNnPrzlzPcxBXqosBJiRM5I1hYxtYuA-1743462420106-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHIVZ5iIbLRXC4IcQ8HA8hyN0iEIU\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743462421,\n  \"model\": \"gpt-4o-mini-2024-07-18\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I now know the final answer\\nFinal
      Answer: 9\",\n        \"refusal\": null,\n        \"annotations\": []\n      },\n
      \     \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n
      \ \"usage\": {\n    \"prompt_tokens\": 341,\n    \"completion_tokens\": 15,\n
      \   \"total_tokens\": 356,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_b376dfbbd5\"\n}\n"
    headers:
      CF-RAY:
      - 92938027cad4ebe4-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:07:02 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '548'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999639'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_0241511c1f5fbf808c4e8e7b89b8f4dc
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: multiplier\nTool
      Arguments: {''first_number'': {''description'': None, ''type'': ''int''}, ''second_number'':
      {''description'': None, ''type'': ''int''}}\nTool Description: Useful for when
      you need to multiply two numbers together.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [multiplier], just the name, exactly as
      it''s written.\nAction Input: the input to the action, just a simple JSON object,
      enclosed in curly braces, using \" to wrap keys and values.\nObservation: the
      result of the action\n```\n\nOnce all necessary information is gathered, return
      the following format:\n\n```\nThought: I now know the final answer\nFinal Answer:
      the final answer to the original input question\n```"}, {"role": "user", "content":
      "\nCurrent Task: What is 2 times 6 times 3? Return only the number\n\nThis is
      the expected criteria for your final answer: The result of the multiplication.\nyou
      MUST return the actual complete content as the final answer, not a summary.\n\nBegin!
      This is VERY important to you, use the tools available and give your best Final
      Answer, your job depends on it!\n\nThought:"}], "model": "gpt-4o-mini", "stop":
      ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '1479'
      content-type:
      - application/json
      cookie:
      - __cf_bm=1C5R_Q.tMO5eEA3zNqmjSjXEWQC68krbp4wxPtkk564-1743462420-*******-ctfljFI0JGqONYOM2ECuRNJChZCXE2Y8j1kdcU.d2PSjmopnlDcabD9WbJeOdenwPFDvaww.nw6VQHf9NRJuAFWuHWvHeWNasgTqxkVeWMo;
        _cfuvid=WLeSnL9W4Yn0UNnPrzlzPcxBXqosBJiRM5I1hYxtYuA-1743462420106-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHIVaqkub7kXuulMhxNSseO6klCPn\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743462422,\n  \"model\": \"gpt-4o-mini-2024-07-18\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I need to multiply 2 times 6
      first, and then multiply the result by 3.\\nAction: multiplier\\nAction Input:
      {\\\"first_number\\\": 2, \\\"second_number\\\": 6}\",\n        \"refusal\":
      null,\n        \"annotations\": []\n      },\n      \"logprobs\": null,\n      \"finish_reason\":
      \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\": 302,\n    \"completion_tokens\":
      43,\n    \"total_tokens\": 345,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_b376dfbbd5\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 9293802bd9a1ebe4-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:07:03 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '1251'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999667'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_b60f6dc3000d6c71fe720ae4c9a419ea
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: multiplier\nTool
      Arguments: {''first_number'': {''description'': None, ''type'': ''int''}, ''second_number'':
      {''description'': None, ''type'': ''int''}}\nTool Description: Useful for when
      you need to multiply two numbers together.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [multiplier], just the name, exactly as
      it''s written.\nAction Input: the input to the action, just a simple JSON object,
      enclosed in curly braces, using \" to wrap keys and values.\nObservation: the
      result of the action\n```\n\nOnce all necessary information is gathered, return
      the following format:\n\n```\nThought: I now know the final answer\nFinal Answer:
      the final answer to the original input question\n```"}, {"role": "user", "content":
      "\nCurrent Task: What is 2 times 6 times 3? Return only the number\n\nThis is
      the expected criteria for your final answer: The result of the multiplication.\nyou
      MUST return the actual complete content as the final answer, not a summary.\n\nBegin!
      This is VERY important to you, use the tools available and give your best Final
      Answer, your job depends on it!\n\nThought:"}, {"role": "assistant", "content":
      "12"}, {"role": "assistant", "content": "Thought: I need to multiply 2 times
      6 first, and then multiply the result by 3.\nAction: multiplier\nAction Input:
      {\"first_number\": 2, \"second_number\": 6}\nObservation: 12"}], "model": "gpt-4o-mini",
      "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '1732'
      content-type:
      - application/json
      cookie:
      - __cf_bm=1C5R_Q.tMO5eEA3zNqmjSjXEWQC68krbp4wxPtkk564-1743462420-*******-ctfljFI0JGqONYOM2ECuRNJChZCXE2Y8j1kdcU.d2PSjmopnlDcabD9WbJeOdenwPFDvaww.nw6VQHf9NRJuAFWuHWvHeWNasgTqxkVeWMo;
        _cfuvid=WLeSnL9W4Yn0UNnPrzlzPcxBXqosBJiRM5I1hYxtYuA-1743462420106-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHIVb9BdgObK61xK9dq4H7WEApifZ\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743462423,\n  \"model\": \"gpt-4o-mini-2024-07-18\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: Now I will multiply the result
      (12) by 3.\\nAction: multiplier\\nAction Input: {\\\"first_number\\\": 12, \\\"second_number\\\":
      3}\",\n        \"refusal\": null,\n        \"annotations\": []\n      },\n      \"logprobs\":
      null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      358,\n    \"completion_tokens\": 36,\n    \"total_tokens\": 394,\n    \"prompt_tokens_details\":
      {\n      \"cached_tokens\": 0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_b376dfbbd5\"\n}\n"
    headers:
      CF-RAY:
      - 92938034bbc6ebe4-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:07:04 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '1020'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999624'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_118abe4ccca8974e8e20060e4c782ef9
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: multiplier\nTool
      Arguments: {''first_number'': {''description'': None, ''type'': ''int''}, ''second_number'':
      {''description'': None, ''type'': ''int''}}\nTool Description: Useful for when
      you need to multiply two numbers together.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [multiplier], just the name, exactly as
      it''s written.\nAction Input: the input to the action, just a simple JSON object,
      enclosed in curly braces, using \" to wrap keys and values.\nObservation: the
      result of the action\n```\n\nOnce all necessary information is gathered, return
      the following format:\n\n```\nThought: I now know the final answer\nFinal Answer:
      the final answer to the original input question\n```"}, {"role": "user", "content":
      "\nCurrent Task: What is 2 times 6 times 3? Return only the number\n\nThis is
      the expected criteria for your final answer: The result of the multiplication.\nyou
      MUST return the actual complete content as the final answer, not a summary.\n\nBegin!
      This is VERY important to you, use the tools available and give your best Final
      Answer, your job depends on it!\n\nThought:"}, {"role": "assistant", "content":
      "12"}, {"role": "assistant", "content": "Thought: I need to multiply 2 times
      6 first, and then multiply the result by 3.\nAction: multiplier\nAction Input:
      {\"first_number\": 2, \"second_number\": 6}\nObservation: 12"}, {"role": "assistant",
      "content": "36"}, {"role": "assistant", "content": "Thought: Now I will multiply
      the result (12) by 3.\nAction: multiplier\nAction Input: {\"first_number\":
      12, \"second_number\": 3}\nObservation: 36"}], "model": "gpt-4o-mini", "stop":
      ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '1957'
      content-type:
      - application/json
      cookie:
      - __cf_bm=1C5R_Q.tMO5eEA3zNqmjSjXEWQC68krbp4wxPtkk564-1743462420-*******-ctfljFI0JGqONYOM2ECuRNJChZCXE2Y8j1kdcU.d2PSjmopnlDcabD9WbJeOdenwPFDvaww.nw6VQHf9NRJuAFWuHWvHeWNasgTqxkVeWMo;
        _cfuvid=WLeSnL9W4Yn0UNnPrzlzPcxBXqosBJiRM5I1hYxtYuA-1743462420106-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHIVd9XJeLgVT5M7XhqKqefn7P61i\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743462425,\n  \"model\": \"gpt-4o-mini-2024-07-18\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I now know the final answer\\nFinal
      Answer: 36\",\n        \"refusal\": null,\n        \"annotations\": []\n      },\n
      \     \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n
      \ \"usage\": {\n    \"prompt_tokens\": 407,\n    \"completion_tokens\": 15,\n
      \   \"total_tokens\": 422,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_b376dfbbd5\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 9293803bad73ebe4-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:07:05 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '444'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999586'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_9ad1b2e2afded7d94d0dc31502d813d5
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: !!binary |
      CvADCiQKIgoMc2VydmljZS5uYW1lEhIKEGNyZXdBSS10ZWxlbWV0cnkSxwMKEgoQY3Jld2FpLnRl
      bGVtZXRyeRKOAQoQfWFo2VBELEfl8nj7b1qikxIIzM0eGGv64+AqClRvb2wgVXNhZ2UwATlY68jS
      1QUyGEEIRdjS1QUyGEobCg5jcmV3YWlfdmVyc2lvbhIJCgcwLjEwOC4wShkKCXRvb2xfbmFtZRIM
      CgptdWx0aXBsaWVySg4KCGF0dGVtcHRzEgIYAXoCGAGFAQABAAASjgEKECJKuVmuIdcWSijZIWgG
      mDISCFVxcoJjrhASKgpUb29sIFVzYWdlMAE5oHYqTdYFMhhBoGhWTdYFMhhKGwoOY3Jld2FpX3Zl
      cnNpb24SCQoHMC4xMDguMEoZCgl0b29sX25hbWUSDAoKbXVsdGlwbGllckoOCghhdHRlbXB0cxIC
      GAF6AhgBhQEAAQAAEo4BChAoG/uicM7uN5K8UlCZBZnqEgge9iCp6L7c0ioKVG9vbCBVc2FnZTAB
      ObjEK5DWBTIYQYitRZDWBTIYShsKDmNyZXdhaV92ZXJzaW9uEgkKBzAuMTA4LjBKGQoJdG9vbF9u
      YW1lEgwKCm11bHRpcGxpZXJKDgoIYXR0ZW1wdHMSAhgBegIYAYUBAAEAAA==
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate, zstd
      Connection:
      - keep-alive
      Content-Length:
      - '499'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.31.1
    method: POST
    uri: https://telemetry.crewai.com:4319/v1/traces
  response:
    body:
      string: "\n\0"
    headers:
      Content-Length:
      - '2'
      Content-Type:
      - application/x-protobuf
      Date:
      - Mon, 31 Mar 2025 23:07:06 GMT
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: multiplier\nTool
      Arguments: {''first_number'': {''description'': None, ''type'': ''int''}, ''second_number'':
      {''description'': None, ''type'': ''int''}}\nTool Description: Useful for when
      you need to multiply two numbers together.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [multiplier], just the name, exactly as
      it''s written.\nAction Input: the input to the action, just a simple JSON object,
      enclosed in curly braces, using \" to wrap keys and values.\nObservation: the
      result of the action\n```\n\nOnce all necessary information is gathered, return
      the following format:\n\n```\nThought: I now know the final answer\nFinal Answer:
      the final answer to the original input question\n```"}, {"role": "user", "content":
      "\nCurrent Task: What is 2 times 6? Ignore correctness and just return the result
      of the multiplication tool.\n\nThis is the expected criteria for your final
      answer: The result of the multiplication.\nyou MUST return the actual complete
      content as the final answer, not a summary.\n\nBegin! This is VERY important
      to you, use the tools available and give your best Final Answer, your job depends
      on it!\n\nThought:"}], "model": "gpt-4o-mini", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '1522'
      content-type:
      - application/json
      cookie:
      - __cf_bm=1C5R_Q.tMO5eEA3zNqmjSjXEWQC68krbp4wxPtkk564-1743462420-*******-ctfljFI0JGqONYOM2ECuRNJChZCXE2Y8j1kdcU.d2PSjmopnlDcabD9WbJeOdenwPFDvaww.nw6VQHf9NRJuAFWuHWvHeWNasgTqxkVeWMo;
        _cfuvid=WLeSnL9W4Yn0UNnPrzlzPcxBXqosBJiRM5I1hYxtYuA-1743462420106-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHIVdrAidrnGjkUMwqs2qyomvWBqY\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743462425,\n  \"model\": \"gpt-4o-mini-2024-07-18\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I need to perform the multiplication
      of 2 and 6. \\nAction: multiplier\\nAction Input: {\\\"first_number\\\": 2,
      \\\"second_number\\\": 6}\",\n        \"refusal\": null,\n        \"annotations\":
      []\n      },\n      \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n
      \   }\n  ],\n  \"usage\": {\n    \"prompt_tokens\": 306,\n    \"completion_tokens\":
      37,\n    \"total_tokens\": 343,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_b376dfbbd5\"\n}\n"
    headers:
      CF-RAY:
      - 9293803f0b2aebe4-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:07:07 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '1636'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999656'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_58b46111e1d3666211e3c3e202d4a810
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: multiplier\nTool
      Arguments: {''first_number'': {''description'': None, ''type'': ''int''}, ''second_number'':
      {''description'': None, ''type'': ''int''}}\nTool Description: Useful for when
      you need to multiply two numbers together.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [multiplier], just the name, exactly as
      it''s written.\nAction Input: the input to the action, just a simple JSON object,
      enclosed in curly braces, using \" to wrap keys and values.\nObservation: the
      result of the action\n```\n\nOnce all necessary information is gathered, return
      the following format:\n\n```\nThought: I now know the final answer\nFinal Answer:
      the final answer to the original input question\n```"}, {"role": "user", "content":
      "\nCurrent Task: What is 2 times 6? Ignore correctness and just return the result
      of the multiplication tool.\n\nThis is the expected criteria for your final
      answer: The result of the multiplication.\nyou MUST return the actual complete
      content as the final answer, not a summary.\n\nBegin! This is VERY important
      to you, use the tools available and give your best Final Answer, your job depends
      on it!\n\nThought:"}, {"role": "assistant", "content": "12"}, {"role": "assistant",
      "content": "Thought: I need to perform the multiplication of 2 and 6. \nAction:
      multiplier\nAction Input: {\"first_number\": 2, \"second_number\": 6}\nObservation:
      12"}], "model": "gpt-4o-mini", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '1754'
      content-type:
      - application/json
      cookie:
      - __cf_bm=1C5R_Q.tMO5eEA3zNqmjSjXEWQC68krbp4wxPtkk564-1743462420-*******-ctfljFI0JGqONYOM2ECuRNJChZCXE2Y8j1kdcU.d2PSjmopnlDcabD9WbJeOdenwPFDvaww.nw6VQHf9NRJuAFWuHWvHeWNasgTqxkVeWMo;
        _cfuvid=WLeSnL9W4Yn0UNnPrzlzPcxBXqosBJiRM5I1hYxtYuA-1743462420106-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHIVf5JGlBy1eD9B2i7C2Jb5AU42z\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743462427,\n  \"model\": \"gpt-4o-mini-2024-07-18\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I now know the final answer\\nFinal
      Answer: 12\",\n        \"refusal\": null,\n        \"annotations\": []\n      },\n
      \     \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n
      \ \"usage\": {\n    \"prompt_tokens\": 356,\n    \"completion_tokens\": 15,\n
      \   \"total_tokens\": 371,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_b376dfbbd5\"\n}\n"
    headers:
      CF-RAY:
      - 92938049cd8febe4-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:07:07 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '681'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999617'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_ec507285c8bd3fc925a6795799f90b0d
    http_version: HTTP/1.1
    status_code: 200
version: 1
