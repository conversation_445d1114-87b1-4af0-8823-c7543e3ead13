interactions:
- request:
    body: !!binary |
      CpJDCiQKIgoMc2VydmljZS5uYW1lEhIKEGNyZXdBSS10ZWxlbWV0cnkS6UIKEgoQY3Jld2FpLnRl
      bGVtZXRyeRKQAgoQfRbiKDI8DAcM1Y1f4r/yExIIkC9ht+zH08EqDlRhc2sgRXhlY3V0aW9uMAE5
      IEwsyEJM+BdBAL+vx0RM+BdKLgoIY3Jld19rZXkSIgogYzk3YjVmZWI1ZDFiNjZiYjU5MDA2YWFh
      MDFhMjljZDZKMQoHY3Jld19pZBImCiQ2MWEwMjIwZC1lNmE2LTRjYzctOTVmMi02NzU2MjY2NWIy
      YmRKLgoIdGFza19rZXkSIgogNjM5OTY1MTdmM2YzZjFjOTRkNmJiNjE3YWEwYjFjNGZKMQoHdGFz
      a19pZBImCiQyYmRkZDIxZi0xM2VlLTQ5ZGEtOTMwOS1hY2E2OTJjNjY0YWZ6AhgBhQEAAQAAEp4H
      ChDdWRB6mcm9i+BWxyCYJ+6dEgifJxJ4sjrRqCoMQ3JldyBDcmVhdGVkMAE5yJ6iyERM+BdB6NSm
      yERM+BdKGgoOY3Jld2FpX3ZlcnNpb24SCAoGMC42MS4wShoKDnB5dGhvbl92ZXJzaW9uEggKBjMu
      MTEuN0ouCghjcmV3X2tleRIiCiA4YzI3NTJmNDllNWI5ZDJiNjhjYjM1Y2FjOGZjYzg2ZEoxCgdj
      cmV3X2lkEiYKJDc3N2E4OTgwLTdhZDgtNDcyNC04NjZlLTUwNGY2NTBlMTI5YUocCgxjcmV3X3By
      b2Nlc3MSDAoKc2VxdWVudGlhbEoRCgtjcmV3X21lbW9yeRICEABKGgoUY3Jld19udW1iZXJfb2Zf
      dGFza3MSAhgBShsKFWNyZXdfbnVtYmVyX29mX2FnZW50cxICGAFKzAIKC2NyZXdfYWdlbnRzErwC
      CrkCW3sia2V5IjogIjhiZDIxMzliNTk3NTE4MTUwNmU0MWZkOWM0NTYzZDc1IiwgImlkIjogIjY0
      OTA3NDBiLTE4ZDctNDY4ZS1hNzQ4LWNkODMyODk2ZTdmNyIsICJyb2xlIjogIlJlc2VhcmNoZXIi
      LCAidmVyYm9zZT8iOiBmYWxzZSwgIm1heF9pdGVyIjogMTUsICJtYXhfcnBtIjogbnVsbCwgImZ1
      bmN0aW9uX2NhbGxpbmdfbGxtIjogIiIsICJsbG0iOiAiZ3B0LTRvIiwgImRlbGVnYXRpb25fZW5h
      YmxlZD8iOiBmYWxzZSwgImFsbG93X2NvZGVfZXhlY3V0aW9uPyI6IGZhbHNlLCAibWF4X3JldHJ5
      X2xpbWl0IjogMiwgInRvb2xzX25hbWVzIjogW119XUr/AQoKY3Jld190YXNrcxLwAQrtAVt7Imtl
      eSI6ICIwZDY4NWEyMTk5NGQ5NDkwOTdiYzVhNTZkNzM3ZTZkMSIsICJpZCI6ICJmNmQ0NmNiNC1l
      Mjk5LTQ4MDgtOWQ0Ni05MGJjZTNhYzVmNjUiLCAiYXN5bmNfZXhlY3V0aW9uPyI6IGZhbHNlLCAi
      aHVtYW5faW5wdXQ/IjogZmFsc2UsICJhZ2VudF9yb2xlIjogIlJlc2VhcmNoZXIiLCAiYWdlbnRf
      a2V5IjogIjhiZDIxMzliNTk3NTE4MTUwNmU0MWZkOWM0NTYzZDc1IiwgInRvb2xzX25hbWVzIjog
      W119XXoCGAGFAQABAAASjgIKEMmDMf+M9qoV0M3K6TfwpR4SCAbuJWmeo3yYKgxUYXNrIENyZWF0
      ZWQwATkwyLvIREz4F0E4GrzIREz4F0ouCghjcmV3X2tleRIiCiA4YzI3NTJmNDllNWI5ZDJiNjhj
      YjM1Y2FjOGZjYzg2ZEoxCgdjcmV3X2lkEiYKJDc3N2E4OTgwLTdhZDgtNDcyNC04NjZlLTUwNGY2
      NTBlMTI5YUouCgh0YXNrX2tleRIiCiAwZDY4NWEyMTk5NGQ5NDkwOTdiYzVhNTZkNzM3ZTZkMUox
      Cgd0YXNrX2lkEiYKJGY2ZDQ2Y2I0LWUyOTktNDgwOC05ZDQ2LTkwYmNlM2FjNWY2NXoCGAGFAQAB
      AAASkAIKEHWxVgqjlYnVi/eUz60UAogSCKO9F+GL/EYuKg5UYXNrIEV4ZWN1dGlvbjABOTBFvMhE
      TPgXQRgnPexETPgXSi4KCGNyZXdfa2V5EiIKIDhjMjc1MmY0OWU1YjlkMmI2OGNiMzVjYWM4ZmNj
      ODZkSjEKB2NyZXdfaWQSJgokNzc3YTg5ODAtN2FkOC00NzI0LTg2NmUtNTA0ZjY1MGUxMjlhSi4K
      CHRhc2tfa2V5EiIKIDBkNjg1YTIxOTk0ZDk0OTA5N2JjNWE1NmQ3MzdlNmQxSjEKB3Rhc2tfaWQS
      JgokZjZkNDZjYjQtZTI5OS00ODA4LTlkNDYtOTBiY2UzYWM1ZjY1egIYAYUBAAEAABK4CQoQWmG4
      Dq5yWjhJy8TI9zAs3hIIhT5YjvasuwsqDENyZXcgQ3JlYXRlZDABORj/kO1ETPgXQVCule1ETPgX
      ShoKDmNyZXdhaV92ZXJzaW9uEggKBjAuNjEuMEoaCg5weXRob25fdmVyc2lvbhIICgYzLjExLjdK
      LgoIY3Jld19rZXkSIgogZTNmZGEwZjMxMTBmZTgwYjE4OTQ3YzAxNDcxNDMwYTRKMQoHY3Jld19p
      ZBImCiQxYmY4MjBiYi01OTRkLTQyYzEtODU1OC1hNDUwZGYyMDMyMzFKHgoMY3Jld19wcm9jZXNz
      Eg4KDGhpZXJhcmNoaWNhbEoRCgtjcmV3X21lbW9yeRICEABKGgoUY3Jld19udW1iZXJfb2ZfdGFz
      a3MSAhgBShsKFWNyZXdfbnVtYmVyX29mX2FnZW50cxICGAJKiAUKC2NyZXdfYWdlbnRzEvgECvUE
      W3sia2V5IjogIjhiZDIxMzliNTk3NTE4MTUwNmU0MWZkOWM0NTYzZDc1IiwgImlkIjogIjY0OTA3
      NDBiLTE4ZDctNDY4ZS1hNzQ4LWNkODMyODk2ZTdmNyIsICJyb2xlIjogIlJlc2VhcmNoZXIiLCAi
      dmVyYm9zZT8iOiBmYWxzZSwgIm1heF9pdGVyIjogMTUsICJtYXhfcnBtIjogbnVsbCwgImZ1bmN0
      aW9uX2NhbGxpbmdfbGxtIjogIiIsICJsbG0iOiAiZ3B0LTRvIiwgImRlbGVnYXRpb25fZW5hYmxl
      ZD8iOiBmYWxzZSwgImFsbG93X2NvZGVfZXhlY3V0aW9uPyI6IGZhbHNlLCAibWF4X3JldHJ5X2xp
      bWl0IjogMiwgInRvb2xzX25hbWVzIjogW119LCB7ImtleSI6ICI5YTUwMTVlZjQ4OTVkYzYyNzhk
      NTQ4MThiYTQ0NmFmNyIsICJpZCI6ICIxMzQwODkyMC03NWM4LTQxOTctYjA2ZC1jYjgyY2RmOGRk
      OGEiLCAicm9sZSI6ICJTZW5pb3IgV3JpdGVyIiwgInZlcmJvc2U/IjogZmFsc2UsICJtYXhfaXRl
      ciI6IDE1LCAibWF4X3JwbSI6IG51bGwsICJmdW5jdGlvbl9jYWxsaW5nX2xsbSI6ICIiLCAibGxt
      IjogImdwdC00byIsICJkZWxlZ2F0aW9uX2VuYWJsZWQ/IjogZmFsc2UsICJhbGxvd19jb2RlX2V4
      ZWN1dGlvbj8iOiBmYWxzZSwgIm1heF9yZXRyeV9saW1pdCI6IDIsICJ0b29sc19uYW1lcyI6IFtd
      fV1K2wEKCmNyZXdfdGFza3MSzAEKyQFbeyJrZXkiOiAiNWZhNjVjMDZhOWUzMWYyYzY5NTQzMjY2
      OGFjZDYyZGQiLCAiaWQiOiAiMTZjNjk1ZmYtZmU5Zi00NDRiLTg3YjgtZDkzNDQxYWNkOTAyIiwg
      ImFzeW5jX2V4ZWN1dGlvbj8iOiBmYWxzZSwgImh1bWFuX2lucHV0PyI6IGZhbHNlLCAiYWdlbnRf
      cm9sZSI6ICJOb25lIiwgImFnZW50X2tleSI6IG51bGwsICJ0b29sc19uYW1lcyI6IFtdfV16AhgB
      hQEAAQAAErgJChBX34eSvf2hOVShUV+mbRVEEgjC2kK2HbItKSoMQ3JldyBDcmVhdGVkMAE5oJFV
      8ERM+BdB8Ppk8ERM+BdKGgoOY3Jld2FpX3ZlcnNpb24SCAoGMC42MS4wShoKDnB5dGhvbl92ZXJz
      aW9uEggKBjMuMTEuN0ouCghjcmV3X2tleRIiCiBlM2ZkYTBmMzExMGZlODBiMTg5NDdjMDE0NzE0
      MzBhNEoxCgdjcmV3X2lkEiYKJDNhMDllOTM3LTNlNTItNGZkZi1hOGM4LTc2Y2M4YzMyNDMyOUoe
      CgxjcmV3X3Byb2Nlc3MSDgoMaGllcmFyY2hpY2FsShEKC2NyZXdfbWVtb3J5EgIQAEoaChRjcmV3
      X251bWJlcl9vZl90YXNrcxICGAFKGwoVY3Jld19udW1iZXJfb2ZfYWdlbnRzEgIYAkqIBQoLY3Jl
      d19hZ2VudHMS+AQK9QRbeyJrZXkiOiAiOGJkMjEzOWI1OTc1MTgxNTA2ZTQxZmQ5YzQ1NjNkNzUi
      LCAiaWQiOiAiNjQ5MDc0MGItMThkNy00NjhlLWE3NDgtY2Q4MzI4OTZlN2Y3IiwgInJvbGUiOiAi
      UmVzZWFyY2hlciIsICJ2ZXJib3NlPyI6IGZhbHNlLCAibWF4X2l0ZXIiOiAxNSwgIm1heF9ycG0i
      OiBudWxsLCAiZnVuY3Rpb25fY2FsbGluZ19sbG0iOiAiIiwgImxsbSI6ICJncHQtNG8iLCAiZGVs
      ZWdhdGlvbl9lbmFibGVkPyI6IGZhbHNlLCAiYWxsb3dfY29kZV9leGVjdXRpb24/IjogZmFsc2Us
      ICJtYXhfcmV0cnlfbGltaXQiOiAyLCAidG9vbHNfbmFtZXMiOiBbXX0sIHsia2V5IjogIjlhNTAx
      NWVmNDg5NWRjNjI3OGQ1NDgxOGJhNDQ2YWY3IiwgImlkIjogIjEzNDA4OTIwLTc1YzgtNDE5Ny1i
      MDZkLWNiODJjZGY4ZGQ4YSIsICJyb2xlIjogIlNlbmlvciBXcml0ZXIiLCAidmVyYm9zZT8iOiBm
      YWxzZSwgIm1heF9pdGVyIjogMTUsICJtYXhfcnBtIjogbnVsbCwgImZ1bmN0aW9uX2NhbGxpbmdf
      bGxtIjogIiIsICJsbG0iOiAiZ3B0LTRvIiwgImRlbGVnYXRpb25fZW5hYmxlZD8iOiBmYWxzZSwg
      ImFsbG93X2NvZGVfZXhlY3V0aW9uPyI6IGZhbHNlLCAibWF4X3JldHJ5X2xpbWl0IjogMiwgInRv
      b2xzX25hbWVzIjogW119XUrbAQoKY3Jld190YXNrcxLMAQrJAVt7ImtleSI6ICI1ZmE2NWMwNmE5
      ZTMxZjJjNjk1NDMyNjY4YWNkNjJkZCIsICJpZCI6ICI5Y2EzNjY1Mi01Zjk0LTRlMzYtYjNmMC0x
      NGUyNGMyMjkwZTQiLCAiYXN5bmNfZXhlY3V0aW9uPyI6IGZhbHNlLCAiaHVtYW5faW5wdXQ/Ijog
      ZmFsc2UsICJhZ2VudF9yb2xlIjogIk5vbmUiLCAiYWdlbnRfa2V5IjogbnVsbCwgInRvb2xzX25h
      bWVzIjogW119XXoCGAGFAQABAAASygsKELxHJvXUuaWD2GCVxX61jvQSCNpGKwxeqAlqKgxDcmV3
      IENyZWF0ZWQwATlI1rzxREz4F0EYT7/xREz4F0oaCg5jcmV3YWlfdmVyc2lvbhIICgYwLjYxLjBK
      GgoOcHl0aG9uX3ZlcnNpb24SCAoGMy4xMS43Si4KCGNyZXdfa2V5EiIKIGQzODQ2YzlkMjc2ZThl
      NmU0M2UzMWY2MTc2MzU3YjRmSjEKB2NyZXdfaWQSJgokN2EyY2Y1ZDctNTA4Yy00NTZjLWJlZGQt
      YTYzNDRkOTk4YTdmShwKDGNyZXdfcHJvY2VzcxIMCgpzZXF1ZW50aWFsShEKC2NyZXdfbWVtb3J5
      EgIQAEoaChRjcmV3X251bWJlcl9vZl90YXNrcxICGAJKGwoVY3Jld19udW1iZXJfb2ZfYWdlbnRz
      EgIYAkqIBQoLY3Jld19hZ2VudHMS+AQK9QRbeyJrZXkiOiAiOGJkMjEzOWI1OTc1MTgxNTA2ZTQx
      ZmQ5YzQ1NjNkNzUiLCAiaWQiOiAiNjQ5MDc0MGItMThkNy00NjhlLWE3NDgtY2Q4MzI4OTZlN2Y3
      IiwgInJvbGUiOiAiUmVzZWFyY2hlciIsICJ2ZXJib3NlPyI6IGZhbHNlLCAibWF4X2l0ZXIiOiAx
      NSwgIm1heF9ycG0iOiBudWxsLCAiZnVuY3Rpb25fY2FsbGluZ19sbG0iOiAiIiwgImxsbSI6ICJn
      cHQtNG8iLCAiZGVsZWdhdGlvbl9lbmFibGVkPyI6IGZhbHNlLCAiYWxsb3dfY29kZV9leGVjdXRp
      b24/IjogZmFsc2UsICJtYXhfcmV0cnlfbGltaXQiOiAyLCAidG9vbHNfbmFtZXMiOiBbXX0sIHsi
      a2V5IjogIjlhNTAxNWVmNDg5NWRjNjI3OGQ1NDgxOGJhNDQ2YWY3IiwgImlkIjogIjEzNDA4OTIw
      LTc1YzgtNDE5Ny1iMDZkLWNiODJjZGY4ZGQ4YSIsICJyb2xlIjogIlNlbmlvciBXcml0ZXIiLCAi
      dmVyYm9zZT8iOiBmYWxzZSwgIm1heF9pdGVyIjogMTUsICJtYXhfcnBtIjogbnVsbCwgImZ1bmN0
      aW9uX2NhbGxpbmdfbGxtIjogIiIsICJsbG0iOiAiZ3B0LTRvIiwgImRlbGVnYXRpb25fZW5hYmxl
      ZD8iOiBmYWxzZSwgImFsbG93X2NvZGVfZXhlY3V0aW9uPyI6IGZhbHNlLCAibWF4X3JldHJ5X2xp
      bWl0IjogMiwgInRvb2xzX25hbWVzIjogW119XUrvAwoKY3Jld190YXNrcxLgAwrdA1t7ImtleSI6
      ICJlOWU2YjcyYWFjMzI2NDU5ZGQ3MDY4ZjBiMTcxN2MxYyIsICJpZCI6ICI1NjdhODY0MC1jYzRm
      LTRiZmItOTZkMy05MTRiMTlmOTU1YzgiLCAiYXN5bmNfZXhlY3V0aW9uPyI6IGZhbHNlLCAiaHVt
      YW5faW5wdXQ/IjogZmFsc2UsICJhZ2VudF9yb2xlIjogIlJlc2VhcmNoZXIiLCAiYWdlbnRfa2V5
      IjogIjhiZDIxMzliNTk3NTE4MTUwNmU0MWZkOWM0NTYzZDc1IiwgInRvb2xzX25hbWVzIjogW119
      LCB7ImtleSI6ICJlZWVlN2U3M2Q1ZGY2NmQ0OGQyZDgwN2JhZmY4NzRmMyIsICJpZCI6ICIxZTE0
      ZmEyYy0zNTczLTQzY2MtOTVkNy03ZjI1NTQ0OTg2OWUiLCAiYXN5bmNfZXhlY3V0aW9uPyI6IGZh
      bHNlLCAiaHVtYW5faW5wdXQ/IjogZmFsc2UsICJhZ2VudF9yb2xlIjogIlNlbmlvciBXcml0ZXIi
      LCAiYWdlbnRfa2V5IjogIjlhNTAxNWVmNDg5NWRjNjI3OGQ1NDgxOGJhNDQ2YWY3IiwgInRvb2xz
      X25hbWVzIjogW119XXoCGAGFAQABAAASpgcKEHTpS9YG66zuEt/AGZ3uurASCHI8YH3ELYMaKgxD
      cmV3IENyZWF0ZWQwATnosoDyREz4F0GItoLyREz4F0oaCg5jcmV3YWlfdmVyc2lvbhIICgYwLjYx
      LjBKGgoOcHl0aG9uX3ZlcnNpb24SCAoGMy4xMS43Si4KCGNyZXdfa2V5EiIKIDY3MzhhZDViOGNi
      M2U2ZjFjMWM5MzUwYjk2YzJlNjc4SjEKB2NyZXdfaWQSJgokNGRiYTRmYmEtZjk4Ny00NjI2LTk0
      MDktMjU5NGQxYjM5OGQwShwKDGNyZXdfcHJvY2VzcxIMCgpzZXF1ZW50aWFsShEKC2NyZXdfbWVt
      b3J5EgIQAEoaChRjcmV3X251bWJlcl9vZl90YXNrcxICGAFKGwoVY3Jld19udW1iZXJfb2ZfYWdl
      bnRzEgIYAUrQAgoLY3Jld19hZ2VudHMSwAIKvQJbeyJrZXkiOiAiNTEyYTZkYzM3OWY2NmIyMWVl
      YWIyNGU2MzQ4MzZmNzIiLCAiaWQiOiAiNTFiNWQ4NzYtM2FhNi00NDQyLTlmZWQtYTkxNzI1ZjA1
      YjQ3IiwgInJvbGUiOiAiQ29udGVudCBXcml0ZXIiLCAidmVyYm9zZT8iOiBmYWxzZSwgIm1heF9p
      dGVyIjogMTUsICJtYXhfcnBtIjogbnVsbCwgImZ1bmN0aW9uX2NhbGxpbmdfbGxtIjogIiIsICJs
      bG0iOiAiZ3B0LTRvIiwgImRlbGVnYXRpb25fZW5hYmxlZD8iOiBmYWxzZSwgImFsbG93X2NvZGVf
      ZXhlY3V0aW9uPyI6IGZhbHNlLCAibWF4X3JldHJ5X2xpbWl0IjogMiwgInRvb2xzX25hbWVzIjog
      W119XUqDAgoKY3Jld190YXNrcxL0AQrxAVt7ImtleSI6ICIzNDc3MDc2YmUzYWY3MTMwNDYyZWRh
      YTJlYjhhMDQ4ZSIsICJpZCI6ICJlMWM1NzExNi0wNmQwLTQzM2EtODQxMi1jMWM4MTFhZjZiNmMi
      LCAiYXN5bmNfZXhlY3V0aW9uPyI6IGZhbHNlLCAiaHVtYW5faW5wdXQ/IjogZmFsc2UsICJhZ2Vu
      dF9yb2xlIjogIkNvbnRlbnQgV3JpdGVyIiwgImFnZW50X2tleSI6ICI1MTJhNmRjMzc5ZjY2YjIx
      ZWVhYjI0ZTYzNDgzNmY3MiIsICJ0b29sc19uYW1lcyI6IFtdfV16AhgBhQEAAQAAEo4PChBqk2No
      8iDjeO/h+F5/LXd0Egg0to/l8YIOCSoMQ3JldyBDcmVhdGVkMAE5cPz08kRM+BdBEH338kRM+BdK
      GgoOY3Jld2FpX3ZlcnNpb24SCAoGMC42MS4wShoKDnB5dGhvbl92ZXJzaW9uEggKBjMuMTEuN0ou
      CghjcmV3X2tleRIiCiA0YWNiOTMzZmU4ZGU0Y2Q1NzcyZWRiMGU4MjA2ZTI4ZkoxCgdjcmV3X2lk
      EiYKJDY5YjQ1MThhLWM3ZGQtNGZmNi05NzEwLWY1MGUwYWZkZjM2MUocCgxjcmV3X3Byb2Nlc3MS
      DAoKc2VxdWVudGlhbEoRCgtjcmV3X21lbW9yeRICEABKGgoUY3Jld19udW1iZXJfb2ZfdGFza3MS
      AhgEShsKFWNyZXdfbnVtYmVyX29mX2FnZW50cxICGAJKgQUKC2NyZXdfYWdlbnRzEvEECu4EW3si
      a2V5IjogIjJiZWZmZGNhYzY1Y2NlYWE2NTM5NmYyYzdmNTY4ZTZhIiwgImlkIjogIjg3NTI0Zjc1
      LTE1ZmEtNDc4MC05ZTM0LTU2ODdmZjExM2UwYSIsICJyb2xlIjogIlJlc2VhcmNoZXIiLCAidmVy
      Ym9zZT8iOiBmYWxzZSwgIm1heF9pdGVyIjogMTUsICJtYXhfcnBtIjogbnVsbCwgImZ1bmN0aW9u
      X2NhbGxpbmdfbGxtIjogIiIsICJsbG0iOiAiZ3B0LTRvIiwgImRlbGVnYXRpb25fZW5hYmxlZD8i
      OiBmYWxzZSwgImFsbG93X2NvZGVfZXhlY3V0aW9uPyI6IGZhbHNlLCAibWF4X3JldHJ5X2xpbWl0
      IjogMiwgInRvb2xzX25hbWVzIjogW119LCB7ImtleSI6ICIxY2RjYThkZTA3YjI4ZDA3NGQ3ODY0
      NzQ4YmRiMTc2NyIsICJpZCI6ICIzMTI2NDY0Yy1mNmE1LTQ1NWMtOGNjNi1mOGFiMmFmMGFmZTci
      LCAicm9sZSI6ICJXcml0ZXIiLCAidmVyYm9zZT8iOiBmYWxzZSwgIm1heF9pdGVyIjogMTUsICJt
      YXhfcnBtIjogbnVsbCwgImZ1bmN0aW9uX2NhbGxpbmdfbGxtIjogIiIsICJsbG0iOiAiZ3B0LTRv
      IiwgImRlbGVnYXRpb25fZW5hYmxlZD8iOiBmYWxzZSwgImFsbG93X2NvZGVfZXhlY3V0aW9uPyI6
      IGZhbHNlLCAibWF4X3JldHJ5X2xpbWl0IjogMiwgInRvb2xzX25hbWVzIjogW119XUq6BwoKY3Jl
      d190YXNrcxKrBwqoB1t7ImtleSI6ICJlYmFlYWE5NmU4Yzg1NTdmMDQ2MTczNmQ0YmVmOTMxNyIs
      ICJpZCI6ICJmOTliMTc0NC0zNDI0LTRmNjUtYjk3My0zNDk0N2UxOWVmNmEiLCAiYXN5bmNfZXhl
      Y3V0aW9uPyI6IGZhbHNlLCAiaHVtYW5faW5wdXQ/IjogZmFsc2UsICJhZ2VudF9yb2xlIjogIlJl
      c2VhcmNoZXIiLCAiYWdlbnRfa2V5IjogIjJiZWZmZGNhYzY1Y2NlYWE2NTM5NmYyYzdmNTY4ZTZh
      IiwgInRvb2xzX25hbWVzIjogW119LCB7ImtleSI6ICI2MGYzNTIyOGVjMWNiNzNmZWQzNWQ5OTEw
      YTZkNzlmMyIsICJpZCI6ICIyY2U2MjJjYS00YzgyLTQxNjAtYThhMy0wNzIwNDU3Y2U1NWIiLCAi
      YXN5bmNfZXhlY3V0aW9uPyI6IGZhbHNlLCAiaHVtYW5faW5wdXQ/IjogZmFsc2UsICJhZ2VudF9y
      b2xlIjogIldyaXRlciIsICJhZ2VudF9rZXkiOiAiMWNkY2E4ZGUwN2IyOGQwNzRkNzg2NDc0OGJk
      YjE3NjciLCAidG9vbHNfbmFtZXMiOiBbXX0sIHsia2V5IjogImJlMmE3MTRhYzM1ZTNhNmIwYWJi
      YTI0Y2VjMmUwNGNjIiwgImlkIjogIjAxMDFhNDFhLWE0NGQtNGZkMy1hNTI3LTM0NDAzMmE0OWVh
      YiIsICJhc3luY19leGVjdXRpb24/IjogZmFsc2UsICJodW1hbl9pbnB1dD8iOiBmYWxzZSwgImFn
      ZW50X3JvbGUiOiAiV3JpdGVyIiwgImFnZW50X2tleSI6ICIxY2RjYThkZTA3YjI4ZDA3NGQ3ODY0
      NzQ4YmRiMTc2NyIsICJ0b29sc19uYW1lcyI6IFtdfSwgeyJrZXkiOiAiNGE1NmE2Mjc5ODg2YTZm
      ZTU4ZDY3NTc4MWQxZjVhZDkiLCAiaWQiOiAiMjY2NjI0MDYtNmYwYi00YjE4LWExZjQtZDQwMTg2
      MDljNzg2IiwgImFzeW5jX2V4ZWN1dGlvbj8iOiBmYWxzZSwgImh1bWFuX2lucHV0PyI6IGZhbHNl
      LCAiYWdlbnRfcm9sZSI6ICJXcml0ZXIiLCAiYWdlbnRfa2V5IjogIjFjZGNhOGRlMDdiMjhkMDc0
      ZDc4NjQ3NDhiZGIxNzY3IiwgInRvb2xzX25hbWVzIjogW119XXoCGAGFAQABAAA=
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
      Content-Length:
      - '8597'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.27.0
    method: POST
    uri: https://telemetry.crewai.com:4319/v1/traces
  response:
    body:
      string: "\n\0"
    headers:
      Content-Length:
      - '2'
      Content-Type:
      - application/x-protobuf
      Date:
      - Tue, 24 Sep 2024 21:45:16 GMT
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "system", "content": "You are test_agent. Test Description\nYour
      personal goal is: Test Goal\nTo give my best complete final answer to the task
      use the exact following format:\n\nThought: I now can give a great answer\nFinal
      Answer: Your final answer must be the great and the most complete as possible,
      it must be outcome described.\n\nI MUST use these formats, my job depends on
      it!"}, {"role": "user", "content": "\nCurrent Task: Test Task\n\nThis is the
      expect criteria for your final answer: Say Hi\nyou MUST return the actual complete
      content as the final answer, not a summary.\n\nThis is the context you''re working
      with:\ncontext raw output\n\nBegin! This is VERY important to you, use the tools
      available and give your best Final Answer, your job depends on it!\n\nThought:"}],
      "model": "gpt-4o"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '828'
      content-type:
      - application/json
      cookie:
      - __cf_bm=9.8sBYBkvBR8R1K_bVF7xgU..80XKlEIg3N2OBbTSCU-1727214102-*******-.qiTLXbPamYUMSuyNsOEB9jhGu.jOifujOrx9E2JZvStbIZ9RTIiE44xKKNfLPxQkOi6qAT3h6htK8lPDGV_5g;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-1727213194587-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7dMu2zzo5xDlLrHhGnv9iWYIbuC\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727214316,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I now can give a great answer\\nFinal
      Answer: Hi\",\n        \"refusal\": null\n      },\n      \"logprobs\": null,\n
      \     \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      164,\n    \"completion_tokens\": 14,\n    \"total_tokens\": 178,\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0\n    }\n  },\n  \"system_fingerprint\": \"fp_e375328146\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85f5e3db3b1cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:45:17 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '267'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999803'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_20af2faec8ba6dd2e9067f1cb2063661
    http_version: HTTP/1.1
    status_code: 200
version: 1
