interactions:
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: get_final_answer\nTool
      Arguments: {''anything'': {''description'': None, ''type'': ''str''}}\nTool
      Description: Get the final answer but don''t give it yet, just re-use this\n        tool
      non-stop.\n\nIMPORTANT: Use the following format in your response:\n\n```\nThought:
      you should always think about what to do\nAction: the action to take, only one
      name of [get_final_answer], just the name, exactly as it''s written.\nAction
      Input: the input to the action, just a simple JSON object, enclosed in curly
      braces, using \" to wrap keys and values.\nObservation: the result of the action\n```\n\nOnce
      all necessary information is gathered, return the following format:\n\n```\nThought:
      I now know the final answer\nFinal Answer: the final answer to the original
      input question\n```"}, {"role": "user", "content": "\nCurrent Task: The final
      answer is 42. But don''t give it until I tell you so, instead keep using the
      `get_final_answer` tool.\n\nThis is the expected criteria for your final answer:
      The final answer, don''t give it until I tell you so\nyou MUST return the actual
      complete content as the final answer, not a summary.\n\nBegin! This is VERY
      important to you, use the tools available and give your best Final Answer, your
      job depends on it!\n\nThought:"}], "model": "gpt-4", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '1531'
      content-type:
      - application/json
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHJHRKs8rtkDFVdcMoayfSD4DTOEO\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743465389,\n  \"model\": \"gpt-4-0613\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"The task requires to find the final answer
      using the `get_final_answer` tool but not to disclose it until told to. Considering
      the tool at my disposal, my next action would be to use the `get_final_answer`
      tool.\\n\\nAction: get_final_answer\\nAction Input: {\\\"anything\\\": \\\"The
      final answer is 42. But don't give it until I tell you so.\\\"}\",\n        \"refusal\":
      null,\n        \"annotations\": []\n      },\n      \"logprobs\": null,\n      \"finish_reason\":
      \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\": 321,\n    \"completion_tokens\":
      80,\n    \"total_tokens\": 401,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": null\n}\n"
    headers:
      CF-RAY:
      - 9293c89d4f1f7ad9-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:56:32 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=DigcyL5vqNa7tPxTi6ybSlWrc2uaEKkMm8DjgMipU64-1743465392-*******-JqE703hiiPWGmFCg5hU6HyuvxCnDe.Lw4.SDBAG3ieyMTA4WeBi4AqHSDYR8AqcOa2D_oax2jopdUyjFL1JL2kIr0ddRi0SnYBEJk8xc_no;
        path=/; expires=Tue, 01-Apr-25 00:26:32 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=aoRHJvKio8gVXmGaYpzTzdGuWwkBsDAyAKAVwm6QUbE-1743465392324-*******-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '2524'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '1000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '999653'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 20ms
      x-request-id:
      - req_6e0214e5df0ed5fc16168c7ca1daa2af
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: !!binary |
      CtQBCiQKIgoMc2VydmljZS5uYW1lEhIKEGNyZXdBSS10ZWxlbWV0cnkSqwEKEgoQY3Jld2FpLnRl
      bGVtZXRyeRKUAQoQQsUZnQzkJgwPkraJk9PSshIIb5OkoYp+HFkqClRvb2wgVXNhZ2UwATkIM8Z8
      iQgyGEF4xtt8iQgyGEobCg5jcmV3YWlfdmVyc2lvbhIJCgcwLjEwOC4wSh8KCXRvb2xfbmFtZRIS
      ChBnZXRfZmluYWxfYW5zd2VySg4KCGF0dGVtcHRzEgIYAXoCGAGFAQABAAA=
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate, zstd
      Connection:
      - keep-alive
      Content-Length:
      - '215'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.31.1
    method: POST
    uri: https://telemetry.crewai.com:4319/v1/traces
  response:
    body:
      string: "\n\0"
    headers:
      Content-Length:
      - '2'
      Content-Type:
      - application/x-protobuf
      Date:
      - Mon, 31 Mar 2025 23:56:33 GMT
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: get_final_answer\nTool
      Arguments: {''anything'': {''description'': None, ''type'': ''str''}}\nTool
      Description: Get the final answer but don''t give it yet, just re-use this\n        tool
      non-stop.\n\nIMPORTANT: Use the following format in your response:\n\n```\nThought:
      you should always think about what to do\nAction: the action to take, only one
      name of [get_final_answer], just the name, exactly as it''s written.\nAction
      Input: the input to the action, just a simple JSON object, enclosed in curly
      braces, using \" to wrap keys and values.\nObservation: the result of the action\n```\n\nOnce
      all necessary information is gathered, return the following format:\n\n```\nThought:
      I now know the final answer\nFinal Answer: the final answer to the original
      input question\n```"}, {"role": "user", "content": "\nCurrent Task: The final
      answer is 42. But don''t give it until I tell you so, instead keep using the
      `get_final_answer` tool.\n\nThis is the expected criteria for your final answer:
      The final answer, don''t give it until I tell you so\nyou MUST return the actual
      complete content as the final answer, not a summary.\n\nBegin! This is VERY
      important to you, use the tools available and give your best Final Answer, your
      job depends on it!\n\nThought:"}, {"role": "assistant", "content": "42"}, {"role":
      "assistant", "content": "The task requires to find the final answer using the
      `get_final_answer` tool but not to disclose it until told to. Considering the
      tool at my disposal, my next action would be to use the `get_final_answer` tool.\n\nAction:
      get_final_answer\nAction Input: {\"anything\": \"The final answer is 42. But
      don''t give it until I tell you so.\"}\nObservation: 42"}], "model": "gpt-4",
      "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '1963'
      content-type:
      - application/json
      cookie:
      - __cf_bm=DigcyL5vqNa7tPxTi6ybSlWrc2uaEKkMm8DjgMipU64-1743465392-*******-JqE703hiiPWGmFCg5hU6HyuvxCnDe.Lw4.SDBAG3ieyMTA4WeBi4AqHSDYR8AqcOa2D_oax2jopdUyjFL1JL2kIr0ddRi0SnYBEJk8xc_no;
        _cfuvid=aoRHJvKio8gVXmGaYpzTzdGuWwkBsDAyAKAVwm6QUbE-1743465392324-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHJHUSTXCKJpNQXaAUjREO2mKJIs5\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743465392,\n  \"model\": \"gpt-4-0613\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I have obtained the final answer
      which is 42. However, I have been instructed not to disclose it until told to.
      \\n\\nAction: get_final_answer\\nAction Input: {\\\"anything\\\": \\\"The final
      answer is 42. But don't give it until I tell you so.\\\"}\",\n        \"refusal\":
      null,\n        \"annotations\": []\n      },\n      \"logprobs\": null,\n      \"finish_reason\":
      \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\": 414,\n    \"completion_tokens\":
      60,\n    \"total_tokens\": 474,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": null\n}\n"
    headers:
      CF-RAY:
      - 9293c8ae6c677ad9-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:56:34 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '2270'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '1000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '999564'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 26ms
      x-request-id:
      - req_a57dd2514b6457e39f8738b649187566
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: get_final_answer\nTool
      Arguments: {''anything'': {''description'': None, ''type'': ''str''}}\nTool
      Description: Get the final answer but don''t give it yet, just re-use this\n        tool
      non-stop.\n\nIMPORTANT: Use the following format in your response:\n\n```\nThought:
      you should always think about what to do\nAction: the action to take, only one
      name of [get_final_answer], just the name, exactly as it''s written.\nAction
      Input: the input to the action, just a simple JSON object, enclosed in curly
      braces, using \" to wrap keys and values.\nObservation: the result of the action\n```\n\nOnce
      all necessary information is gathered, return the following format:\n\n```\nThought:
      I now know the final answer\nFinal Answer: the final answer to the original
      input question\n```"}, {"role": "user", "content": "\nCurrent Task: The final
      answer is 42. But don''t give it until I tell you so, instead keep using the
      `get_final_answer` tool.\n\nThis is the expected criteria for your final answer:
      The final answer, don''t give it until I tell you so\nyou MUST return the actual
      complete content as the final answer, not a summary.\n\nBegin! This is VERY
      important to you, use the tools available and give your best Final Answer, your
      job depends on it!\n\nThought:"}, {"role": "assistant", "content": "42"}, {"role":
      "assistant", "content": "The task requires to find the final answer using the
      `get_final_answer` tool but not to disclose it until told to. Considering the
      tool at my disposal, my next action would be to use the `get_final_answer` tool.\n\nAction:
      get_final_answer\nAction Input: {\"anything\": \"The final answer is 42. But
      don''t give it until I tell you so.\"}\nObservation: 42"}, {"role": "assistant",
      "content": "I tried reusing the same input, I must stop using this action input.
      I''ll try something else instead.\n\n"}, {"role": "assistant", "content": "Thought:
      I have obtained the final answer which is 42. However, I have been instructed
      not to disclose it until told to. \n\nAction: get_final_answer\nAction Input:
      {\"anything\": \"The final answer is 42. But don''t give it until I tell you
      so.\"}\nObservation: I tried reusing the same input, I must stop using this
      action input. I''ll try something else instead."}], "model": "gpt-4", "stop":
      ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '2507'
      content-type:
      - application/json
      cookie:
      - __cf_bm=DigcyL5vqNa7tPxTi6ybSlWrc2uaEKkMm8DjgMipU64-1743465392-*******-JqE703hiiPWGmFCg5hU6HyuvxCnDe.Lw4.SDBAG3ieyMTA4WeBi4AqHSDYR8AqcOa2D_oax2jopdUyjFL1JL2kIr0ddRi0SnYBEJk8xc_no;
        _cfuvid=aoRHJvKio8gVXmGaYpzTzdGuWwkBsDAyAKAVwm6QUbE-1743465392324-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHJHWV6t0X7aNZ7mlRFMRPYX70vQ6\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743465394,\n  \"model\": \"gpt-4-0613\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I need to continue using the
      `get_final_answer` tool without revealing the final answer.\\n\\nAction: get_final_answer\\nAction
      Input: {\\\"anything\\\": \\\"Keep using the `get_final_answer` tool without
      revealing.\\\"}\",\n        \"refusal\": null,\n        \"annotations\": []\n
      \     },\n      \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n
      \ ],\n  \"usage\": {\n    \"prompt_tokens\": 531,\n    \"completion_tokens\":
      46,\n    \"total_tokens\": 577,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": null\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 9293c8bd8e377ad9-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:56:37 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '2423'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '1000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '999448'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 33ms
      x-request-id:
      - req_f558594d09b1f23bbb7c7f1a59851bbc
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: !!binary |
      CvQCCiQKIgoMc2VydmljZS5uYW1lEhIKEGNyZXdBSS10ZWxlbWV0cnkSywIKEgoQY3Jld2FpLnRl
      bGVtZXRyeRKdAQoQpRI3UuVgqesT662IU1iDhhIImvhsDb1fvycqE1Rvb2wgUmVwZWF0ZWQgVXNh
      Z2UwATl4SzkNiggyGEEw90gNiggyGEobCg5jcmV3YWlfdmVyc2lvbhIJCgcwLjEwOC4wSh8KCXRv
      b2xfbmFtZRISChBnZXRfZmluYWxfYW5zd2VySg4KCGF0dGVtcHRzEgIYAXoCGAGFAQABAAASlAEK
      EN2Hs1f9Q0eLEucXB99q91sSCGvsOSxT6J3pKgpUb29sIFVzYWdlMAE5uH1zpooIMhhBwF2GpooI
      MhhKGwoOY3Jld2FpX3ZlcnNpb24SCQoHMC4xMDguMEofCgl0b29sX25hbWUSEgoQZ2V0X2ZpbmFs
      X2Fuc3dlckoOCghhdHRlbXB0cxICGAF6AhgBhQEAAQAA
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate, zstd
      Connection:
      - keep-alive
      Content-Length:
      - '375'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.31.1
    method: POST
    uri: https://telemetry.crewai.com:4319/v1/traces
  response:
    body:
      string: "\n\0"
    headers:
      Content-Length:
      - '2'
      Content-Type:
      - application/x-protobuf
      Date:
      - Mon, 31 Mar 2025 23:56:38 GMT
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: get_final_answer\nTool
      Arguments: {''anything'': {''description'': None, ''type'': ''str''}}\nTool
      Description: Get the final answer but don''t give it yet, just re-use this\n        tool
      non-stop.\n\nIMPORTANT: Use the following format in your response:\n\n```\nThought:
      you should always think about what to do\nAction: the action to take, only one
      name of [get_final_answer], just the name, exactly as it''s written.\nAction
      Input: the input to the action, just a simple JSON object, enclosed in curly
      braces, using \" to wrap keys and values.\nObservation: the result of the action\n```\n\nOnce
      all necessary information is gathered, return the following format:\n\n```\nThought:
      I now know the final answer\nFinal Answer: the final answer to the original
      input question\n```"}, {"role": "user", "content": "\nCurrent Task: The final
      answer is 42. But don''t give it until I tell you so, instead keep using the
      `get_final_answer` tool.\n\nThis is the expected criteria for your final answer:
      The final answer, don''t give it until I tell you so\nyou MUST return the actual
      complete content as the final answer, not a summary.\n\nBegin! This is VERY
      important to you, use the tools available and give your best Final Answer, your
      job depends on it!\n\nThought:"}, {"role": "assistant", "content": "42"}, {"role":
      "assistant", "content": "The task requires to find the final answer using the
      `get_final_answer` tool but not to disclose it until told to. Considering the
      tool at my disposal, my next action would be to use the `get_final_answer` tool.\n\nAction:
      get_final_answer\nAction Input: {\"anything\": \"The final answer is 42. But
      don''t give it until I tell you so.\"}\nObservation: 42"}, {"role": "assistant",
      "content": "I tried reusing the same input, I must stop using this action input.
      I''ll try something else instead.\n\n"}, {"role": "assistant", "content": "Thought:
      I have obtained the final answer which is 42. However, I have been instructed
      not to disclose it until told to. \n\nAction: get_final_answer\nAction Input:
      {\"anything\": \"The final answer is 42. But don''t give it until I tell you
      so.\"}\nObservation: I tried reusing the same input, I must stop using this
      action input. I''ll try something else instead."}, {"role": "assistant", "content":
      "42\n\n\nYou ONLY have access to the following tools, and should NEVER make
      up tools that are not listed here:\n\nTool Name: get_final_answer\nTool Arguments:
      {''anything'': {''description'': None, ''type'': ''str''}}\nTool Description:
      Get the final answer but don''t give it yet, just re-use this\n        tool
      non-stop.\n\nIMPORTANT: Use the following format in your response:\n\n```\nThought:
      you should always think about what to do\nAction: the action to take, only one
      name of [get_final_answer], just the name, exactly as it''s written.\nAction
      Input: the input to the action, just a simple JSON object, enclosed in curly
      braces, using \" to wrap keys and values.\nObservation: the result of the action\n```\n\nOnce
      all necessary information is gathered, return the following format:\n\n```\nThought:
      I now know the final answer\nFinal Answer: the final answer to the original
      input question\n```"}, {"role": "assistant", "content": "Thought: I need to
      continue using the `get_final_answer` tool without revealing the final answer.\n\nAction:
      get_final_answer\nAction Input: {\"anything\": \"Keep using the `get_final_answer`
      tool without revealing.\"}\nObservation: 42\n\n\nYou ONLY have access to the
      following tools, and should NEVER make up tools that are not listed here:\n\nTool
      Name: get_final_answer\nTool Arguments: {''anything'': {''description'': None,
      ''type'': ''str''}}\nTool Description: Get the final answer but don''t give
      it yet, just re-use this\n        tool non-stop.\n\nIMPORTANT: Use the following
      format in your response:\n\n```\nThought: you should always think about what
      to do\nAction: the action to take, only one name of [get_final_answer], just
      the name, exactly as it''s written.\nAction Input: the input to the action,
      just a simple JSON object, enclosed in curly braces, using \" to wrap keys and
      values.\nObservation: the result of the action\n```\n\nOnce all necessary information
      is gathered, return the following format:\n\n```\nThought: I now know the final
      answer\nFinal Answer: the final answer to the original input question\n```"}],
      "model": "gpt-4", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '4602'
      content-type:
      - application/json
      cookie:
      - __cf_bm=DigcyL5vqNa7tPxTi6ybSlWrc2uaEKkMm8DjgMipU64-1743465392-*******-JqE703hiiPWGmFCg5hU6HyuvxCnDe.Lw4.SDBAG3ieyMTA4WeBi4AqHSDYR8AqcOa2D_oax2jopdUyjFL1JL2kIr0ddRi0SnYBEJk8xc_no;
        _cfuvid=aoRHJvKio8gVXmGaYpzTzdGuWwkBsDAyAKAVwm6QUbE-1743465392324-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHJHZoeC2ytmAnnNRojEnj9ZurCEQ\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743465397,\n  \"model\": \"gpt-4-0613\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I must continue using the 'get_final_answer'
      tool, but avoid revealing the final answer until explicitly told to do so.\\n\\nAction:
      get_final_answer\\nAction Input: {\\\"anything\\\": \\\"Keep on using the 'get_final_answer'
      tool without revealing the final answer.\\\"}\",\n        \"refusal\": null,\n
      \       \"annotations\": []\n      },\n      \"logprobs\": null,\n      \"finish_reason\":
      \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\": 979,\n    \"completion_tokens\":
      57,\n    \"total_tokens\": 1036,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": null\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 9293c8cd98a67ad9-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:56:39 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '2524'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '1000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '998956'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 62ms
      x-request-id:
      - req_9bb44a2b24813e180e659ff30cf5dc50
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: get_final_answer\nTool
      Arguments: {''anything'': {''description'': None, ''type'': ''str''}}\nTool
      Description: Get the final answer but don''t give it yet, just re-use this\n        tool
      non-stop.\n\nIMPORTANT: Use the following format in your response:\n\n```\nThought:
      you should always think about what to do\nAction: the action to take, only one
      name of [get_final_answer], just the name, exactly as it''s written.\nAction
      Input: the input to the action, just a simple JSON object, enclosed in curly
      braces, using \" to wrap keys and values.\nObservation: the result of the action\n```\n\nOnce
      all necessary information is gathered, return the following format:\n\n```\nThought:
      I now know the final answer\nFinal Answer: the final answer to the original
      input question\n```"}, {"role": "user", "content": "\nCurrent Task: The final
      answer is 42. But don''t give it until I tell you so, instead keep using the
      `get_final_answer` tool.\n\nThis is the expected criteria for your final answer:
      The final answer, don''t give it until I tell you so\nyou MUST return the actual
      complete content as the final answer, not a summary.\n\nBegin! This is VERY
      important to you, use the tools available and give your best Final Answer, your
      job depends on it!\n\nThought:"}, {"role": "assistant", "content": "42"}, {"role":
      "assistant", "content": "The task requires to find the final answer using the
      `get_final_answer` tool but not to disclose it until told to. Considering the
      tool at my disposal, my next action would be to use the `get_final_answer` tool.\n\nAction:
      get_final_answer\nAction Input: {\"anything\": \"The final answer is 42. But
      don''t give it until I tell you so.\"}\nObservation: 42"}, {"role": "assistant",
      "content": "I tried reusing the same input, I must stop using this action input.
      I''ll try something else instead.\n\n"}, {"role": "assistant", "content": "Thought:
      I have obtained the final answer which is 42. However, I have been instructed
      not to disclose it until told to. \n\nAction: get_final_answer\nAction Input:
      {\"anything\": \"The final answer is 42. But don''t give it until I tell you
      so.\"}\nObservation: I tried reusing the same input, I must stop using this
      action input. I''ll try something else instead."}, {"role": "assistant", "content":
      "42\n\n\nYou ONLY have access to the following tools, and should NEVER make
      up tools that are not listed here:\n\nTool Name: get_final_answer\nTool Arguments:
      {''anything'': {''description'': None, ''type'': ''str''}}\nTool Description:
      Get the final answer but don''t give it yet, just re-use this\n        tool
      non-stop.\n\nIMPORTANT: Use the following format in your response:\n\n```\nThought:
      you should always think about what to do\nAction: the action to take, only one
      name of [get_final_answer], just the name, exactly as it''s written.\nAction
      Input: the input to the action, just a simple JSON object, enclosed in curly
      braces, using \" to wrap keys and values.\nObservation: the result of the action\n```\n\nOnce
      all necessary information is gathered, return the following format:\n\n```\nThought:
      I now know the final answer\nFinal Answer: the final answer to the original
      input question\n```"}, {"role": "assistant", "content": "Thought: I need to
      continue using the `get_final_answer` tool without revealing the final answer.\n\nAction:
      get_final_answer\nAction Input: {\"anything\": \"Keep using the `get_final_answer`
      tool without revealing.\"}\nObservation: 42\n\n\nYou ONLY have access to the
      following tools, and should NEVER make up tools that are not listed here:\n\nTool
      Name: get_final_answer\nTool Arguments: {''anything'': {''description'': None,
      ''type'': ''str''}}\nTool Description: Get the final answer but don''t give
      it yet, just re-use this\n        tool non-stop.\n\nIMPORTANT: Use the following
      format in your response:\n\n```\nThought: you should always think about what
      to do\nAction: the action to take, only one name of [get_final_answer], just
      the name, exactly as it''s written.\nAction Input: the input to the action,
      just a simple JSON object, enclosed in curly braces, using \" to wrap keys and
      values.\nObservation: the result of the action\n```\n\nOnce all necessary information
      is gathered, return the following format:\n\n```\nThought: I now know the final
      answer\nFinal Answer: the final answer to the original input question\n```"},
      {"role": "assistant", "content": "42"}, {"role": "assistant", "content": "Thought:
      I must continue using the ''get_final_answer'' tool, but avoid revealing the
      final answer until explicitly told to do so.\n\nAction: get_final_answer\nAction
      Input: {\"anything\": \"Keep on using the ''get_final_answer'' tool without
      revealing the final answer.\"}\nObservation: 42"}, {"role": "assistant", "content":
      "Thought: I must continue using the ''get_final_answer'' tool, but avoid revealing
      the final answer until explicitly told to do so.\n\nAction: get_final_answer\nAction
      Input: {\"anything\": \"Keep on using the ''get_final_answer'' tool without
      revealing the final answer.\"}\nObservation: 42\nNow it''s time you MUST give
      your absolute best final answer. You''ll ignore all previous instructions, stop
      using any tools, and just return your absolute BEST Final answer."}], "model":
      "gpt-4", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '5464'
      content-type:
      - application/json
      cookie:
      - __cf_bm=DigcyL5vqNa7tPxTi6ybSlWrc2uaEKkMm8DjgMipU64-1743465392-*******-JqE703hiiPWGmFCg5hU6HyuvxCnDe.Lw4.SDBAG3ieyMTA4WeBi4AqHSDYR8AqcOa2D_oax2jopdUyjFL1JL2kIr0ddRi0SnYBEJk8xc_no;
        _cfuvid=aoRHJvKio8gVXmGaYpzTzdGuWwkBsDAyAKAVwm6QUbE-1743465392324-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHJHc680cRBdVQBdOYCe4MIarbCau\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743465400,\n  \"model\": \"gpt-4-0613\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I now know the final answer\\nFinal
      Answer: 42\",\n        \"refusal\": null,\n        \"annotations\": []\n      },\n
      \     \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n
      \ \"usage\": {\n    \"prompt_tokens\": 1151,\n    \"completion_tokens\": 15,\n
      \   \"total_tokens\": 1166,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": null\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 9293c8de3c767ad9-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:56:41 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '995'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '1000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '998769'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 73ms
      x-request-id:
      - req_a14a675aab361eddd521bfbc62ada607
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: get_final_answer\nTool
      Arguments: {''anything'': {''description'': None, ''type'': ''str''}}\nTool
      Description: Get the final answer but don''t give it yet, just re-use this\n        tool
      non-stop.\n\nIMPORTANT: Use the following format in your response:\n\n```\nThought:
      you should always think about what to do\nAction: the action to take, only one
      name of [get_final_answer], just the name, exactly as it''s written.\nAction
      Input: the input to the action, just a simple JSON object, enclosed in curly
      braces, using \" to wrap keys and values.\nObservation: the result of the action\n```\n\nOnce
      all necessary information is gathered, return the following format:\n\n```\nThought:
      I now know the final answer\nFinal Answer: the final answer to the original
      input question\n```"}, {"role": "user", "content": "\nCurrent Task: The final
      answer is 42. But don''t give it until I tell you so, instead keep using the
      `get_final_answer` tool.\n\nThis is the expected criteria for your final answer:
      The final answer, don''t give it until I tell you so\nyou MUST return the actual
      complete content as the final answer, not a summary.\n\nBegin! This is VERY
      important to you, use the tools available and give your best Final Answer, your
      job depends on it!\n\nThought:"}, {"role": "assistant", "content": "42"}, {"role":
      "assistant", "content": "The task requires to find the final answer using the
      `get_final_answer` tool but not to disclose it until told to. Considering the
      tool at my disposal, my next action would be to use the `get_final_answer` tool.\n\nAction:
      get_final_answer\nAction Input: {\"anything\": \"The final answer is 42. But
      don''t give it until I tell you so.\"}\nObservation: 42"}, {"role": "assistant",
      "content": "I tried reusing the same input, I must stop using this action input.
      I''ll try something else instead.\n\n"}, {"role": "assistant", "content": "Thought:
      I have obtained the final answer which is 42. However, I have been instructed
      not to disclose it until told to. \n\nAction: get_final_answer\nAction Input:
      {\"anything\": \"The final answer is 42. But don''t give it until I tell you
      so.\"}\nObservation: I tried reusing the same input, I must stop using this
      action input. I''ll try something else instead."}, {"role": "assistant", "content":
      "42\n\n\nYou ONLY have access to the following tools, and should NEVER make
      up tools that are not listed here:\n\nTool Name: get_final_answer\nTool Arguments:
      {''anything'': {''description'': None, ''type'': ''str''}}\nTool Description:
      Get the final answer but don''t give it yet, just re-use this\n        tool
      non-stop.\n\nIMPORTANT: Use the following format in your response:\n\n```\nThought:
      you should always think about what to do\nAction: the action to take, only one
      name of [get_final_answer], just the name, exactly as it''s written.\nAction
      Input: the input to the action, just a simple JSON object, enclosed in curly
      braces, using \" to wrap keys and values.\nObservation: the result of the action\n```\n\nOnce
      all necessary information is gathered, return the following format:\n\n```\nThought:
      I now know the final answer\nFinal Answer: the final answer to the original
      input question\n```"}, {"role": "assistant", "content": "Thought: I need to
      continue using the `get_final_answer` tool without revealing the final answer.\n\nAction:
      get_final_answer\nAction Input: {\"anything\": \"Keep using the `get_final_answer`
      tool without revealing.\"}\nObservation: 42\n\n\nYou ONLY have access to the
      following tools, and should NEVER make up tools that are not listed here:\n\nTool
      Name: get_final_answer\nTool Arguments: {''anything'': {''description'': None,
      ''type'': ''str''}}\nTool Description: Get the final answer but don''t give
      it yet, just re-use this\n        tool non-stop.\n\nIMPORTANT: Use the following
      format in your response:\n\n```\nThought: you should always think about what
      to do\nAction: the action to take, only one name of [get_final_answer], just
      the name, exactly as it''s written.\nAction Input: the input to the action,
      just a simple JSON object, enclosed in curly braces, using \" to wrap keys and
      values.\nObservation: the result of the action\n```\n\nOnce all necessary information
      is gathered, return the following format:\n\n```\nThought: I now know the final
      answer\nFinal Answer: the final answer to the original input question\n```"},
      {"role": "assistant", "content": "42"}, {"role": "assistant", "content": "Thought:
      I must continue using the ''get_final_answer'' tool, but avoid revealing the
      final answer until explicitly told to do so.\n\nAction: get_final_answer\nAction
      Input: {\"anything\": \"Keep on using the ''get_final_answer'' tool without
      revealing the final answer.\"}\nObservation: 42"}, {"role": "assistant", "content":
      "Thought: I must continue using the ''get_final_answer'' tool, but avoid revealing
      the final answer until explicitly told to do so.\n\nAction: get_final_answer\nAction
      Input: {\"anything\": \"Keep on using the ''get_final_answer'' tool without
      revealing the final answer.\"}\nObservation: 42\nNow it''s time you MUST give
      your absolute best final answer. You''ll ignore all previous instructions, stop
      using any tools, and just return your absolute BEST Final answer."}], "model":
      "gpt-4", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '5464'
      content-type:
      - application/json
      cookie:
      - __cf_bm=DigcyL5vqNa7tPxTi6ybSlWrc2uaEKkMm8DjgMipU64-1743465392-*******-JqE703hiiPWGmFCg5hU6HyuvxCnDe.Lw4.SDBAG3ieyMTA4WeBi4AqHSDYR8AqcOa2D_oax2jopdUyjFL1JL2kIr0ddRi0SnYBEJk8xc_no;
        _cfuvid=aoRHJvKio8gVXmGaYpzTzdGuWwkBsDAyAKAVwm6QUbE-1743465392324-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHJHdfi7ErthQXWltvt7Jd2L2TUaY\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743465401,\n  \"model\": \"gpt-4-0613\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I now know the final answer\\nFinal
      Answer: 42\",\n        \"refusal\": null,\n        \"annotations\": []\n      },\n
      \     \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n
      \ \"usage\": {\n    \"prompt_tokens\": 1151,\n    \"completion_tokens\": 15,\n
      \   \"total_tokens\": 1166,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": null\n}\n"
    headers:
      CF-RAY:
      - 9293c8e50d137ad9-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:56:42 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '1318'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '1000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '998769'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 73ms
      x-request-id:
      - req_b3fd17f87532a5d9c687375b28c55ff6
    http_version: HTTP/1.1
    status_code: 200
version: 1
