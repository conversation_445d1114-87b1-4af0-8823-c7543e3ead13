---
title: "Zapier Trigger"
description: "Trigger CrewAI crews from Zapier workflows to automate cross-app workflows"
icon: "bolt"
---

This guide will walk you through the process of setting up Zapier triggers for CrewAI Enterprise, allowing you to automate workflows between CrewAI Enterprise and other applications.

## Prerequisites

- A CrewAI Enterprise account
- A Zapier account
- A Slack account (for this specific example)

## Step-by-Step Setup

<Steps>
    <Step title="Set Up the Slack Trigger">
        - In Zapier, create a new Zap.

        <Frame>
            <img src="/images/enterprise/zapier-1.png" alt="Zapier 1" />
        </Frame>
    </Step>

    <Step title="Choose Slack as your trigger app">
        <Frame>
            <img src="/images/enterprise/zapier-2.png" alt="Zapier 2" />
        </Frame>
        - Select `New Pushed Message` as the Trigger Event.
        - Connect your Slack account if you haven't already.
    </Step>

    <Step title="Configure the CrewAI Enterprise Action">
        - Add a new action step to your Zap.
        - Choose CrewAI+ as your action app and Kickoff as the Action Event

        <Frame>
            <img src="/images/enterprise/zapier-3.png" alt="Zapier 5" />
        </Frame>
    </Step>

    <Step title="Connect your CrewAI Enterprise account">
        - Connect your CrewAI Enterprise account.
        - Select the appropriate Crew for your workflow.

        <Frame>
            <img src="/images/enterprise/zapier-4.png" alt="Zapier 6" />
        </Frame>
        - Configure the inputs for the Crew using the data from the Slack message.
    </Step>

    <Step title="Format the CrewAI Enterprise Output">
        - Add another action step to format the text output from CrewAI Enterprise.
        - Use Zapier's formatting tools to convert the Markdown output to HTML.

        <Frame>
            <img src="/images/enterprise/zapier-5.png" alt="Zapier 8" />
        </Frame>
        <Frame>
            <img src="/images/enterprise/zapier-6.png" alt="Zapier 9" />
        </Frame>
    </Step>

    <Step title="Send the Output via Email">
        - Add a final action step to send the formatted output via email.
        - Choose your preferred email service (e.g., Gmail, Outlook).
        - Configure the email details, including recipient, subject, and body.
        - Insert the formatted CrewAI Enterprise output into the email body.

        <Frame>
            <img src="/images/enterprise/zapier-7.png" alt="Zapier 7" />
        </Frame>
    </Step>

    <Step title="Kick Off the crew from Slack">
        - Enter the text in your Slack channel

        <Frame>
            <img src="/images/enterprise/zapier-7b.png" alt="Zapier 10" />
        </Frame>

        - Select the 3 ellipsis button and then chose Push to Zapier

        <Frame>
            <img src="/images/enterprise/zapier-8.png" alt="Zapier 11" />
        </Frame>
    </Step>

    <Step title="Select the crew and then Push to Kick Off">
        <Frame>
            <img src="/images/enterprise/zapier-9.png" alt="Zapier 12" />
        </Frame>
    </Step>
</Steps>

## Tips for Success

- Ensure that your CrewAI Enterprise inputs are correctly mapped from the Slack message.
- Test your Zap thoroughly before turning it on to catch any potential issues.
- Consider adding error handling steps to manage potential failures in the workflow.

By following these steps, you'll have successfully set up Zapier triggers for CrewAI Enterprise, allowing for automated workflows triggered by Slack messages and resulting in email notifications with CrewAI Enterprise output. 