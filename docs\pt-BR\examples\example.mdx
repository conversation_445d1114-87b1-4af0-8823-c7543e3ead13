---
title: Exemplos CrewAI
description: Uma coleção de exemplos que mostram como usar o framework CrewAI para automatizar fluxos de trabalho.
icon: rocket-launch
---

<CardGroup cols={3}>
  <Card
  title="Estratégia de Marketing"
  color="#F3A78B"
  href="https://github.com/crewAIInc/crewAI-examples/tree/main/marketing_strategy"
  icon="bullhorn"
    iconType="solid"
  >
    Automatize a criação de estratégias de marketing com CrewAI.
  </Card>
  <Card
    title="Viagem Surpresa"
    color="#F3A78B"
    href="https://github.com/crewAIInc/crewAI-examples/tree/main/surprise_trip"
    icon="plane"
      iconType="duotone"
    >
      Crie um roteiro de viagem surpresa com CrewAI.
    </Card>
    <Card
    title="Relacionar Perfil a Posições"
    color="#F3A78B"
    href="https://github.com/crewAIInc/crewAI-examples/tree/main/match_profile_to_positions"
    icon="linkedin"
      iconType="duotone"
    >
      Relacione um perfil a vagas de emprego com CrewAI.
    </Card>
    <Card
    title="Criar Vaga"
    color="#F3A78B"
    href="https://github.com/crewAIInc/crewAI-examples/tree/main/job-posting"
    icon="newspaper"
      iconType="duotone"
    >
      Crie uma vaga de emprego com CrewAI.
    </Card>
    <Card
    title="Gerador de Jogos"
    color="#F3A78B"
    href="https://github.com/crewAIInc/crewAI-examples/tree/main/game-builder-crew"
    icon="gamepad"
      iconType="duotone"
    >
      Crie um jogo com CrewAI.
    </Card>
    <Card
    title="Encontrar Candidatos"
    color="#F3A78B"
    href="https://github.com/crewAIInc/crewAI-examples/tree/main/recruitment"
    icon="user-group"
      iconType="duotone"
    >
      Encontre candidatos a vagas com CrewAI.
    </Card>
</CardGroup>