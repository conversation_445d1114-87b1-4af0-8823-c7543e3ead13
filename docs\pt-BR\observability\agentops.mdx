---
title: Integração com AgentOps
description: Entendendo e registrando a performance do seu agente com AgentOps.
icon: paperclip
---

# Introdução

Observabilidade é um aspecto fundamental no desenvolvimento e implantação de agentes de IA conversacional. Ela permite que desenvolvedores compreendam como seus agentes estão performando, 
como eles estão interagindo com os usuários e como utilizam ferramentas externas e APIs. 
AgentOps é um produto independente do CrewAI que fornece uma solução completa de observabilidade para agentes.

## AgentOps

[AgentOps](https://agentops.ai/?=crew) oferece replay de sessões, métricas e monitoramento para agentes.

Em um alto nível, o AgentOps oferece a capacidade de monitorar custos, uso de tokens, latência, falhas do agente, estatísticas de sessão e muito mais. 
Para mais informações, confira o [Repositório do AgentOps](https://github.com/AgentOps-AI/agentops).

### Visão Geral

AgentOps fornece monitoramento para agentes em desenvolvimento e produção. 
Disponibiliza um dashboard para acompanhamento de performance dos agentes, replay de sessões e relatórios personalizados.

Além disso, o AgentOps traz análises detalhadas das sessões para visualizar interações do agente Crew, chamadas LLM e uso de ferramentas em tempo real. 
Esse recurso é útil para depuração e entendimento de como os agentes interagem com usuários e entre si.

![Visão geral de uma série selecionada de execuções de sessões do agente](/images/agentops-overview.png)
![Visão geral das análises detalhadas de sessões para examinar execuções de agentes](/images/agentops-session.png)
![Visualizando um gráfico de execução passo a passo do replay do agente](/images/agentops-replay.png)

### Funcionalidades

- **Gerenciamento e Rastreamento de Custos de LLM**: Acompanhe gastos com provedores de modelos fundamentais.
- **Análises de Replay**: Assista gráficos de execução do agente, passo a passo.
- **Detecção de Pensamento Recursivo**: Identifique quando agentes entram em loops infinitos.
- **Relatórios Personalizados**: Crie análises customizadas sobre a performance dos agentes.
- **Dashboard Analítico**: Monitore estatísticas gerais de agentes em desenvolvimento e produção.
- **Teste de Modelos Públicos**: Teste seus agentes em benchmarks e rankings.
- **Testes Personalizados**: Execute seus agentes em testes específicos de domínio.
- **Depuração com Viagem no Tempo**: Reinicie suas sessões a partir de checkpoints.
- **Conformidade e Segurança**: Crie registros de auditoria e detecte possíveis ameaças como uso de palavrões e vazamento de dados pessoais.
- **Detecção de Prompt Injection**: Identifique possíveis injeções de código e vazamentos de segredos.

### Utilizando o AgentOps

<Steps>
   <Step title="Crie uma Chave de API">
      Crie uma chave de API de usuário aqui: [Create API Key](https://app.agentops.ai/account)
   </Step>
   <Step title="Configure seu Ambiente">
      Adicione sua chave API nas variáveis de ambiente:
      ```bash
      AGENTOPS_API_KEY=<YOUR_AGENTOPS_API_KEY>
      ```
   </Step>
   <Step title="Instale o AgentOps">
      Instale o AgentOps com:
      ```bash
      pip install 'crewai[agentops]'
      ```
      ou
      ```bash
      pip install agentops
      ```
   </Step>
   <Step title="Inicialize o AgentOps">
      Antes de utilizar o `Crew` no seu script, inclua estas linhas:

      ```python
      import agentops
      agentops.init()
      ```

      Isso irá iniciar uma sessão do AgentOps e também rastrear automaticamente os agentes Crew. Para mais detalhes sobre como adaptar sistemas de agentes mais complexos, 
      confira a [documentação do AgentOps](https://docs.agentops.ai) ou participe do [Discord](https://discord.gg/j4f3KbeH).
   </Step>
</Steps>

### Exemplos de Crew + AgentOps

<CardGroup cols={3}>
  <Card
  title="Vaga de Emprego"
  color="#F3A78B"
  href="https://github.com/joaomdmoura/crewAI-examples/tree/main/job-posting"
  icon="briefcase"
    iconType="solid"
  >
    Exemplo de um agente Crew que gera vagas de emprego.
  </Card>
  <Card
    title="Validador de Markdown"
    color="#F3A78B"
    href="https://github.com/joaomdmoura/crewAI-examples/tree/main/markdown_validator"
    icon="markdown"
      iconType="solid"
    >
         Exemplo de um agente Crew que valida arquivos Markdown.
    </Card>
    <Card
    title="Post no Instagram"
    color="#F3A78B"
    href="https://github.com/joaomdmoura/crewAI-examples/tree/main/instagram_post"
    icon="square-instagram"
      iconType="brands"
    >
      Exemplo de um agente Crew que gera posts para Instagram.
  </Card>
</CardGroup>

### Mais Informações

Para começar, crie uma [conta AgentOps](https://agentops.ai/?=crew).

Para sugestões de funcionalidades ou relatos de bugs, entre em contato com o time do AgentOps pelo [Repositório do AgentOps](https://github.com/AgentOps-AI/agentops).

#### Links Extras

<a href="https://twitter.com/agentopsai/">🐦 Twitter</a>
<span>&nbsp;&nbsp;•&nbsp;&nbsp;</span>
<a href="https://discord.gg/JHPt4C7r">📢 Discord</a>
<span>&nbsp;&nbsp;•&nbsp;&nbsp;</span>
<a href="https://app.agentops.ai/?=crew">🖇️ Dashboard AgentOps</a>
<span>&nbsp;&nbsp;•&nbsp;&nbsp;</span>
<a href="https://docs.agentops.ai/introduction">📙 Documentação</a>