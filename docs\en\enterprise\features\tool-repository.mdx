---
title: Tool Repository
description: "Using the Tool Repository to manage your tools"
icon: "toolbox"
---

## Overview

The Tool Repository is a package manager for CrewAI tools. It allows users to publish, install, and manage tools that integrate with CrewAI crews and flows.

Tools can be:

- **Private**: accessible only within your organization (default)
- **Public**: accessible to all CrewAI users if published with the `--public` flag

The repository is not a version control system. Use Git to track code changes and enable collaboration.

## Prerequisites

Before using the Tool Repository, ensure you have:

- A [CrewAI Enterprise](https://app.crewai.com) account
- [CrewAI CLI](https://docs.crewai.com/concepts/cli#cli) installed
- uv>=0.5.0 installed. Check out [how to upgrade](https://docs.astral.sh/uv/getting-started/installation/#upgrading-uv)
- [Git](https://git-scm.com) installed and configured
- Access permissions to publish or install tools in your CrewAI Enterprise organization

## Installing Tools

To install a tool:

```bash
crewai tool install <tool-name>
```

This installs the tool and adds it to `pyproject.toml`.

## Creating and Publishing Tools

To create a new tool project:

```bash
crewai tool create <tool-name>
```

This generates a scaffolded tool project locally.

After making changes, initialize a Git repository and commit the code:

```bash
git init
git add .
git commit -m "Initial version"
```

To publish the tool:

```bash
crewai tool publish
```

By default, tools are published as private. To make a tool public:

```bash
crewai tool publish --public
```

For more details on how to build tools, see [Creating your own tools](https://docs.crewai.com/concepts/tools#creating-your-own-tools).

## Updating Tools

To update a published tool:

1. Modify the tool locally
2. Update the version in `pyproject.toml` (e.g., from `0.1.0` to `0.1.1`)
3. Commit the changes and publish

```bash
git commit -m "Update version to 0.1.1"
crewai tool publish
```

## Deleting Tools

To delete a tool:

1. Go to [CrewAI Enterprise](https://app.crewai.com)
2. Navigate to **Tools**
3. Select the tool
4. Click **Delete**

<Warning>
Deletion is permanent. Deleted tools cannot be restored or re-installed.
</Warning>

## Security Checks

Every published version undergoes automated security checks, and are only available to install after they pass. 

You can check the security check status of a tool at:

`CrewAI Enterprise > Tools > Your Tool > Versions`


<Card title="Need Help?" icon="headset" href="mailto:<EMAIL>">
  Contact our support team for assistance with API integration or troubleshooting.
</Card>
