interactions:
- request:
    body: '{"messages": [{"role": "system", "content": "You are Manager. You''re great
      at delegating work about scoring.\nYour personal goal is: Coordinate scoring
      processes\nYou ONLY have access to the following tools, and should NEVER make
      up tools that are not listed here:\n\nTool Name: Delegate work to coworker(task:
      str, context: str, coworker: Optional[str] = None, **kwargs)\nTool Description:
      Delegate a specific task to one of the following coworkers: Scorer\nThe input
      to this tool should be the coworker, the task you want them to do, and ALL necessary
      context to execute the task, they know nothing about the task, so share absolute
      everything you know, don''t reference things but instead explain them.\nTool
      Arguments: {''task'': {''title'': ''Task'', ''type'': ''string''}, ''context'':
      {''title'': ''Context'', ''type'': ''string''}, ''coworker'': {''title'': ''Coworker'',
      ''type'': ''string''}, ''kwargs'': {''title'': ''Kwargs'', ''type'': ''object''}}\nTool
      Name: Ask question to coworker(question: str, context: str, coworker: Optional[str]
      = None, **kwargs)\nTool Description: Ask a specific question to one of the following
      coworkers: Scorer\nThe input to this tool should be the coworker, the question
      you have for them, and ALL necessary context to ask the question properly, they
      know nothing about the question, so share absolute everything you know, don''t
      reference things but instead explain them.\nTool Arguments: {''question'': {''title'':
      ''Question'', ''type'': ''string''}, ''context'': {''title'': ''Context'', ''type'':
      ''string''}, ''coworker'': {''title'': ''Coworker'', ''type'': ''string''},
      ''kwargs'': {''title'': ''Kwargs'', ''type'': ''object''}}\n\nUse the following
      format:\n\nThought: you should always think about what to do\nAction: the action
      to take, only one name of [Delegate work to coworker, Ask question to coworker],
      just the name, exactly as it''s written.\nAction Input: the input to the action,
      just a simple python dictionary, enclosed in curly braces, using \" to wrap
      keys and values.\nObservation: the result of the action\n\nOnce all necessary
      information is gathered:\n\nThought: I now know the final answer\nFinal Answer:
      the final answer to the original input question\n"}, {"role": "user", "content":
      "\nCurrent Task: Give me an integer score between 1-5 for the following title:
      ''The impact of AI in the future of work''\n\nThis is the expect criteria for
      your final answer: The score of the title.\nyou MUST return the actual complete
      content as the final answer, not a summary.\n\nBegin! This is VERY important
      to you, use the tools available and give your best Final Answer, your job depends
      on it!\n\nThought:"}], "model": "gpt-4o"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '2613'
      content-type:
      - application/json
      cookie:
      - __cf_bm=9.8sBYBkvBR8R1K_bVF7xgU..80XKlEIg3N2OBbTSCU-**********-*******-.qiTLXbPamYUMSuyNsOEB9jhGu.jOifujOrx9E2JZvStbIZ9RTIiE44xKKNfLPxQkOi6qAT3h6htK8lPDGV_5g;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-*************-*******-*********
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7gSk1Z1rcVo96hskKeiE3yki3Kn\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727214508,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: To provide an accurate score
      for the title \\\"The impact of AI in the future of work,\\\" I need to delegate
      the task of scoring it to the Scorer, who is specialized in this activity.\\n\\nAction:
      Delegate work to coworker\\nAction Input: {\\\"coworker\\\": \\\"Scorer\\\",
      \\\"task\\\": \\\"Give me an integer score between 1-5 for the following title:
      'The impact of AI in the future of work'\\\", \\\"context\\\": \\\"Your task
      is to evaluate the provided title on a scale from 1 to 5 based on criteria such
      as relevance, clarity, and intrigue.\\\"}\\n\\nObservation: The scorer has received
      the task to score the title 'The impact of AI in the future of work'.\",\n        \"refusal\":
      null\n      },\n      \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n
      \   }\n  ],\n  \"usage\": {\n    \"prompt_tokens\": 584,\n    \"completion_tokens\":
      149,\n    \"total_tokens\": 733,\n    \"completion_tokens_details\": {\n      \"reasoning_tokens\":
      0\n    }\n  },\n  \"system_fingerprint\": \"fp_e375328146\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85fa95dfaf1cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:48:31 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '2017'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999362'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 1ms
      x-request-id:
      - req_c7823a91a290312f8047ade2fdcd5aa2
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: !!binary |
      Cs0PCiQKIgoMc2VydmljZS5uYW1lEhIKEGNyZXdBSS10ZWxlbWV0cnkSpA8KEgoQY3Jld2FpLnRl
      bGVtZXRyeRKbAQoQ7367K9U97eyRi7bbPe59GhIIWkx0YIhfwgMqClRvb2wgVXNhZ2UwATmIeAh+
      cUz4F0EYtA9+cUz4F0oaCg5jcmV3YWlfdmVyc2lvbhIICgYwLjYxLjBKJwoJdG9vbF9uYW1lEhoK
      GEFzayBxdWVzdGlvbiB0byBjb3dvcmtlckoOCghhdHRlbXB0cxICGAF6AhgBhQEAAQAAEpACChA3
      ZEUD8bm3GCD8YO06mrD1EghNOH69zXvzMyoOVGFzayBFeGVjdXRpb24wATnAGCK1cEz4F0FoTL65
      cUz4F0ouCghjcmV3X2tleRIiCiA1ZTZlZmZlNjgwYTVkOTdkYzM4NzNiMTQ4MjVjY2ZhM0oxCgdj
      cmV3X2lkEiYKJGY1OWQ2YjEwLTM4ODEtNDRiYy05OTgwLTdmMTIwMjhiMDdkY0ouCgh0YXNrX2tl
      eRIiCiAyN2VmMzhjYzk5ZGE0YThkZWQ3MGVkNDA2ZTQ0YWI4NkoxCgd0YXNrX2lkEiYKJGU2Mzdh
      ODM1LTZiZjMtNDk5NC1iMjczLWU5ZjYzOWEwYmE5NXoCGAGFAQABAAASywkKEAwM1HgGpiGyl6rQ
      LeVsYFsSCJN0H+EImgQNKgxDcmV3IENyZWF0ZWQwATmwCsG6cUz4F0HYksW6cUz4F0oaCg5jcmV3
      YWlfdmVyc2lvbhIICgYwLjYxLjBKGgoOcHl0aG9uX3ZlcnNpb24SCAoGMy4xMS43Si4KCGNyZXdf
      a2V5EiIKIDc0Mjc1NzMxMmVmN2JiNGVlMGIwNjYyZDFjMmUyMTc5SjEKB2NyZXdfaWQSJgokNzEw
      YjdjNDMtMTJiYS00NmM3LWI5ZWMtY2QzYzE4OThjYWFkShwKDGNyZXdfcHJvY2VzcxIMCgpzZXF1
      ZW50aWFsShEKC2NyZXdfbWVtb3J5EgIQAEoaChRjcmV3X251bWJlcl9vZl90YXNrcxICGAFKGwoV
      Y3Jld19udW1iZXJfb2ZfYWdlbnRzEgIYAkr8BAoLY3Jld19hZ2VudHMS7AQK6QRbeyJrZXkiOiAi
      ODljZjMxMWI0OGI1MjE2OWQ0MmYzOTI1YzViZTFjNWEiLCAiaWQiOiAiMWVlMTRiZDYtNzU5My00
      ODE3LWFjOGYtZmE3ZmJiYTI2NTQxIiwgInJvbGUiOiAiTWFuYWdlciIsICJ2ZXJib3NlPyI6IGZh
      bHNlLCAibWF4X2l0ZXIiOiAxNSwgIm1heF9ycG0iOiBudWxsLCAiZnVuY3Rpb25fY2FsbGluZ19s
      bG0iOiAiIiwgImxsbSI6ICJncHQtNG8iLCAiZGVsZWdhdGlvbl9lbmFibGVkPyI6IHRydWUsICJh
      bGxvd19jb2RlX2V4ZWN1dGlvbj8iOiBmYWxzZSwgIm1heF9yZXRyeV9saW1pdCI6IDIsICJ0b29s
      c19uYW1lcyI6IFtdfSwgeyJrZXkiOiAiOTJlN2ViMTkxNjY0YzkzNTc4NWVkN2Q0MjQwYTI5NGQi
      LCAiaWQiOiAiMzZmNmIyYmQtZmFkMi00NTRmLTkwMmEtZWMyYmQzMmVhM2YyIiwgInJvbGUiOiAi
      U2NvcmVyIiwgInZlcmJvc2U/IjogZmFsc2UsICJtYXhfaXRlciI6IDE1LCAibWF4X3JwbSI6IG51
      bGwsICJmdW5jdGlvbl9jYWxsaW5nX2xsbSI6ICIiLCAibGxtIjogImdwdC00byIsICJkZWxlZ2F0
      aW9uX2VuYWJsZWQ/IjogdHJ1ZSwgImFsbG93X2NvZGVfZXhlY3V0aW9uPyI6IGZhbHNlLCAibWF4
      X3JldHJ5X2xpbWl0IjogMiwgInRvb2xzX25hbWVzIjogW119XUr8AQoKY3Jld190YXNrcxLtAQrq
      AVt7ImtleSI6ICIyN2VmMzhjYzk5ZGE0YThkZWQ3MGVkNDA2ZTQ0YWI4NiIsICJpZCI6ICI1YjY4
      MzlmMC1jZTdmLTQ1MjUtOTA0MS0yNDA3OGY4ODQyYjAiLCAiYXN5bmNfZXhlY3V0aW9uPyI6IGZh
      bHNlLCAiaHVtYW5faW5wdXQ/IjogZmFsc2UsICJhZ2VudF9yb2xlIjogIk1hbmFnZXIiLCAiYWdl
      bnRfa2V5IjogIjg5Y2YzMTFiNDhiNTIxNjlkNDJmMzkyNWM1YmUxYzVhIiwgInRvb2xzX25hbWVz
      IjogW119XXoCGAGFAQABAAASjgIKENITRuTzJvz4egiwtMnZ6vcSCPj1izbtPyMQKgxUYXNrIENy
      ZWF0ZWQwATlAzIK7cUz4F0EQzoO7cUz4F0ouCghjcmV3X2tleRIiCiA3NDI3NTczMTJlZjdiYjRl
      ZTBiMDY2MmQxYzJlMjE3OUoxCgdjcmV3X2lkEiYKJDcxMGI3YzQzLTEyYmEtNDZjNy1iOWVjLWNk
      M2MxODk4Y2FhZEouCgh0YXNrX2tleRIiCiAyN2VmMzhjYzk5ZGE0YThkZWQ3MGVkNDA2ZTQ0YWI4
      NkoxCgd0YXNrX2lkEiYKJDViNjgzOWYwLWNlN2YtNDUyNS05MDQxLTI0MDc4Zjg4NDJiMHoCGAGF
      AQABAAA=
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
      Content-Length:
      - '2000'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.27.0
    method: POST
    uri: https://telemetry.crewai.com:4319/v1/traces
  response:
    body:
      string: "\n\0"
    headers:
      Content-Length:
      - '2'
      Content-Type:
      - application/x-protobuf
      Date:
      - Tue, 24 Sep 2024 21:48:32 GMT
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "system", "content": "You are Scorer. You''re an
      expert scorer, specialized in scoring titles.\nYour personal goal is: Score
      the title\nTo give my best complete final answer to the task use the exact following
      format:\n\nThought: I now can give a great answer\nFinal Answer: Your final
      answer must be the great and the most complete as possible, it must be outcome
      described.\n\nI MUST use these formats, my job depends on it!"}, {"role": "user",
      "content": "\nCurrent Task: Give me an integer score between 1-5 for the following
      title: ''The impact of AI in the future of work''\n\nThis is the expect criteria
      for your final answer: Your best answer to your coworker asking you this, accounting
      for the context shared.\nyou MUST return the actual complete content as the
      final answer, not a summary.\n\nThis is the context you''re working with:\nYour
      task is to evaluate the provided title on a scale from 1 to 5 based on criteria
      such as relevance, clarity, and intrigue.\n\nBegin! This is VERY important to
      you, use the tools available and give your best Final Answer, your job depends
      on it!\n\nThought:"}], "model": "gpt-4o"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1149'
      content-type:
      - application/json
      cookie:
      - __cf_bm=9.8sBYBkvBR8R1K_bVF7xgU..80XKlEIg3N2OBbTSCU-**********-*******-.qiTLXbPamYUMSuyNsOEB9jhGu.jOifujOrx9E2JZvStbIZ9RTIiE44xKKNfLPxQkOi6qAT3h6htK8lPDGV_5g;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-*************-*******-*********
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7gVWj6JmMibt7mwANbQBq91qey2\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727214511,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I now can give a great answer\\nFinal
      Answer: 4. The title 'The impact of AI in the future of work' is clear and directly
      addresses a highly relevant and intriguing topic. It captures the essence of
      how AI could shape the job market, which is a subject of significant interest.
      However, it could be more specific or detailed to achieve a perfect score.\",\n
      \       \"refusal\": null\n      },\n      \"logprobs\": null,\n      \"finish_reason\":
      \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\": 234,\n    \"completion_tokens\":
      77,\n    \"total_tokens\": 311,\n    \"completion_tokens_details\": {\n      \"reasoning_tokens\":
      0\n    }\n  },\n  \"system_fingerprint\": \"fp_52a7f40b0b\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85faa6cfcd1cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:48:32 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '643'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999723'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_133da93827bffd651411aa405d650b76
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are Manager. You''re great
      at delegating work about scoring.\nYour personal goal is: Coordinate scoring
      processes\nYou ONLY have access to the following tools, and should NEVER make
      up tools that are not listed here:\n\nTool Name: Delegate work to coworker(task:
      str, context: str, coworker: Optional[str] = None, **kwargs)\nTool Description:
      Delegate a specific task to one of the following coworkers: Scorer\nThe input
      to this tool should be the coworker, the task you want them to do, and ALL necessary
      context to execute the task, they know nothing about the task, so share absolute
      everything you know, don''t reference things but instead explain them.\nTool
      Arguments: {''task'': {''title'': ''Task'', ''type'': ''string''}, ''context'':
      {''title'': ''Context'', ''type'': ''string''}, ''coworker'': {''title'': ''Coworker'',
      ''type'': ''string''}, ''kwargs'': {''title'': ''Kwargs'', ''type'': ''object''}}\nTool
      Name: Ask question to coworker(question: str, context: str, coworker: Optional[str]
      = None, **kwargs)\nTool Description: Ask a specific question to one of the following
      coworkers: Scorer\nThe input to this tool should be the coworker, the question
      you have for them, and ALL necessary context to ask the question properly, they
      know nothing about the question, so share absolute everything you know, don''t
      reference things but instead explain them.\nTool Arguments: {''question'': {''title'':
      ''Question'', ''type'': ''string''}, ''context'': {''title'': ''Context'', ''type'':
      ''string''}, ''coworker'': {''title'': ''Coworker'', ''type'': ''string''},
      ''kwargs'': {''title'': ''Kwargs'', ''type'': ''object''}}\n\nUse the following
      format:\n\nThought: you should always think about what to do\nAction: the action
      to take, only one name of [Delegate work to coworker, Ask question to coworker],
      just the name, exactly as it''s written.\nAction Input: the input to the action,
      just a simple python dictionary, enclosed in curly braces, using \" to wrap
      keys and values.\nObservation: the result of the action\n\nOnce all necessary
      information is gathered:\n\nThought: I now know the final answer\nFinal Answer:
      the final answer to the original input question\n"}, {"role": "user", "content":
      "\nCurrent Task: Give me an integer score between 1-5 for the following title:
      ''The impact of AI in the future of work''\n\nThis is the expect criteria for
      your final answer: The score of the title.\nyou MUST return the actual complete
      content as the final answer, not a summary.\n\nBegin! This is VERY important
      to you, use the tools available and give your best Final Answer, your job depends
      on it!\n\nThought:"}, {"role": "assistant", "content": "Thought: To provide
      an accurate score for the title \"The impact of AI in the future of work,\"
      I need to delegate the task of scoring it to the Scorer, who is specialized
      in this activity.\n\nAction: Delegate work to coworker\nAction Input: {\"coworker\":
      \"Scorer\", \"task\": \"Give me an integer score between 1-5 for the following
      title: ''The impact of AI in the future of work''\", \"context\": \"Your task
      is to evaluate the provided title on a scale from 1 to 5 based on criteria such
      as relevance, clarity, and intrigue.\"}\n\nObservation: The scorer has received
      the task to score the title ''The impact of AI in the future of work''.\nObservation:
      4. The title ''The impact of AI in the future of work'' is clear and directly
      addresses a highly relevant and intriguing topic. It captures the essence of
      how AI could shape the job market, which is a subject of significant interest.
      However, it could be more specific or detailed to achieve a perfect score."}],
      "model": "gpt-4o"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '3613'
      content-type:
      - application/json
      cookie:
      - __cf_bm=9.8sBYBkvBR8R1K_bVF7xgU..80XKlEIg3N2OBbTSCU-**********-*******-.qiTLXbPamYUMSuyNsOEB9jhGu.jOifujOrx9E2JZvStbIZ9RTIiE44xKKNfLPxQkOi6qAT3h6htK8lPDGV_5g;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-*************-*******-*********
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7gWvoNTNn6C1ZVbf5LEjk6zmRlG\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727214512,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I now know the final answer
      as the Scorer has provided the score for the title based on the given criteria.\\n\\nFinal
      Answer: 4\",\n        \"refusal\": null\n      },\n      \"logprobs\": null,\n
      \     \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      803,\n    \"completion_tokens\": 30,\n    \"total_tokens\": 833,\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0\n    }\n  },\n  \"system_fingerprint\": \"fp_e375328146\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85faad48021cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:48:33 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '454'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999125'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 1ms
      x-request-id:
      - req_32907c02e96c6932e9424220bb24bb7a
    http_version: HTTP/1.1
    status_code: 200
version: 1
