---
title: <PERSON><PERSON><PERSON>
description: Construa seu primeiro agente de IA com a CrewAI em menos de 5 minutos.
icon: rocket
---

## Construa seu primeiro Agente CrewAI

Vamos criar uma tripulação simples que nos ajudará a `pesquisar` e `relatar` sobre os `últimos avanços em IA` para um determinado tópico ou assunto.

Antes de prosseguir, certifique-se de ter concluído a instalação da CrewAI.
Se ainda não instalou, faça isso seguindo o [guia de instalação](/pt-BR/installation).

Siga os passos abaixo para começar a tripular! 🚣‍♂️

<Steps>
  <Step title="Crie sua tripulação">
  Crie um novo projeto de tripulação executando o comando abaixo em seu terminal.
  Isso criará um novo diretório chamado `latest-ai-development` com a estrutura básica para sua tripulação.
    <CodeGroup>
      ```shell Terminal
      crewai create crew latest-ai-development
      ```
    </CodeGroup>
  </Step>
  <Step title="Navegue até o novo projeto da sua tripulação">
    <CodeGroup>
      ```shell Terminal
      cd latest-ai-development
      ```
    </CodeGroup>
  </Step>
  <Step title="Modifique seu arquivo `agents.yaml`">
  <Tip>
  Você também pode modificar os agentes conforme necessário para atender ao seu caso de uso ou copiar e colar como está para seu projeto.
  Qualquer variável interpolada nos seus arquivos `agents.yaml` e `tasks.yaml`, como `{topic}`, será substituída pelo valor da variável no arquivo `main.py`.
  </Tip>
    ```yaml agents.yaml
    # src/latest_ai_development/config/agents.yaml
    researcher:
      role: >
        Pesquisador Sênior de Dados em {topic}
      goal: >
        Descobrir os avanços mais recentes em {topic}
      backstory: >
        Você é um pesquisador experiente com talento para descobrir os últimos avanços em {topic}. Conhecido por sua habilidade em encontrar as informações mais relevantes e apresentá-las de forma clara e concisa.

    reporting_analyst:
      role: >
        Analista de Relatórios em {topic}
      goal: >
        Criar relatórios detalhados com base na análise de dados e descobertas de pesquisa em {topic}
      backstory: >
        Você é um analista meticuloso com um olhar atento aos detalhes. É conhecido por sua capacidade de transformar dados complexos em relatórios claros e concisos, facilitando o entendimento e a tomada de decisão por parte dos outros.
    ```
  </Step>
  <Step title="Modifique seu arquivo `tasks.yaml`">
    ```yaml tasks.yaml
    # src/latest_ai_development/config/tasks.yaml
    research_task:
      description: >
        Realize uma pesquisa aprofundada sobre {topic}.
        Certifique-se de encontrar informações interessantes e relevantes considerando que o ano atual é 2025.
      expected_output: >
        Uma lista com 10 tópicos dos dados mais relevantes sobre {topic}
      agent: researcher

    reporting_task:
      description: >
        Revise o contexto obtido e expanda cada tópico em uma seção completa para um relatório.
        Certifique-se de que o relatório seja detalhado e contenha todas as informações relevantes.
      expected_output: >
        Um relatório completo com os principais tópicos, cada um com uma seção detalhada de informações.
        Formate como markdown sem usar '```'
      agent: reporting_analyst
      output_file: report.md
    ```
  </Step>
  <Step title="Modifique seu arquivo `crew.py`">
    ```python crew.py
    # src/latest_ai_development/crew.py
    from crewai import Agent, Crew, Process, Task
    from crewai.project import CrewBase, agent, crew, task
    from crewai_tools import SerperDevTool
    from crewai.agents.agent_builder.base_agent import BaseAgent
    from typing import List

    @CrewBase
    class LatestAiDevelopmentCrew():
      """LatestAiDevelopment crew"""

      agents: List[BaseAgent]
      tasks: List[Task]

      @agent
      def researcher(self) -> Agent:
        return Agent(
          config=self.agents_config['researcher'], # type: ignore[index]
          verbose=True,
          tools=[SerperDevTool()]
        )

      @agent
      def reporting_analyst(self) -> Agent:
        return Agent(
          config=self.agents_config['reporting_analyst'], # type: ignore[index]
          verbose=True
        )

      @task
      def research_task(self) -> Task:
        return Task(
          config=self.tasks_config['research_task'], # type: ignore[index]
        )

      @task
      def reporting_task(self) -> Task:
        return Task(
          config=self.tasks_config['reporting_task'], # type: ignore[index]
          output_file='output/report.md' # Este é o arquivo que conterá o relatório final.
        )

      @crew
      def crew(self) -> Crew:
        """Creates the LatestAiDevelopment crew"""
        return Crew(
          agents=self.agents, # Criado automaticamente pelo decorador @agent
          tasks=self.tasks, # Criado automaticamente pelo decorador @task
          process=Process.sequential,
          verbose=True,
        )
    ```
  </Step>
  <Step title="[Opcional] Adicione funções de pré e pós execução da tripulação">
    ```python crew.py
    # src/latest_ai_development/crew.py
    from crewai import Agent, Crew, Process, Task
    from crewai.project import CrewBase, agent, crew, task, before_kickoff, after_kickoff
    from crewai_tools import SerperDevTool

    @CrewBase
    class LatestAiDevelopmentCrew():
      """LatestAiDevelopment crew"""

      @before_kickoff
      def before_kickoff_function(self, inputs):
        print(f"Before kickoff function with inputs: {inputs}")
        return inputs # You can return the inputs or modify them as needed

      @after_kickoff
      def after_kickoff_function(self, result):
        print(f"After kickoff function with result: {result}")
        return result # You can return the result or modify it as needed

      # ... remaining code
    ```
  </Step>
  <Step title="Fique à vontade para passar entradas personalizadas para sua tripulação">
  Por exemplo, você pode passar o input `topic` para sua tripulação para personalizar a pesquisa e o relatório.
    ```python main.py
    #!/usr/bin/env python
    # src/latest_ai_development/main.py
    import sys
    from latest_ai_development.crew import LatestAiDevelopmentCrew

    def run():
      """
      Run the crew.
      """
      inputs = {
        'topic': 'AI Agents'
      }
      LatestAiDevelopmentCrew().crew().kickoff(inputs=inputs)
    ```
  </Step>
  <Step title="Defina suas variáveis de ambiente">
  Antes de executar sua tripulação, certifique-se de ter as seguintes chaves configuradas como variáveis de ambiente no seu arquivo `.env`:
    - Uma chave da API do [Serper.dev](https://serper.dev/): `SERPER_API_KEY=YOUR_KEY_HERE`
    - A configuração do modelo de sua escolha, como uma chave de API. Veja o
        [guia de configuração do LLM](/pt-BR/concepts/llms#setting-up-your-llm) para aprender como configurar modelos de qualquer provedor.
  </Step>
  <Step title="Trave e instale as dependências">
    - Trave e instale as dependências utilizando o comando da CLI:
      <CodeGroup>
        ```shell Terminal
        crewai install
        ```
      </CodeGroup>
    - Se quiser instalar pacotes adicionais, faça isso executando:
      <CodeGroup>
        ```shell Terminal
        uv add <package-name>
        ```
      </CodeGroup>
  </Step>
  <Step title="Execute sua tripulação">
    - Para executar sua tripulação, rode o seguinte comando na raiz do projeto:
      <CodeGroup>
        ```bash Terminal
        crewai run
        ```
      </CodeGroup>
  </Step>

  <Step title="Alternativa para Empresas: Crie no Crew Studio">
  Para usuários do CrewAI Enterprise, você pode criar a mesma tripulação sem escrever código:

  1. Faça login na sua conta CrewAI Enterprise (crie uma conta gratuita em [app.crewai.com](https://app.crewai.com))
  2. Abra o Crew Studio
  3. Digite qual automação deseja construir
  4. Crie suas tarefas visualmente e conecte-as em sequência
  5. Configure seus inputs e clique em "Download Code" ou "Deploy"

  ![Crew Studio Quickstart](/images/enterprise/crew-studio-interface.png)

  <Card title="Experimente o CrewAI Enterprise" icon="rocket" href="https://app.crewai.com">
    Comece sua conta gratuita no CrewAI Enterprise
  </Card>
  </Step>
  <Step title="Veja seu relatório final">
  Você verá a saída no console e o arquivo `report.md` deve ser criado na raiz do seu projeto com o relatório final.

  Veja um exemplo de como o relatório deve ser:

  <CodeGroup>
    ```markdown output/report.md
    # Relatório Abrangente sobre a Ascensão e o Impacto dos Agentes de IA em 2025

    ## 1. Introduction to AI Agents
    In 2025, Artificial Intelligence (AI) agents are at the forefront of innovation across various industries. As intelligent systems that can perform tasks typically requiring human cognition, AI agents are paving the way for significant advancements in operational efficiency, decision-making, and overall productivity within sectors like Human Resources (HR) and Finance. This report aims to detail the rise of AI agents, their frameworks, applications, and potential implications on the workforce.

    ## 2. Benefits of AI Agents
    AI agents bring numerous advantages that are transforming traditional work environments. Key benefits include:

    - **Task Automation**: AI agents can carry out repetitive tasks such as data entry, scheduling, and payroll processing without human intervention, greatly reducing the time and resources spent on these activities.
    - **Improved Efficiency**: By quickly processing large datasets and performing analyses that would take humans significantly longer, AI agents enhance operational efficiency. This allows teams to focus on strategic tasks that require higher-level thinking.
    - **Enhanced Decision-Making**: AI agents can analyze trends and patterns in data, provide insights, and even suggest actions, helping stakeholders make informed decisions based on factual data rather than intuition alone.

    ## 3. Popular AI Agent Frameworks
    Several frameworks have emerged to facilitate the development of AI agents, each with its own unique features and capabilities. Some of the most popular frameworks include:

    - **Autogen**: A framework designed to streamline the development of AI agents through automation of code generation.
    - **Semantic Kernel**: Focuses on natural language processing and understanding, enabling agents to comprehend user intentions better.
    - **Promptflow**: Provides tools for developers to create conversational agents that can navigate complex interactions seamlessly.
    - **Langchain**: Specializes in leveraging various APIs to ensure agents can access and utilize external data effectively.
    - **CrewAI**: Aimed at collaborative environments, CrewAI strengthens teamwork by facilitating communication through AI-driven insights.
    - **MemGPT**: Combines memory-optimized architectures with generative capabilities, allowing for more personalized interactions with users.

    These frameworks empower developers to build versatile and intelligent agents that can engage users, perform advanced analytics, and execute various tasks aligned with organizational goals.

    ## 4. AI Agents in Human Resources
    AI agents are revolutionizing HR practices by automating and optimizing key functions:

    - **Recruiting**: AI agents can screen resumes, schedule interviews, and even conduct initial assessments, thus accelerating the hiring process while minimizing biases.
    - **Succession Planning**: AI systems analyze employee performance data and potential, helping organizations identify future leaders and plan appropriate training.
    - **Employee Engagement**: Chatbots powered by AI can facilitate feedback loops between employees and management, promoting an open culture and addressing concerns promptly.

    As AI continues to evolve, HR departments leveraging these agents can realize substantial improvements in both efficiency and employee satisfaction.

    ## 5. AI Agents in Finance
    The finance sector is seeing extensive integration of AI agents that enhance financial practices:

    - **Expense Tracking**: Automated systems manage and monitor expenses, flagging anomalies and offering recommendations based on spending patterns.
    - **Risk Assessment**: AI models assess credit risk and uncover potential fraud by analyzing transaction data and behavioral patterns.
    - **Investment Decisions**: AI agents provide stock predictions and analytics based on historical data and current market conditions, empowering investors with informative insights.

    The incorporation of AI agents into finance is fostering a more responsive and risk-aware financial landscape.

    ## 6. Market Trends and Investments
    The growth of AI agents has attracted significant investment, especially amidst the rising popularity of chatbots and generative AI technologies. Companies and entrepreneurs are eager to explore the potential of these systems, recognizing their ability to streamline operations and improve customer engagement.

    Conversely, corporations like Microsoft are taking strides to integrate AI agents into their product offerings, with enhancements to their Copilot 365 applications. This strategic move emphasizes the importance of AI literacy in the modern workplace and indicates the stabilizing of AI agents as essential business tools.

    ## 7. Future Predictions and Implications
    Experts predict that AI agents will transform essential aspects of work life. As we look toward the future, several anticipated changes include:

    - Enhanced integration of AI agents across all business functions, creating interconnected systems that leverage data from various departmental silos for comprehensive decision-making.
    - Continued advancement of AI technologies, resulting in smarter, more adaptable agents capable of learning and evolving from user interactions.
    - Increased regulatory scrutiny to ensure ethical use, especially concerning data privacy and employee surveillance as AI agents become more prevalent.

    To stay competitive and harness the full potential of AI agents, organizations must remain vigilant about latest developments in AI technology and consider continuous learning and adaptation in their strategic planning.

    ## 8. Conclusion
    The emergence of AI agents is undeniably reshaping the workplace landscape in 5. With their ability to automate tasks, enhance efficiency, and improve decision-making, AI agents are critical in driving operational success. Organizations must embrace and adapt to AI developments to thrive in an increasingly digital business environment.
    ```
  </CodeGroup>
  </Step>
</Steps>

<Check>
Parabéns!

Você configurou seu projeto de tripulação com sucesso e está pronto para começar a construir seus próprios fluxos de trabalho baseados em agentes!
</Check>

### Observação sobre Consistência nos Nomes

Os nomes utilizados nos seus arquivos YAML (`agents.yaml` e `tasks.yaml`) devem corresponder aos nomes dos métodos no seu código Python.
Por exemplo, você pode referenciar o agente para tarefas específicas a partir do arquivo `tasks.yaml`.
Essa consistência de nomes permite que a CrewAI conecte automaticamente suas configurações ao seu código; caso contrário, sua tarefa não reconhecerá a referência corretamente.

#### Exemplos de Referências

<Tip>
  Observe como usamos o mesmo nome para o agente no arquivo `agents.yaml` (`email_summarizer`) e no método do arquivo `crew.py` (`email_summarizer`).
</Tip>

```yaml agents.yaml
email_summarizer:
    role: >
      Email Summarizer
    goal: >
      Summarize emails into a concise and clear summary
    backstory: >
      You will create a 5 bullet point summary of the report
    llm: provider/model-id  # Add your choice of model here
```

<Tip>
  Observe como usamos o mesmo nome para a tarefa no arquivo `tasks.yaml` (`email_summarizer_task`) e no método no arquivo `crew.py` (`email_summarizer_task`).
</Tip>

```yaml tasks.yaml
email_summarizer_task:
    description: >
      Summarize the email into a 5 bullet point summary
    expected_output: >
      A 5 bullet point summary of the email
    agent: email_summarizer
    context:
      - reporting_task
      - research_task
```

## Fazendo o Deploy da Sua Tripulação

A forma mais fácil de fazer deploy da sua tripulação em produção é através da [CrewAI Enterprise](http://app.crewai.com).

Assista a este vídeo tutorial para uma demonstração detalhada de como fazer deploy da sua tripulação na [CrewAI Enterprise](http://app.crewai.com) usando a CLI.

<iframe
  width="100%"
  height="400"
  src="https://www.youtube.com/embed/3EqSV-CYDZA"
  title="CrewAI Deployment Guide"
  frameborder="0"
  style={{ borderRadius: '10px' }}
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
  allowfullscreen
></iframe>

<CardGroup cols={2}>
  <Card
    title="Deploy no Enterprise"
    icon="rocket"
    href="http://app.crewai.com"
  >
    Comece com o CrewAI Enterprise e faça o deploy da sua tripulação em ambiente de produção com apenas alguns cliques.
  </Card>
  <Card
    title="Junte-se à Comunidade"
    icon="comments"
    href="https://community.crewai.com"
  >
    Participe da nossa comunidade open source para discutir ideias, compartilhar seus projetos e conectar-se com outros desenvolvedores CrewAI.
  </Card>
</CardGroup>
