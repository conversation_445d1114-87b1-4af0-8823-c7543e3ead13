interactions:
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: dummy_tool\nTool
      Arguments: {''query'': {''description'': None, ''type'': ''str''}}\nTool Description:
      Useful for when you need to get a dummy result for a query.\n\nUse the following
      format:\n\nThought: you should always think about what to do\nAction: the action
      to take, only one name of [dummy_tool], just the name, exactly as it''s written.\nAction
      Input: the input to the action, just a simple python dictionary, enclosed in
      curly braces, using \" to wrap keys and values.\nObservation: the result of
      the action\n\nOnce all necessary information is gathered:\n\nThought: I now
      know the final answer\nFinal Answer: the final answer to the original input
      question"}, {"role": "user", "content": "\nCurrent Task: Use the dummy tool
      to get a result for ''test query''\n\nThis is the expect criteria for your final
      answer: The result from the dummy tool\nyou MUST return the actual complete
      content as the final answer, not a summary.\n\nBegin! This is VERY important
      to you, use the tools available and give your best Final Answer, your job depends
      on it!\n\nThought:"}], "model": "gpt-3.5-turbo", "stop": ["\nObservation:"],
      "stream": false}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1363'
      content-type:
      - application/json
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.52.1
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.52.1
      x-stainless-raw-response:
      - 'true'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AmjTkjHtNtJfKGo6wS35grXEzfoqv\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1736177928,\n  \"model\": \"gpt-3.5-turbo-0125\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"I should use the dummy tool to get a
      result for the 'test query'.\\n\\nAction: dummy_tool\\nAction Input: {\\\"query\\\":
      \\\"test query\\\"}\",\n        \"refusal\": null\n      },\n      \"logprobs\":
      null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      271,\n    \"completion_tokens\": 31,\n    \"total_tokens\": 302,\n    \"prompt_tokens_details\":
      {\n      \"cached_tokens\": 0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"system_fingerprint\":
      null\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8fdccc13af387bb2-ATL
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 06 Jan 2025 15:38:48 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=PdbRW9vzO7559czIqn0xmXQjbN8_vV_J7k1DlkB4d_Y-1736177928-*******-7yNcyljwqHI.TVflr9ZnkS705G.K5hgPbHpxRzcO3ZMFi5lHCBPs_KB5pFE043wYzPmDIHpn6fu6jIY9mlNoLQ;
        path=/; expires=Mon, 06-Jan-25 16:08:48 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=lOOz0FbrrPaRb4IFEeHNcj7QghHzxI1tTV2N0jD9icA-1736177928767-*******-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '444'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '50000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '49999686'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_5b3e93f5d4e6ab8feef83dc26b6eb623
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: dummy_tool\nTool
      Arguments: {''query'': {''description'': None, ''type'': ''str''}}\nTool Description:
      Useful for when you need to get a dummy result for a query.\n\nUse the following
      format:\n\nThought: you should always think about what to do\nAction: the action
      to take, only one name of [dummy_tool], just the name, exactly as it''s written.\nAction
      Input: the input to the action, just a simple python dictionary, enclosed in
      curly braces, using \" to wrap keys and values.\nObservation: the result of
      the action\n\nOnce all necessary information is gathered:\n\nThought: I now
      know the final answer\nFinal Answer: the final answer to the original input
      question"}, {"role": "user", "content": "\nCurrent Task: Use the dummy tool
      to get a result for ''test query''\n\nThis is the expect criteria for your final
      answer: The result from the dummy tool\nyou MUST return the actual complete
      content as the final answer, not a summary.\n\nBegin! This is VERY important
      to you, use the tools available and give your best Final Answer, your job depends
      on it!\n\nThought:"}, {"role": "assistant", "content": "I should use the dummy
      tool to get a result for the ''test query''.\n\nAction: dummy_tool\nAction Input:
      {\"query\": \"test query\"}\nObservation: Dummy result for: test query"}], "model":
      "gpt-3.5-turbo", "stop": ["\nObservation:"], "stream": false}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1574'
      content-type:
      - application/json
      cookie:
      - __cf_bm=PdbRW9vzO7559czIqn0xmXQjbN8_vV_J7k1DlkB4d_Y-1736177928-*******-7yNcyljwqHI.TVflr9ZnkS705G.K5hgPbHpxRzcO3ZMFi5lHCBPs_KB5pFE043wYzPmDIHpn6fu6jIY9mlNoLQ;
        _cfuvid=lOOz0FbrrPaRb4IFEeHNcj7QghHzxI1tTV2N0jD9icA-1736177928767-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.52.1
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.52.1
      x-stainless-raw-response:
      - 'true'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AmjTkjtDnt98YQ3k4y71C523EQM9p\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1736177928,\n  \"model\": \"gpt-3.5-turbo-0125\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Final Answer: Dummy result for: test
      query\",\n        \"refusal\": null\n      },\n      \"logprobs\": null,\n      \"finish_reason\":
      \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\": 315,\n    \"completion_tokens\":
      9,\n    \"total_tokens\": 324,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"system_fingerprint\":
      null\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8fdccc171b647bb2-ATL
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 06 Jan 2025 15:38:49 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '249'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '50000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '49999643'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_cdc7b25a3877bb9a7cb7c6d2645ff447
    http_version: HTTP/1.1
    status_code: 200
version: 1
