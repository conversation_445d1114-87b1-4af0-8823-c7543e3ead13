## CrewAI Security Vulnerability Reporting Policy

CrewAI prioritizes the security of our software products, services, and GitHub repositories. To promptly address vulnerabilities, follow these steps for reporting security issues:

### Reporting Process
Do **not** report vulnerabilities via public GitHub issues.

Email all vulnerability reports directly to:  
**<EMAIL>**

### Required Information
To help us quickly validate and remediate the issue, your report must include:

- **Vulnerability Type:** Clearly state the vulnerability type (e.g., SQL injection, XSS, privilege escalation).
- **Affected Source Code:** Provide full file paths and direct URLs (branch, tag, or commit).
- **Reproduction Steps:** Include detailed, step-by-step instructions. Screenshots are recommended.
- **Special Configuration:** Document any special settings or configurations required to reproduce.
- **Proof-of-Concept (PoC):** Provide exploit or PoC code (if available).
- **Impact Assessment:** Clearly explain the severity and potential exploitation scenarios.

### Our Response
- We will acknowledge receipt of your report promptly via your provided email.
- Confirmed vulnerabilities will receive priority remediation based on severity.
- Patches will be released as swiftly as possible following verification.

### Reward Notice
Currently, we do not offer a bug bounty program. Rewards, if issued, are discretionary.
