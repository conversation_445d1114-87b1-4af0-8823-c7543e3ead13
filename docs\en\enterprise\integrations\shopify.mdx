---
title: Shopify Integration
description: "E-commerce and online store management with Shopify integration for CrewAI."
icon: "shopify"
---

## Overview

Enable your agents to manage e-commerce operations through Shopify. Handle customers, orders, products, inventory, and store analytics to streamline your online business with AI-powered automation.

## Prerequisites

Before using the Shopify integration, ensure you have:

- A [CrewAI Enterprise](https://app.crewai.com) account with an active subscription
- A Shopify store with appropriate admin permissions
- Connected your Shopify store through the [Integrations page](https://app.crewai.com/integrations)

## Available Tools

### **Customer Management**

<AccordionGroup>
  <Accordion title="SHOPIFY_GET_CUSTOMERS">
    **Description:** Retrieve a list of customers from your Shopify store.

    **Parameters:**
    - `customerIds` (string, optional): Comma-separated list of customer IDs to filter by (example: "*********, *********")
    - `createdAtMin` (string, optional): Only return customers created after this date (ISO or Unix timestamp)
    - `createdAtMax` (string, optional): Only return customers created before this date (ISO or Unix timestamp)
    - `updatedAtMin` (string, optional): Only return customers updated after this date (ISO or Unix timestamp)
    - `updatedAtMax` (string, optional): Only return customers updated before this date (ISO or Unix timestamp)
    - `limit` (string, optional): Maximum number of customers to return (defaults to 250)
  </Accordion>

  <Accordion title="SHOPIFY_SEARCH_CUSTOMERS">
    **Description:** Search for customers using advanced filtering criteria.

    **Parameters:**
    - `filterFormula` (object, optional): Advanced filter in disjunctive normal form with field-specific operators
    - `limit` (string, optional): Maximum number of customers to return (defaults to 250)
  </Accordion>

  <Accordion title="SHOPIFY_CREATE_CUSTOMER">
    **Description:** Create a new customer in your Shopify store.

    **Parameters:**
    - `firstName` (string, required): Customer's first name
    - `lastName` (string, required): Customer's last name
    - `email` (string, required): Customer's email address
    - `company` (string, optional): Company name
    - `streetAddressLine1` (string, optional): Street address
    - `streetAddressLine2` (string, optional): Street address line 2
    - `city` (string, optional): City
    - `state` (string, optional): State or province code
    - `country` (string, optional): Country
    - `zipCode` (string, optional): Zip code
    - `phone` (string, optional): Phone number
    - `tags` (string, optional): Tags as array or comma-separated list
    - `note` (string, optional): Customer note
    - `sendEmailInvite` (boolean, optional): Whether to send email invitation
    - `metafields` (object, optional): Additional metafields in JSON format
  </Accordion>

  <Accordion title="SHOPIFY_UPDATE_CUSTOMER">
    **Description:** Update an existing customer in your Shopify store.

    **Parameters:**
    - `customerId` (string, required): The ID of the customer to update
    - `firstName` (string, optional): Customer's first name
    - `lastName` (string, optional): Customer's last name
    - `email` (string, optional): Customer's email address
    - `company` (string, optional): Company name
    - `streetAddressLine1` (string, optional): Street address
    - `streetAddressLine2` (string, optional): Street address line 2
    - `city` (string, optional): City
    - `state` (string, optional): State or province code
    - `country` (string, optional): Country
    - `zipCode` (string, optional): Zip code
    - `phone` (string, optional): Phone number
    - `tags` (string, optional): Tags as array or comma-separated list
    - `note` (string, optional): Customer note
    - `sendEmailInvite` (boolean, optional): Whether to send email invitation
    - `metafields` (object, optional): Additional metafields in JSON format
  </Accordion>
</AccordionGroup>

### **Order Management**

<AccordionGroup>
  <Accordion title="SHOPIFY_GET_ORDERS">
    **Description:** Retrieve a list of orders from your Shopify store.

    **Parameters:**
    - `orderIds` (string, optional): Comma-separated list of order IDs to filter by (example: "*********, *********")
    - `createdAtMin` (string, optional): Only return orders created after this date (ISO or Unix timestamp)
    - `createdAtMax` (string, optional): Only return orders created before this date (ISO or Unix timestamp)
    - `updatedAtMin` (string, optional): Only return orders updated after this date (ISO or Unix timestamp)
    - `updatedAtMax` (string, optional): Only return orders updated before this date (ISO or Unix timestamp)
    - `limit` (string, optional): Maximum number of orders to return (defaults to 250)
  </Accordion>

  <Accordion title="SHOPIFY_CREATE_ORDER">
    **Description:** Create a new order in your Shopify store.

    **Parameters:**
    - `email` (string, required): Customer email address
    - `lineItems` (object, required): Order line items in JSON format with title, price, quantity, and variant_id
    - `sendReceipt` (boolean, optional): Whether to send order receipt
    - `fulfillmentStatus` (string, optional): Fulfillment status - Options: fulfilled, null, partial, restocked
    - `financialStatus` (string, optional): Financial status - Options: pending, authorized, partially_paid, paid, partially_refunded, refunded, voided
    - `inventoryBehaviour` (string, optional): Inventory behavior - Options: bypass, decrement_ignoring_policy, decrement_obeying_policy
    - `note` (string, optional): Order note
  </Accordion>

  <Accordion title="SHOPIFY_UPDATE_ORDER">
    **Description:** Update an existing order in your Shopify store.

    **Parameters:**
    - `orderId` (string, required): The ID of the order to update
    - `email` (string, optional): Customer email address
    - `lineItems` (object, optional): Updated order line items in JSON format
    - `sendReceipt` (boolean, optional): Whether to send order receipt
    - `fulfillmentStatus` (string, optional): Fulfillment status - Options: fulfilled, null, partial, restocked
    - `financialStatus` (string, optional): Financial status - Options: pending, authorized, partially_paid, paid, partially_refunded, refunded, voided
    - `inventoryBehaviour` (string, optional): Inventory behavior - Options: bypass, decrement_ignoring_policy, decrement_obeying_policy
    - `note` (string, optional): Order note
  </Accordion>

  <Accordion title="SHOPIFY_GET_ABANDONED_CARTS">
    **Description:** Retrieve abandoned carts from your Shopify store.

    **Parameters:**
    - `createdWithInLast` (string, optional): Restrict results to checkouts created within specified time
    - `createdAfterId` (string, optional): Restrict results to after the specified ID
    - `status` (string, optional): Show checkouts with given status - Options: open, closed (defaults to open)
    - `createdAtMin` (string, optional): Only return carts created after this date (ISO or Unix timestamp)
    - `createdAtMax` (string, optional): Only return carts created before this date (ISO or Unix timestamp)
    - `limit` (string, optional): Maximum number of carts to return (defaults to 250)
  </Accordion>
</AccordionGroup>

### **Product Management (REST API)**

<AccordionGroup>
  <Accordion title="SHOPIFY_GET_PRODUCTS">
    **Description:** Retrieve a list of products from your Shopify store using REST API.

    **Parameters:**
    - `productIds` (string, optional): Comma-separated list of product IDs to filter by (example: "632910392, 632910393")
    - `title` (string, optional): Filter by product title
    - `productType` (string, optional): Filter by product type
    - `vendor` (string, optional): Filter by vendor
    - `status` (string, optional): Filter by status - Options: active, archived, draft
    - `createdAtMin` (string, optional): Only return products created after this date (ISO or Unix timestamp)
    - `createdAtMax` (string, optional): Only return products created before this date (ISO or Unix timestamp)
    - `updatedAtMin` (string, optional): Only return products updated after this date (ISO or Unix timestamp)
    - `updatedAtMax` (string, optional): Only return products updated before this date (ISO or Unix timestamp)
    - `limit` (string, optional): Maximum number of products to return (defaults to 250)
  </Accordion>

  <Accordion title="SHOPIFY_CREATE_PRODUCT">
    **Description:** Create a new product in your Shopify store using REST API.

    **Parameters:**
    - `title` (string, required): Product title
    - `productType` (string, required): Product type/category
    - `vendor` (string, required): Product vendor
    - `productDescription` (string, optional): Product description (accepts plain text or HTML)
    - `tags` (string, optional): Product tags as array or comma-separated list
    - `price` (string, optional): Product price
    - `inventoryPolicy` (string, optional): Inventory policy - Options: deny, continue
    - `imageUrl` (string, optional): Product image URL
    - `isPublished` (boolean, optional): Whether product is published
    - `publishToPointToSale` (boolean, optional): Whether to publish to point of sale
  </Accordion>

  <Accordion title="SHOPIFY_UPDATE_PRODUCT">
    **Description:** Update an existing product in your Shopify store using REST API.

    **Parameters:**
    - `productId` (string, required): The ID of the product to update
    - `title` (string, optional): Product title
    - `productType` (string, optional): Product type/category
    - `vendor` (string, optional): Product vendor
    - `productDescription` (string, optional): Product description (accepts plain text or HTML)
    - `tags` (string, optional): Product tags as array or comma-separated list
    - `price` (string, optional): Product price
    - `inventoryPolicy` (string, optional): Inventory policy - Options: deny, continue
    - `imageUrl` (string, optional): Product image URL
    - `isPublished` (boolean, optional): Whether product is published
    - `publishToPointToSale` (boolean, optional): Whether to publish to point of sale
  </Accordion>
</AccordionGroup>

### **Product Management (GraphQL)**

<AccordionGroup>
  <Accordion title="SHOPIFY_GET_PRODUCTS_GRAPHQL">
    **Description:** Retrieve products using advanced GraphQL filtering capabilities.

    **Parameters:**
    - `productFilterFormula` (object, optional): Advanced filter in disjunctive normal form with support for fields like id, title, vendor, status, handle, tag, created_at, updated_at, published_at
  </Accordion>

  <Accordion title="SHOPIFY_CREATE_PRODUCT_GRAPHQL">
    **Description:** Create a new product using GraphQL API with enhanced media support.

    **Parameters:**
    - `title` (string, required): Product title
    - `productType` (string, required): Product type/category
    - `vendor` (string, required): Product vendor
    - `productDescription` (string, optional): Product description (accepts plain text or HTML)
    - `tags` (string, optional): Product tags as array or comma-separated list
    - `media` (object, optional): Media objects with alt text, content type, and source URL
    - `additionalFields` (object, optional): Additional product fields like status, requiresSellingPlan, giftCard
  </Accordion>

  <Accordion title="SHOPIFY_UPDATE_PRODUCT_GRAPHQL">
    **Description:** Update an existing product using GraphQL API with enhanced media support.

    **Parameters:**
    - `productId` (string, required): The GraphQL ID of the product to update (e.g., "gid://shopify/Product/913144112")
    - `title` (string, optional): Product title
    - `productType` (string, optional): Product type/category
    - `vendor` (string, optional): Product vendor
    - `productDescription` (string, optional): Product description (accepts plain text or HTML)
    - `tags` (string, optional): Product tags as array or comma-separated list
    - `media` (object, optional): Updated media objects with alt text, content type, and source URL
    - `additionalFields` (object, optional): Additional product fields like status, requiresSellingPlan, giftCard
  </Accordion>
</AccordionGroup>

## Usage Examples

### Basic Shopify Agent Setup

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

# Get enterprise tools (Shopify tools will be included)
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

# Create an agent with Shopify capabilities
shopify_agent = Agent(
    role="E-commerce Manager",
    goal="Manage online store operations and customer relationships efficiently",
    backstory="An AI assistant specialized in e-commerce operations and online store management.",
    tools=[enterprise_tools]
)

# Task to create a new customer
create_customer_task = Task(
    description="Create a new VIP customer Jane Smith <NAME_EMAIL> and phone ******-0123",
    agent=shopify_agent,
    expected_output="Customer created successfully with customer ID"
)

# Run the task
crew = Crew(
    agents=[shopify_agent],
    tasks=[create_customer_task]
)

crew.kickoff()
```

### Filtering Specific Shopify Tools

```python
from crewai_tools import CrewaiEnterpriseTools

# Get only specific Shopify tools
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token",
    actions_list=["shopify_create_customer", "shopify_create_order", "shopify_get_products"]
)

store_manager = Agent(
    role="Store Manager",
    goal="Manage customer orders and product catalog",
    backstory="An experienced store manager who handles customer relationships and inventory management.",
    tools=enterprise_tools
)

# Task to manage store operations
store_task = Task(
    description="Create a new customer and process their order for 2 Premium Coffee Mugs",
    agent=store_manager,
    expected_output="Customer created and order processed successfully"
)

crew = Crew(
    agents=[store_manager],
    tasks=[store_task]
)

crew.kickoff()
```

### Product Management with GraphQL

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

product_manager = Agent(
    role="Product Manager",
    goal="Manage product catalog and inventory with advanced GraphQL capabilities",
    backstory="An AI assistant that specializes in product management and catalog optimization.",
    tools=[enterprise_tools]
)

# Task to manage product catalog
catalog_task = Task(
    description="""
    1. Create a new product "Premium Coffee Mug" from Coffee Co vendor
    2. Add high-quality product images and descriptions
    3. Search for similar products from the same vendor
    4. Update product tags and pricing strategy
    """,
    agent=product_manager,
    expected_output="Product created and catalog optimized successfully"
)

crew = Crew(
    agents=[product_manager],
    tasks=[catalog_task]
)

crew.kickoff()
```

### Order and Customer Analytics

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

analytics_agent = Agent(
    role="E-commerce Analyst",
    goal="Analyze customer behavior and order patterns to optimize store performance",
    backstory="An analytical AI that excels at extracting insights from e-commerce data.",
    tools=[enterprise_tools]
)

# Complex task involving multiple operations
analytics_task = Task(
    description="""
    1. Retrieve recent customer data and order history
    2. Identify abandoned carts from the last 7 days
    3. Analyze product performance and inventory levels
    4. Generate recommendations for customer retention
    """,
    agent=analytics_agent,
    expected_output="Comprehensive e-commerce analytics report with actionable insights"
)

crew = Crew(
    agents=[analytics_agent],
    tasks=[analytics_task]
)

crew.kickoff()
```

### Getting Help

<Card title="Need Help?" icon="headset" href="mailto:<EMAIL>">
  Contact our support team for assistance with Shopify integration setup or troubleshooting.
</Card>
