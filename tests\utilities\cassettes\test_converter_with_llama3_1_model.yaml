interactions:
- request:
    body: '{"name": "llama3.2:3b"}'
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '23'
      content-type:
      - application/json
      host:
      - localhost:11434
      user-agent:
      - litellm/1.60.2
    method: POST
    uri: http://localhost:11434/api/show
  response:
    content: "{\"license\":\"LLAMA 3.2 COMMUNITY LICENSE AGREEMENT\\nLlama 3.2 Version
      Release Date: September 25, 2024\\n\\n\u201CAgreement\u201D means the terms
      and conditions for use, reproduction, distribution \\nand modification of the
      Llama Materials set forth herein.\\n\\n\u201CDocumentation\u201D means the specifications,
      manuals and documentation accompanying Llama 3.2\\ndistributed by Meta at https://llama.meta.com/doc/overview.\\n\\n\u201CLicensee\u201D
      or \u201Cyou\u201D means you, or your employer or any other person or entity
      (if you are \\nentering into this Agreement on such person or entity\u2019s
      behalf), of the age required under\\napplicable laws, rules or regulations to
      provide legal consent and that has legal authority\\nto bind your employer or
      such other person or entity if you are entering in this Agreement\\non their
      behalf.\\n\\n\u201CLlama 3.2\u201D means the foundational large language models
      and software and algorithms, including\\nmachine-learning model code, trained
      model weights, inference-enabling code, training-enabling code,\\nfine-tuning
      enabling code and other elements of the foregoing distributed by Meta at \\nhttps://www.llama.com/llama-downloads.\\n\\n\u201CLlama
      Materials\u201D means, collectively, Meta\u2019s proprietary Llama 3.2 and Documentation
      (and \\nany portion thereof) made available under this Agreement.\\n\\n\u201CMeta\u201D
      or \u201Cwe\u201D means Meta Platforms Ireland Limited (if you are located in
      or, \\nif you are an entity, your principal place of business is in the EEA
      or Switzerland) \\nand Meta Platforms, Inc. (if you are located outside of the
      EEA or Switzerland). \\n\\n\\nBy clicking \u201CI Accept\u201D below or by using
      or distributing any portion or element of the Llama Materials,\\nyou agree to
      be bound by this Agreement.\\n\\n\\n1. License Rights and Redistribution.\\n\\n
      \   a. Grant of Rights. You are granted a non-exclusive, worldwide, \\nnon-transferable
      and royalty-free limited license under Meta\u2019s intellectual property or
      other rights \\nowned by Meta embodied in the Llama Materials to use, reproduce,
      distribute, copy, create derivative works \\nof, and make modifications to the
      Llama Materials.  \\n\\n    b. Redistribution and Use.  \\n\\n        i. If
      you distribute or make available the Llama Materials (or any derivative works
      thereof), \\nor a product or service (including another AI model) that contains
      any of them, you shall (A) provide\\na copy of this Agreement with any such
      Llama Materials; and (B) prominently display \u201CBuilt with Llama\u201D\\non
      a related website, user interface, blogpost, about page, or product documentation.
      If you use the\\nLlama Materials or any outputs or results of the Llama Materials
      to create, train, fine tune, or\\notherwise improve an AI model, which is distributed
      or made available, you shall also include \u201CLlama\u201D\\nat the beginning
      of any such AI model name.\\n\\n        ii. If you receive Llama Materials,
      or any derivative works thereof, from a Licensee as part\\nof an integrated
      end user product, then Section 2 of this Agreement will not apply to you. \\n\\n
      \       iii. You must retain in all copies of the Llama Materials that you distribute
      the \\nfollowing attribution notice within a \u201CNotice\u201D text file distributed
      as a part of such copies: \\n\u201CLlama 3.2 is licensed under the Llama 3.2
      Community License, Copyright \xA9 Meta Platforms,\\nInc. All Rights Reserved.\u201D\\n\\n
      \       iv. Your use of the Llama Materials must comply with applicable laws
      and regulations\\n(including trade compliance laws and regulations) and adhere
      to the Acceptable Use Policy for\\nthe Llama Materials (available at https://www.llama.com/llama3_2/use-policy),
      which is hereby \\nincorporated by reference into this Agreement.\\n  \\n2.
      Additional Commercial Terms. If, on the Llama 3.2 version release date, the
      monthly active users\\nof the products or services made available by or for
      Licensee, or Licensee\u2019s affiliates, \\nis greater than 700 million monthly
      active users in the preceding calendar month, you must request \\na license
      from Meta, which Meta may grant to you in its sole discretion, and you are not
      authorized to\\nexercise any of the rights under this Agreement unless or until
      Meta otherwise expressly grants you such rights.\\n\\n3. Disclaimer of Warranty.
      UNLESS REQUIRED BY APPLICABLE LAW, THE LLAMA MATERIALS AND ANY OUTPUT AND \\nRESULTS
      THEREFROM ARE PROVIDED ON AN \u201CAS IS\u201D BASIS, WITHOUT WARRANTIES OF
      ANY KIND, AND META DISCLAIMS\\nALL WARRANTIES OF ANY KIND, BOTH EXPRESS AND
      IMPLIED, INCLUDING, WITHOUT LIMITATION, ANY WARRANTIES\\nOF TITLE, NON-INFRINGEMENT,
      MERCHANTABILITY, OR FITNESS FOR A PARTICULAR PURPOSE. YOU ARE SOLELY RESPONSIBLE\\nFOR
      DETERMINING THE APPROPRIATENESS OF USING OR REDISTRIBUTING THE LLAMA MATERIALS
      AND ASSUME ANY RISKS ASSOCIATED\\nWITH YOUR USE OF THE LLAMA MATERIALS AND ANY
      OUTPUT AND RESULTS.\\n\\n4. Limitation of Liability. IN NO EVENT WILL META OR
      ITS AFFILIATES BE LIABLE UNDER ANY THEORY OF LIABILITY, \\nWHETHER IN CONTRACT,
      TORT, NEGLIGENCE, PRODUCTS LIABILITY, OR OTHERWISE, ARISING OUT OF THIS AGREEMENT,
      \\nFOR ANY LOST PROFITS OR ANY INDIRECT, SPECIAL, CONSEQUENTIAL, INCIDENTAL,
      EXEMPLARY OR PUNITIVE DAMAGES, EVEN \\nIF META OR ITS AFFILIATES HAVE BEEN ADVISED
      OF THE POSSIBILITY OF ANY OF THE FOREGOING.\\n\\n5. Intellectual Property.\\n\\n
      \   a. No trademark licenses are granted under this Agreement, and in connection
      with the Llama Materials, \\nneither Meta nor Licensee may use any name or mark
      owned by or associated with the other or any of its affiliates, \\nexcept as
      required for reasonable and customary use in describing and redistributing the
      Llama Materials or as \\nset forth in this Section 5(a). Meta hereby grants
      you a license to use \u201CLlama\u201D (the \u201CMark\u201D) solely as required
      \\nto comply with the last sentence of Section 1.b.i. You will comply with Meta\u2019s
      brand guidelines (currently accessible \\nat https://about.meta.com/brand/resources/meta/company-brand/).
      All goodwill arising out of your use of the Mark \\nwill inure to the benefit
      of Meta.\\n\\n    b. Subject to Meta\u2019s ownership of Llama Materials and
      derivatives made by or for Meta, with respect to any\\n    derivative works
      and modifications of the Llama Materials that are made by you, as between you
      and Meta,\\n    you are and will be the owner of such derivative works and modifications.\\n\\n
      \   c. If you institute litigation or other proceedings against Meta or any
      entity (including a cross-claim or\\n    counterclaim in a lawsuit) alleging
      that the Llama Materials or Llama 3.2 outputs or results, or any portion\\n
      \   of any of the foregoing, constitutes infringement of intellectual property
      or other rights owned or licensable\\n    by you, then any licenses granted
      to you under this Agreement shall terminate as of the date such litigation or\\n
      \   claim is filed or instituted. You will indemnify and hold harmless Meta
      from and against any claim by any third\\n    party arising out of or related
      to your use or distribution of the Llama Materials.\\n\\n6. Term and Termination.
      The term of this Agreement will commence upon your acceptance of this Agreement
      or access\\nto the Llama Materials and will continue in full force and effect
      until terminated in accordance with the terms\\nand conditions herein. Meta
      may terminate this Agreement if you are in breach of any term or condition of
      this\\nAgreement. Upon termination of this Agreement, you shall delete and cease
      use of the Llama Materials. Sections 3,\\n4 and 7 shall survive the termination
      of this Agreement. \\n\\n7. Governing Law and Jurisdiction. This Agreement will
      be governed and construed under the laws of the State of \\nCalifornia without
      regard to choice of law principles, and the UN Convention on Contracts for the
      International\\nSale of Goods does not apply to this Agreement. The courts of
      California shall have exclusive jurisdiction of\\nany dispute arising out of
      this Agreement.\\n**Llama 3.2** **Acceptable Use Policy**\\n\\nMeta is committed
      to promoting safe and fair use of its tools and features, including Llama 3.2.
      If you access or use Llama 3.2, you agree to this Acceptable Use Policy (\u201C**Policy**\u201D).
      The most recent copy of this policy can be found at [https://www.llama.com/llama3_2/use-policy](https://www.llama.com/llama3_2/use-policy).\\n\\n**Prohibited
      Uses**\\n\\nWe want everyone to use Llama 3.2 safely and responsibly. You agree
      you will not use, or allow others to use, Llama 3.2 to:\\n\\n\\n\\n1. Violate
      the law or others\u2019 rights, including to:\\n    1. Engage in, promote, generate,
      contribute to, encourage, plan, incite, or further illegal or unlawful activity
      or content, such as:\\n        1. Violence or terrorism\\n        2. Exploitation
      or harm to children, including the solicitation, creation, acquisition, or dissemination
      of child exploitative content or failure to report Child Sexual Abuse Material\\n
      \       3. Human trafficking, exploitation, and sexual violence\\n        4.
      The illegal distribution of information or materials to minors, including obscene
      materials, or failure to employ legally required age-gating in connection with
      such information or materials.\\n        5. Sexual solicitation\\n        6.
      Any other criminal activity\\n    1. Engage in, promote, incite, or facilitate
      the harassment, abuse, threatening, or bullying of individuals or groups of
      individuals\\n    2. Engage in, promote, incite, or facilitate discrimination
      or other unlawful or harmful conduct in the provision of employment, employment
      benefits, credit, housing, other economic benefits, or other essential goods
      and services\\n    3. Engage in the unauthorized or unlicensed practice of any
      profession including, but not limited to, financial, legal, medical/health,
      or related professional practices\\n    4. Collect, process, disclose, generate,
      or infer private or sensitive information about individuals, including information
      about individuals\u2019 identity, health, or demographic information, unless
      you have obtained the right to do so in accordance with applicable law\\n    5.
      Engage in or facilitate any action or generate any content that infringes, misappropriates,
      or otherwise violates any third-party rights, including the outputs or results
      of any products or services using the Llama Materials\\n    6. Create, generate,
      or facilitate the creation of malicious code, malware, computer viruses or do
      anything else that could disable, overburden, interfere with or impair the proper
      working, integrity, operation or appearance of a website or computer system\\n
      \   7. Engage in any action, or facilitate any action, to intentionally circumvent
      or remove usage restrictions or other safety measures, or to enable functionality
      disabled by Meta\\n2. Engage in, promote, incite, facilitate, or assist in the
      planning or development of activities that present a risk of death or bodily
      harm to individuals, including use of Llama 3.2 related to the following:\\n
      \   8. Military, warfare, nuclear industries or applications, espionage, use
      for materials or activities that are subject to the International Traffic Arms
      Regulations (ITAR) maintained by the United States Department of State or to
      the U.S. Biological Weapons Anti-Terrorism Act of 1989 or the Chemical Weapons
      Convention Implementation Act of 1997\\n    9. Guns and illegal weapons (including
      weapon development)\\n    10. Illegal drugs and regulated/controlled substances\\n
      \   11. Operation of critical infrastructure, transportation technologies, or
      heavy machinery\\n    12. Self-harm or harm to others, including suicide, cutting,
      and eating disorders\\n    13. Any content intended to incite or promote violence,
      abuse, or any infliction of bodily harm to an individual\\n3. Intentionally
      deceive or mislead others, including use of Llama 3.2 related to the following:\\n
      \   14. Generating, promoting, or furthering fraud or the creation or promotion
      of disinformation\\n    15. Generating, promoting, or furthering defamatory
      content, including the creation of defamatory statements, images, or other content\\n
      \   16. Generating, promoting, or further distributing spam\\n    17. Impersonating
      another individual without consent, authorization, or legal right\\n    18.
      Representing that the use of Llama 3.2 or outputs are human-generated\\n    19.
      Generating or facilitating false online engagement, including fake reviews and
      other means of fake online engagement\\n4. Fail to appropriately disclose to
      end users any known dangers of your AI system\\n5. Interact with third party
      tools, models, or software designed to generate unlawful content or engage in
      unlawful or harmful conduct and/or represent that the outputs of such tools,
      models, or software are associated with Meta or Llama 3.2\\n\\nWith respect
      to any multimodal models included in Llama 3.2, the rights granted under Section
      1(a) of the Llama 3.2 Community License Agreement are not being granted to you
      if you are an individual domiciled in, or a company with a principal place of
      business in, the European Union. This restriction does not apply to end users
      of a product or service that incorporates any such multimodal models.\\n\\nPlease
      report any violation of this Policy, software \u201Cbug,\u201D or other problems
      that could lead to a violation of this Policy through one of the following means:\\n\\n\\n\\n*
      Reporting issues with the model: [https://github.com/meta-llama/llama-models/issues](https://l.workplace.com/l.php?u=https%3A%2F%2Fgithub.com%2Fmeta-llama%2Fllama-models%2Fissues\\u0026h=AT0qV8W9BFT6NwihiOHRuKYQM_UnkzN_NmHMy91OT55gkLpgi4kQupHUl0ssR4dQsIQ8n3tfd0vtkobvsEvt1l4Ic6GXI2EeuHV8N08OG2WnbAmm0FL4ObkazC6G_256vN0lN9DsykCvCqGZ)\\n*
      Reporting risky content generated by the model: [developers.facebook.com/llama_output_feedback](http://developers.facebook.com/llama_output_feedback)\\n*
      Reporting bugs and security concerns: [facebook.com/whitehat/info](http://facebook.com/whitehat/info)\\n*
      Reporting violations of the Acceptable Use Policy or unlicensed uses of Llama
      3.2: <EMAIL>\",\"modelfile\":\"# Modelfile generated by \\\"ollama
      show\\\"\\n# To build a new Modelfile based on this, replace FROM with:\\n#
      FROM llama3.2:3b\\n\\nFROM /Users/<USER>/.ollama/models/blobs/sha256-dde5aa3fc5ffc17176b5e8bdc82f587b24b2678c6c66101bf7da77af9f7ccdff\\nTEMPLATE
      \\\"\\\"\\\"\\u003c|start_header_id|\\u003esystem\\u003c|end_header_id|\\u003e\\n\\nCutting
      Knowledge Date: December 2023\\n\\n{{ if .System }}{{ .System }}\\n{{- end }}\\n{{-
      if .Tools }}When you receive a tool call response, use the output to format
      an answer to the orginal user question.\\n\\nYou are a helpful assistant with
      tool calling capabilities.\\n{{- end }}\\u003c|eot_id|\\u003e\\n{{- range $i,
      $_ := .Messages }}\\n{{- $last := eq (len (slice $.Messages $i)) 1 }}\\n{{-
      if eq .Role \\\"user\\\" }}\\u003c|start_header_id|\\u003euser\\u003c|end_header_id|\\u003e\\n{{-
      if and $.Tools $last }}\\n\\nGiven the following functions, please respond with
      a JSON for a function call with its proper arguments that best answers the given
      prompt.\\n\\nRespond in the format {\\\"name\\\": function name, \\\"parameters\\\":
      dictionary of argument name and its value}. Do not use variables.\\n\\n{{ range
      $.Tools }}\\n{{- . }}\\n{{ end }}\\n{{ .Content }}\\u003c|eot_id|\\u003e\\n{{-
      else }}\\n\\n{{ .Content }}\\u003c|eot_id|\\u003e\\n{{- end }}{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
      end }}\\n{{- else if eq .Role \\\"assistant\\\" }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n{{-
      if .ToolCalls }}\\n{{ range .ToolCalls }}\\n{\\\"name\\\": \\\"{{ .Function.Name
      }}\\\", \\\"parameters\\\": {{ .Function.Arguments }}}{{ end }}\\n{{- else }}\\n\\n{{
      .Content }}\\n{{- end }}{{ if not $last }}\\u003c|eot_id|\\u003e{{ end }}\\n{{-
      else if eq .Role \\\"tool\\\" }}\\u003c|start_header_id|\\u003eipython\\u003c|end_header_id|\\u003e\\n\\n{{
      .Content }}\\u003c|eot_id|\\u003e{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
      end }}\\n{{- end }}\\n{{- end }}\\\"\\\"\\\"\\nPARAMETER stop \\u003c|start_header_id|\\u003e\\nPARAMETER
      stop \\u003c|end_header_id|\\u003e\\nPARAMETER stop \\u003c|eot_id|\\u003e\\nLICENSE
      \\\"LLAMA 3.2 COMMUNITY LICENSE AGREEMENT\\nLlama 3.2 Version Release Date:
      September 25, 2024\\n\\n\u201CAgreement\u201D means the terms and conditions
      for use, reproduction, distribution \\nand modification of the Llama Materials
      set forth herein.\\n\\n\u201CDocumentation\u201D means the specifications, manuals
      and documentation accompanying Llama 3.2\\ndistributed by Meta at https://llama.meta.com/doc/overview.\\n\\n\u201CLicensee\u201D
      or \u201Cyou\u201D means you, or your employer or any other person or entity
      (if you are \\nentering into this Agreement on such person or entity\u2019s
      behalf), of the age required under\\napplicable laws, rules or regulations to
      provide legal consent and that has legal authority\\nto bind your employer or
      such other person or entity if you are entering in this Agreement\\non their
      behalf.\\n\\n\u201CLlama 3.2\u201D means the foundational large language models
      and software and algorithms, including\\nmachine-learning model code, trained
      model weights, inference-enabling code, training-enabling code,\\nfine-tuning
      enabling code and other elements of the foregoing distributed by Meta at \\nhttps://www.llama.com/llama-downloads.\\n\\n\u201CLlama
      Materials\u201D means, collectively, Meta\u2019s proprietary Llama 3.2 and Documentation
      (and \\nany portion thereof) made available under this Agreement.\\n\\n\u201CMeta\u201D
      or \u201Cwe\u201D means Meta Platforms Ireland Limited (if you are located in
      or, \\nif you are an entity, your principal place of business is in the EEA
      or Switzerland) \\nand Meta Platforms, Inc. (if you are located outside of the
      EEA or Switzerland). \\n\\n\\nBy clicking \u201CI Accept\u201D below or by using
      or distributing any portion or element of the Llama Materials,\\nyou agree to
      be bound by this Agreement.\\n\\n\\n1. License Rights and Redistribution.\\n\\n
      \   a. Grant of Rights. You are granted a non-exclusive, worldwide, \\nnon-transferable
      and royalty-free limited license under Meta\u2019s intellectual property or
      other rights \\nowned by Meta embodied in the Llama Materials to use, reproduce,
      distribute, copy, create derivative works \\nof, and make modifications to the
      Llama Materials.  \\n\\n    b. Redistribution and Use.  \\n\\n        i. If
      you distribute or make available the Llama Materials (or any derivative works
      thereof), \\nor a product or service (including another AI model) that contains
      any of them, you shall (A) provide\\na copy of this Agreement with any such
      Llama Materials; and (B) prominently display \u201CBuilt with Llama\u201D\\non
      a related website, user interface, blogpost, about page, or product documentation.
      If you use the\\nLlama Materials or any outputs or results of the Llama Materials
      to create, train, fine tune, or\\notherwise improve an AI model, which is distributed
      or made available, you shall also include \u201CLlama\u201D\\nat the beginning
      of any such AI model name.\\n\\n        ii. If you receive Llama Materials,
      or any derivative works thereof, from a Licensee as part\\nof an integrated
      end user product, then Section 2 of this Agreement will not apply to you. \\n\\n
      \       iii. You must retain in all copies of the Llama Materials that you distribute
      the \\nfollowing attribution notice within a \u201CNotice\u201D text file distributed
      as a part of such copies: \\n\u201CLlama 3.2 is licensed under the Llama 3.2
      Community License, Copyright \xA9 Meta Platforms,\\nInc. All Rights Reserved.\u201D\\n\\n
      \       iv. Your use of the Llama Materials must comply with applicable laws
      and regulations\\n(including trade compliance laws and regulations) and adhere
      to the Acceptable Use Policy for\\nthe Llama Materials (available at https://www.llama.com/llama3_2/use-policy),
      which is hereby \\nincorporated by reference into this Agreement.\\n  \\n2.
      Additional Commercial Terms. If, on the Llama 3.2 version release date, the
      monthly active users\\nof the products or services made available by or for
      Licensee, or Licensee\u2019s affiliates, \\nis greater than 700 million monthly
      active users in the preceding calendar month, you must request \\na license
      from Meta, which Meta may grant to you in its sole discretion, and you are not
      authorized to\\nexercise any of the rights under this Agreement unless or until
      Meta otherwise expressly grants you such rights.\\n\\n3. Disclaimer of Warranty.
      UNLESS REQUIRED BY APPLICABLE LAW, THE LLAMA MATERIALS AND ANY OUTPUT AND \\nRESULTS
      THEREFROM ARE PROVIDED ON AN \u201CAS IS\u201D BASIS, WITHOUT WARRANTIES OF
      ANY KIND, AND META DISCLAIMS\\nALL WARRANTIES OF ANY KIND, BOTH EXPRESS AND
      IMPLIED, INCLUDING, WITHOUT LIMITATION, ANY WARRANTIES\\nOF TITLE, NON-INFRINGEMENT,
      MERCHANTABILITY, OR FITNESS FOR A PARTICULAR PURPOSE. YOU ARE SOLELY RESPONSIBLE\\nFOR
      DETERMINING THE APPROPRIATENESS OF USING OR REDISTRIBUTING THE LLAMA MATERIALS
      AND ASSUME ANY RISKS ASSOCIATED\\nWITH YOUR USE OF THE LLAMA MATERIALS AND ANY
      OUTPUT AND RESULTS.\\n\\n4. Limitation of Liability. IN NO EVENT WILL META OR
      ITS AFFILIATES BE LIABLE UNDER ANY THEORY OF LIABILITY, \\nWHETHER IN CONTRACT,
      TORT, NEGLIGENCE, PRODUCTS LIABILITY, OR OTHERWISE, ARISING OUT OF THIS AGREEMENT,
      \\nFOR ANY LOST PROFITS OR ANY INDIRECT, SPECIAL, CONSEQUENTIAL, INCIDENTAL,
      EXEMPLARY OR PUNITIVE DAMAGES, EVEN \\nIF META OR ITS AFFILIATES HAVE BEEN ADVISED
      OF THE POSSIBILITY OF ANY OF THE FOREGOING.\\n\\n5. Intellectual Property.\\n\\n
      \   a. No trademark licenses are granted under this Agreement, and in connection
      with the Llama Materials, \\nneither Meta nor Licensee may use any name or mark
      owned by or associated with the other or any of its affiliates, \\nexcept as
      required for reasonable and customary use in describing and redistributing the
      Llama Materials or as \\nset forth in this Section 5(a). Meta hereby grants
      you a license to use \u201CLlama\u201D (the \u201CMark\u201D) solely as required
      \\nto comply with the last sentence of Section 1.b.i. You will comply with Meta\u2019s
      brand guidelines (currently accessible \\nat https://about.meta.com/brand/resources/meta/company-brand/).
      All goodwill arising out of your use of the Mark \\nwill inure to the benefit
      of Meta.\\n\\n    b. Subject to Meta\u2019s ownership of Llama Materials and
      derivatives made by or for Meta, with respect to any\\n    derivative works
      and modifications of the Llama Materials that are made by you, as between you
      and Meta,\\n    you are and will be the owner of such derivative works and modifications.\\n\\n
      \   c. If you institute litigation or other proceedings against Meta or any
      entity (including a cross-claim or\\n    counterclaim in a lawsuit) alleging
      that the Llama Materials or Llama 3.2 outputs or results, or any portion\\n
      \   of any of the foregoing, constitutes infringement of intellectual property
      or other rights owned or licensable\\n    by you, then any licenses granted
      to you under this Agreement shall terminate as of the date such litigation or\\n
      \   claim is filed or instituted. You will indemnify and hold harmless Meta
      from and against any claim by any third\\n    party arising out of or related
      to your use or distribution of the Llama Materials.\\n\\n6. Term and Termination.
      The term of this Agreement will commence upon your acceptance of this Agreement
      or access\\nto the Llama Materials and will continue in full force and effect
      until terminated in accordance with the terms\\nand conditions herein. Meta
      may terminate this Agreement if you are in breach of any term or condition of
      this\\nAgreement. Upon termination of this Agreement, you shall delete and cease
      use of the Llama Materials. Sections 3,\\n4 and 7 shall survive the termination
      of this Agreement. \\n\\n7. Governing Law and Jurisdiction. This Agreement will
      be governed and construed under the laws of the State of \\nCalifornia without
      regard to choice of law principles, and the UN Convention on Contracts for the
      International\\nSale of Goods does not apply to this Agreement. The courts of
      California shall have exclusive jurisdiction of\\nany dispute arising out of
      this Agreement.\\\"\\nLICENSE \\\"**Llama 3.2** **Acceptable Use Policy**\\n\\nMeta
      is committed to promoting safe and fair use of its tools and features, including
      Llama 3.2. If you access or use Llama 3.2, you agree to this Acceptable Use
      Policy (\u201C**Policy**\u201D). The most recent copy of this policy can be
      found at [https://www.llama.com/llama3_2/use-policy](https://www.llama.com/llama3_2/use-policy).\\n\\n**Prohibited
      Uses**\\n\\nWe want everyone to use Llama 3.2 safely and responsibly. You agree
      you will not use, or allow others to use, Llama 3.2 to:\\n\\n\\n\\n1. Violate
      the law or others\u2019 rights, including to:\\n    1. Engage in, promote, generate,
      contribute to, encourage, plan, incite, or further illegal or unlawful activity
      or content, such as:\\n        1. Violence or terrorism\\n        2. Exploitation
      or harm to children, including the solicitation, creation, acquisition, or dissemination
      of child exploitative content or failure to report Child Sexual Abuse Material\\n
      \       3. Human trafficking, exploitation, and sexual violence\\n        4.
      The illegal distribution of information or materials to minors, including obscene
      materials, or failure to employ legally required age-gating in connection with
      such information or materials.\\n        5. Sexual solicitation\\n        6.
      Any other criminal activity\\n    1. Engage in, promote, incite, or facilitate
      the harassment, abuse, threatening, or bullying of individuals or groups of
      individuals\\n    2. Engage in, promote, incite, or facilitate discrimination
      or other unlawful or harmful conduct in the provision of employment, employment
      benefits, credit, housing, other economic benefits, or other essential goods
      and services\\n    3. Engage in the unauthorized or unlicensed practice of any
      profession including, but not limited to, financial, legal, medical/health,
      or related professional practices\\n    4. Collect, process, disclose, generate,
      or infer private or sensitive information about individuals, including information
      about individuals\u2019 identity, health, or demographic information, unless
      you have obtained the right to do so in accordance with applicable law\\n    5.
      Engage in or facilitate any action or generate any content that infringes, misappropriates,
      or otherwise violates any third-party rights, including the outputs or results
      of any products or services using the Llama Materials\\n    6. Create, generate,
      or facilitate the creation of malicious code, malware, computer viruses or do
      anything else that could disable, overburden, interfere with or impair the proper
      working, integrity, operation or appearance of a website or computer system\\n
      \   7. Engage in any action, or facilitate any action, to intentionally circumvent
      or remove usage restrictions or other safety measures, or to enable functionality
      disabled by Meta\\n2. Engage in, promote, incite, facilitate, or assist in the
      planning or development of activities that present a risk of death or bodily
      harm to individuals, including use of Llama 3.2 related to the following:\\n
      \   8. Military, warfare, nuclear industries or applications, espionage, use
      for materials or activities that are subject to the International Traffic Arms
      Regulations (ITAR) maintained by the United States Department of State or to
      the U.S. Biological Weapons Anti-Terrorism Act of 1989 or the Chemical Weapons
      Convention Implementation Act of 1997\\n    9. Guns and illegal weapons (including
      weapon development)\\n    10. Illegal drugs and regulated/controlled substances\\n
      \   11. Operation of critical infrastructure, transportation technologies, or
      heavy machinery\\n    12. Self-harm or harm to others, including suicide, cutting,
      and eating disorders\\n    13. Any content intended to incite or promote violence,
      abuse, or any infliction of bodily harm to an individual\\n3. Intentionally
      deceive or mislead others, including use of Llama 3.2 related to the following:\\n
      \   14. Generating, promoting, or furthering fraud or the creation or promotion
      of disinformation\\n    15. Generating, promoting, or furthering defamatory
      content, including the creation of defamatory statements, images, or other content\\n
      \   16. Generating, promoting, or further distributing spam\\n    17. Impersonating
      another individual without consent, authorization, or legal right\\n    18.
      Representing that the use of Llama 3.2 or outputs are human-generated\\n    19.
      Generating or facilitating false online engagement, including fake reviews and
      other means of fake online engagement\\n4. Fail to appropriately disclose to
      end users any known dangers of your AI system\\n5. Interact with third party
      tools, models, or software designed to generate unlawful content or engage in
      unlawful or harmful conduct and/or represent that the outputs of such tools,
      models, or software are associated with Meta or Llama 3.2\\n\\nWith respect
      to any multimodal models included in Llama 3.2, the rights granted under Section
      1(a) of the Llama 3.2 Community License Agreement are not being granted to you
      if you are an individual domiciled in, or a company with a principal place of
      business in, the European Union. This restriction does not apply to end users
      of a product or service that incorporates any such multimodal models.\\n\\nPlease
      report any violation of this Policy, software \u201Cbug,\u201D or other problems
      that could lead to a violation of this Policy through one of the following means:\\n\\n\\n\\n*
      Reporting issues with the model: [https://github.com/meta-llama/llama-models/issues](https://l.workplace.com/l.php?u=https%3A%2F%2Fgithub.com%2Fmeta-llama%2Fllama-models%2Fissues\\u0026h=AT0qV8W9BFT6NwihiOHRuKYQM_UnkzN_NmHMy91OT55gkLpgi4kQupHUl0ssR4dQsIQ8n3tfd0vtkobvsEvt1l4Ic6GXI2EeuHV8N08OG2WnbAmm0FL4ObkazC6G_256vN0lN9DsykCvCqGZ)\\n*
      Reporting risky content generated by the model: [developers.facebook.com/llama_output_feedback](http://developers.facebook.com/llama_output_feedback)\\n*
      Reporting bugs and security concerns: [facebook.com/whitehat/info](http://facebook.com/whitehat/info)\\n*
      Reporting violations of the Acceptable Use Policy or unlicensed uses of Llama
      3.2: <EMAIL>\\\"\\n\",\"parameters\":\"stop                           \\\"\\u003c|start_header_id|\\u003e\\\"\\nstop
      \                          \\\"\\u003c|end_header_id|\\u003e\\\"\\nstop                           \\\"\\u003c|eot_id|\\u003e\\\"\",\"template\":\"\\u003c|start_header_id|\\u003esystem\\u003c|end_header_id|\\u003e\\n\\nCutting
      Knowledge Date: December 2023\\n\\n{{ if .System }}{{ .System }}\\n{{- end }}\\n{{-
      if .Tools }}When you receive a tool call response, use the output to format
      an answer to the orginal user question.\\n\\nYou are a helpful assistant with
      tool calling capabilities.\\n{{- end }}\\u003c|eot_id|\\u003e\\n{{- range $i,
      $_ := .Messages }}\\n{{- $last := eq (len (slice $.Messages $i)) 1 }}\\n{{-
      if eq .Role \\\"user\\\" }}\\u003c|start_header_id|\\u003euser\\u003c|end_header_id|\\u003e\\n{{-
      if and $.Tools $last }}\\n\\nGiven the following functions, please respond with
      a JSON for a function call with its proper arguments that best answers the given
      prompt.\\n\\nRespond in the format {\\\"name\\\": function name, \\\"parameters\\\":
      dictionary of argument name and its value}. Do not use variables.\\n\\n{{ range
      $.Tools }}\\n{{- . }}\\n{{ end }}\\n{{ .Content }}\\u003c|eot_id|\\u003e\\n{{-
      else }}\\n\\n{{ .Content }}\\u003c|eot_id|\\u003e\\n{{- end }}{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
      end }}\\n{{- else if eq .Role \\\"assistant\\\" }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n{{-
      if .ToolCalls }}\\n{{ range .ToolCalls }}\\n{\\\"name\\\": \\\"{{ .Function.Name
      }}\\\", \\\"parameters\\\": {{ .Function.Arguments }}}{{ end }}\\n{{- else }}\\n\\n{{
      .Content }}\\n{{- end }}{{ if not $last }}\\u003c|eot_id|\\u003e{{ end }}\\n{{-
      else if eq .Role \\\"tool\\\" }}\\u003c|start_header_id|\\u003eipython\\u003c|end_header_id|\\u003e\\n\\n{{
      .Content }}\\u003c|eot_id|\\u003e{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
      end }}\\n{{- end }}\\n{{- end }}\",\"details\":{\"parent_model\":\"\",\"format\":\"gguf\",\"family\":\"llama\",\"families\":[\"llama\"],\"parameter_size\":\"3.2B\",\"quantization_level\":\"Q4_K_M\"},\"model_info\":{\"general.architecture\":\"llama\",\"general.basename\":\"Llama-3.2\",\"general.file_type\":15,\"general.finetune\":\"Instruct\",\"general.languages\":[\"en\",\"de\",\"fr\",\"it\",\"pt\",\"hi\",\"es\",\"th\"],\"general.parameter_count\":3212749888,\"general.quantization_version\":2,\"general.size_label\":\"3B\",\"general.tags\":[\"facebook\",\"meta\",\"pytorch\",\"llama\",\"llama-3\",\"text-generation\"],\"general.type\":\"model\",\"llama.attention.head_count\":24,\"llama.attention.head_count_kv\":8,\"llama.attention.key_length\":128,\"llama.attention.layer_norm_rms_epsilon\":0.00001,\"llama.attention.value_length\":128,\"llama.block_count\":28,\"llama.context_length\":131072,\"llama.embedding_length\":3072,\"llama.feed_forward_length\":8192,\"llama.rope.dimension_count\":128,\"llama.rope.freq_base\":500000,\"llama.vocab_size\":128256,\"tokenizer.ggml.bos_token_id\":128000,\"tokenizer.ggml.eos_token_id\":128009,\"tokenizer.ggml.merges\":null,\"tokenizer.ggml.model\":\"gpt2\",\"tokenizer.ggml.pre\":\"llama-bpe\",\"tokenizer.ggml.token_type\":null,\"tokenizer.ggml.tokens\":null},\"modified_at\":\"2025-02-20T18:55:09.150577031-08:00\"}"
    headers:
      Content-Type:
      - application/json; charset=utf-8
      Date:
      - Fri, 21 Feb 2025 02:57:55 GMT
      Transfer-Encoding:
      - chunked
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"model": "llama3.1", "prompt": "### System:\nPlease convert the following
      text into valid JSON.\n\nOutput ONLY the valid JSON and nothing else.\n\nThe
      JSON must follow this format exactly:\n{\n  \"name\": str,\n  \"age\": int\n}\n\n###
      User:\nName: Alice Llama, Age: 30\n\n", "options": {"stop": []}, "stream": false}'
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '318'
      host:
      - localhost:11434
      user-agent:
      - litellm/1.60.2
    method: POST
    uri: http://localhost:11434/api/generate
  response:
    content: '{"model":"llama3.1","created_at":"2025-02-21T02:58:15.591873Z","response":"```\n{\n  \"name\":
      \"Alice Llama\",\n  \"age\": 30\n}\n```","done":true,"done_reason":"stop","context":[128006,882,128007,271,14711,744,512,5618,5625,279,2768,1495,1139,2764,4823,382,5207,27785,279,2764,4823,323,4400,775,382,791,4823,2011,1833,420,3645,7041,512,517,220,330,609,794,610,345,220,330,425,794,528,198,633,14711,2724,512,678,25,30505,445,81101,11,13381,25,220,966,271,128009,128006,78191,128007,271,14196,4077,517,220,330,609,794,330,62786,445,81101,761,220,330,425,794,220,966,198,534,74694],"total_duration":20230916375,"load_duration":11878250500,"prompt_eval_count":67,"prompt_eval_duration":7472000000,"eval_count":22,"eval_duration":877000000}'
    headers:
      Content-Length:
      - '737'
      Content-Type:
      - application/json; charset=utf-8
      Date:
      - Fri, 21 Feb 2025 02:58:15 GMT
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"name": "llama3.1"}'
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '20'
      content-type:
      - application/json
      host:
      - localhost:11434
      user-agent:
      - litellm/1.60.2
    method: POST
    uri: http://localhost:11434/api/show
  response:
    content: "{\"license\":\"LLAMA 3.1 COMMUNITY LICENSE AGREEMENT\\nLlama 3.1 Version
      Release Date: July 23, 2024\\n\\n\u201CAgreement\u201D means the terms and conditions
      for use, reproduction, distribution and modification of the\\nLlama Materials
      set forth herein.\\n\\n\u201CDocumentation\u201D means the specifications, manuals
      and documentation accompanying Llama 3.1\\ndistributed by Meta at https://llama.meta.com/doc/overview.\\n\\n\u201CLicensee\u201D
      or \u201Cyou\u201D means you, or your employer or any other person or entity
      (if you are entering into\\nthis Agreement on such person or entity\u2019s behalf),
      of the age required under applicable laws, rules or\\nregulations to provide
      legal consent and that has legal authority to bind your employer or such other\\nperson
      or entity if you are entering in this Agreement on their behalf.\\n\\n\u201CLlama
      3.1\u201D means the foundational large language models and software and algorithms,
      including\\nmachine-learning model code, trained model weights, inference-enabling
      code, training-enabling code,\\nfine-tuning enabling code and other elements
      of the foregoing distributed by Meta at\\nhttps://llama.meta.com/llama-downloads.\\n\\n\u201CLlama
      Materials\u201D means, collectively, Meta\u2019s proprietary Llama 3.1 and Documentation
      (and any\\nportion thereof) made available under this Agreement.\\n\\n\u201CMeta\u201D
      or \u201Cwe\u201D means Meta Platforms Ireland Limited (if you are located in
      or, if you are an entity, your\\nprincipal place of business is in the EEA or
      Switzerland) and Meta Platforms, Inc. (if you are located\\noutside of the EEA
      or Switzerland).\\n\\nBy clicking \u201CI Accept\u201D below or by using or
      distributing any portion or element of the Llama Materials,\\nyou agree to be
      bound by this Agreement.\\n\\n1. License Rights and Redistribution.\\n\\n  a.
      Grant of Rights. You are granted a non-exclusive, worldwide, non-transferable
      and royalty-free\\nlimited license under Meta\u2019s intellectual property or
      other rights owned by Meta embodied in the Llama\\nMaterials to use, reproduce,
      distribute, copy, create derivative works of, and make modifications to the\\nLlama
      Materials.\\n\\n  b. Redistribution and Use.\\n\\n      i. If you distribute
      or make available the Llama Materials (or any derivative works\\nthereof), or
      a product or service (including another AI model) that contains any of them,
      you shall (A)\\nprovide a copy of this Agreement with any such Llama Materials;
      and (B) prominently display \u201CBuilt with\\nLlama\u201D on a related website,
      user interface, blogpost, about page, or product documentation. If you use\\nthe
      Llama Materials or any outputs or results of the Llama Materials to create,
      train, fine tune, or\\notherwise improve an AI model, which is distributed or
      made available, you shall also include \u201CLlama\u201D at\\nthe beginning
      of any such AI model name.\\n\\n      ii. If you receive Llama Materials, or
      any derivative works thereof, from a Licensee as part \\nof an integrated end
      user product, then Section 2 of this Agreement will not apply to you.\\n\\n
      \     iii. You must retain in all copies of the Llama Materials that you distribute
      the following\\nattribution notice within a \u201CNotice\u201D text file distributed
      as a part of such copies: \u201CLlama 3.1 is\\nlicensed under the Llama 3.1
      Community License, Copyright \xA9 Meta Platforms, Inc. All Rights\\nReserved.\u201D\\n\\n
      \     iv. Your use of the Llama Materials must comply with applicable laws and
      regulations\\n(including trade compliance laws and regulations) and adhere to
      the Acceptable Use Policy for the Llama\\nMaterials (available at https://llama.meta.com/llama3_1/use-policy),
      which is hereby incorporated by\\nreference into this Agreement.\\n\\n2. Additional
      Commercial Terms. If, on the Llama 3.1 version release date, the monthly active
      users\\nof the products or services made available by or for Licensee, or Licensee\u2019s
      affiliates, is greater than 700\\nmillion monthly active users in the preceding
      calendar month, you must request a license from Meta,\\nwhich Meta may grant
      to you in its sole discretion, and you are not authorized to exercise any of
      the\\nrights under this Agreement unless or until Meta otherwise expressly grants
      you such rights.\\n\\n3. Disclaimer of Warranty. UNLESS REQUIRED BY APPLICABLE
      LAW, THE LLAMA MATERIALS AND ANY\\nOUTPUT AND RESULTS THEREFROM ARE PROVIDED
      ON AN \u201CAS IS\u201D BASIS, WITHOUT WARRANTIES OF\\nANY KIND, AND META DISCLAIMS
      ALL WARRANTIES OF ANY KIND, BOTH EXPRESS AND IMPLIED,\\nINCLUDING, WITHOUT LIMITATION,
      ANY WARRANTIES OF TITLE, NON-INFRINGEMENT,\\nMERCHANTABILITY, OR FITNESS FOR
      A PARTICULAR PURPOSE. YOU ARE SOLELY RESPONSIBLE FOR\\nDETERMINING THE APPROPRIATENESS
      OF USING OR REDISTRIBUTING THE LLAMA MATERIALS AND\\nASSUME ANY RISKS ASSOCIATED
      WITH YOUR USE OF THE LLAMA MATERIALS AND ANY OUTPUT AND\\nRESULTS.\\n\\n4. Limitation
      of Liability. IN NO EVENT WILL META OR ITS AFFILIATES BE LIABLE UNDER ANY THEORY
      OF\\nLIABILITY, WHETHER IN CONTRACT, TORT, NEGLIGENCE, PRODUCTS LIABILITY, OR
      OTHERWISE, ARISING\\nOUT OF THIS AGREEMENT, FOR ANY LOST PROFITS OR ANY INDIRECT,
      SPECIAL, CONSEQUENTIAL,\\nINCIDENTAL, EXEMPLARY OR PUNITIVE DAMAGES, EVEN IF
      META OR ITS AFFILIATES HAVE BEEN ADVISED\\nOF THE POSSIBILITY OF ANY OF THE
      FOREGOING.\\n\\n5. Intellectual Property.\\n\\n  a. No trademark licenses are
      granted under this Agreement, and in connection with the Llama\\nMaterials,
      neither Meta nor Licensee may use any name or mark owned by or associated with
      the other\\nor any of its affiliates, except as required for reasonable and
      customary use in describing and\\nredistributing the Llama Materials or as set
      forth in this Section 5(a). Meta hereby grants you a license to\\nuse \u201CLlama\u201D
      (the \u201CMark\u201D) solely as required to comply with the last sentence of
      Section 1.b.i. You will\\ncomply with Meta\u2019s brand guidelines (currently
      accessible at\\nhttps://about.meta.com/brand/resources/meta/company-brand/ ).
      All goodwill arising out of your use\\nof the Mark will inure to the benefit
      of Meta.\\n\\n  b. Subject to Meta\u2019s ownership of Llama Materials and derivatives
      made by or for Meta, with\\nrespect to any derivative works and modifications
      of the Llama Materials that are made by you, as\\nbetween you and Meta, you
      are and will be the owner of such derivative works and modifications.\\n\\n
      \ c. If you institute litigation or other proceedings against Meta or any entity
      (including a\\ncross-claim or counterclaim in a lawsuit) alleging that the Llama
      Materials or Llama 3.1 outputs or\\nresults, or any portion of any of the foregoing,
      constitutes infringement of intellectual property or other\\nrights owned or
      licensable by you, then any licenses granted to you under this Agreement shall\\nterminate
      as of the date such litigation or claim is filed or instituted. You will indemnify
      and hold\\nharmless Meta from and against any claim by any third party arising
      out of or related to your use or\\ndistribution of the Llama Materials.\\n\\n6.
      Term and Termination. The term of this Agreement will commence upon your acceptance
      of this\\nAgreement or access to the Llama Materials and will continue in full
      force and effect until terminated in\\naccordance with the terms and conditions
      herein. Meta may terminate this Agreement if you are in\\nbreach of any term
      or condition of this Agreement. Upon termination of this Agreement, you shall
      delete\\nand cease use of the Llama Materials. Sections 3, 4 and 7 shall survive
      the termination of this\\nAgreement.\\n\\n7. Governing Law and Jurisdiction.
      This Agreement will be governed and construed under the laws of\\nthe State
      of California without regard to choice of law principles, and the UN Convention
      on Contracts\\nfor the International Sale of Goods does not apply to this Agreement.
      The courts of California shall have\\nexclusive jurisdiction of any dispute
      arising out of this Agreement.\\n\\n# Llama 3.1 Acceptable Use Policy\\n\\nMeta
      is committed to promoting safe and fair use of its tools and features, including
      Llama 3.1. If you\\naccess or use Llama 3.1, you agree to this Acceptable Use
      Policy (\u201CPolicy\u201D). The most recent copy of\\nthis policy can be found
      at [https://llama.meta.com/llama3_1/use-policy](https://llama.meta.com/llama3_1/use-policy)\\n\\n##
      Prohibited Uses\\n\\nWe want everyone to use Llama 3.1 safely and responsibly.
      You agree you will not use, or allow\\nothers to use, Llama 3.1 to:\\n\\n1.
      Violate the law or others\u2019 rights, including to:\\n    1. Engage in, promote,
      generate, contribute to, encourage, plan, incite, or further illegal or unlawful
      activity or content, such as:\\n        1. Violence or terrorism\\n        2.
      Exploitation or harm to children, including the solicitation, creation, acquisition,
      or dissemination of child exploitative content or failure to report Child Sexual
      Abuse Material\\n        3. Human trafficking, exploitation, and sexual violence\\n
      \       4. The illegal distribution of information or materials to minors, including
      obscene materials, or failure to employ legally required age-gating in connection
      with such information or materials.\\n        5. Sexual solicitation\\n        6.
      Any other criminal activity\\n    3. Engage in, promote, incite, or facilitate
      the harassment, abuse, threatening, or bullying of individuals or groups of
      individuals\\n    4. Engage in, promote, incite, or facilitate discrimination
      or other unlawful or harmful conduct in the provision of employment, employment
      benefits, credit, housing, other economic benefits, or other essential goods
      and services\\n    5. Engage in the unauthorized or unlicensed practice of any
      profession including, but not limited to, financial, legal, medical/health,
      or related professional practices\\n    6. Collect, process, disclose, generate,
      or infer health, demographic, or other sensitive personal or private information
      about individuals without rights and consents required by applicable laws\\n
      \   7. Engage in or facilitate any action or generate any content that infringes,
      misappropriates, or otherwise violates any third-party rights, including the
      outputs or results of any products or services using the Llama Materials\\n
      \   8. Create, generate, or facilitate the creation of malicious code, malware,
      computer viruses or do anything else that could disable, overburden, interfere
      with or impair the proper working, integrity, operation or appearance of a website
      or computer system\\n\\n2. Engage in, promote, incite, facilitate, or assist
      in the planning or development of activities that present a risk of death or
      bodily harm to individuals, including use of Llama 3.1 related to the following:\\n
      \   1. Military, warfare, nuclear industries or applications, espionage, use
      for materials or activities that are subject to the International Traffic Arms
      Regulations (ITAR) maintained by the United States Department of State\\n    2.
      Guns and illegal weapons (including weapon development)\\n    3. Illegal drugs
      and regulated/controlled substances\\n    4. Operation of critical infrastructure,
      transportation technologies, or heavy machinery\\n    5. Self-harm or harm to
      others, including suicide, cutting, and eating disorders\\n    6. Any content
      intended to incite or promote violence, abuse, or any infliction of bodily harm
      to an individual\\n\\n3. Intentionally deceive or mislead others, including
      use of Llama 3.1 related to the following:\\n    1. Generating, promoting, or
      furthering fraud or the creation or promotion of disinformation\\n    2. Generating,
      promoting, or furthering defamatory content, including the creation of defamatory
      statements, images, or other content\\n    3. Generating, promoting, or further
      distributing spam\\n    4. Impersonating another individual without consent,
      authorization, or legal right\\n    5. Representing that the use of Llama 3.1
      or outputs are human-generated\\n    6. Generating or facilitating false online
      engagement, including fake reviews and other means of fake online engagement\\n\\n4.
      Fail to appropriately disclose to end users any known dangers of your AI system\\n\\nPlease
      report any violation of this Policy, software \u201Cbug,\u201D or other problems
      that could lead to a violation\\nof this Policy through one of the following
      means:\\n\\n* Reporting issues with the model: [https://github.com/meta-llama/llama-models/issues](https://github.com/meta-llama/llama-models/issues)\\n*
      Reporting risky content generated by the model: developers.facebook.com/llama_output_feedback\\n*
      Reporting bugs and security concerns: facebook.com/whitehat/info\\n* Reporting
      violations of the Acceptable Use Policy or unlicensed uses of Llama 3.1: <EMAIL>\",\"modelfile\":\"#
      Modelfile generated by \\\"ollama show\\\"\\n# To build a new Modelfile based
      on this, replace FROM with:\\n# FROM llama3.1:latest\\n\\nFROM /Users/<USER>/.ollama/models/blobs/sha256-667b0c1932bc6ffc593ed1d03f895bf2dc8dc6df21db3042284a6f4416b06a29\\nTEMPLATE
      \\\"\\\"\\\"{{- if or .System .Tools }}\\u003c|start_header_id|\\u003esystem\\u003c|end_header_id|\\u003e\\n{{-
      if .System }}\\n\\n{{ .System }}\\n{{- end }}\\n{{- if .Tools }}\\n\\nCutting
      Knowledge Date: December 2023\\n\\nWhen you receive a tool call response, use
      the output to format an answer to the orginal user question.\\n\\nYou are a
      helpful assistant with tool calling capabilities.\\n{{- end }}\\u003c|eot_id|\\u003e\\n{{-
      end }}\\n{{- range $i, $_ := .Messages }}\\n{{- $last := eq (len (slice $.Messages
      $i)) 1 }}\\n{{- if eq .Role \\\"user\\\" }}\\u003c|start_header_id|\\u003euser\\u003c|end_header_id|\\u003e\\n{{-
      if and $.Tools $last }}\\n\\nGiven the following functions, please respond with
      a JSON for a function call with its proper arguments that best answers the given
      prompt.\\n\\nRespond in the format {\\\"name\\\": function name, \\\"parameters\\\":
      dictionary of argument name and its value}. Do not use variables.\\n\\n{{ range
      $.Tools }}\\n{{- . }}\\n{{ end }}\\nQuestion: {{ .Content }}\\u003c|eot_id|\\u003e\\n{{-
      else }}\\n\\n{{ .Content }}\\u003c|eot_id|\\u003e\\n{{- end }}{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
      end }}\\n{{- else if eq .Role \\\"assistant\\\" }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n{{-
      if .ToolCalls }}\\n{{ range .ToolCalls }}\\n{\\\"name\\\": \\\"{{ .Function.Name
      }}\\\", \\\"parameters\\\": {{ .Function.Arguments }}}{{ end }}\\n{{- else }}\\n\\n{{
      .Content }}\\n{{- end }}{{ if not $last }}\\u003c|eot_id|\\u003e{{ end }}\\n{{-
      else if eq .Role \\\"tool\\\" }}\\u003c|start_header_id|\\u003eipython\\u003c|end_header_id|\\u003e\\n\\n{{
      .Content }}\\u003c|eot_id|\\u003e{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
      end }}\\n{{- end }}\\n{{- end }}\\\"\\\"\\\"\\nPARAMETER stop \\u003c|start_header_id|\\u003e\\nPARAMETER
      stop \\u003c|end_header_id|\\u003e\\nPARAMETER stop \\u003c|eot_id|\\u003e\\nLICENSE
      \\\"LLAMA 3.1 COMMUNITY LICENSE AGREEMENT\\nLlama 3.1 Version Release Date:
      July 23, 2024\\n\\n\u201CAgreement\u201D means the terms and conditions for
      use, reproduction, distribution and modification of the\\nLlama Materials set
      forth herein.\\n\\n\u201CDocumentation\u201D means the specifications, manuals
      and documentation accompanying Llama 3.1\\ndistributed by Meta at https://llama.meta.com/doc/overview.\\n\\n\u201CLicensee\u201D
      or \u201Cyou\u201D means you, or your employer or any other person or entity
      (if you are entering into\\nthis Agreement on such person or entity\u2019s behalf),
      of the age required under applicable laws, rules or\\nregulations to provide
      legal consent and that has legal authority to bind your employer or such other\\nperson
      or entity if you are entering in this Agreement on their behalf.\\n\\n\u201CLlama
      3.1\u201D means the foundational large language models and software and algorithms,
      including\\nmachine-learning model code, trained model weights, inference-enabling
      code, training-enabling code,\\nfine-tuning enabling code and other elements
      of the foregoing distributed by Meta at\\nhttps://llama.meta.com/llama-downloads.\\n\\n\u201CLlama
      Materials\u201D means, collectively, Meta\u2019s proprietary Llama 3.1 and Documentation
      (and any\\nportion thereof) made available under this Agreement.\\n\\n\u201CMeta\u201D
      or \u201Cwe\u201D means Meta Platforms Ireland Limited (if you are located in
      or, if you are an entity, your\\nprincipal place of business is in the EEA or
      Switzerland) and Meta Platforms, Inc. (if you are located\\noutside of the EEA
      or Switzerland).\\n\\nBy clicking \u201CI Accept\u201D below or by using or
      distributing any portion or element of the Llama Materials,\\nyou agree to be
      bound by this Agreement.\\n\\n1. License Rights and Redistribution.\\n\\n  a.
      Grant of Rights. You are granted a non-exclusive, worldwide, non-transferable
      and royalty-free\\nlimited license under Meta\u2019s intellectual property or
      other rights owned by Meta embodied in the Llama\\nMaterials to use, reproduce,
      distribute, copy, create derivative works of, and make modifications to the\\nLlama
      Materials.\\n\\n  b. Redistribution and Use.\\n\\n      i. If you distribute
      or make available the Llama Materials (or any derivative works\\nthereof), or
      a product or service (including another AI model) that contains any of them,
      you shall (A)\\nprovide a copy of this Agreement with any such Llama Materials;
      and (B) prominently display \u201CBuilt with\\nLlama\u201D on a related website,
      user interface, blogpost, about page, or product documentation. If you use\\nthe
      Llama Materials or any outputs or results of the Llama Materials to create,
      train, fine tune, or\\notherwise improve an AI model, which is distributed or
      made available, you shall also include \u201CLlama\u201D at\\nthe beginning
      of any such AI model name.\\n\\n      ii. If you receive Llama Materials, or
      any derivative works thereof, from a Licensee as part \\nof an integrated end
      user product, then Section 2 of this Agreement will not apply to you.\\n\\n
      \     iii. You must retain in all copies of the Llama Materials that you distribute
      the following\\nattribution notice within a \u201CNotice\u201D text file distributed
      as a part of such copies: \u201CLlama 3.1 is\\nlicensed under the Llama 3.1
      Community License, Copyright \xA9 Meta Platforms, Inc. All Rights\\nReserved.\u201D\\n\\n
      \     iv. Your use of the Llama Materials must comply with applicable laws and
      regulations\\n(including trade compliance laws and regulations) and adhere to
      the Acceptable Use Policy for the Llama\\nMaterials (available at https://llama.meta.com/llama3_1/use-policy),
      which is hereby incorporated by\\nreference into this Agreement.\\n\\n2. Additional
      Commercial Terms. If, on the Llama 3.1 version release date, the monthly active
      users\\nof the products or services made available by or for Licensee, or Licensee\u2019s
      affiliates, is greater than 700\\nmillion monthly active users in the preceding
      calendar month, you must request a license from Meta,\\nwhich Meta may grant
      to you in its sole discretion, and you are not authorized to exercise any of
      the\\nrights under this Agreement unless or until Meta otherwise expressly grants
      you such rights.\\n\\n3. Disclaimer of Warranty. UNLESS REQUIRED BY APPLICABLE
      LAW, THE LLAMA MATERIALS AND ANY\\nOUTPUT AND RESULTS THEREFROM ARE PROVIDED
      ON AN \u201CAS IS\u201D BASIS, WITHOUT WARRANTIES OF\\nANY KIND, AND META DISCLAIMS
      ALL WARRANTIES OF ANY KIND, BOTH EXPRESS AND IMPLIED,\\nINCLUDING, WITHOUT LIMITATION,
      ANY WARRANTIES OF TITLE, NON-INFRINGEMENT,\\nMERCHANTABILITY, OR FITNESS FOR
      A PARTICULAR PURPOSE. YOU ARE SOLELY RESPONSIBLE FOR\\nDETERMINING THE APPROPRIATENESS
      OF USING OR REDISTRIBUTING THE LLAMA MATERIALS AND\\nASSUME ANY RISKS ASSOCIATED
      WITH YOUR USE OF THE LLAMA MATERIALS AND ANY OUTPUT AND\\nRESULTS.\\n\\n4. Limitation
      of Liability. IN NO EVENT WILL META OR ITS AFFILIATES BE LIABLE UNDER ANY THEORY
      OF\\nLIABILITY, WHETHER IN CONTRACT, TORT, NEGLIGENCE, PRODUCTS LIABILITY, OR
      OTHERWISE, ARISING\\nOUT OF THIS AGREEMENT, FOR ANY LOST PROFITS OR ANY INDIRECT,
      SPECIAL, CONSEQUENTIAL,\\nINCIDENTAL, EXEMPLARY OR PUNITIVE DAMAGES, EVEN IF
      META OR ITS AFFILIATES HAVE BEEN ADVISED\\nOF THE POSSIBILITY OF ANY OF THE
      FOREGOING.\\n\\n5. Intellectual Property.\\n\\n  a. No trademark licenses are
      granted under this Agreement, and in connection with the Llama\\nMaterials,
      neither Meta nor Licensee may use any name or mark owned by or associated with
      the other\\nor any of its affiliates, except as required for reasonable and
      customary use in describing and\\nredistributing the Llama Materials or as set
      forth in this Section 5(a). Meta hereby grants you a license to\\nuse \u201CLlama\u201D
      (the \u201CMark\u201D) solely as required to comply with the last sentence of
      Section 1.b.i. You will\\ncomply with Meta\u2019s brand guidelines (currently
      accessible at\\nhttps://about.meta.com/brand/resources/meta/company-brand/ ).
      All goodwill arising out of your use\\nof the Mark will inure to the benefit
      of Meta.\\n\\n  b. Subject to Meta\u2019s ownership of Llama Materials and derivatives
      made by or for Meta, with\\nrespect to any derivative works and modifications
      of the Llama Materials that are made by you, as\\nbetween you and Meta, you
      are and will be the owner of such derivative works and modifications.\\n\\n
      \ c. If you institute litigation or other proceedings against Meta or any entity
      (including a\\ncross-claim or counterclaim in a lawsuit) alleging that the Llama
      Materials or Llama 3.1 outputs or\\nresults, or any portion of any of the foregoing,
      constitutes infringement of intellectual property or other\\nrights owned or
      licensable by you, then any licenses granted to you under this Agreement shall\\nterminate
      as of the date such litigation or claim is filed or instituted. You will indemnify
      and hold\\nharmless Meta from and against any claim by any third party arising
      out of or related to your use or\\ndistribution of the Llama Materials.\\n\\n6.
      Term and Termination. The term of this Agreement will commence upon your acceptance
      of this\\nAgreement or access to the Llama Materials and will continue in full
      force and effect until terminated in\\naccordance with the terms and conditions
      herein. Meta may terminate this Agreement if you are in\\nbreach of any term
      or condition of this Agreement. Upon termination of this Agreement, you shall
      delete\\nand cease use of the Llama Materials. Sections 3, 4 and 7 shall survive
      the termination of this\\nAgreement.\\n\\n7. Governing Law and Jurisdiction.
      This Agreement will be governed and construed under the laws of\\nthe State
      of California without regard to choice of law principles, and the UN Convention
      on Contracts\\nfor the International Sale of Goods does not apply to this Agreement.
      The courts of California shall have\\nexclusive jurisdiction of any dispute
      arising out of this Agreement.\\n\\n# Llama 3.1 Acceptable Use Policy\\n\\nMeta
      is committed to promoting safe and fair use of its tools and features, including
      Llama 3.1. If you\\naccess or use Llama 3.1, you agree to this Acceptable Use
      Policy (\u201CPolicy\u201D). The most recent copy of\\nthis policy can be found
      at [https://llama.meta.com/llama3_1/use-policy](https://llama.meta.com/llama3_1/use-policy)\\n\\n##
      Prohibited Uses\\n\\nWe want everyone to use Llama 3.1 safely and responsibly.
      You agree you will not use, or allow\\nothers to use, Llama 3.1 to:\\n\\n1.
      Violate the law or others\u2019 rights, including to:\\n    1. Engage in, promote,
      generate, contribute to, encourage, plan, incite, or further illegal or unlawful
      activity or content, such as:\\n        1. Violence or terrorism\\n        2.
      Exploitation or harm to children, including the solicitation, creation, acquisition,
      or dissemination of child exploitative content or failure to report Child Sexual
      Abuse Material\\n        3. Human trafficking, exploitation, and sexual violence\\n
      \       4. The illegal distribution of information or materials to minors, including
      obscene materials, or failure to employ legally required age-gating in connection
      with such information or materials.\\n        5. Sexual solicitation\\n        6.
      Any other criminal activity\\n    3. Engage in, promote, incite, or facilitate
      the harassment, abuse, threatening, or bullying of individuals or groups of
      individuals\\n    4. Engage in, promote, incite, or facilitate discrimination
      or other unlawful or harmful conduct in the provision of employment, employment
      benefits, credit, housing, other economic benefits, or other essential goods
      and services\\n    5. Engage in the unauthorized or unlicensed practice of any
      profession including, but not limited to, financial, legal, medical/health,
      or related professional practices\\n    6. Collect, process, disclose, generate,
      or infer health, demographic, or other sensitive personal or private information
      about individuals without rights and consents required by applicable laws\\n
      \   7. Engage in or facilitate any action or generate any content that infringes,
      misappropriates, or otherwise violates any third-party rights, including the
      outputs or results of any products or services using the Llama Materials\\n
      \   8. Create, generate, or facilitate the creation of malicious code, malware,
      computer viruses or do anything else that could disable, overburden, interfere
      with or impair the proper working, integrity, operation or appearance of a website
      or computer system\\n\\n2. Engage in, promote, incite, facilitate, or assist
      in the planning or development of activities that present a risk of death or
      bodily harm to individuals, including use of Llama 3.1 related to the following:\\n
      \   1. Military, warfare, nuclear industries or applications, espionage, use
      for materials or activities that are subject to the International Traffic Arms
      Regulations (ITAR) maintained by the United States Department of State\\n    2.
      Guns and illegal weapons (including weapon development)\\n    3. Illegal drugs
      and regulated/controlled substances\\n    4. Operation of critical infrastructure,
      transportation technologies, or heavy machinery\\n    5. Self-harm or harm to
      others, including suicide, cutting, and eating disorders\\n    6. Any content
      intended to incite or promote violence, abuse, or any infliction of bodily harm
      to an individual\\n\\n3. Intentionally deceive or mislead others, including
      use of Llama 3.1 related to the following:\\n    1. Generating, promoting, or
      furthering fraud or the creation or promotion of disinformation\\n    2. Generating,
      promoting, or furthering defamatory content, including the creation of defamatory
      statements, images, or other content\\n    3. Generating, promoting, or further
      distributing spam\\n    4. Impersonating another individual without consent,
      authorization, or legal right\\n    5. Representing that the use of Llama 3.1
      or outputs are human-generated\\n    6. Generating or facilitating false online
      engagement, including fake reviews and other means of fake online engagement\\n\\n4.
      Fail to appropriately disclose to end users any known dangers of your AI system\\n\\nPlease
      report any violation of this Policy, software \u201Cbug,\u201D or other problems
      that could lead to a violation\\nof this Policy through one of the following
      means:\\n\\n* Reporting issues with the model: [https://github.com/meta-llama/llama-models/issues](https://github.com/meta-llama/llama-models/issues)\\n*
      Reporting risky content generated by the model: developers.facebook.com/llama_output_feedback\\n*
      Reporting bugs and security concerns: facebook.com/whitehat/info\\n* Reporting
      violations of the Acceptable Use Policy or unlicensed uses of Llama 3.1: <EMAIL>\\\"\\n\",\"parameters\":\"stop
      \                          \\\"\\u003c|start_header_id|\\u003e\\\"\\nstop                           \\\"\\u003c|end_header_id|\\u003e\\\"\\nstop
      \                          \\\"\\u003c|eot_id|\\u003e\\\"\",\"template\":\"{{-
      if or .System .Tools }}\\u003c|start_header_id|\\u003esystem\\u003c|end_header_id|\\u003e\\n{{-
      if .System }}\\n\\n{{ .System }}\\n{{- end }}\\n{{- if .Tools }}\\n\\nCutting
      Knowledge Date: December 2023\\n\\nWhen you receive a tool call response, use
      the output to format an answer to the orginal user question.\\n\\nYou are a
      helpful assistant with tool calling capabilities.\\n{{- end }}\\u003c|eot_id|\\u003e\\n{{-
      end }}\\n{{- range $i, $_ := .Messages }}\\n{{- $last := eq (len (slice $.Messages
      $i)) 1 }}\\n{{- if eq .Role \\\"user\\\" }}\\u003c|start_header_id|\\u003euser\\u003c|end_header_id|\\u003e\\n{{-
      if and $.Tools $last }}\\n\\nGiven the following functions, please respond with
      a JSON for a function call with its proper arguments that best answers the given
      prompt.\\n\\nRespond in the format {\\\"name\\\": function name, \\\"parameters\\\":
      dictionary of argument name and its value}. Do not use variables.\\n\\n{{ range
      $.Tools }}\\n{{- . }}\\n{{ end }}\\nQuestion: {{ .Content }}\\u003c|eot_id|\\u003e\\n{{-
      else }}\\n\\n{{ .Content }}\\u003c|eot_id|\\u003e\\n{{- end }}{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
      end }}\\n{{- else if eq .Role \\\"assistant\\\" }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n{{-
      if .ToolCalls }}\\n{{ range .ToolCalls }}\\n{\\\"name\\\": \\\"{{ .Function.Name
      }}\\\", \\\"parameters\\\": {{ .Function.Arguments }}}{{ end }}\\n{{- else }}\\n\\n{{
      .Content }}\\n{{- end }}{{ if not $last }}\\u003c|eot_id|\\u003e{{ end }}\\n{{-
      else if eq .Role \\\"tool\\\" }}\\u003c|start_header_id|\\u003eipython\\u003c|end_header_id|\\u003e\\n\\n{{
      .Content }}\\u003c|eot_id|\\u003e{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
      end }}\\n{{- end }}\\n{{- end }}\",\"details\":{\"parent_model\":\"\",\"format\":\"gguf\",\"family\":\"llama\",\"families\":[\"llama\"],\"parameter_size\":\"8.0B\",\"quantization_level\":\"Q4_K_M\"},\"model_info\":{\"general.architecture\":\"llama\",\"general.basename\":\"Meta-Llama-3.1\",\"general.file_type\":15,\"general.finetune\":\"Instruct\",\"general.languages\":[\"en\",\"de\",\"fr\",\"it\",\"pt\",\"hi\",\"es\",\"th\"],\"general.license\":\"llama3.1\",\"general.parameter_count\":**********,\"general.quantization_version\":2,\"general.size_label\":\"8B\",\"general.tags\":[\"facebook\",\"meta\",\"pytorch\",\"llama\",\"llama-3\",\"text-generation\"],\"general.type\":\"model\",\"llama.attention.head_count\":32,\"llama.attention.head_count_kv\":8,\"llama.attention.layer_norm_rms_epsilon\":0.00001,\"llama.block_count\":32,\"llama.context_length\":131072,\"llama.embedding_length\":4096,\"llama.feed_forward_length\":14336,\"llama.rope.dimension_count\":128,\"llama.rope.freq_base\":500000,\"llama.vocab_size\":128256,\"tokenizer.ggml.bos_token_id\":128000,\"tokenizer.ggml.eos_token_id\":128009,\"tokenizer.ggml.merges\":null,\"tokenizer.ggml.model\":\"gpt2\",\"tokenizer.ggml.pre\":\"llama-bpe\",\"tokenizer.ggml.token_type\":null,\"tokenizer.ggml.tokens\":null},\"modified_at\":\"2025-02-20T18:56:54.293648887-08:00\"}"
    headers:
      Content-Type:
      - application/json; charset=utf-8
      Date:
      - Fri, 21 Feb 2025 02:58:15 GMT
      Transfer-Encoding:
      - chunked
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"name": "llama3.1"}'
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '20'
      content-type:
      - application/json
      host:
      - localhost:11434
      user-agent:
      - litellm/1.60.2
    method: POST
    uri: http://localhost:11434/api/show
  response:
    content: "{\"license\":\"LLAMA 3.1 COMMUNITY LICENSE AGREEMENT\\nLlama 3.1 Version
      Release Date: July 23, 2024\\n\\n\u201CAgreement\u201D means the terms and conditions
      for use, reproduction, distribution and modification of the\\nLlama Materials
      set forth herein.\\n\\n\u201CDocumentation\u201D means the specifications, manuals
      and documentation accompanying Llama 3.1\\ndistributed by Meta at https://llama.meta.com/doc/overview.\\n\\n\u201CLicensee\u201D
      or \u201Cyou\u201D means you, or your employer or any other person or entity
      (if you are entering into\\nthis Agreement on such person or entity\u2019s behalf),
      of the age required under applicable laws, rules or\\nregulations to provide
      legal consent and that has legal authority to bind your employer or such other\\nperson
      or entity if you are entering in this Agreement on their behalf.\\n\\n\u201CLlama
      3.1\u201D means the foundational large language models and software and algorithms,
      including\\nmachine-learning model code, trained model weights, inference-enabling
      code, training-enabling code,\\nfine-tuning enabling code and other elements
      of the foregoing distributed by Meta at\\nhttps://llama.meta.com/llama-downloads.\\n\\n\u201CLlama
      Materials\u201D means, collectively, Meta\u2019s proprietary Llama 3.1 and Documentation
      (and any\\nportion thereof) made available under this Agreement.\\n\\n\u201CMeta\u201D
      or \u201Cwe\u201D means Meta Platforms Ireland Limited (if you are located in
      or, if you are an entity, your\\nprincipal place of business is in the EEA or
      Switzerland) and Meta Platforms, Inc. (if you are located\\noutside of the EEA
      or Switzerland).\\n\\nBy clicking \u201CI Accept\u201D below or by using or
      distributing any portion or element of the Llama Materials,\\nyou agree to be
      bound by this Agreement.\\n\\n1. License Rights and Redistribution.\\n\\n  a.
      Grant of Rights. You are granted a non-exclusive, worldwide, non-transferable
      and royalty-free\\nlimited license under Meta\u2019s intellectual property or
      other rights owned by Meta embodied in the Llama\\nMaterials to use, reproduce,
      distribute, copy, create derivative works of, and make modifications to the\\nLlama
      Materials.\\n\\n  b. Redistribution and Use.\\n\\n      i. If you distribute
      or make available the Llama Materials (or any derivative works\\nthereof), or
      a product or service (including another AI model) that contains any of them,
      you shall (A)\\nprovide a copy of this Agreement with any such Llama Materials;
      and (B) prominently display \u201CBuilt with\\nLlama\u201D on a related website,
      user interface, blogpost, about page, or product documentation. If you use\\nthe
      Llama Materials or any outputs or results of the Llama Materials to create,
      train, fine tune, or\\notherwise improve an AI model, which is distributed or
      made available, you shall also include \u201CLlama\u201D at\\nthe beginning
      of any such AI model name.\\n\\n      ii. If you receive Llama Materials, or
      any derivative works thereof, from a Licensee as part \\nof an integrated end
      user product, then Section 2 of this Agreement will not apply to you.\\n\\n
      \     iii. You must retain in all copies of the Llama Materials that you distribute
      the following\\nattribution notice within a \u201CNotice\u201D text file distributed
      as a part of such copies: \u201CLlama 3.1 is\\nlicensed under the Llama 3.1
      Community License, Copyright \xA9 Meta Platforms, Inc. All Rights\\nReserved.\u201D\\n\\n
      \     iv. Your use of the Llama Materials must comply with applicable laws and
      regulations\\n(including trade compliance laws and regulations) and adhere to
      the Acceptable Use Policy for the Llama\\nMaterials (available at https://llama.meta.com/llama3_1/use-policy),
      which is hereby incorporated by\\nreference into this Agreement.\\n\\n2. Additional
      Commercial Terms. If, on the Llama 3.1 version release date, the monthly active
      users\\nof the products or services made available by or for Licensee, or Licensee\u2019s
      affiliates, is greater than 700\\nmillion monthly active users in the preceding
      calendar month, you must request a license from Meta,\\nwhich Meta may grant
      to you in its sole discretion, and you are not authorized to exercise any of
      the\\nrights under this Agreement unless or until Meta otherwise expressly grants
      you such rights.\\n\\n3. Disclaimer of Warranty. UNLESS REQUIRED BY APPLICABLE
      LAW, THE LLAMA MATERIALS AND ANY\\nOUTPUT AND RESULTS THEREFROM ARE PROVIDED
      ON AN \u201CAS IS\u201D BASIS, WITHOUT WARRANTIES OF\\nANY KIND, AND META DISCLAIMS
      ALL WARRANTIES OF ANY KIND, BOTH EXPRESS AND IMPLIED,\\nINCLUDING, WITHOUT LIMITATION,
      ANY WARRANTIES OF TITLE, NON-INFRINGEMENT,\\nMERCHANTABILITY, OR FITNESS FOR
      A PARTICULAR PURPOSE. YOU ARE SOLELY RESPONSIBLE FOR\\nDETERMINING THE APPROPRIATENESS
      OF USING OR REDISTRIBUTING THE LLAMA MATERIALS AND\\nASSUME ANY RISKS ASSOCIATED
      WITH YOUR USE OF THE LLAMA MATERIALS AND ANY OUTPUT AND\\nRESULTS.\\n\\n4. Limitation
      of Liability. IN NO EVENT WILL META OR ITS AFFILIATES BE LIABLE UNDER ANY THEORY
      OF\\nLIABILITY, WHETHER IN CONTRACT, TORT, NEGLIGENCE, PRODUCTS LIABILITY, OR
      OTHERWISE, ARISING\\nOUT OF THIS AGREEMENT, FOR ANY LOST PROFITS OR ANY INDIRECT,
      SPECIAL, CONSEQUENTIAL,\\nINCIDENTAL, EXEMPLARY OR PUNITIVE DAMAGES, EVEN IF
      META OR ITS AFFILIATES HAVE BEEN ADVISED\\nOF THE POSSIBILITY OF ANY OF THE
      FOREGOING.\\n\\n5. Intellectual Property.\\n\\n  a. No trademark licenses are
      granted under this Agreement, and in connection with the Llama\\nMaterials,
      neither Meta nor Licensee may use any name or mark owned by or associated with
      the other\\nor any of its affiliates, except as required for reasonable and
      customary use in describing and\\nredistributing the Llama Materials or as set
      forth in this Section 5(a). Meta hereby grants you a license to\\nuse \u201CLlama\u201D
      (the \u201CMark\u201D) solely as required to comply with the last sentence of
      Section 1.b.i. You will\\ncomply with Meta\u2019s brand guidelines (currently
      accessible at\\nhttps://about.meta.com/brand/resources/meta/company-brand/ ).
      All goodwill arising out of your use\\nof the Mark will inure to the benefit
      of Meta.\\n\\n  b. Subject to Meta\u2019s ownership of Llama Materials and derivatives
      made by or for Meta, with\\nrespect to any derivative works and modifications
      of the Llama Materials that are made by you, as\\nbetween you and Meta, you
      are and will be the owner of such derivative works and modifications.\\n\\n
      \ c. If you institute litigation or other proceedings against Meta or any entity
      (including a\\ncross-claim or counterclaim in a lawsuit) alleging that the Llama
      Materials or Llama 3.1 outputs or\\nresults, or any portion of any of the foregoing,
      constitutes infringement of intellectual property or other\\nrights owned or
      licensable by you, then any licenses granted to you under this Agreement shall\\nterminate
      as of the date such litigation or claim is filed or instituted. You will indemnify
      and hold\\nharmless Meta from and against any claim by any third party arising
      out of or related to your use or\\ndistribution of the Llama Materials.\\n\\n6.
      Term and Termination. The term of this Agreement will commence upon your acceptance
      of this\\nAgreement or access to the Llama Materials and will continue in full
      force and effect until terminated in\\naccordance with the terms and conditions
      herein. Meta may terminate this Agreement if you are in\\nbreach of any term
      or condition of this Agreement. Upon termination of this Agreement, you shall
      delete\\nand cease use of the Llama Materials. Sections 3, 4 and 7 shall survive
      the termination of this\\nAgreement.\\n\\n7. Governing Law and Jurisdiction.
      This Agreement will be governed and construed under the laws of\\nthe State
      of California without regard to choice of law principles, and the UN Convention
      on Contracts\\nfor the International Sale of Goods does not apply to this Agreement.
      The courts of California shall have\\nexclusive jurisdiction of any dispute
      arising out of this Agreement.\\n\\n# Llama 3.1 Acceptable Use Policy\\n\\nMeta
      is committed to promoting safe and fair use of its tools and features, including
      Llama 3.1. If you\\naccess or use Llama 3.1, you agree to this Acceptable Use
      Policy (\u201CPolicy\u201D). The most recent copy of\\nthis policy can be found
      at [https://llama.meta.com/llama3_1/use-policy](https://llama.meta.com/llama3_1/use-policy)\\n\\n##
      Prohibited Uses\\n\\nWe want everyone to use Llama 3.1 safely and responsibly.
      You agree you will not use, or allow\\nothers to use, Llama 3.1 to:\\n\\n1.
      Violate the law or others\u2019 rights, including to:\\n    1. Engage in, promote,
      generate, contribute to, encourage, plan, incite, or further illegal or unlawful
      activity or content, such as:\\n        1. Violence or terrorism\\n        2.
      Exploitation or harm to children, including the solicitation, creation, acquisition,
      or dissemination of child exploitative content or failure to report Child Sexual
      Abuse Material\\n        3. Human trafficking, exploitation, and sexual violence\\n
      \       4. The illegal distribution of information or materials to minors, including
      obscene materials, or failure to employ legally required age-gating in connection
      with such information or materials.\\n        5. Sexual solicitation\\n        6.
      Any other criminal activity\\n    3. Engage in, promote, incite, or facilitate
      the harassment, abuse, threatening, or bullying of individuals or groups of
      individuals\\n    4. Engage in, promote, incite, or facilitate discrimination
      or other unlawful or harmful conduct in the provision of employment, employment
      benefits, credit, housing, other economic benefits, or other essential goods
      and services\\n    5. Engage in the unauthorized or unlicensed practice of any
      profession including, but not limited to, financial, legal, medical/health,
      or related professional practices\\n    6. Collect, process, disclose, generate,
      or infer health, demographic, or other sensitive personal or private information
      about individuals without rights and consents required by applicable laws\\n
      \   7. Engage in or facilitate any action or generate any content that infringes,
      misappropriates, or otherwise violates any third-party rights, including the
      outputs or results of any products or services using the Llama Materials\\n
      \   8. Create, generate, or facilitate the creation of malicious code, malware,
      computer viruses or do anything else that could disable, overburden, interfere
      with or impair the proper working, integrity, operation or appearance of a website
      or computer system\\n\\n2. Engage in, promote, incite, facilitate, or assist
      in the planning or development of activities that present a risk of death or
      bodily harm to individuals, including use of Llama 3.1 related to the following:\\n
      \   1. Military, warfare, nuclear industries or applications, espionage, use
      for materials or activities that are subject to the International Traffic Arms
      Regulations (ITAR) maintained by the United States Department of State\\n    2.
      Guns and illegal weapons (including weapon development)\\n    3. Illegal drugs
      and regulated/controlled substances\\n    4. Operation of critical infrastructure,
      transportation technologies, or heavy machinery\\n    5. Self-harm or harm to
      others, including suicide, cutting, and eating disorders\\n    6. Any content
      intended to incite or promote violence, abuse, or any infliction of bodily harm
      to an individual\\n\\n3. Intentionally deceive or mislead others, including
      use of Llama 3.1 related to the following:\\n    1. Generating, promoting, or
      furthering fraud or the creation or promotion of disinformation\\n    2. Generating,
      promoting, or furthering defamatory content, including the creation of defamatory
      statements, images, or other content\\n    3. Generating, promoting, or further
      distributing spam\\n    4. Impersonating another individual without consent,
      authorization, or legal right\\n    5. Representing that the use of Llama 3.1
      or outputs are human-generated\\n    6. Generating or facilitating false online
      engagement, including fake reviews and other means of fake online engagement\\n\\n4.
      Fail to appropriately disclose to end users any known dangers of your AI system\\n\\nPlease
      report any violation of this Policy, software \u201Cbug,\u201D or other problems
      that could lead to a violation\\nof this Policy through one of the following
      means:\\n\\n* Reporting issues with the model: [https://github.com/meta-llama/llama-models/issues](https://github.com/meta-llama/llama-models/issues)\\n*
      Reporting risky content generated by the model: developers.facebook.com/llama_output_feedback\\n*
      Reporting bugs and security concerns: facebook.com/whitehat/info\\n* Reporting
      violations of the Acceptable Use Policy or unlicensed uses of Llama 3.1: <EMAIL>\",\"modelfile\":\"#
      Modelfile generated by \\\"ollama show\\\"\\n# To build a new Modelfile based
      on this, replace FROM with:\\n# FROM llama3.1:latest\\n\\nFROM /Users/<USER>/.ollama/models/blobs/sha256-667b0c1932bc6ffc593ed1d03f895bf2dc8dc6df21db3042284a6f4416b06a29\\nTEMPLATE
      \\\"\\\"\\\"{{- if or .System .Tools }}\\u003c|start_header_id|\\u003esystem\\u003c|end_header_id|\\u003e\\n{{-
      if .System }}\\n\\n{{ .System }}\\n{{- end }}\\n{{- if .Tools }}\\n\\nCutting
      Knowledge Date: December 2023\\n\\nWhen you receive a tool call response, use
      the output to format an answer to the orginal user question.\\n\\nYou are a
      helpful assistant with tool calling capabilities.\\n{{- end }}\\u003c|eot_id|\\u003e\\n{{-
      end }}\\n{{- range $i, $_ := .Messages }}\\n{{- $last := eq (len (slice $.Messages
      $i)) 1 }}\\n{{- if eq .Role \\\"user\\\" }}\\u003c|start_header_id|\\u003euser\\u003c|end_header_id|\\u003e\\n{{-
      if and $.Tools $last }}\\n\\nGiven the following functions, please respond with
      a JSON for a function call with its proper arguments that best answers the given
      prompt.\\n\\nRespond in the format {\\\"name\\\": function name, \\\"parameters\\\":
      dictionary of argument name and its value}. Do not use variables.\\n\\n{{ range
      $.Tools }}\\n{{- . }}\\n{{ end }}\\nQuestion: {{ .Content }}\\u003c|eot_id|\\u003e\\n{{-
      else }}\\n\\n{{ .Content }}\\u003c|eot_id|\\u003e\\n{{- end }}{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
      end }}\\n{{- else if eq .Role \\\"assistant\\\" }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n{{-
      if .ToolCalls }}\\n{{ range .ToolCalls }}\\n{\\\"name\\\": \\\"{{ .Function.Name
      }}\\\", \\\"parameters\\\": {{ .Function.Arguments }}}{{ end }}\\n{{- else }}\\n\\n{{
      .Content }}\\n{{- end }}{{ if not $last }}\\u003c|eot_id|\\u003e{{ end }}\\n{{-
      else if eq .Role \\\"tool\\\" }}\\u003c|start_header_id|\\u003eipython\\u003c|end_header_id|\\u003e\\n\\n{{
      .Content }}\\u003c|eot_id|\\u003e{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
      end }}\\n{{- end }}\\n{{- end }}\\\"\\\"\\\"\\nPARAMETER stop \\u003c|start_header_id|\\u003e\\nPARAMETER
      stop \\u003c|end_header_id|\\u003e\\nPARAMETER stop \\u003c|eot_id|\\u003e\\nLICENSE
      \\\"LLAMA 3.1 COMMUNITY LICENSE AGREEMENT\\nLlama 3.1 Version Release Date:
      July 23, 2024\\n\\n\u201CAgreement\u201D means the terms and conditions for
      use, reproduction, distribution and modification of the\\nLlama Materials set
      forth herein.\\n\\n\u201CDocumentation\u201D means the specifications, manuals
      and documentation accompanying Llama 3.1\\ndistributed by Meta at https://llama.meta.com/doc/overview.\\n\\n\u201CLicensee\u201D
      or \u201Cyou\u201D means you, or your employer or any other person or entity
      (if you are entering into\\nthis Agreement on such person or entity\u2019s behalf),
      of the age required under applicable laws, rules or\\nregulations to provide
      legal consent and that has legal authority to bind your employer or such other\\nperson
      or entity if you are entering in this Agreement on their behalf.\\n\\n\u201CLlama
      3.1\u201D means the foundational large language models and software and algorithms,
      including\\nmachine-learning model code, trained model weights, inference-enabling
      code, training-enabling code,\\nfine-tuning enabling code and other elements
      of the foregoing distributed by Meta at\\nhttps://llama.meta.com/llama-downloads.\\n\\n\u201CLlama
      Materials\u201D means, collectively, Meta\u2019s proprietary Llama 3.1 and Documentation
      (and any\\nportion thereof) made available under this Agreement.\\n\\n\u201CMeta\u201D
      or \u201Cwe\u201D means Meta Platforms Ireland Limited (if you are located in
      or, if you are an entity, your\\nprincipal place of business is in the EEA or
      Switzerland) and Meta Platforms, Inc. (if you are located\\noutside of the EEA
      or Switzerland).\\n\\nBy clicking \u201CI Accept\u201D below or by using or
      distributing any portion or element of the Llama Materials,\\nyou agree to be
      bound by this Agreement.\\n\\n1. License Rights and Redistribution.\\n\\n  a.
      Grant of Rights. You are granted a non-exclusive, worldwide, non-transferable
      and royalty-free\\nlimited license under Meta\u2019s intellectual property or
      other rights owned by Meta embodied in the Llama\\nMaterials to use, reproduce,
      distribute, copy, create derivative works of, and make modifications to the\\nLlama
      Materials.\\n\\n  b. Redistribution and Use.\\n\\n      i. If you distribute
      or make available the Llama Materials (or any derivative works\\nthereof), or
      a product or service (including another AI model) that contains any of them,
      you shall (A)\\nprovide a copy of this Agreement with any such Llama Materials;
      and (B) prominently display \u201CBuilt with\\nLlama\u201D on a related website,
      user interface, blogpost, about page, or product documentation. If you use\\nthe
      Llama Materials or any outputs or results of the Llama Materials to create,
      train, fine tune, or\\notherwise improve an AI model, which is distributed or
      made available, you shall also include \u201CLlama\u201D at\\nthe beginning
      of any such AI model name.\\n\\n      ii. If you receive Llama Materials, or
      any derivative works thereof, from a Licensee as part \\nof an integrated end
      user product, then Section 2 of this Agreement will not apply to you.\\n\\n
      \     iii. You must retain in all copies of the Llama Materials that you distribute
      the following\\nattribution notice within a \u201CNotice\u201D text file distributed
      as a part of such copies: \u201CLlama 3.1 is\\nlicensed under the Llama 3.1
      Community License, Copyright \xA9 Meta Platforms, Inc. All Rights\\nReserved.\u201D\\n\\n
      \     iv. Your use of the Llama Materials must comply with applicable laws and
      regulations\\n(including trade compliance laws and regulations) and adhere to
      the Acceptable Use Policy for the Llama\\nMaterials (available at https://llama.meta.com/llama3_1/use-policy),
      which is hereby incorporated by\\nreference into this Agreement.\\n\\n2. Additional
      Commercial Terms. If, on the Llama 3.1 version release date, the monthly active
      users\\nof the products or services made available by or for Licensee, or Licensee\u2019s
      affiliates, is greater than 700\\nmillion monthly active users in the preceding
      calendar month, you must request a license from Meta,\\nwhich Meta may grant
      to you in its sole discretion, and you are not authorized to exercise any of
      the\\nrights under this Agreement unless or until Meta otherwise expressly grants
      you such rights.\\n\\n3. Disclaimer of Warranty. UNLESS REQUIRED BY APPLICABLE
      LAW, THE LLAMA MATERIALS AND ANY\\nOUTPUT AND RESULTS THEREFROM ARE PROVIDED
      ON AN \u201CAS IS\u201D BASIS, WITHOUT WARRANTIES OF\\nANY KIND, AND META DISCLAIMS
      ALL WARRANTIES OF ANY KIND, BOTH EXPRESS AND IMPLIED,\\nINCLUDING, WITHOUT LIMITATION,
      ANY WARRANTIES OF TITLE, NON-INFRINGEMENT,\\nMERCHANTABILITY, OR FITNESS FOR
      A PARTICULAR PURPOSE. YOU ARE SOLELY RESPONSIBLE FOR\\nDETERMINING THE APPROPRIATENESS
      OF USING OR REDISTRIBUTING THE LLAMA MATERIALS AND\\nASSUME ANY RISKS ASSOCIATED
      WITH YOUR USE OF THE LLAMA MATERIALS AND ANY OUTPUT AND\\nRESULTS.\\n\\n4. Limitation
      of Liability. IN NO EVENT WILL META OR ITS AFFILIATES BE LIABLE UNDER ANY THEORY
      OF\\nLIABILITY, WHETHER IN CONTRACT, TORT, NEGLIGENCE, PRODUCTS LIABILITY, OR
      OTHERWISE, ARISING\\nOUT OF THIS AGREEMENT, FOR ANY LOST PROFITS OR ANY INDIRECT,
      SPECIAL, CONSEQUENTIAL,\\nINCIDENTAL, EXEMPLARY OR PUNITIVE DAMAGES, EVEN IF
      META OR ITS AFFILIATES HAVE BEEN ADVISED\\nOF THE POSSIBILITY OF ANY OF THE
      FOREGOING.\\n\\n5. Intellectual Property.\\n\\n  a. No trademark licenses are
      granted under this Agreement, and in connection with the Llama\\nMaterials,
      neither Meta nor Licensee may use any name or mark owned by or associated with
      the other\\nor any of its affiliates, except as required for reasonable and
      customary use in describing and\\nredistributing the Llama Materials or as set
      forth in this Section 5(a). Meta hereby grants you a license to\\nuse \u201CLlama\u201D
      (the \u201CMark\u201D) solely as required to comply with the last sentence of
      Section 1.b.i. You will\\ncomply with Meta\u2019s brand guidelines (currently
      accessible at\\nhttps://about.meta.com/brand/resources/meta/company-brand/ ).
      All goodwill arising out of your use\\nof the Mark will inure to the benefit
      of Meta.\\n\\n  b. Subject to Meta\u2019s ownership of Llama Materials and derivatives
      made by or for Meta, with\\nrespect to any derivative works and modifications
      of the Llama Materials that are made by you, as\\nbetween you and Meta, you
      are and will be the owner of such derivative works and modifications.\\n\\n
      \ c. If you institute litigation or other proceedings against Meta or any entity
      (including a\\ncross-claim or counterclaim in a lawsuit) alleging that the Llama
      Materials or Llama 3.1 outputs or\\nresults, or any portion of any of the foregoing,
      constitutes infringement of intellectual property or other\\nrights owned or
      licensable by you, then any licenses granted to you under this Agreement shall\\nterminate
      as of the date such litigation or claim is filed or instituted. You will indemnify
      and hold\\nharmless Meta from and against any claim by any third party arising
      out of or related to your use or\\ndistribution of the Llama Materials.\\n\\n6.
      Term and Termination. The term of this Agreement will commence upon your acceptance
      of this\\nAgreement or access to the Llama Materials and will continue in full
      force and effect until terminated in\\naccordance with the terms and conditions
      herein. Meta may terminate this Agreement if you are in\\nbreach of any term
      or condition of this Agreement. Upon termination of this Agreement, you shall
      delete\\nand cease use of the Llama Materials. Sections 3, 4 and 7 shall survive
      the termination of this\\nAgreement.\\n\\n7. Governing Law and Jurisdiction.
      This Agreement will be governed and construed under the laws of\\nthe State
      of California without regard to choice of law principles, and the UN Convention
      on Contracts\\nfor the International Sale of Goods does not apply to this Agreement.
      The courts of California shall have\\nexclusive jurisdiction of any dispute
      arising out of this Agreement.\\n\\n# Llama 3.1 Acceptable Use Policy\\n\\nMeta
      is committed to promoting safe and fair use of its tools and features, including
      Llama 3.1. If you\\naccess or use Llama 3.1, you agree to this Acceptable Use
      Policy (\u201CPolicy\u201D). The most recent copy of\\nthis policy can be found
      at [https://llama.meta.com/llama3_1/use-policy](https://llama.meta.com/llama3_1/use-policy)\\n\\n##
      Prohibited Uses\\n\\nWe want everyone to use Llama 3.1 safely and responsibly.
      You agree you will not use, or allow\\nothers to use, Llama 3.1 to:\\n\\n1.
      Violate the law or others\u2019 rights, including to:\\n    1. Engage in, promote,
      generate, contribute to, encourage, plan, incite, or further illegal or unlawful
      activity or content, such as:\\n        1. Violence or terrorism\\n        2.
      Exploitation or harm to children, including the solicitation, creation, acquisition,
      or dissemination of child exploitative content or failure to report Child Sexual
      Abuse Material\\n        3. Human trafficking, exploitation, and sexual violence\\n
      \       4. The illegal distribution of information or materials to minors, including
      obscene materials, or failure to employ legally required age-gating in connection
      with such information or materials.\\n        5. Sexual solicitation\\n        6.
      Any other criminal activity\\n    3. Engage in, promote, incite, or facilitate
      the harassment, abuse, threatening, or bullying of individuals or groups of
      individuals\\n    4. Engage in, promote, incite, or facilitate discrimination
      or other unlawful or harmful conduct in the provision of employment, employment
      benefits, credit, housing, other economic benefits, or other essential goods
      and services\\n    5. Engage in the unauthorized or unlicensed practice of any
      profession including, but not limited to, financial, legal, medical/health,
      or related professional practices\\n    6. Collect, process, disclose, generate,
      or infer health, demographic, or other sensitive personal or private information
      about individuals without rights and consents required by applicable laws\\n
      \   7. Engage in or facilitate any action or generate any content that infringes,
      misappropriates, or otherwise violates any third-party rights, including the
      outputs or results of any products or services using the Llama Materials\\n
      \   8. Create, generate, or facilitate the creation of malicious code, malware,
      computer viruses or do anything else that could disable, overburden, interfere
      with or impair the proper working, integrity, operation or appearance of a website
      or computer system\\n\\n2. Engage in, promote, incite, facilitate, or assist
      in the planning or development of activities that present a risk of death or
      bodily harm to individuals, including use of Llama 3.1 related to the following:\\n
      \   1. Military, warfare, nuclear industries or applications, espionage, use
      for materials or activities that are subject to the International Traffic Arms
      Regulations (ITAR) maintained by the United States Department of State\\n    2.
      Guns and illegal weapons (including weapon development)\\n    3. Illegal drugs
      and regulated/controlled substances\\n    4. Operation of critical infrastructure,
      transportation technologies, or heavy machinery\\n    5. Self-harm or harm to
      others, including suicide, cutting, and eating disorders\\n    6. Any content
      intended to incite or promote violence, abuse, or any infliction of bodily harm
      to an individual\\n\\n3. Intentionally deceive or mislead others, including
      use of Llama 3.1 related to the following:\\n    1. Generating, promoting, or
      furthering fraud or the creation or promotion of disinformation\\n    2. Generating,
      promoting, or furthering defamatory content, including the creation of defamatory
      statements, images, or other content\\n    3. Generating, promoting, or further
      distributing spam\\n    4. Impersonating another individual without consent,
      authorization, or legal right\\n    5. Representing that the use of Llama 3.1
      or outputs are human-generated\\n    6. Generating or facilitating false online
      engagement, including fake reviews and other means of fake online engagement\\n\\n4.
      Fail to appropriately disclose to end users any known dangers of your AI system\\n\\nPlease
      report any violation of this Policy, software \u201Cbug,\u201D or other problems
      that could lead to a violation\\nof this Policy through one of the following
      means:\\n\\n* Reporting issues with the model: [https://github.com/meta-llama/llama-models/issues](https://github.com/meta-llama/llama-models/issues)\\n*
      Reporting risky content generated by the model: developers.facebook.com/llama_output_feedback\\n*
      Reporting bugs and security concerns: facebook.com/whitehat/info\\n* Reporting
      violations of the Acceptable Use Policy or unlicensed uses of Llama 3.1: <EMAIL>\\\"\\n\",\"parameters\":\"stop
      \                          \\\"\\u003c|start_header_id|\\u003e\\\"\\nstop                           \\\"\\u003c|end_header_id|\\u003e\\\"\\nstop
      \                          \\\"\\u003c|eot_id|\\u003e\\\"\",\"template\":\"{{-
      if or .System .Tools }}\\u003c|start_header_id|\\u003esystem\\u003c|end_header_id|\\u003e\\n{{-
      if .System }}\\n\\n{{ .System }}\\n{{- end }}\\n{{- if .Tools }}\\n\\nCutting
      Knowledge Date: December 2023\\n\\nWhen you receive a tool call response, use
      the output to format an answer to the orginal user question.\\n\\nYou are a
      helpful assistant with tool calling capabilities.\\n{{- end }}\\u003c|eot_id|\\u003e\\n{{-
      end }}\\n{{- range $i, $_ := .Messages }}\\n{{- $last := eq (len (slice $.Messages
      $i)) 1 }}\\n{{- if eq .Role \\\"user\\\" }}\\u003c|start_header_id|\\u003euser\\u003c|end_header_id|\\u003e\\n{{-
      if and $.Tools $last }}\\n\\nGiven the following functions, please respond with
      a JSON for a function call with its proper arguments that best answers the given
      prompt.\\n\\nRespond in the format {\\\"name\\\": function name, \\\"parameters\\\":
      dictionary of argument name and its value}. Do not use variables.\\n\\n{{ range
      $.Tools }}\\n{{- . }}\\n{{ end }}\\nQuestion: {{ .Content }}\\u003c|eot_id|\\u003e\\n{{-
      else }}\\n\\n{{ .Content }}\\u003c|eot_id|\\u003e\\n{{- end }}{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
      end }}\\n{{- else if eq .Role \\\"assistant\\\" }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n{{-
      if .ToolCalls }}\\n{{ range .ToolCalls }}\\n{\\\"name\\\": \\\"{{ .Function.Name
      }}\\\", \\\"parameters\\\": {{ .Function.Arguments }}}{{ end }}\\n{{- else }}\\n\\n{{
      .Content }}\\n{{- end }}{{ if not $last }}\\u003c|eot_id|\\u003e{{ end }}\\n{{-
      else if eq .Role \\\"tool\\\" }}\\u003c|start_header_id|\\u003eipython\\u003c|end_header_id|\\u003e\\n\\n{{
      .Content }}\\u003c|eot_id|\\u003e{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
      end }}\\n{{- end }}\\n{{- end }}\",\"details\":{\"parent_model\":\"\",\"format\":\"gguf\",\"family\":\"llama\",\"families\":[\"llama\"],\"parameter_size\":\"8.0B\",\"quantization_level\":\"Q4_K_M\"},\"model_info\":{\"general.architecture\":\"llama\",\"general.basename\":\"Meta-Llama-3.1\",\"general.file_type\":15,\"general.finetune\":\"Instruct\",\"general.languages\":[\"en\",\"de\",\"fr\",\"it\",\"pt\",\"hi\",\"es\",\"th\"],\"general.license\":\"llama3.1\",\"general.parameter_count\":**********,\"general.quantization_version\":2,\"general.size_label\":\"8B\",\"general.tags\":[\"facebook\",\"meta\",\"pytorch\",\"llama\",\"llama-3\",\"text-generation\"],\"general.type\":\"model\",\"llama.attention.head_count\":32,\"llama.attention.head_count_kv\":8,\"llama.attention.layer_norm_rms_epsilon\":0.00001,\"llama.block_count\":32,\"llama.context_length\":131072,\"llama.embedding_length\":4096,\"llama.feed_forward_length\":14336,\"llama.rope.dimension_count\":128,\"llama.rope.freq_base\":500000,\"llama.vocab_size\":128256,\"tokenizer.ggml.bos_token_id\":128000,\"tokenizer.ggml.eos_token_id\":128009,\"tokenizer.ggml.merges\":null,\"tokenizer.ggml.model\":\"gpt2\",\"tokenizer.ggml.pre\":\"llama-bpe\",\"tokenizer.ggml.token_type\":null,\"tokenizer.ggml.tokens\":null},\"modified_at\":\"2025-02-20T18:56:54.293648887-08:00\"}"
    headers:
      Content-Type:
      - application/json; charset=utf-8
      Date:
      - Fri, 21 Feb 2025 02:58:15 GMT
      Transfer-Encoding:
      - chunked
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"model": "llama3.1", "prompt": "### User:\nName: Alice Llama, Age: 30\n\n###
      System:\nProduce JSON OUTPUT ONLY! Adhere to this format {\"name\": \"function_name\",
      \"arguments\":{\"argument_name\": \"argument_value\"}} The following functions
      are available to you:\n{''type'': ''function'', ''function'': {''name'': ''SimpleModel'',
      ''description'': ''Correctly extracted `SimpleModel` with all the required parameters
      with correct types'', ''parameters'': {''properties'': {''name'': {''title'':
      ''Name'', ''type'': ''string''}, ''age'': {''title'': ''Age'', ''type'': ''integer''}},
      ''required'': [''age'', ''name''], ''type'': ''object''}}}\n\n\n", "options":
      {}, "stream": false, "format": "json", "images": []}'
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '668'
      host:
      - localhost:11434
      user-agent:
      - litellm/1.68.0
    method: POST
    uri: http://localhost:11434/api/generate
  response:
    body:
      string: '{"model":"llama3.1","created_at":"2025-05-07T01:16:23.653756921Z","response":"{\"name\":
        \"SimpleModel\", \"arguments\":{\"name\": \"Alice Llama\", \"age\": 30}}","done":true,"done_reason":"stop","context":[128006,882,128007,271,14711,2724,512,678,25,30505,445,81101,11,13381,25,220,966,271,14711,744,512,1360,13677,4823,32090,27785,0,2467,6881,311,420,3645,5324,609,794,330,1723,1292,498,330,16774,23118,14819,1292,794,330,14819,3220,32075,578,2768,5865,527,2561,311,499,512,13922,1337,1232,364,1723,518,364,1723,1232,5473,609,1232,364,16778,1747,518,364,4789,1232,364,34192,398,28532,1595,16778,1747,63,449,682,279,2631,5137,449,4495,4595,518,364,14105,1232,5473,13495,1232,5473,609,1232,5473,2150,1232,364,678,518,364,1337,1232,364,928,25762,364,425,1232,5473,2150,1232,364,17166,518,364,1337,1232,364,11924,8439,2186,364,6413,1232,2570,425,518,364,609,4181,364,1337,1232,364,1735,23742,3818,128009,128006,78191,128007,271,5018,609,794,330,16778,1747,498,330,16774,23118,609,794,330,62786,445,81101,498,330,425,794,220,966,3500],"total_duration":5656133628,"load_duration":19896000,"prompt_eval_count":152,"prompt_eval_duration":4544235710,"eval_count":24,"eval_duration":1089740418}'
    headers:
      Content-Length:
      - '1186'
      Content-Type:
      - application/json; charset=utf-8
      Date:
      - Wed, 07 May 2025 01:16:23 GMT
    status:
      code: 200
      message: OK
- request:
    body: '{"name": "llama3.1"}'
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '20'
      content-type:
      - application/json
      host:
      - localhost:11434
      user-agent:
      - litellm/1.68.0
    method: POST
    uri: http://localhost:11434/api/show
  response:
    body:
      string: "{\"license\":\"LLAMA 3.1 COMMUNITY LICENSE AGREEMENT\\nLlama 3.1 Version
        Release Date: July 23, 2024\\n\\n\u201CAgreement\u201D means the terms and
        conditions for use, reproduction, distribution and modification of the\\nLlama
        Materials set forth herein.\\n\\n\u201CDocumentation\u201D means the specifications,
        manuals and documentation accompanying Llama 3.1\\ndistributed by Meta at
        https://llama.meta.com/doc/overview.\\n\\n\u201CLicensee\u201D or \u201Cyou\u201D
        means you, or your employer or any other person or entity (if you are entering
        into\\nthis Agreement on such person or entity\u2019s behalf), of the age
        required under applicable laws, rules or\\nregulations to provide legal consent
        and that has legal authority to bind your employer or such other\\nperson
        or entity if you are entering in this Agreement on their behalf.\\n\\n\u201CLlama
        3.1\u201D means the foundational large language models and software and algorithms,
        including\\nmachine-learning model code, trained model weights, inference-enabling
        code, training-enabling code,\\nfine-tuning enabling code and other elements
        of the foregoing distributed by Meta at\\nhttps://llama.meta.com/llama-downloads.\\n\\n\u201CLlama
        Materials\u201D means, collectively, Meta\u2019s proprietary Llama 3.1 and
        Documentation (and any\\nportion thereof) made available under this Agreement.\\n\\n\u201CMeta\u201D
        or \u201Cwe\u201D means Meta Platforms Ireland Limited (if you are located
        in or, if you are an entity, your\\nprincipal place of business is in the
        EEA or Switzerland) and Meta Platforms, Inc. (if you are located\\noutside
        of the EEA or Switzerland).\\n\\nBy clicking \u201CI Accept\u201D below or
        by using or distributing any portion or element of the Llama Materials,\\nyou
        agree to be bound by this Agreement.\\n\\n1. License Rights and Redistribution.\\n\\n
        \ a. Grant of Rights. You are granted a non-exclusive, worldwide, non-transferable
        and royalty-free\\nlimited license under Meta\u2019s intellectual property
        or other rights owned by Meta embodied in the Llama\\nMaterials to use, reproduce,
        distribute, copy, create derivative works of, and make modifications to the\\nLlama
        Materials.\\n\\n  b. Redistribution and Use.\\n\\n      i. If you distribute
        or make available the Llama Materials (or any derivative works\\nthereof),
        or a product or service (including another AI model) that contains any of
        them, you shall (A)\\nprovide a copy of this Agreement with any such Llama
        Materials; and (B) prominently display \u201CBuilt with\\nLlama\u201D on a
        related website, user interface, blogpost, about page, or product documentation.
        If you use\\nthe Llama Materials or any outputs or results of the Llama Materials
        to create, train, fine tune, or\\notherwise improve an AI model, which is
        distributed or made available, you shall also include \u201CLlama\u201D at\\nthe
        beginning of any such AI model name.\\n\\n      ii. If you receive Llama Materials,
        or any derivative works thereof, from a Licensee as part \\nof an integrated
        end user product, then Section 2 of this Agreement will not apply to you.\\n\\n
        \     iii. You must retain in all copies of the Llama Materials that you distribute
        the following\\nattribution notice within a \u201CNotice\u201D text file distributed
        as a part of such copies: \u201CLlama 3.1 is\\nlicensed under the Llama 3.1
        Community License, Copyright \xA9 Meta Platforms, Inc. All Rights\\nReserved.\u201D\\n\\n
        \     iv. Your use of the Llama Materials must comply with applicable laws
        and regulations\\n(including trade compliance laws and regulations) and adhere
        to the Acceptable Use Policy for the Llama\\nMaterials (available at https://llama.meta.com/llama3_1/use-policy),
        which is hereby incorporated by\\nreference into this Agreement.\\n\\n2. Additional
        Commercial Terms. If, on the Llama 3.1 version release date, the monthly active
        users\\nof the products or services made available by or for Licensee, or
        Licensee\u2019s affiliates, is greater than 700\\nmillion monthly active users
        in the preceding calendar month, you must request a license from Meta,\\nwhich
        Meta may grant to you in its sole discretion, and you are not authorized to
        exercise any of the\\nrights under this Agreement unless or until Meta otherwise
        expressly grants you such rights.\\n\\n3. Disclaimer of Warranty. UNLESS REQUIRED
        BY APPLICABLE LAW, THE LLAMA MATERIALS AND ANY\\nOUTPUT AND RESULTS THEREFROM
        ARE PROVIDED ON AN \u201CAS IS\u201D BASIS, WITHOUT WARRANTIES OF\\nANY KIND,
        AND META DISCLAIMS ALL WARRANTIES OF ANY KIND, BOTH EXPRESS AND IMPLIED,\\nINCLUDING,
        WITHOUT LIMITATION, ANY WARRANTIES OF TITLE, NON-INFRINGEMENT,\\nMERCHANTABILITY,
        OR FITNESS FOR A PARTICULAR PURPOSE. YOU ARE SOLELY RESPONSIBLE FOR\\nDETERMINING
        THE APPROPRIATENESS OF USING OR REDISTRIBUTING THE LLAMA MATERIALS AND\\nASSUME
        ANY RISKS ASSOCIATED WITH YOUR USE OF THE LLAMA MATERIALS AND ANY OUTPUT AND\\nRESULTS.\\n\\n4.
        Limitation of Liability. IN NO EVENT WILL META OR ITS AFFILIATES BE LIABLE
        UNDER ANY THEORY OF\\nLIABILITY, WHETHER IN CONTRACT, TORT, NEGLIGENCE, PRODUCTS
        LIABILITY, OR OTHERWISE, ARISING\\nOUT OF THIS AGREEMENT, FOR ANY LOST PROFITS
        OR ANY INDIRECT, SPECIAL, CONSEQUENTIAL,\\nINCIDENTAL, EXEMPLARY OR PUNITIVE
        DAMAGES, EVEN IF META OR ITS AFFILIATES HAVE BEEN ADVISED\\nOF THE POSSIBILITY
        OF ANY OF THE FOREGOING.\\n\\n5. Intellectual Property.\\n\\n  a. No trademark
        licenses are granted under this Agreement, and in connection with the Llama\\nMaterials,
        neither Meta nor Licensee may use any name or mark owned by or associated
        with the other\\nor any of its affiliates, except as required for reasonable
        and customary use in describing and\\nredistributing the Llama Materials or
        as set forth in this Section 5(a). Meta hereby grants you a license to\\nuse
        \u201CLlama\u201D (the \u201CMark\u201D) solely as required to comply with
        the last sentence of Section 1.b.i. You will\\ncomply with Meta\u2019s brand
        guidelines (currently accessible at\\nhttps://about.meta.com/brand/resources/meta/company-brand/
        ). All goodwill arising out of your use\\nof the Mark will inure to the benefit
        of Meta.\\n\\n  b. Subject to Meta\u2019s ownership of Llama Materials and
        derivatives made by or for Meta, with\\nrespect to any derivative works and
        modifications of the Llama Materials that are made by you, as\\nbetween you
        and Meta, you are and will be the owner of such derivative works and modifications.\\n\\n
        \ c. If you institute litigation or other proceedings against Meta or any
        entity (including a\\ncross-claim or counterclaim in a lawsuit) alleging that
        the Llama Materials or Llama 3.1 outputs or\\nresults, or any portion of any
        of the foregoing, constitutes infringement of intellectual property or other\\nrights
        owned or licensable by you, then any licenses granted to you under this Agreement
        shall\\nterminate as of the date such litigation or claim is filed or instituted.
        You will indemnify and hold\\nharmless Meta from and against any claim by
        any third party arising out of or related to your use or\\ndistribution of
        the Llama Materials.\\n\\n6. Term and Termination. The term of this Agreement
        will commence upon your acceptance of this\\nAgreement or access to the Llama
        Materials and will continue in full force and effect until terminated in\\naccordance
        with the terms and conditions herein. Meta may terminate this Agreement if
        you are in\\nbreach of any term or condition of this Agreement. Upon termination
        of this Agreement, you shall delete\\nand cease use of the Llama Materials.
        Sections 3, 4 and 7 shall survive the termination of this\\nAgreement.\\n\\n7.
        Governing Law and Jurisdiction. This Agreement will be governed and construed
        under the laws of\\nthe State of California without regard to choice of law
        principles, and the UN Convention on Contracts\\nfor the International Sale
        of Goods does not apply to this Agreement. The courts of California shall
        have\\nexclusive jurisdiction of any dispute arising out of this Agreement.\\n\\n#
        Llama 3.1 Acceptable Use Policy\\n\\nMeta is committed to promoting safe and
        fair use of its tools and features, including Llama 3.1. If you\\naccess or
        use Llama 3.1, you agree to this Acceptable Use Policy (\u201CPolicy\u201D).
        The most recent copy of\\nthis policy can be found at [https://llama.meta.com/llama3_1/use-policy](https://llama.meta.com/llama3_1/use-policy)\\n\\n##
        Prohibited Uses\\n\\nWe want everyone to use Llama 3.1 safely and responsibly.
        You agree you will not use, or allow\\nothers to use, Llama 3.1 to:\\n\\n1.
        Violate the law or others\u2019 rights, including to:\\n    1. Engage in,
        promote, generate, contribute to, encourage, plan, incite, or further illegal
        or unlawful activity or content, such as:\\n        1. Violence or terrorism\\n
        \       2. Exploitation or harm to children, including the solicitation, creation,
        acquisition, or dissemination of child exploitative content or failure to
        report Child Sexual Abuse Material\\n        3. Human trafficking, exploitation,
        and sexual violence\\n        4. The illegal distribution of information or
        materials to minors, including obscene materials, or failure to employ legally
        required age-gating in connection with such information or materials.\\n        5.
        Sexual solicitation\\n        6. Any other criminal activity\\n    3. Engage
        in, promote, incite, or facilitate the harassment, abuse, threatening, or
        bullying of individuals or groups of individuals\\n    4. Engage in, promote,
        incite, or facilitate discrimination or other unlawful or harmful conduct
        in the provision of employment, employment benefits, credit, housing, other
        economic benefits, or other essential goods and services\\n    5. Engage in
        the unauthorized or unlicensed practice of any profession including, but not
        limited to, financial, legal, medical/health, or related professional practices\\n
        \   6. Collect, process, disclose, generate, or infer health, demographic,
        or other sensitive personal or private information about individuals without
        rights and consents required by applicable laws\\n    7. Engage in or facilitate
        any action or generate any content that infringes, misappropriates, or otherwise
        violates any third-party rights, including the outputs or results of any products
        or services using the Llama Materials\\n    8. Create, generate, or facilitate
        the creation of malicious code, malware, computer viruses or do anything else
        that could disable, overburden, interfere with or impair the proper working,
        integrity, operation or appearance of a website or computer system\\n\\n2.
        Engage in, promote, incite, facilitate, or assist in the planning or development
        of activities that present a risk of death or bodily harm to individuals,
        including use of Llama 3.1 related to the following:\\n    1. Military, warfare,
        nuclear industries or applications, espionage, use for materials or activities
        that are subject to the International Traffic Arms Regulations (ITAR) maintained
        by the United States Department of State\\n    2. Guns and illegal weapons
        (including weapon development)\\n    3. Illegal drugs and regulated/controlled
        substances\\n    4. Operation of critical infrastructure, transportation technologies,
        or heavy machinery\\n    5. Self-harm or harm to others, including suicide,
        cutting, and eating disorders\\n    6. Any content intended to incite or promote
        violence, abuse, or any infliction of bodily harm to an individual\\n\\n3.
        Intentionally deceive or mislead others, including use of Llama 3.1 related
        to the following:\\n    1. Generating, promoting, or furthering fraud or the
        creation or promotion of disinformation\\n    2. Generating, promoting, or
        furthering defamatory content, including the creation of defamatory statements,
        images, or other content\\n    3. Generating, promoting, or further distributing
        spam\\n    4. Impersonating another individual without consent, authorization,
        or legal right\\n    5. Representing that the use of Llama 3.1 or outputs
        are human-generated\\n    6. Generating or facilitating false online engagement,
        including fake reviews and other means of fake online engagement\\n\\n4. Fail
        to appropriately disclose to end users any known dangers of your AI system\\n\\nPlease
        report any violation of this Policy, software \u201Cbug,\u201D or other problems
        that could lead to a violation\\nof this Policy through one of the following
        means:\\n\\n* Reporting issues with the model: [https://github.com/meta-llama/llama-models/issues](https://github.com/meta-llama/llama-models/issues)\\n*
        Reporting risky content generated by the model: developers.facebook.com/llama_output_feedback\\n*
        Reporting bugs and security concerns: facebook.com/whitehat/info\\n* Reporting
        violations of the Acceptable Use Policy or unlicensed uses of Llama 3.1: <EMAIL>\",\"modelfile\":\"#
        Modelfile generated by \\\"ollama show\\\"\\n# To build a new Modelfile based
        on this, replace FROM with:\\n# FROM llama3.1:latest\\n\\nFROM /root/.ollama/models/blobs/sha256-667b0c1932bc6ffc593ed1d03f895bf2dc8dc6df21db3042284a6f4416b06a29\\nTEMPLATE
        \\\"\\\"\\\"{{- if or .System .Tools }}\\u003c|start_header_id|\\u003esystem\\u003c|end_header_id|\\u003e\\n{{-
        if .System }}\\n\\n{{ .System }}\\n{{- end }}\\n{{- if .Tools }}\\n\\nCutting
        Knowledge Date: December 2023\\n\\nWhen you receive a tool call response,
        use the output to format an answer to the orginal user question.\\n\\nYou
        are a helpful assistant with tool calling capabilities.\\n{{- end }}\\u003c|eot_id|\\u003e\\n{{-
        end }}\\n{{- range $i, $_ := .Messages }}\\n{{- $last := eq (len (slice $.Messages
        $i)) 1 }}\\n{{- if eq .Role \\\"user\\\" }}\\u003c|start_header_id|\\u003euser\\u003c|end_header_id|\\u003e\\n{{-
        if and $.Tools $last }}\\n\\nGiven the following functions, please respond
        with a JSON for a function call with its proper arguments that best answers
        the given prompt.\\n\\nRespond in the format {\\\"name\\\": function name,
        \\\"parameters\\\": dictionary of argument name and its value}. Do not use
        variables.\\n\\n{{ range $.Tools }}\\n{{- . }}\\n{{ end }}\\nQuestion: {{
        .Content }}\\u003c|eot_id|\\u003e\\n{{- else }}\\n\\n{{ .Content }}\\u003c|eot_id|\\u003e\\n{{-
        end }}{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
        end }}\\n{{- else if eq .Role \\\"assistant\\\" }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n{{-
        if .ToolCalls }}\\n{{ range .ToolCalls }}\\n{\\\"name\\\": \\\"{{ .Function.Name
        }}\\\", \\\"parameters\\\": {{ .Function.Arguments }}}{{ end }}\\n{{- else
        }}\\n\\n{{ .Content }}\\n{{- end }}{{ if not $last }}\\u003c|eot_id|\\u003e{{
        end }}\\n{{- else if eq .Role \\\"tool\\\" }}\\u003c|start_header_id|\\u003eipython\\u003c|end_header_id|\\u003e\\n\\n{{
        .Content }}\\u003c|eot_id|\\u003e{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
        end }}\\n{{- end }}\\n{{- end }}\\\"\\\"\\\"\\nPARAMETER stop \\u003c|start_header_id|\\u003e\\nPARAMETER
        stop \\u003c|end_header_id|\\u003e\\nPARAMETER stop \\u003c|eot_id|\\u003e\\nLICENSE
        \\\"LLAMA 3.1 COMMUNITY LICENSE AGREEMENT\\nLlama 3.1 Version Release Date:
        July 23, 2024\\n\\n\u201CAgreement\u201D means the terms and conditions for
        use, reproduction, distribution and modification of the\\nLlama Materials
        set forth herein.\\n\\n\u201CDocumentation\u201D means the specifications,
        manuals and documentation accompanying Llama 3.1\\ndistributed by Meta at
        https://llama.meta.com/doc/overview.\\n\\n\u201CLicensee\u201D or \u201Cyou\u201D
        means you, or your employer or any other person or entity (if you are entering
        into\\nthis Agreement on such person or entity\u2019s behalf), of the age
        required under applicable laws, rules or\\nregulations to provide legal consent
        and that has legal authority to bind your employer or such other\\nperson
        or entity if you are entering in this Agreement on their behalf.\\n\\n\u201CLlama
        3.1\u201D means the foundational large language models and software and algorithms,
        including\\nmachine-learning model code, trained model weights, inference-enabling
        code, training-enabling code,\\nfine-tuning enabling code and other elements
        of the foregoing distributed by Meta at\\nhttps://llama.meta.com/llama-downloads.\\n\\n\u201CLlama
        Materials\u201D means, collectively, Meta\u2019s proprietary Llama 3.1 and
        Documentation (and any\\nportion thereof) made available under this Agreement.\\n\\n\u201CMeta\u201D
        or \u201Cwe\u201D means Meta Platforms Ireland Limited (if you are located
        in or, if you are an entity, your\\nprincipal place of business is in the
        EEA or Switzerland) and Meta Platforms, Inc. (if you are located\\noutside
        of the EEA or Switzerland).\\n\\nBy clicking \u201CI Accept\u201D below or
        by using or distributing any portion or element of the Llama Materials,\\nyou
        agree to be bound by this Agreement.\\n\\n1. License Rights and Redistribution.\\n\\n
        \ a. Grant of Rights. You are granted a non-exclusive, worldwide, non-transferable
        and royalty-free\\nlimited license under Meta\u2019s intellectual property
        or other rights owned by Meta embodied in the Llama\\nMaterials to use, reproduce,
        distribute, copy, create derivative works of, and make modifications to the\\nLlama
        Materials.\\n\\n  b. Redistribution and Use.\\n\\n      i. If you distribute
        or make available the Llama Materials (or any derivative works\\nthereof),
        or a product or service (including another AI model) that contains any of
        them, you shall (A)\\nprovide a copy of this Agreement with any such Llama
        Materials; and (B) prominently display \u201CBuilt with\\nLlama\u201D on a
        related website, user interface, blogpost, about page, or product documentation.
        If you use\\nthe Llama Materials or any outputs or results of the Llama Materials
        to create, train, fine tune, or\\notherwise improve an AI model, which is
        distributed or made available, you shall also include \u201CLlama\u201D at\\nthe
        beginning of any such AI model name.\\n\\n      ii. If you receive Llama Materials,
        or any derivative works thereof, from a Licensee as part \\nof an integrated
        end user product, then Section 2 of this Agreement will not apply to you.\\n\\n
        \     iii. You must retain in all copies of the Llama Materials that you distribute
        the following\\nattribution notice within a \u201CNotice\u201D text file distributed
        as a part of such copies: \u201CLlama 3.1 is\\nlicensed under the Llama 3.1
        Community License, Copyright \xA9 Meta Platforms, Inc. All Rights\\nReserved.\u201D\\n\\n
        \     iv. Your use of the Llama Materials must comply with applicable laws
        and regulations\\n(including trade compliance laws and regulations) and adhere
        to the Acceptable Use Policy for the Llama\\nMaterials (available at https://llama.meta.com/llama3_1/use-policy),
        which is hereby incorporated by\\nreference into this Agreement.\\n\\n2. Additional
        Commercial Terms. If, on the Llama 3.1 version release date, the monthly active
        users\\nof the products or services made available by or for Licensee, or
        Licensee\u2019s affiliates, is greater than 700\\nmillion monthly active users
        in the preceding calendar month, you must request a license from Meta,\\nwhich
        Meta may grant to you in its sole discretion, and you are not authorized to
        exercise any of the\\nrights under this Agreement unless or until Meta otherwise
        expressly grants you such rights.\\n\\n3. Disclaimer of Warranty. UNLESS REQUIRED
        BY APPLICABLE LAW, THE LLAMA MATERIALS AND ANY\\nOUTPUT AND RESULTS THEREFROM
        ARE PROVIDED ON AN \u201CAS IS\u201D BASIS, WITHOUT WARRANTIES OF\\nANY KIND,
        AND META DISCLAIMS ALL WARRANTIES OF ANY KIND, BOTH EXPRESS AND IMPLIED,\\nINCLUDING,
        WITHOUT LIMITATION, ANY WARRANTIES OF TITLE, NON-INFRINGEMENT,\\nMERCHANTABILITY,
        OR FITNESS FOR A PARTICULAR PURPOSE. YOU ARE SOLELY RESPONSIBLE FOR\\nDETERMINING
        THE APPROPRIATENESS OF USING OR REDISTRIBUTING THE LLAMA MATERIALS AND\\nASSUME
        ANY RISKS ASSOCIATED WITH YOUR USE OF THE LLAMA MATERIALS AND ANY OUTPUT AND\\nRESULTS.\\n\\n4.
        Limitation of Liability. IN NO EVENT WILL META OR ITS AFFILIATES BE LIABLE
        UNDER ANY THEORY OF\\nLIABILITY, WHETHER IN CONTRACT, TORT, NEGLIGENCE, PRODUCTS
        LIABILITY, OR OTHERWISE, ARISING\\nOUT OF THIS AGREEMENT, FOR ANY LOST PROFITS
        OR ANY INDIRECT, SPECIAL, CONSEQUENTIAL,\\nINCIDENTAL, EXEMPLARY OR PUNITIVE
        DAMAGES, EVEN IF META OR ITS AFFILIATES HAVE BEEN ADVISED\\nOF THE POSSIBILITY
        OF ANY OF THE FOREGOING.\\n\\n5. Intellectual Property.\\n\\n  a. No trademark
        licenses are granted under this Agreement, and in connection with the Llama\\nMaterials,
        neither Meta nor Licensee may use any name or mark owned by or associated
        with the other\\nor any of its affiliates, except as required for reasonable
        and customary use in describing and\\nredistributing the Llama Materials or
        as set forth in this Section 5(a). Meta hereby grants you a license to\\nuse
        \u201CLlama\u201D (the \u201CMark\u201D) solely as required to comply with
        the last sentence of Section 1.b.i. You will\\ncomply with Meta\u2019s brand
        guidelines (currently accessible at\\nhttps://about.meta.com/brand/resources/meta/company-brand/
        ). All goodwill arising out of your use\\nof the Mark will inure to the benefit
        of Meta.\\n\\n  b. Subject to Meta\u2019s ownership of Llama Materials and
        derivatives made by or for Meta, with\\nrespect to any derivative works and
        modifications of the Llama Materials that are made by you, as\\nbetween you
        and Meta, you are and will be the owner of such derivative works and modifications.\\n\\n
        \ c. If you institute litigation or other proceedings against Meta or any
        entity (including a\\ncross-claim or counterclaim in a lawsuit) alleging that
        the Llama Materials or Llama 3.1 outputs or\\nresults, or any portion of any
        of the foregoing, constitutes infringement of intellectual property or other\\nrights
        owned or licensable by you, then any licenses granted to you under this Agreement
        shall\\nterminate as of the date such litigation or claim is filed or instituted.
        You will indemnify and hold\\nharmless Meta from and against any claim by
        any third party arising out of or related to your use or\\ndistribution of
        the Llama Materials.\\n\\n6. Term and Termination. The term of this Agreement
        will commence upon your acceptance of this\\nAgreement or access to the Llama
        Materials and will continue in full force and effect until terminated in\\naccordance
        with the terms and conditions herein. Meta may terminate this Agreement if
        you are in\\nbreach of any term or condition of this Agreement. Upon termination
        of this Agreement, you shall delete\\nand cease use of the Llama Materials.
        Sections 3, 4 and 7 shall survive the termination of this\\nAgreement.\\n\\n7.
        Governing Law and Jurisdiction. This Agreement will be governed and construed
        under the laws of\\nthe State of California without regard to choice of law
        principles, and the UN Convention on Contracts\\nfor the International Sale
        of Goods does not apply to this Agreement. The courts of California shall
        have\\nexclusive jurisdiction of any dispute arising out of this Agreement.\\n\\n#
        Llama 3.1 Acceptable Use Policy\\n\\nMeta is committed to promoting safe and
        fair use of its tools and features, including Llama 3.1. If you\\naccess or
        use Llama 3.1, you agree to this Acceptable Use Policy (\u201CPolicy\u201D).
        The most recent copy of\\nthis policy can be found at [https://llama.meta.com/llama3_1/use-policy](https://llama.meta.com/llama3_1/use-policy)\\n\\n##
        Prohibited Uses\\n\\nWe want everyone to use Llama 3.1 safely and responsibly.
        You agree you will not use, or allow\\nothers to use, Llama 3.1 to:\\n\\n1.
        Violate the law or others\u2019 rights, including to:\\n    1. Engage in,
        promote, generate, contribute to, encourage, plan, incite, or further illegal
        or unlawful activity or content, such as:\\n        1. Violence or terrorism\\n
        \       2. Exploitation or harm to children, including the solicitation, creation,
        acquisition, or dissemination of child exploitative content or failure to
        report Child Sexual Abuse Material\\n        3. Human trafficking, exploitation,
        and sexual violence\\n        4. The illegal distribution of information or
        materials to minors, including obscene materials, or failure to employ legally
        required age-gating in connection with such information or materials.\\n        5.
        Sexual solicitation\\n        6. Any other criminal activity\\n    3. Engage
        in, promote, incite, or facilitate the harassment, abuse, threatening, or
        bullying of individuals or groups of individuals\\n    4. Engage in, promote,
        incite, or facilitate discrimination or other unlawful or harmful conduct
        in the provision of employment, employment benefits, credit, housing, other
        economic benefits, or other essential goods and services\\n    5. Engage in
        the unauthorized or unlicensed practice of any profession including, but not
        limited to, financial, legal, medical/health, or related professional practices\\n
        \   6. Collect, process, disclose, generate, or infer health, demographic,
        or other sensitive personal or private information about individuals without
        rights and consents required by applicable laws\\n    7. Engage in or facilitate
        any action or generate any content that infringes, misappropriates, or otherwise
        violates any third-party rights, including the outputs or results of any products
        or services using the Llama Materials\\n    8. Create, generate, or facilitate
        the creation of malicious code, malware, computer viruses or do anything else
        that could disable, overburden, interfere with or impair the proper working,
        integrity, operation or appearance of a website or computer system\\n\\n2.
        Engage in, promote, incite, facilitate, or assist in the planning or development
        of activities that present a risk of death or bodily harm to individuals,
        including use of Llama 3.1 related to the following:\\n    1. Military, warfare,
        nuclear industries or applications, espionage, use for materials or activities
        that are subject to the International Traffic Arms Regulations (ITAR) maintained
        by the United States Department of State\\n    2. Guns and illegal weapons
        (including weapon development)\\n    3. Illegal drugs and regulated/controlled
        substances\\n    4. Operation of critical infrastructure, transportation technologies,
        or heavy machinery\\n    5. Self-harm or harm to others, including suicide,
        cutting, and eating disorders\\n    6. Any content intended to incite or promote
        violence, abuse, or any infliction of bodily harm to an individual\\n\\n3.
        Intentionally deceive or mislead others, including use of Llama 3.1 related
        to the following:\\n    1. Generating, promoting, or furthering fraud or the
        creation or promotion of disinformation\\n    2. Generating, promoting, or
        furthering defamatory content, including the creation of defamatory statements,
        images, or other content\\n    3. Generating, promoting, or further distributing
        spam\\n    4. Impersonating another individual without consent, authorization,
        or legal right\\n    5. Representing that the use of Llama 3.1 or outputs
        are human-generated\\n    6. Generating or facilitating false online engagement,
        including fake reviews and other means of fake online engagement\\n\\n4. Fail
        to appropriately disclose to end users any known dangers of your AI system\\n\\nPlease
        report any violation of this Policy, software \u201Cbug,\u201D or other problems
        that could lead to a violation\\nof this Policy through one of the following
        means:\\n\\n* Reporting issues with the model: [https://github.com/meta-llama/llama-models/issues](https://github.com/meta-llama/llama-models/issues)\\n*
        Reporting risky content generated by the model: developers.facebook.com/llama_output_feedback\\n*
        Reporting bugs and security concerns: facebook.com/whitehat/info\\n* Reporting
        violations of the Acceptable Use Policy or unlicensed uses of Llama 3.1: <EMAIL>\\\"\\n\",\"parameters\":\"stop
        \                          \\\"\\u003c|start_header_id|\\u003e\\\"\\nstop
        \                          \\\"\\u003c|end_header_id|\\u003e\\\"\\nstop                           \\\"\\u003c|eot_id|\\u003e\\\"\",\"template\":\"{{-
        if or .System .Tools }}\\u003c|start_header_id|\\u003esystem\\u003c|end_header_id|\\u003e\\n{{-
        if .System }}\\n\\n{{ .System }}\\n{{- end }}\\n{{- if .Tools }}\\n\\nCutting
        Knowledge Date: December 2023\\n\\nWhen you receive a tool call response,
        use the output to format an answer to the orginal user question.\\n\\nYou
        are a helpful assistant with tool calling capabilities.\\n{{- end }}\\u003c|eot_id|\\u003e\\n{{-
        end }}\\n{{- range $i, $_ := .Messages }}\\n{{- $last := eq (len (slice $.Messages
        $i)) 1 }}\\n{{- if eq .Role \\\"user\\\" }}\\u003c|start_header_id|\\u003euser\\u003c|end_header_id|\\u003e\\n{{-
        if and $.Tools $last }}\\n\\nGiven the following functions, please respond
        with a JSON for a function call with its proper arguments that best answers
        the given prompt.\\n\\nRespond in the format {\\\"name\\\": function name,
        \\\"parameters\\\": dictionary of argument name and its value}. Do not use
        variables.\\n\\n{{ range $.Tools }}\\n{{- . }}\\n{{ end }}\\nQuestion: {{
        .Content }}\\u003c|eot_id|\\u003e\\n{{- else }}\\n\\n{{ .Content }}\\u003c|eot_id|\\u003e\\n{{-
        end }}{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
        end }}\\n{{- else if eq .Role \\\"assistant\\\" }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n{{-
        if .ToolCalls }}\\n{{ range .ToolCalls }}\\n{\\\"name\\\": \\\"{{ .Function.Name
        }}\\\", \\\"parameters\\\": {{ .Function.Arguments }}}{{ end }}\\n{{- else
        }}\\n\\n{{ .Content }}\\n{{- end }}{{ if not $last }}\\u003c|eot_id|\\u003e{{
        end }}\\n{{- else if eq .Role \\\"tool\\\" }}\\u003c|start_header_id|\\u003eipython\\u003c|end_header_id|\\u003e\\n\\n{{
        .Content }}\\u003c|eot_id|\\u003e{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
        end }}\\n{{- end }}\\n{{- end }}\",\"details\":{\"parent_model\":\"\",\"format\":\"gguf\",\"family\":\"llama\",\"families\":[\"llama\"],\"parameter_size\":\"8.0B\",\"quantization_level\":\"Q4_K_M\"},\"model_info\":{\"general.architecture\":\"llama\",\"general.basename\":\"Meta-Llama-3.1\",\"general.file_type\":15,\"general.finetune\":\"Instruct\",\"general.languages\":[\"en\",\"de\",\"fr\",\"it\",\"pt\",\"hi\",\"es\",\"th\"],\"general.license\":\"llama3.1\",\"general.parameter_count\":**********,\"general.quantization_version\":2,\"general.size_label\":\"8B\",\"general.tags\":[\"facebook\",\"meta\",\"pytorch\",\"llama\",\"llama-3\",\"text-generation\"],\"general.type\":\"model\",\"llama.attention.head_count\":32,\"llama.attention.head_count_kv\":8,\"llama.attention.layer_norm_rms_epsilon\":0.00001,\"llama.block_count\":32,\"llama.context_length\":131072,\"llama.embedding_length\":4096,\"llama.feed_forward_length\":14336,\"llama.rope.dimension_count\":128,\"llama.rope.freq_base\":500000,\"llama.vocab_size\":128256,\"tokenizer.ggml.bos_token_id\":128000,\"tokenizer.ggml.eos_token_id\":128009,\"tokenizer.ggml.merges\":null,\"tokenizer.ggml.model\":\"gpt2\",\"tokenizer.ggml.pre\":\"llama-bpe\",\"tokenizer.ggml.token_type\":null,\"tokenizer.ggml.tokens\":null},\"tensors\":[{\"name\":\"token_embd.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,128256]},{\"name\":\"rope_freqs.weight\",\"type\":\"F32\",\"shape\":[64]},{\"name\":\"blk.0.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.0.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.0.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.0.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.0.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.0.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.0.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.0.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.0.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.1.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.1.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.1.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.1.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.1.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.1.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.1.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.1.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.1.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.2.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.2.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.2.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.2.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.2.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.2.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.2.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.2.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.2.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.3.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.3.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.3.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.3.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.3.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.3.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.3.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.3.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.3.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.4.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.4.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.4.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.4.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.4.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.4.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.4.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.4.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.4.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.5.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.5.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.5.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.5.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.5.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.5.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.5.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.5.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.5.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.6.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.6.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.6.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.6.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.6.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.6.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.6.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.6.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.6.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.7.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.7.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.7.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.7.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.7.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.7.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.7.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.7.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.7.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.8.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.8.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.8.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.8.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.8.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.8.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.8.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.8.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.8.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.10.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.10.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.10.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.10.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.10.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.10.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.10.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.10.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.10.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.11.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.11.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.11.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.11.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.11.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.11.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.11.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.11.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.11.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.12.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.12.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.12.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.12.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.12.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.12.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.12.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.12.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.12.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.13.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.13.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.13.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.13.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.13.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.13.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.13.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.13.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.13.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.14.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.14.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.14.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.14.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.14.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.14.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.14.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.14.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.14.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.15.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.15.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.15.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.15.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.15.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.15.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.15.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.15.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.15.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.16.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.16.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.16.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.16.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.16.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.16.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.16.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.16.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.16.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.17.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.17.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.17.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.17.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.17.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.17.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.17.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.17.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.17.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.18.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.18.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.18.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.18.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.18.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.18.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.18.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.18.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.18.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.19.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.19.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.19.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.19.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.19.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.19.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.19.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.19.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.19.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.20.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.20.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.20.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.20.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.20.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.9.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.9.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.9.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.9.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.9.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.9.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.9.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.9.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.9.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.20.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.20.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.20.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.20.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.21.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.21.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.21.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.21.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.21.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.21.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.21.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.21.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.21.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.22.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.22.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.22.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.22.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.22.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.22.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.22.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.22.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.22.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.23.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.23.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.23.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.23.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.23.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.23.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.23.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.23.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.23.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.24.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.24.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.24.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.24.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.24.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.24.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.24.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.24.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.24.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.25.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.25.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.25.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.25.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.25.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.25.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.25.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.25.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.25.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.26.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.26.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.26.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.26.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.26.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.26.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.26.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.26.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.26.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.27.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.27.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.27.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.27.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.27.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.27.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.27.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.27.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.27.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.28.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.28.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.28.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.28.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.28.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.28.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.28.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.28.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.28.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.29.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.29.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.29.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.29.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.29.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.29.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.29.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.29.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.29.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.30.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.30.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.30.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.30.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.30.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.30.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.30.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.30.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.30.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.31.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.31.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.31.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.31.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.31.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.31.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"output.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,128256]},{\"name\":\"blk.31.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.31.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.31.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"output_norm.weight\",\"type\":\"F32\",\"shape\":[4096]}],\"capabilities\":[\"completion\",\"tools\"],\"modified_at\":\"2025-04-11T14:41:15.05985701Z\"}"
    headers:
      Content-Type:
      - application/json; charset=utf-8
      Date:
      - Wed, 07 May 2025 01:16:23 GMT
      Transfer-Encoding:
      - chunked
    status:
      code: 200
      message: OK
- request:
    body: '{"name": "llama3.1"}'
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '20'
      content-type:
      - application/json
      host:
      - localhost:11434
      user-agent:
      - litellm/1.68.0
    method: POST
    uri: http://localhost:11434/api/show
  response:
    body:
      string: "{\"license\":\"LLAMA 3.1 COMMUNITY LICENSE AGREEMENT\\nLlama 3.1 Version
        Release Date: July 23, 2024\\n\\n\u201CAgreement\u201D means the terms and
        conditions for use, reproduction, distribution and modification of the\\nLlama
        Materials set forth herein.\\n\\n\u201CDocumentation\u201D means the specifications,
        manuals and documentation accompanying Llama 3.1\\ndistributed by Meta at
        https://llama.meta.com/doc/overview.\\n\\n\u201CLicensee\u201D or \u201Cyou\u201D
        means you, or your employer or any other person or entity (if you are entering
        into\\nthis Agreement on such person or entity\u2019s behalf), of the age
        required under applicable laws, rules or\\nregulations to provide legal consent
        and that has legal authority to bind your employer or such other\\nperson
        or entity if you are entering in this Agreement on their behalf.\\n\\n\u201CLlama
        3.1\u201D means the foundational large language models and software and algorithms,
        including\\nmachine-learning model code, trained model weights, inference-enabling
        code, training-enabling code,\\nfine-tuning enabling code and other elements
        of the foregoing distributed by Meta at\\nhttps://llama.meta.com/llama-downloads.\\n\\n\u201CLlama
        Materials\u201D means, collectively, Meta\u2019s proprietary Llama 3.1 and
        Documentation (and any\\nportion thereof) made available under this Agreement.\\n\\n\u201CMeta\u201D
        or \u201Cwe\u201D means Meta Platforms Ireland Limited (if you are located
        in or, if you are an entity, your\\nprincipal place of business is in the
        EEA or Switzerland) and Meta Platforms, Inc. (if you are located\\noutside
        of the EEA or Switzerland).\\n\\nBy clicking \u201CI Accept\u201D below or
        by using or distributing any portion or element of the Llama Materials,\\nyou
        agree to be bound by this Agreement.\\n\\n1. License Rights and Redistribution.\\n\\n
        \ a. Grant of Rights. You are granted a non-exclusive, worldwide, non-transferable
        and royalty-free\\nlimited license under Meta\u2019s intellectual property
        or other rights owned by Meta embodied in the Llama\\nMaterials to use, reproduce,
        distribute, copy, create derivative works of, and make modifications to the\\nLlama
        Materials.\\n\\n  b. Redistribution and Use.\\n\\n      i. If you distribute
        or make available the Llama Materials (or any derivative works\\nthereof),
        or a product or service (including another AI model) that contains any of
        them, you shall (A)\\nprovide a copy of this Agreement with any such Llama
        Materials; and (B) prominently display \u201CBuilt with\\nLlama\u201D on a
        related website, user interface, blogpost, about page, or product documentation.
        If you use\\nthe Llama Materials or any outputs or results of the Llama Materials
        to create, train, fine tune, or\\notherwise improve an AI model, which is
        distributed or made available, you shall also include \u201CLlama\u201D at\\nthe
        beginning of any such AI model name.\\n\\n      ii. If you receive Llama Materials,
        or any derivative works thereof, from a Licensee as part \\nof an integrated
        end user product, then Section 2 of this Agreement will not apply to you.\\n\\n
        \     iii. You must retain in all copies of the Llama Materials that you distribute
        the following\\nattribution notice within a \u201CNotice\u201D text file distributed
        as a part of such copies: \u201CLlama 3.1 is\\nlicensed under the Llama 3.1
        Community License, Copyright \xA9 Meta Platforms, Inc. All Rights\\nReserved.\u201D\\n\\n
        \     iv. Your use of the Llama Materials must comply with applicable laws
        and regulations\\n(including trade compliance laws and regulations) and adhere
        to the Acceptable Use Policy for the Llama\\nMaterials (available at https://llama.meta.com/llama3_1/use-policy),
        which is hereby incorporated by\\nreference into this Agreement.\\n\\n2. Additional
        Commercial Terms. If, on the Llama 3.1 version release date, the monthly active
        users\\nof the products or services made available by or for Licensee, or
        Licensee\u2019s affiliates, is greater than 700\\nmillion monthly active users
        in the preceding calendar month, you must request a license from Meta,\\nwhich
        Meta may grant to you in its sole discretion, and you are not authorized to
        exercise any of the\\nrights under this Agreement unless or until Meta otherwise
        expressly grants you such rights.\\n\\n3. Disclaimer of Warranty. UNLESS REQUIRED
        BY APPLICABLE LAW, THE LLAMA MATERIALS AND ANY\\nOUTPUT AND RESULTS THEREFROM
        ARE PROVIDED ON AN \u201CAS IS\u201D BASIS, WITHOUT WARRANTIES OF\\nANY KIND,
        AND META DISCLAIMS ALL WARRANTIES OF ANY KIND, BOTH EXPRESS AND IMPLIED,\\nINCLUDING,
        WITHOUT LIMITATION, ANY WARRANTIES OF TITLE, NON-INFRINGEMENT,\\nMERCHANTABILITY,
        OR FITNESS FOR A PARTICULAR PURPOSE. YOU ARE SOLELY RESPONSIBLE FOR\\nDETERMINING
        THE APPROPRIATENESS OF USING OR REDISTRIBUTING THE LLAMA MATERIALS AND\\nASSUME
        ANY RISKS ASSOCIATED WITH YOUR USE OF THE LLAMA MATERIALS AND ANY OUTPUT AND\\nRESULTS.\\n\\n4.
        Limitation of Liability. IN NO EVENT WILL META OR ITS AFFILIATES BE LIABLE
        UNDER ANY THEORY OF\\nLIABILITY, WHETHER IN CONTRACT, TORT, NEGLIGENCE, PRODUCTS
        LIABILITY, OR OTHERWISE, ARISING\\nOUT OF THIS AGREEMENT, FOR ANY LOST PROFITS
        OR ANY INDIRECT, SPECIAL, CONSEQUENTIAL,\\nINCIDENTAL, EXEMPLARY OR PUNITIVE
        DAMAGES, EVEN IF META OR ITS AFFILIATES HAVE BEEN ADVISED\\nOF THE POSSIBILITY
        OF ANY OF THE FOREGOING.\\n\\n5. Intellectual Property.\\n\\n  a. No trademark
        licenses are granted under this Agreement, and in connection with the Llama\\nMaterials,
        neither Meta nor Licensee may use any name or mark owned by or associated
        with the other\\nor any of its affiliates, except as required for reasonable
        and customary use in describing and\\nredistributing the Llama Materials or
        as set forth in this Section 5(a). Meta hereby grants you a license to\\nuse
        \u201CLlama\u201D (the \u201CMark\u201D) solely as required to comply with
        the last sentence of Section 1.b.i. You will\\ncomply with Meta\u2019s brand
        guidelines (currently accessible at\\nhttps://about.meta.com/brand/resources/meta/company-brand/
        ). All goodwill arising out of your use\\nof the Mark will inure to the benefit
        of Meta.\\n\\n  b. Subject to Meta\u2019s ownership of Llama Materials and
        derivatives made by or for Meta, with\\nrespect to any derivative works and
        modifications of the Llama Materials that are made by you, as\\nbetween you
        and Meta, you are and will be the owner of such derivative works and modifications.\\n\\n
        \ c. If you institute litigation or other proceedings against Meta or any
        entity (including a\\ncross-claim or counterclaim in a lawsuit) alleging that
        the Llama Materials or Llama 3.1 outputs or\\nresults, or any portion of any
        of the foregoing, constitutes infringement of intellectual property or other\\nrights
        owned or licensable by you, then any licenses granted to you under this Agreement
        shall\\nterminate as of the date such litigation or claim is filed or instituted.
        You will indemnify and hold\\nharmless Meta from and against any claim by
        any third party arising out of or related to your use or\\ndistribution of
        the Llama Materials.\\n\\n6. Term and Termination. The term of this Agreement
        will commence upon your acceptance of this\\nAgreement or access to the Llama
        Materials and will continue in full force and effect until terminated in\\naccordance
        with the terms and conditions herein. Meta may terminate this Agreement if
        you are in\\nbreach of any term or condition of this Agreement. Upon termination
        of this Agreement, you shall delete\\nand cease use of the Llama Materials.
        Sections 3, 4 and 7 shall survive the termination of this\\nAgreement.\\n\\n7.
        Governing Law and Jurisdiction. This Agreement will be governed and construed
        under the laws of\\nthe State of California without regard to choice of law
        principles, and the UN Convention on Contracts\\nfor the International Sale
        of Goods does not apply to this Agreement. The courts of California shall
        have\\nexclusive jurisdiction of any dispute arising out of this Agreement.\\n\\n#
        Llama 3.1 Acceptable Use Policy\\n\\nMeta is committed to promoting safe and
        fair use of its tools and features, including Llama 3.1. If you\\naccess or
        use Llama 3.1, you agree to this Acceptable Use Policy (\u201CPolicy\u201D).
        The most recent copy of\\nthis policy can be found at [https://llama.meta.com/llama3_1/use-policy](https://llama.meta.com/llama3_1/use-policy)\\n\\n##
        Prohibited Uses\\n\\nWe want everyone to use Llama 3.1 safely and responsibly.
        You agree you will not use, or allow\\nothers to use, Llama 3.1 to:\\n\\n1.
        Violate the law or others\u2019 rights, including to:\\n    1. Engage in,
        promote, generate, contribute to, encourage, plan, incite, or further illegal
        or unlawful activity or content, such as:\\n        1. Violence or terrorism\\n
        \       2. Exploitation or harm to children, including the solicitation, creation,
        acquisition, or dissemination of child exploitative content or failure to
        report Child Sexual Abuse Material\\n        3. Human trafficking, exploitation,
        and sexual violence\\n        4. The illegal distribution of information or
        materials to minors, including obscene materials, or failure to employ legally
        required age-gating in connection with such information or materials.\\n        5.
        Sexual solicitation\\n        6. Any other criminal activity\\n    3. Engage
        in, promote, incite, or facilitate the harassment, abuse, threatening, or
        bullying of individuals or groups of individuals\\n    4. Engage in, promote,
        incite, or facilitate discrimination or other unlawful or harmful conduct
        in the provision of employment, employment benefits, credit, housing, other
        economic benefits, or other essential goods and services\\n    5. Engage in
        the unauthorized or unlicensed practice of any profession including, but not
        limited to, financial, legal, medical/health, or related professional practices\\n
        \   6. Collect, process, disclose, generate, or infer health, demographic,
        or other sensitive personal or private information about individuals without
        rights and consents required by applicable laws\\n    7. Engage in or facilitate
        any action or generate any content that infringes, misappropriates, or otherwise
        violates any third-party rights, including the outputs or results of any products
        or services using the Llama Materials\\n    8. Create, generate, or facilitate
        the creation of malicious code, malware, computer viruses or do anything else
        that could disable, overburden, interfere with or impair the proper working,
        integrity, operation or appearance of a website or computer system\\n\\n2.
        Engage in, promote, incite, facilitate, or assist in the planning or development
        of activities that present a risk of death or bodily harm to individuals,
        including use of Llama 3.1 related to the following:\\n    1. Military, warfare,
        nuclear industries or applications, espionage, use for materials or activities
        that are subject to the International Traffic Arms Regulations (ITAR) maintained
        by the United States Department of State\\n    2. Guns and illegal weapons
        (including weapon development)\\n    3. Illegal drugs and regulated/controlled
        substances\\n    4. Operation of critical infrastructure, transportation technologies,
        or heavy machinery\\n    5. Self-harm or harm to others, including suicide,
        cutting, and eating disorders\\n    6. Any content intended to incite or promote
        violence, abuse, or any infliction of bodily harm to an individual\\n\\n3.
        Intentionally deceive or mislead others, including use of Llama 3.1 related
        to the following:\\n    1. Generating, promoting, or furthering fraud or the
        creation or promotion of disinformation\\n    2. Generating, promoting, or
        furthering defamatory content, including the creation of defamatory statements,
        images, or other content\\n    3. Generating, promoting, or further distributing
        spam\\n    4. Impersonating another individual without consent, authorization,
        or legal right\\n    5. Representing that the use of Llama 3.1 or outputs
        are human-generated\\n    6. Generating or facilitating false online engagement,
        including fake reviews and other means of fake online engagement\\n\\n4. Fail
        to appropriately disclose to end users any known dangers of your AI system\\n\\nPlease
        report any violation of this Policy, software \u201Cbug,\u201D or other problems
        that could lead to a violation\\nof this Policy through one of the following
        means:\\n\\n* Reporting issues with the model: [https://github.com/meta-llama/llama-models/issues](https://github.com/meta-llama/llama-models/issues)\\n*
        Reporting risky content generated by the model: developers.facebook.com/llama_output_feedback\\n*
        Reporting bugs and security concerns: facebook.com/whitehat/info\\n* Reporting
        violations of the Acceptable Use Policy or unlicensed uses of Llama 3.1: <EMAIL>\",\"modelfile\":\"#
        Modelfile generated by \\\"ollama show\\\"\\n# To build a new Modelfile based
        on this, replace FROM with:\\n# FROM llama3.1:latest\\n\\nFROM /root/.ollama/models/blobs/sha256-667b0c1932bc6ffc593ed1d03f895bf2dc8dc6df21db3042284a6f4416b06a29\\nTEMPLATE
        \\\"\\\"\\\"{{- if or .System .Tools }}\\u003c|start_header_id|\\u003esystem\\u003c|end_header_id|\\u003e\\n{{-
        if .System }}\\n\\n{{ .System }}\\n{{- end }}\\n{{- if .Tools }}\\n\\nCutting
        Knowledge Date: December 2023\\n\\nWhen you receive a tool call response,
        use the output to format an answer to the orginal user question.\\n\\nYou
        are a helpful assistant with tool calling capabilities.\\n{{- end }}\\u003c|eot_id|\\u003e\\n{{-
        end }}\\n{{- range $i, $_ := .Messages }}\\n{{- $last := eq (len (slice $.Messages
        $i)) 1 }}\\n{{- if eq .Role \\\"user\\\" }}\\u003c|start_header_id|\\u003euser\\u003c|end_header_id|\\u003e\\n{{-
        if and $.Tools $last }}\\n\\nGiven the following functions, please respond
        with a JSON for a function call with its proper arguments that best answers
        the given prompt.\\n\\nRespond in the format {\\\"name\\\": function name,
        \\\"parameters\\\": dictionary of argument name and its value}. Do not use
        variables.\\n\\n{{ range $.Tools }}\\n{{- . }}\\n{{ end }}\\nQuestion: {{
        .Content }}\\u003c|eot_id|\\u003e\\n{{- else }}\\n\\n{{ .Content }}\\u003c|eot_id|\\u003e\\n{{-
        end }}{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
        end }}\\n{{- else if eq .Role \\\"assistant\\\" }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n{{-
        if .ToolCalls }}\\n{{ range .ToolCalls }}\\n{\\\"name\\\": \\\"{{ .Function.Name
        }}\\\", \\\"parameters\\\": {{ .Function.Arguments }}}{{ end }}\\n{{- else
        }}\\n\\n{{ .Content }}\\n{{- end }}{{ if not $last }}\\u003c|eot_id|\\u003e{{
        end }}\\n{{- else if eq .Role \\\"tool\\\" }}\\u003c|start_header_id|\\u003eipython\\u003c|end_header_id|\\u003e\\n\\n{{
        .Content }}\\u003c|eot_id|\\u003e{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
        end }}\\n{{- end }}\\n{{- end }}\\\"\\\"\\\"\\nPARAMETER stop \\u003c|start_header_id|\\u003e\\nPARAMETER
        stop \\u003c|end_header_id|\\u003e\\nPARAMETER stop \\u003c|eot_id|\\u003e\\nLICENSE
        \\\"LLAMA 3.1 COMMUNITY LICENSE AGREEMENT\\nLlama 3.1 Version Release Date:
        July 23, 2024\\n\\n\u201CAgreement\u201D means the terms and conditions for
        use, reproduction, distribution and modification of the\\nLlama Materials
        set forth herein.\\n\\n\u201CDocumentation\u201D means the specifications,
        manuals and documentation accompanying Llama 3.1\\ndistributed by Meta at
        https://llama.meta.com/doc/overview.\\n\\n\u201CLicensee\u201D or \u201Cyou\u201D
        means you, or your employer or any other person or entity (if you are entering
        into\\nthis Agreement on such person or entity\u2019s behalf), of the age
        required under applicable laws, rules or\\nregulations to provide legal consent
        and that has legal authority to bind your employer or such other\\nperson
        or entity if you are entering in this Agreement on their behalf.\\n\\n\u201CLlama
        3.1\u201D means the foundational large language models and software and algorithms,
        including\\nmachine-learning model code, trained model weights, inference-enabling
        code, training-enabling code,\\nfine-tuning enabling code and other elements
        of the foregoing distributed by Meta at\\nhttps://llama.meta.com/llama-downloads.\\n\\n\u201CLlama
        Materials\u201D means, collectively, Meta\u2019s proprietary Llama 3.1 and
        Documentation (and any\\nportion thereof) made available under this Agreement.\\n\\n\u201CMeta\u201D
        or \u201Cwe\u201D means Meta Platforms Ireland Limited (if you are located
        in or, if you are an entity, your\\nprincipal place of business is in the
        EEA or Switzerland) and Meta Platforms, Inc. (if you are located\\noutside
        of the EEA or Switzerland).\\n\\nBy clicking \u201CI Accept\u201D below or
        by using or distributing any portion or element of the Llama Materials,\\nyou
        agree to be bound by this Agreement.\\n\\n1. License Rights and Redistribution.\\n\\n
        \ a. Grant of Rights. You are granted a non-exclusive, worldwide, non-transferable
        and royalty-free\\nlimited license under Meta\u2019s intellectual property
        or other rights owned by Meta embodied in the Llama\\nMaterials to use, reproduce,
        distribute, copy, create derivative works of, and make modifications to the\\nLlama
        Materials.\\n\\n  b. Redistribution and Use.\\n\\n      i. If you distribute
        or make available the Llama Materials (or any derivative works\\nthereof),
        or a product or service (including another AI model) that contains any of
        them, you shall (A)\\nprovide a copy of this Agreement with any such Llama
        Materials; and (B) prominently display \u201CBuilt with\\nLlama\u201D on a
        related website, user interface, blogpost, about page, or product documentation.
        If you use\\nthe Llama Materials or any outputs or results of the Llama Materials
        to create, train, fine tune, or\\notherwise improve an AI model, which is
        distributed or made available, you shall also include \u201CLlama\u201D at\\nthe
        beginning of any such AI model name.\\n\\n      ii. If you receive Llama Materials,
        or any derivative works thereof, from a Licensee as part \\nof an integrated
        end user product, then Section 2 of this Agreement will not apply to you.\\n\\n
        \     iii. You must retain in all copies of the Llama Materials that you distribute
        the following\\nattribution notice within a \u201CNotice\u201D text file distributed
        as a part of such copies: \u201CLlama 3.1 is\\nlicensed under the Llama 3.1
        Community License, Copyright \xA9 Meta Platforms, Inc. All Rights\\nReserved.\u201D\\n\\n
        \     iv. Your use of the Llama Materials must comply with applicable laws
        and regulations\\n(including trade compliance laws and regulations) and adhere
        to the Acceptable Use Policy for the Llama\\nMaterials (available at https://llama.meta.com/llama3_1/use-policy),
        which is hereby incorporated by\\nreference into this Agreement.\\n\\n2. Additional
        Commercial Terms. If, on the Llama 3.1 version release date, the monthly active
        users\\nof the products or services made available by or for Licensee, or
        Licensee\u2019s affiliates, is greater than 700\\nmillion monthly active users
        in the preceding calendar month, you must request a license from Meta,\\nwhich
        Meta may grant to you in its sole discretion, and you are not authorized to
        exercise any of the\\nrights under this Agreement unless or until Meta otherwise
        expressly grants you such rights.\\n\\n3. Disclaimer of Warranty. UNLESS REQUIRED
        BY APPLICABLE LAW, THE LLAMA MATERIALS AND ANY\\nOUTPUT AND RESULTS THEREFROM
        ARE PROVIDED ON AN \u201CAS IS\u201D BASIS, WITHOUT WARRANTIES OF\\nANY KIND,
        AND META DISCLAIMS ALL WARRANTIES OF ANY KIND, BOTH EXPRESS AND IMPLIED,\\nINCLUDING,
        WITHOUT LIMITATION, ANY WARRANTIES OF TITLE, NON-INFRINGEMENT,\\nMERCHANTABILITY,
        OR FITNESS FOR A PARTICULAR PURPOSE. YOU ARE SOLELY RESPONSIBLE FOR\\nDETERMINING
        THE APPROPRIATENESS OF USING OR REDISTRIBUTING THE LLAMA MATERIALS AND\\nASSUME
        ANY RISKS ASSOCIATED WITH YOUR USE OF THE LLAMA MATERIALS AND ANY OUTPUT AND\\nRESULTS.\\n\\n4.
        Limitation of Liability. IN NO EVENT WILL META OR ITS AFFILIATES BE LIABLE
        UNDER ANY THEORY OF\\nLIABILITY, WHETHER IN CONTRACT, TORT, NEGLIGENCE, PRODUCTS
        LIABILITY, OR OTHERWISE, ARISING\\nOUT OF THIS AGREEMENT, FOR ANY LOST PROFITS
        OR ANY INDIRECT, SPECIAL, CONSEQUENTIAL,\\nINCIDENTAL, EXEMPLARY OR PUNITIVE
        DAMAGES, EVEN IF META OR ITS AFFILIATES HAVE BEEN ADVISED\\nOF THE POSSIBILITY
        OF ANY OF THE FOREGOING.\\n\\n5. Intellectual Property.\\n\\n  a. No trademark
        licenses are granted under this Agreement, and in connection with the Llama\\nMaterials,
        neither Meta nor Licensee may use any name or mark owned by or associated
        with the other\\nor any of its affiliates, except as required for reasonable
        and customary use in describing and\\nredistributing the Llama Materials or
        as set forth in this Section 5(a). Meta hereby grants you a license to\\nuse
        \u201CLlama\u201D (the \u201CMark\u201D) solely as required to comply with
        the last sentence of Section 1.b.i. You will\\ncomply with Meta\u2019s brand
        guidelines (currently accessible at\\nhttps://about.meta.com/brand/resources/meta/company-brand/
        ). All goodwill arising out of your use\\nof the Mark will inure to the benefit
        of Meta.\\n\\n  b. Subject to Meta\u2019s ownership of Llama Materials and
        derivatives made by or for Meta, with\\nrespect to any derivative works and
        modifications of the Llama Materials that are made by you, as\\nbetween you
        and Meta, you are and will be the owner of such derivative works and modifications.\\n\\n
        \ c. If you institute litigation or other proceedings against Meta or any
        entity (including a\\ncross-claim or counterclaim in a lawsuit) alleging that
        the Llama Materials or Llama 3.1 outputs or\\nresults, or any portion of any
        of the foregoing, constitutes infringement of intellectual property or other\\nrights
        owned or licensable by you, then any licenses granted to you under this Agreement
        shall\\nterminate as of the date such litigation or claim is filed or instituted.
        You will indemnify and hold\\nharmless Meta from and against any claim by
        any third party arising out of or related to your use or\\ndistribution of
        the Llama Materials.\\n\\n6. Term and Termination. The term of this Agreement
        will commence upon your acceptance of this\\nAgreement or access to the Llama
        Materials and will continue in full force and effect until terminated in\\naccordance
        with the terms and conditions herein. Meta may terminate this Agreement if
        you are in\\nbreach of any term or condition of this Agreement. Upon termination
        of this Agreement, you shall delete\\nand cease use of the Llama Materials.
        Sections 3, 4 and 7 shall survive the termination of this\\nAgreement.\\n\\n7.
        Governing Law and Jurisdiction. This Agreement will be governed and construed
        under the laws of\\nthe State of California without regard to choice of law
        principles, and the UN Convention on Contracts\\nfor the International Sale
        of Goods does not apply to this Agreement. The courts of California shall
        have\\nexclusive jurisdiction of any dispute arising out of this Agreement.\\n\\n#
        Llama 3.1 Acceptable Use Policy\\n\\nMeta is committed to promoting safe and
        fair use of its tools and features, including Llama 3.1. If you\\naccess or
        use Llama 3.1, you agree to this Acceptable Use Policy (\u201CPolicy\u201D).
        The most recent copy of\\nthis policy can be found at [https://llama.meta.com/llama3_1/use-policy](https://llama.meta.com/llama3_1/use-policy)\\n\\n##
        Prohibited Uses\\n\\nWe want everyone to use Llama 3.1 safely and responsibly.
        You agree you will not use, or allow\\nothers to use, Llama 3.1 to:\\n\\n1.
        Violate the law or others\u2019 rights, including to:\\n    1. Engage in,
        promote, generate, contribute to, encourage, plan, incite, or further illegal
        or unlawful activity or content, such as:\\n        1. Violence or terrorism\\n
        \       2. Exploitation or harm to children, including the solicitation, creation,
        acquisition, or dissemination of child exploitative content or failure to
        report Child Sexual Abuse Material\\n        3. Human trafficking, exploitation,
        and sexual violence\\n        4. The illegal distribution of information or
        materials to minors, including obscene materials, or failure to employ legally
        required age-gating in connection with such information or materials.\\n        5.
        Sexual solicitation\\n        6. Any other criminal activity\\n    3. Engage
        in, promote, incite, or facilitate the harassment, abuse, threatening, or
        bullying of individuals or groups of individuals\\n    4. Engage in, promote,
        incite, or facilitate discrimination or other unlawful or harmful conduct
        in the provision of employment, employment benefits, credit, housing, other
        economic benefits, or other essential goods and services\\n    5. Engage in
        the unauthorized or unlicensed practice of any profession including, but not
        limited to, financial, legal, medical/health, or related professional practices\\n
        \   6. Collect, process, disclose, generate, or infer health, demographic,
        or other sensitive personal or private information about individuals without
        rights and consents required by applicable laws\\n    7. Engage in or facilitate
        any action or generate any content that infringes, misappropriates, or otherwise
        violates any third-party rights, including the outputs or results of any products
        or services using the Llama Materials\\n    8. Create, generate, or facilitate
        the creation of malicious code, malware, computer viruses or do anything else
        that could disable, overburden, interfere with or impair the proper working,
        integrity, operation or appearance of a website or computer system\\n\\n2.
        Engage in, promote, incite, facilitate, or assist in the planning or development
        of activities that present a risk of death or bodily harm to individuals,
        including use of Llama 3.1 related to the following:\\n    1. Military, warfare,
        nuclear industries or applications, espionage, use for materials or activities
        that are subject to the International Traffic Arms Regulations (ITAR) maintained
        by the United States Department of State\\n    2. Guns and illegal weapons
        (including weapon development)\\n    3. Illegal drugs and regulated/controlled
        substances\\n    4. Operation of critical infrastructure, transportation technologies,
        or heavy machinery\\n    5. Self-harm or harm to others, including suicide,
        cutting, and eating disorders\\n    6. Any content intended to incite or promote
        violence, abuse, or any infliction of bodily harm to an individual\\n\\n3.
        Intentionally deceive or mislead others, including use of Llama 3.1 related
        to the following:\\n    1. Generating, promoting, or furthering fraud or the
        creation or promotion of disinformation\\n    2. Generating, promoting, or
        furthering defamatory content, including the creation of defamatory statements,
        images, or other content\\n    3. Generating, promoting, or further distributing
        spam\\n    4. Impersonating another individual without consent, authorization,
        or legal right\\n    5. Representing that the use of Llama 3.1 or outputs
        are human-generated\\n    6. Generating or facilitating false online engagement,
        including fake reviews and other means of fake online engagement\\n\\n4. Fail
        to appropriately disclose to end users any known dangers of your AI system\\n\\nPlease
        report any violation of this Policy, software \u201Cbug,\u201D or other problems
        that could lead to a violation\\nof this Policy through one of the following
        means:\\n\\n* Reporting issues with the model: [https://github.com/meta-llama/llama-models/issues](https://github.com/meta-llama/llama-models/issues)\\n*
        Reporting risky content generated by the model: developers.facebook.com/llama_output_feedback\\n*
        Reporting bugs and security concerns: facebook.com/whitehat/info\\n* Reporting
        violations of the Acceptable Use Policy or unlicensed uses of Llama 3.1: <EMAIL>\\\"\\n\",\"parameters\":\"stop
        \                          \\\"\\u003c|start_header_id|\\u003e\\\"\\nstop
        \                          \\\"\\u003c|end_header_id|\\u003e\\\"\\nstop                           \\\"\\u003c|eot_id|\\u003e\\\"\",\"template\":\"{{-
        if or .System .Tools }}\\u003c|start_header_id|\\u003esystem\\u003c|end_header_id|\\u003e\\n{{-
        if .System }}\\n\\n{{ .System }}\\n{{- end }}\\n{{- if .Tools }}\\n\\nCutting
        Knowledge Date: December 2023\\n\\nWhen you receive a tool call response,
        use the output to format an answer to the orginal user question.\\n\\nYou
        are a helpful assistant with tool calling capabilities.\\n{{- end }}\\u003c|eot_id|\\u003e\\n{{-
        end }}\\n{{- range $i, $_ := .Messages }}\\n{{- $last := eq (len (slice $.Messages
        $i)) 1 }}\\n{{- if eq .Role \\\"user\\\" }}\\u003c|start_header_id|\\u003euser\\u003c|end_header_id|\\u003e\\n{{-
        if and $.Tools $last }}\\n\\nGiven the following functions, please respond
        with a JSON for a function call with its proper arguments that best answers
        the given prompt.\\n\\nRespond in the format {\\\"name\\\": function name,
        \\\"parameters\\\": dictionary of argument name and its value}. Do not use
        variables.\\n\\n{{ range $.Tools }}\\n{{- . }}\\n{{ end }}\\nQuestion: {{
        .Content }}\\u003c|eot_id|\\u003e\\n{{- else }}\\n\\n{{ .Content }}\\u003c|eot_id|\\u003e\\n{{-
        end }}{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
        end }}\\n{{- else if eq .Role \\\"assistant\\\" }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n{{-
        if .ToolCalls }}\\n{{ range .ToolCalls }}\\n{\\\"name\\\": \\\"{{ .Function.Name
        }}\\\", \\\"parameters\\\": {{ .Function.Arguments }}}{{ end }}\\n{{- else
        }}\\n\\n{{ .Content }}\\n{{- end }}{{ if not $last }}\\u003c|eot_id|\\u003e{{
        end }}\\n{{- else if eq .Role \\\"tool\\\" }}\\u003c|start_header_id|\\u003eipython\\u003c|end_header_id|\\u003e\\n\\n{{
        .Content }}\\u003c|eot_id|\\u003e{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
        end }}\\n{{- end }}\\n{{- end }}\",\"details\":{\"parent_model\":\"\",\"format\":\"gguf\",\"family\":\"llama\",\"families\":[\"llama\"],\"parameter_size\":\"8.0B\",\"quantization_level\":\"Q4_K_M\"},\"model_info\":{\"general.architecture\":\"llama\",\"general.basename\":\"Meta-Llama-3.1\",\"general.file_type\":15,\"general.finetune\":\"Instruct\",\"general.languages\":[\"en\",\"de\",\"fr\",\"it\",\"pt\",\"hi\",\"es\",\"th\"],\"general.license\":\"llama3.1\",\"general.parameter_count\":**********,\"general.quantization_version\":2,\"general.size_label\":\"8B\",\"general.tags\":[\"facebook\",\"meta\",\"pytorch\",\"llama\",\"llama-3\",\"text-generation\"],\"general.type\":\"model\",\"llama.attention.head_count\":32,\"llama.attention.head_count_kv\":8,\"llama.attention.layer_norm_rms_epsilon\":0.00001,\"llama.block_count\":32,\"llama.context_length\":131072,\"llama.embedding_length\":4096,\"llama.feed_forward_length\":14336,\"llama.rope.dimension_count\":128,\"llama.rope.freq_base\":500000,\"llama.vocab_size\":128256,\"tokenizer.ggml.bos_token_id\":128000,\"tokenizer.ggml.eos_token_id\":128009,\"tokenizer.ggml.merges\":null,\"tokenizer.ggml.model\":\"gpt2\",\"tokenizer.ggml.pre\":\"llama-bpe\",\"tokenizer.ggml.token_type\":null,\"tokenizer.ggml.tokens\":null},\"tensors\":[{\"name\":\"token_embd.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,128256]},{\"name\":\"rope_freqs.weight\",\"type\":\"F32\",\"shape\":[64]},{\"name\":\"blk.0.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.0.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.0.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.0.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.0.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.0.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.0.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.0.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.0.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.1.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.1.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.1.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.1.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.1.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.1.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.1.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.1.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.1.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.2.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.2.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.2.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.2.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.2.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.2.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.2.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.2.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.2.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.3.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.3.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.3.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.3.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.3.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.3.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.3.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.3.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.3.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.4.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.4.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.4.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.4.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.4.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.4.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.4.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.4.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.4.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.5.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.5.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.5.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.5.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.5.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.5.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.5.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.5.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.5.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.6.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.6.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.6.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.6.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.6.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.6.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.6.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.6.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.6.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.7.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.7.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.7.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.7.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.7.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.7.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.7.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.7.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.7.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.8.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.8.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.8.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.8.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.8.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.8.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.8.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.8.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.8.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.10.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.10.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.10.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.10.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.10.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.10.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.10.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.10.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.10.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.11.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.11.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.11.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.11.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.11.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.11.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.11.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.11.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.11.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.12.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.12.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.12.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.12.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.12.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.12.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.12.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.12.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.12.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.13.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.13.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.13.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.13.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.13.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.13.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.13.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.13.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.13.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.14.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.14.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.14.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.14.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.14.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.14.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.14.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.14.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.14.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.15.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.15.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.15.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.15.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.15.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.15.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.15.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.15.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.15.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.16.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.16.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.16.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.16.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.16.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.16.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.16.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.16.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.16.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.17.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.17.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.17.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.17.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.17.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.17.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.17.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.17.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.17.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.18.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.18.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.18.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.18.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.18.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.18.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.18.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.18.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.18.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.19.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.19.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.19.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.19.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.19.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.19.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.19.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.19.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.19.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.20.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.20.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.20.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.20.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.20.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.9.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.9.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.9.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.9.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.9.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.9.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.9.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.9.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.9.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.20.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.20.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.20.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.20.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.21.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.21.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.21.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.21.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.21.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.21.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.21.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.21.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.21.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.22.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.22.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.22.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.22.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.22.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.22.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.22.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.22.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.22.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.23.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.23.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.23.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.23.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.23.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.23.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.23.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.23.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.23.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.24.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.24.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.24.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.24.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.24.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.24.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.24.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.24.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.24.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.25.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.25.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.25.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.25.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.25.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.25.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.25.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.25.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.25.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.26.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.26.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.26.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.26.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.26.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.26.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.26.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.26.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.26.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.27.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.27.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.27.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.27.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.27.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.27.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.27.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.27.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.27.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.28.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.28.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.28.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.28.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.28.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.28.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.28.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.28.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.28.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.29.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.29.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.29.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.29.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.29.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.29.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.29.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.29.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.29.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.30.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.30.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.30.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.30.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.30.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.30.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.30.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.30.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.30.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.31.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.31.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.31.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.31.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.31.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.31.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"output.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,128256]},{\"name\":\"blk.31.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.31.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.31.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"output_norm.weight\",\"type\":\"F32\",\"shape\":[4096]}],\"capabilities\":[\"completion\",\"tools\"],\"modified_at\":\"2025-04-11T14:41:15.05985701Z\"}"
    headers:
      Content-Type:
      - application/json; charset=utf-8
      Date:
      - Wed, 07 May 2025 01:16:23 GMT
      Transfer-Encoding:
      - chunked
    status:
      code: 200
      message: OK
- request:
    body: '{"name": "llama3.1"}'
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '20'
      content-type:
      - application/json
      host:
      - localhost:11434
      user-agent:
      - litellm/1.68.0
    method: POST
    uri: http://localhost:11434/api/show
  response:
    body:
      string: "{\"license\":\"LLAMA 3.1 COMMUNITY LICENSE AGREEMENT\\nLlama 3.1 Version
        Release Date: July 23, 2024\\n\\n\u201CAgreement\u201D means the terms and
        conditions for use, reproduction, distribution and modification of the\\nLlama
        Materials set forth herein.\\n\\n\u201CDocumentation\u201D means the specifications,
        manuals and documentation accompanying Llama 3.1\\ndistributed by Meta at
        https://llama.meta.com/doc/overview.\\n\\n\u201CLicensee\u201D or \u201Cyou\u201D
        means you, or your employer or any other person or entity (if you are entering
        into\\nthis Agreement on such person or entity\u2019s behalf), of the age
        required under applicable laws, rules or\\nregulations to provide legal consent
        and that has legal authority to bind your employer or such other\\nperson
        or entity if you are entering in this Agreement on their behalf.\\n\\n\u201CLlama
        3.1\u201D means the foundational large language models and software and algorithms,
        including\\nmachine-learning model code, trained model weights, inference-enabling
        code, training-enabling code,\\nfine-tuning enabling code and other elements
        of the foregoing distributed by Meta at\\nhttps://llama.meta.com/llama-downloads.\\n\\n\u201CLlama
        Materials\u201D means, collectively, Meta\u2019s proprietary Llama 3.1 and
        Documentation (and any\\nportion thereof) made available under this Agreement.\\n\\n\u201CMeta\u201D
        or \u201Cwe\u201D means Meta Platforms Ireland Limited (if you are located
        in or, if you are an entity, your\\nprincipal place of business is in the
        EEA or Switzerland) and Meta Platforms, Inc. (if you are located\\noutside
        of the EEA or Switzerland).\\n\\nBy clicking \u201CI Accept\u201D below or
        by using or distributing any portion or element of the Llama Materials,\\nyou
        agree to be bound by this Agreement.\\n\\n1. License Rights and Redistribution.\\n\\n
        \ a. Grant of Rights. You are granted a non-exclusive, worldwide, non-transferable
        and royalty-free\\nlimited license under Meta\u2019s intellectual property
        or other rights owned by Meta embodied in the Llama\\nMaterials to use, reproduce,
        distribute, copy, create derivative works of, and make modifications to the\\nLlama
        Materials.\\n\\n  b. Redistribution and Use.\\n\\n      i. If you distribute
        or make available the Llama Materials (or any derivative works\\nthereof),
        or a product or service (including another AI model) that contains any of
        them, you shall (A)\\nprovide a copy of this Agreement with any such Llama
        Materials; and (B) prominently display \u201CBuilt with\\nLlama\u201D on a
        related website, user interface, blogpost, about page, or product documentation.
        If you use\\nthe Llama Materials or any outputs or results of the Llama Materials
        to create, train, fine tune, or\\notherwise improve an AI model, which is
        distributed or made available, you shall also include \u201CLlama\u201D at\\nthe
        beginning of any such AI model name.\\n\\n      ii. If you receive Llama Materials,
        or any derivative works thereof, from a Licensee as part \\nof an integrated
        end user product, then Section 2 of this Agreement will not apply to you.\\n\\n
        \     iii. You must retain in all copies of the Llama Materials that you distribute
        the following\\nattribution notice within a \u201CNotice\u201D text file distributed
        as a part of such copies: \u201CLlama 3.1 is\\nlicensed under the Llama 3.1
        Community License, Copyright \xA9 Meta Platforms, Inc. All Rights\\nReserved.\u201D\\n\\n
        \     iv. Your use of the Llama Materials must comply with applicable laws
        and regulations\\n(including trade compliance laws and regulations) and adhere
        to the Acceptable Use Policy for the Llama\\nMaterials (available at https://llama.meta.com/llama3_1/use-policy),
        which is hereby incorporated by\\nreference into this Agreement.\\n\\n2. Additional
        Commercial Terms. If, on the Llama 3.1 version release date, the monthly active
        users\\nof the products or services made available by or for Licensee, or
        Licensee\u2019s affiliates, is greater than 700\\nmillion monthly active users
        in the preceding calendar month, you must request a license from Meta,\\nwhich
        Meta may grant to you in its sole discretion, and you are not authorized to
        exercise any of the\\nrights under this Agreement unless or until Meta otherwise
        expressly grants you such rights.\\n\\n3. Disclaimer of Warranty. UNLESS REQUIRED
        BY APPLICABLE LAW, THE LLAMA MATERIALS AND ANY\\nOUTPUT AND RESULTS THEREFROM
        ARE PROVIDED ON AN \u201CAS IS\u201D BASIS, WITHOUT WARRANTIES OF\\nANY KIND,
        AND META DISCLAIMS ALL WARRANTIES OF ANY KIND, BOTH EXPRESS AND IMPLIED,\\nINCLUDING,
        WITHOUT LIMITATION, ANY WARRANTIES OF TITLE, NON-INFRINGEMENT,\\nMERCHANTABILITY,
        OR FITNESS FOR A PARTICULAR PURPOSE. YOU ARE SOLELY RESPONSIBLE FOR\\nDETERMINING
        THE APPROPRIATENESS OF USING OR REDISTRIBUTING THE LLAMA MATERIALS AND\\nASSUME
        ANY RISKS ASSOCIATED WITH YOUR USE OF THE LLAMA MATERIALS AND ANY OUTPUT AND\\nRESULTS.\\n\\n4.
        Limitation of Liability. IN NO EVENT WILL META OR ITS AFFILIATES BE LIABLE
        UNDER ANY THEORY OF\\nLIABILITY, WHETHER IN CONTRACT, TORT, NEGLIGENCE, PRODUCTS
        LIABILITY, OR OTHERWISE, ARISING\\nOUT OF THIS AGREEMENT, FOR ANY LOST PROFITS
        OR ANY INDIRECT, SPECIAL, CONSEQUENTIAL,\\nINCIDENTAL, EXEMPLARY OR PUNITIVE
        DAMAGES, EVEN IF META OR ITS AFFILIATES HAVE BEEN ADVISED\\nOF THE POSSIBILITY
        OF ANY OF THE FOREGOING.\\n\\n5. Intellectual Property.\\n\\n  a. No trademark
        licenses are granted under this Agreement, and in connection with the Llama\\nMaterials,
        neither Meta nor Licensee may use any name or mark owned by or associated
        with the other\\nor any of its affiliates, except as required for reasonable
        and customary use in describing and\\nredistributing the Llama Materials or
        as set forth in this Section 5(a). Meta hereby grants you a license to\\nuse
        \u201CLlama\u201D (the \u201CMark\u201D) solely as required to comply with
        the last sentence of Section 1.b.i. You will\\ncomply with Meta\u2019s brand
        guidelines (currently accessible at\\nhttps://about.meta.com/brand/resources/meta/company-brand/
        ). All goodwill arising out of your use\\nof the Mark will inure to the benefit
        of Meta.\\n\\n  b. Subject to Meta\u2019s ownership of Llama Materials and
        derivatives made by or for Meta, with\\nrespect to any derivative works and
        modifications of the Llama Materials that are made by you, as\\nbetween you
        and Meta, you are and will be the owner of such derivative works and modifications.\\n\\n
        \ c. If you institute litigation or other proceedings against Meta or any
        entity (including a\\ncross-claim or counterclaim in a lawsuit) alleging that
        the Llama Materials or Llama 3.1 outputs or\\nresults, or any portion of any
        of the foregoing, constitutes infringement of intellectual property or other\\nrights
        owned or licensable by you, then any licenses granted to you under this Agreement
        shall\\nterminate as of the date such litigation or claim is filed or instituted.
        You will indemnify and hold\\nharmless Meta from and against any claim by
        any third party arising out of or related to your use or\\ndistribution of
        the Llama Materials.\\n\\n6. Term and Termination. The term of this Agreement
        will commence upon your acceptance of this\\nAgreement or access to the Llama
        Materials and will continue in full force and effect until terminated in\\naccordance
        with the terms and conditions herein. Meta may terminate this Agreement if
        you are in\\nbreach of any term or condition of this Agreement. Upon termination
        of this Agreement, you shall delete\\nand cease use of the Llama Materials.
        Sections 3, 4 and 7 shall survive the termination of this\\nAgreement.\\n\\n7.
        Governing Law and Jurisdiction. This Agreement will be governed and construed
        under the laws of\\nthe State of California without regard to choice of law
        principles, and the UN Convention on Contracts\\nfor the International Sale
        of Goods does not apply to this Agreement. The courts of California shall
        have\\nexclusive jurisdiction of any dispute arising out of this Agreement.\\n\\n#
        Llama 3.1 Acceptable Use Policy\\n\\nMeta is committed to promoting safe and
        fair use of its tools and features, including Llama 3.1. If you\\naccess or
        use Llama 3.1, you agree to this Acceptable Use Policy (\u201CPolicy\u201D).
        The most recent copy of\\nthis policy can be found at [https://llama.meta.com/llama3_1/use-policy](https://llama.meta.com/llama3_1/use-policy)\\n\\n##
        Prohibited Uses\\n\\nWe want everyone to use Llama 3.1 safely and responsibly.
        You agree you will not use, or allow\\nothers to use, Llama 3.1 to:\\n\\n1.
        Violate the law or others\u2019 rights, including to:\\n    1. Engage in,
        promote, generate, contribute to, encourage, plan, incite, or further illegal
        or unlawful activity or content, such as:\\n        1. Violence or terrorism\\n
        \       2. Exploitation or harm to children, including the solicitation, creation,
        acquisition, or dissemination of child exploitative content or failure to
        report Child Sexual Abuse Material\\n        3. Human trafficking, exploitation,
        and sexual violence\\n        4. The illegal distribution of information or
        materials to minors, including obscene materials, or failure to employ legally
        required age-gating in connection with such information or materials.\\n        5.
        Sexual solicitation\\n        6. Any other criminal activity\\n    3. Engage
        in, promote, incite, or facilitate the harassment, abuse, threatening, or
        bullying of individuals or groups of individuals\\n    4. Engage in, promote,
        incite, or facilitate discrimination or other unlawful or harmful conduct
        in the provision of employment, employment benefits, credit, housing, other
        economic benefits, or other essential goods and services\\n    5. Engage in
        the unauthorized or unlicensed practice of any profession including, but not
        limited to, financial, legal, medical/health, or related professional practices\\n
        \   6. Collect, process, disclose, generate, or infer health, demographic,
        or other sensitive personal or private information about individuals without
        rights and consents required by applicable laws\\n    7. Engage in or facilitate
        any action or generate any content that infringes, misappropriates, or otherwise
        violates any third-party rights, including the outputs or results of any products
        or services using the Llama Materials\\n    8. Create, generate, or facilitate
        the creation of malicious code, malware, computer viruses or do anything else
        that could disable, overburden, interfere with or impair the proper working,
        integrity, operation or appearance of a website or computer system\\n\\n2.
        Engage in, promote, incite, facilitate, or assist in the planning or development
        of activities that present a risk of death or bodily harm to individuals,
        including use of Llama 3.1 related to the following:\\n    1. Military, warfare,
        nuclear industries or applications, espionage, use for materials or activities
        that are subject to the International Traffic Arms Regulations (ITAR) maintained
        by the United States Department of State\\n    2. Guns and illegal weapons
        (including weapon development)\\n    3. Illegal drugs and regulated/controlled
        substances\\n    4. Operation of critical infrastructure, transportation technologies,
        or heavy machinery\\n    5. Self-harm or harm to others, including suicide,
        cutting, and eating disorders\\n    6. Any content intended to incite or promote
        violence, abuse, or any infliction of bodily harm to an individual\\n\\n3.
        Intentionally deceive or mislead others, including use of Llama 3.1 related
        to the following:\\n    1. Generating, promoting, or furthering fraud or the
        creation or promotion of disinformation\\n    2. Generating, promoting, or
        furthering defamatory content, including the creation of defamatory statements,
        images, or other content\\n    3. Generating, promoting, or further distributing
        spam\\n    4. Impersonating another individual without consent, authorization,
        or legal right\\n    5. Representing that the use of Llama 3.1 or outputs
        are human-generated\\n    6. Generating or facilitating false online engagement,
        including fake reviews and other means of fake online engagement\\n\\n4. Fail
        to appropriately disclose to end users any known dangers of your AI system\\n\\nPlease
        report any violation of this Policy, software \u201Cbug,\u201D or other problems
        that could lead to a violation\\nof this Policy through one of the following
        means:\\n\\n* Reporting issues with the model: [https://github.com/meta-llama/llama-models/issues](https://github.com/meta-llama/llama-models/issues)\\n*
        Reporting risky content generated by the model: developers.facebook.com/llama_output_feedback\\n*
        Reporting bugs and security concerns: facebook.com/whitehat/info\\n* Reporting
        violations of the Acceptable Use Policy or unlicensed uses of Llama 3.1: <EMAIL>\",\"modelfile\":\"#
        Modelfile generated by \\\"ollama show\\\"\\n# To build a new Modelfile based
        on this, replace FROM with:\\n# FROM llama3.1:latest\\n\\nFROM /root/.ollama/models/blobs/sha256-667b0c1932bc6ffc593ed1d03f895bf2dc8dc6df21db3042284a6f4416b06a29\\nTEMPLATE
        \\\"\\\"\\\"{{- if or .System .Tools }}\\u003c|start_header_id|\\u003esystem\\u003c|end_header_id|\\u003e\\n{{-
        if .System }}\\n\\n{{ .System }}\\n{{- end }}\\n{{- if .Tools }}\\n\\nCutting
        Knowledge Date: December 2023\\n\\nWhen you receive a tool call response,
        use the output to format an answer to the orginal user question.\\n\\nYou
        are a helpful assistant with tool calling capabilities.\\n{{- end }}\\u003c|eot_id|\\u003e\\n{{-
        end }}\\n{{- range $i, $_ := .Messages }}\\n{{- $last := eq (len (slice $.Messages
        $i)) 1 }}\\n{{- if eq .Role \\\"user\\\" }}\\u003c|start_header_id|\\u003euser\\u003c|end_header_id|\\u003e\\n{{-
        if and $.Tools $last }}\\n\\nGiven the following functions, please respond
        with a JSON for a function call with its proper arguments that best answers
        the given prompt.\\n\\nRespond in the format {\\\"name\\\": function name,
        \\\"parameters\\\": dictionary of argument name and its value}. Do not use
        variables.\\n\\n{{ range $.Tools }}\\n{{- . }}\\n{{ end }}\\nQuestion: {{
        .Content }}\\u003c|eot_id|\\u003e\\n{{- else }}\\n\\n{{ .Content }}\\u003c|eot_id|\\u003e\\n{{-
        end }}{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
        end }}\\n{{- else if eq .Role \\\"assistant\\\" }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n{{-
        if .ToolCalls }}\\n{{ range .ToolCalls }}\\n{\\\"name\\\": \\\"{{ .Function.Name
        }}\\\", \\\"parameters\\\": {{ .Function.Arguments }}}{{ end }}\\n{{- else
        }}\\n\\n{{ .Content }}\\n{{- end }}{{ if not $last }}\\u003c|eot_id|\\u003e{{
        end }}\\n{{- else if eq .Role \\\"tool\\\" }}\\u003c|start_header_id|\\u003eipython\\u003c|end_header_id|\\u003e\\n\\n{{
        .Content }}\\u003c|eot_id|\\u003e{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
        end }}\\n{{- end }}\\n{{- end }}\\\"\\\"\\\"\\nPARAMETER stop \\u003c|start_header_id|\\u003e\\nPARAMETER
        stop \\u003c|end_header_id|\\u003e\\nPARAMETER stop \\u003c|eot_id|\\u003e\\nLICENSE
        \\\"LLAMA 3.1 COMMUNITY LICENSE AGREEMENT\\nLlama 3.1 Version Release Date:
        July 23, 2024\\n\\n\u201CAgreement\u201D means the terms and conditions for
        use, reproduction, distribution and modification of the\\nLlama Materials
        set forth herein.\\n\\n\u201CDocumentation\u201D means the specifications,
        manuals and documentation accompanying Llama 3.1\\ndistributed by Meta at
        https://llama.meta.com/doc/overview.\\n\\n\u201CLicensee\u201D or \u201Cyou\u201D
        means you, or your employer or any other person or entity (if you are entering
        into\\nthis Agreement on such person or entity\u2019s behalf), of the age
        required under applicable laws, rules or\\nregulations to provide legal consent
        and that has legal authority to bind your employer or such other\\nperson
        or entity if you are entering in this Agreement on their behalf.\\n\\n\u201CLlama
        3.1\u201D means the foundational large language models and software and algorithms,
        including\\nmachine-learning model code, trained model weights, inference-enabling
        code, training-enabling code,\\nfine-tuning enabling code and other elements
        of the foregoing distributed by Meta at\\nhttps://llama.meta.com/llama-downloads.\\n\\n\u201CLlama
        Materials\u201D means, collectively, Meta\u2019s proprietary Llama 3.1 and
        Documentation (and any\\nportion thereof) made available under this Agreement.\\n\\n\u201CMeta\u201D
        or \u201Cwe\u201D means Meta Platforms Ireland Limited (if you are located
        in or, if you are an entity, your\\nprincipal place of business is in the
        EEA or Switzerland) and Meta Platforms, Inc. (if you are located\\noutside
        of the EEA or Switzerland).\\n\\nBy clicking \u201CI Accept\u201D below or
        by using or distributing any portion or element of the Llama Materials,\\nyou
        agree to be bound by this Agreement.\\n\\n1. License Rights and Redistribution.\\n\\n
        \ a. Grant of Rights. You are granted a non-exclusive, worldwide, non-transferable
        and royalty-free\\nlimited license under Meta\u2019s intellectual property
        or other rights owned by Meta embodied in the Llama\\nMaterials to use, reproduce,
        distribute, copy, create derivative works of, and make modifications to the\\nLlama
        Materials.\\n\\n  b. Redistribution and Use.\\n\\n      i. If you distribute
        or make available the Llama Materials (or any derivative works\\nthereof),
        or a product or service (including another AI model) that contains any of
        them, you shall (A)\\nprovide a copy of this Agreement with any such Llama
        Materials; and (B) prominently display \u201CBuilt with\\nLlama\u201D on a
        related website, user interface, blogpost, about page, or product documentation.
        If you use\\nthe Llama Materials or any outputs or results of the Llama Materials
        to create, train, fine tune, or\\notherwise improve an AI model, which is
        distributed or made available, you shall also include \u201CLlama\u201D at\\nthe
        beginning of any such AI model name.\\n\\n      ii. If you receive Llama Materials,
        or any derivative works thereof, from a Licensee as part \\nof an integrated
        end user product, then Section 2 of this Agreement will not apply to you.\\n\\n
        \     iii. You must retain in all copies of the Llama Materials that you distribute
        the following\\nattribution notice within a \u201CNotice\u201D text file distributed
        as a part of such copies: \u201CLlama 3.1 is\\nlicensed under the Llama 3.1
        Community License, Copyright \xA9 Meta Platforms, Inc. All Rights\\nReserved.\u201D\\n\\n
        \     iv. Your use of the Llama Materials must comply with applicable laws
        and regulations\\n(including trade compliance laws and regulations) and adhere
        to the Acceptable Use Policy for the Llama\\nMaterials (available at https://llama.meta.com/llama3_1/use-policy),
        which is hereby incorporated by\\nreference into this Agreement.\\n\\n2. Additional
        Commercial Terms. If, on the Llama 3.1 version release date, the monthly active
        users\\nof the products or services made available by or for Licensee, or
        Licensee\u2019s affiliates, is greater than 700\\nmillion monthly active users
        in the preceding calendar month, you must request a license from Meta,\\nwhich
        Meta may grant to you in its sole discretion, and you are not authorized to
        exercise any of the\\nrights under this Agreement unless or until Meta otherwise
        expressly grants you such rights.\\n\\n3. Disclaimer of Warranty. UNLESS REQUIRED
        BY APPLICABLE LAW, THE LLAMA MATERIALS AND ANY\\nOUTPUT AND RESULTS THEREFROM
        ARE PROVIDED ON AN \u201CAS IS\u201D BASIS, WITHOUT WARRANTIES OF\\nANY KIND,
        AND META DISCLAIMS ALL WARRANTIES OF ANY KIND, BOTH EXPRESS AND IMPLIED,\\nINCLUDING,
        WITHOUT LIMITATION, ANY WARRANTIES OF TITLE, NON-INFRINGEMENT,\\nMERCHANTABILITY,
        OR FITNESS FOR A PARTICULAR PURPOSE. YOU ARE SOLELY RESPONSIBLE FOR\\nDETERMINING
        THE APPROPRIATENESS OF USING OR REDISTRIBUTING THE LLAMA MATERIALS AND\\nASSUME
        ANY RISKS ASSOCIATED WITH YOUR USE OF THE LLAMA MATERIALS AND ANY OUTPUT AND\\nRESULTS.\\n\\n4.
        Limitation of Liability. IN NO EVENT WILL META OR ITS AFFILIATES BE LIABLE
        UNDER ANY THEORY OF\\nLIABILITY, WHETHER IN CONTRACT, TORT, NEGLIGENCE, PRODUCTS
        LIABILITY, OR OTHERWISE, ARISING\\nOUT OF THIS AGREEMENT, FOR ANY LOST PROFITS
        OR ANY INDIRECT, SPECIAL, CONSEQUENTIAL,\\nINCIDENTAL, EXEMPLARY OR PUNITIVE
        DAMAGES, EVEN IF META OR ITS AFFILIATES HAVE BEEN ADVISED\\nOF THE POSSIBILITY
        OF ANY OF THE FOREGOING.\\n\\n5. Intellectual Property.\\n\\n  a. No trademark
        licenses are granted under this Agreement, and in connection with the Llama\\nMaterials,
        neither Meta nor Licensee may use any name or mark owned by or associated
        with the other\\nor any of its affiliates, except as required for reasonable
        and customary use in describing and\\nredistributing the Llama Materials or
        as set forth in this Section 5(a). Meta hereby grants you a license to\\nuse
        \u201CLlama\u201D (the \u201CMark\u201D) solely as required to comply with
        the last sentence of Section 1.b.i. You will\\ncomply with Meta\u2019s brand
        guidelines (currently accessible at\\nhttps://about.meta.com/brand/resources/meta/company-brand/
        ). All goodwill arising out of your use\\nof the Mark will inure to the benefit
        of Meta.\\n\\n  b. Subject to Meta\u2019s ownership of Llama Materials and
        derivatives made by or for Meta, with\\nrespect to any derivative works and
        modifications of the Llama Materials that are made by you, as\\nbetween you
        and Meta, you are and will be the owner of such derivative works and modifications.\\n\\n
        \ c. If you institute litigation or other proceedings against Meta or any
        entity (including a\\ncross-claim or counterclaim in a lawsuit) alleging that
        the Llama Materials or Llama 3.1 outputs or\\nresults, or any portion of any
        of the foregoing, constitutes infringement of intellectual property or other\\nrights
        owned or licensable by you, then any licenses granted to you under this Agreement
        shall\\nterminate as of the date such litigation or claim is filed or instituted.
        You will indemnify and hold\\nharmless Meta from and against any claim by
        any third party arising out of or related to your use or\\ndistribution of
        the Llama Materials.\\n\\n6. Term and Termination. The term of this Agreement
        will commence upon your acceptance of this\\nAgreement or access to the Llama
        Materials and will continue in full force and effect until terminated in\\naccordance
        with the terms and conditions herein. Meta may terminate this Agreement if
        you are in\\nbreach of any term or condition of this Agreement. Upon termination
        of this Agreement, you shall delete\\nand cease use of the Llama Materials.
        Sections 3, 4 and 7 shall survive the termination of this\\nAgreement.\\n\\n7.
        Governing Law and Jurisdiction. This Agreement will be governed and construed
        under the laws of\\nthe State of California without regard to choice of law
        principles, and the UN Convention on Contracts\\nfor the International Sale
        of Goods does not apply to this Agreement. The courts of California shall
        have\\nexclusive jurisdiction of any dispute arising out of this Agreement.\\n\\n#
        Llama 3.1 Acceptable Use Policy\\n\\nMeta is committed to promoting safe and
        fair use of its tools and features, including Llama 3.1. If you\\naccess or
        use Llama 3.1, you agree to this Acceptable Use Policy (\u201CPolicy\u201D).
        The most recent copy of\\nthis policy can be found at [https://llama.meta.com/llama3_1/use-policy](https://llama.meta.com/llama3_1/use-policy)\\n\\n##
        Prohibited Uses\\n\\nWe want everyone to use Llama 3.1 safely and responsibly.
        You agree you will not use, or allow\\nothers to use, Llama 3.1 to:\\n\\n1.
        Violate the law or others\u2019 rights, including to:\\n    1. Engage in,
        promote, generate, contribute to, encourage, plan, incite, or further illegal
        or unlawful activity or content, such as:\\n        1. Violence or terrorism\\n
        \       2. Exploitation or harm to children, including the solicitation, creation,
        acquisition, or dissemination of child exploitative content or failure to
        report Child Sexual Abuse Material\\n        3. Human trafficking, exploitation,
        and sexual violence\\n        4. The illegal distribution of information or
        materials to minors, including obscene materials, or failure to employ legally
        required age-gating in connection with such information or materials.\\n        5.
        Sexual solicitation\\n        6. Any other criminal activity\\n    3. Engage
        in, promote, incite, or facilitate the harassment, abuse, threatening, or
        bullying of individuals or groups of individuals\\n    4. Engage in, promote,
        incite, or facilitate discrimination or other unlawful or harmful conduct
        in the provision of employment, employment benefits, credit, housing, other
        economic benefits, or other essential goods and services\\n    5. Engage in
        the unauthorized or unlicensed practice of any profession including, but not
        limited to, financial, legal, medical/health, or related professional practices\\n
        \   6. Collect, process, disclose, generate, or infer health, demographic,
        or other sensitive personal or private information about individuals without
        rights and consents required by applicable laws\\n    7. Engage in or facilitate
        any action or generate any content that infringes, misappropriates, or otherwise
        violates any third-party rights, including the outputs or results of any products
        or services using the Llama Materials\\n    8. Create, generate, or facilitate
        the creation of malicious code, malware, computer viruses or do anything else
        that could disable, overburden, interfere with or impair the proper working,
        integrity, operation or appearance of a website or computer system\\n\\n2.
        Engage in, promote, incite, facilitate, or assist in the planning or development
        of activities that present a risk of death or bodily harm to individuals,
        including use of Llama 3.1 related to the following:\\n    1. Military, warfare,
        nuclear industries or applications, espionage, use for materials or activities
        that are subject to the International Traffic Arms Regulations (ITAR) maintained
        by the United States Department of State\\n    2. Guns and illegal weapons
        (including weapon development)\\n    3. Illegal drugs and regulated/controlled
        substances\\n    4. Operation of critical infrastructure, transportation technologies,
        or heavy machinery\\n    5. Self-harm or harm to others, including suicide,
        cutting, and eating disorders\\n    6. Any content intended to incite or promote
        violence, abuse, or any infliction of bodily harm to an individual\\n\\n3.
        Intentionally deceive or mislead others, including use of Llama 3.1 related
        to the following:\\n    1. Generating, promoting, or furthering fraud or the
        creation or promotion of disinformation\\n    2. Generating, promoting, or
        furthering defamatory content, including the creation of defamatory statements,
        images, or other content\\n    3. Generating, promoting, or further distributing
        spam\\n    4. Impersonating another individual without consent, authorization,
        or legal right\\n    5. Representing that the use of Llama 3.1 or outputs
        are human-generated\\n    6. Generating or facilitating false online engagement,
        including fake reviews and other means of fake online engagement\\n\\n4. Fail
        to appropriately disclose to end users any known dangers of your AI system\\n\\nPlease
        report any violation of this Policy, software \u201Cbug,\u201D or other problems
        that could lead to a violation\\nof this Policy through one of the following
        means:\\n\\n* Reporting issues with the model: [https://github.com/meta-llama/llama-models/issues](https://github.com/meta-llama/llama-models/issues)\\n*
        Reporting risky content generated by the model: developers.facebook.com/llama_output_feedback\\n*
        Reporting bugs and security concerns: facebook.com/whitehat/info\\n* Reporting
        violations of the Acceptable Use Policy or unlicensed uses of Llama 3.1: <EMAIL>\\\"\\n\",\"parameters\":\"stop
        \                          \\\"\\u003c|start_header_id|\\u003e\\\"\\nstop
        \                          \\\"\\u003c|end_header_id|\\u003e\\\"\\nstop                           \\\"\\u003c|eot_id|\\u003e\\\"\",\"template\":\"{{-
        if or .System .Tools }}\\u003c|start_header_id|\\u003esystem\\u003c|end_header_id|\\u003e\\n{{-
        if .System }}\\n\\n{{ .System }}\\n{{- end }}\\n{{- if .Tools }}\\n\\nCutting
        Knowledge Date: December 2023\\n\\nWhen you receive a tool call response,
        use the output to format an answer to the orginal user question.\\n\\nYou
        are a helpful assistant with tool calling capabilities.\\n{{- end }}\\u003c|eot_id|\\u003e\\n{{-
        end }}\\n{{- range $i, $_ := .Messages }}\\n{{- $last := eq (len (slice $.Messages
        $i)) 1 }}\\n{{- if eq .Role \\\"user\\\" }}\\u003c|start_header_id|\\u003euser\\u003c|end_header_id|\\u003e\\n{{-
        if and $.Tools $last }}\\n\\nGiven the following functions, please respond
        with a JSON for a function call with its proper arguments that best answers
        the given prompt.\\n\\nRespond in the format {\\\"name\\\": function name,
        \\\"parameters\\\": dictionary of argument name and its value}. Do not use
        variables.\\n\\n{{ range $.Tools }}\\n{{- . }}\\n{{ end }}\\nQuestion: {{
        .Content }}\\u003c|eot_id|\\u003e\\n{{- else }}\\n\\n{{ .Content }}\\u003c|eot_id|\\u003e\\n{{-
        end }}{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
        end }}\\n{{- else if eq .Role \\\"assistant\\\" }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n{{-
        if .ToolCalls }}\\n{{ range .ToolCalls }}\\n{\\\"name\\\": \\\"{{ .Function.Name
        }}\\\", \\\"parameters\\\": {{ .Function.Arguments }}}{{ end }}\\n{{- else
        }}\\n\\n{{ .Content }}\\n{{- end }}{{ if not $last }}\\u003c|eot_id|\\u003e{{
        end }}\\n{{- else if eq .Role \\\"tool\\\" }}\\u003c|start_header_id|\\u003eipython\\u003c|end_header_id|\\u003e\\n\\n{{
        .Content }}\\u003c|eot_id|\\u003e{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
        end }}\\n{{- end }}\\n{{- end }}\",\"details\":{\"parent_model\":\"\",\"format\":\"gguf\",\"family\":\"llama\",\"families\":[\"llama\"],\"parameter_size\":\"8.0B\",\"quantization_level\":\"Q4_K_M\"},\"model_info\":{\"general.architecture\":\"llama\",\"general.basename\":\"Meta-Llama-3.1\",\"general.file_type\":15,\"general.finetune\":\"Instruct\",\"general.languages\":[\"en\",\"de\",\"fr\",\"it\",\"pt\",\"hi\",\"es\",\"th\"],\"general.license\":\"llama3.1\",\"general.parameter_count\":**********,\"general.quantization_version\":2,\"general.size_label\":\"8B\",\"general.tags\":[\"facebook\",\"meta\",\"pytorch\",\"llama\",\"llama-3\",\"text-generation\"],\"general.type\":\"model\",\"llama.attention.head_count\":32,\"llama.attention.head_count_kv\":8,\"llama.attention.layer_norm_rms_epsilon\":0.00001,\"llama.block_count\":32,\"llama.context_length\":131072,\"llama.embedding_length\":4096,\"llama.feed_forward_length\":14336,\"llama.rope.dimension_count\":128,\"llama.rope.freq_base\":500000,\"llama.vocab_size\":128256,\"tokenizer.ggml.bos_token_id\":128000,\"tokenizer.ggml.eos_token_id\":128009,\"tokenizer.ggml.merges\":null,\"tokenizer.ggml.model\":\"gpt2\",\"tokenizer.ggml.pre\":\"llama-bpe\",\"tokenizer.ggml.token_type\":null,\"tokenizer.ggml.tokens\":null},\"tensors\":[{\"name\":\"token_embd.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,128256]},{\"name\":\"rope_freqs.weight\",\"type\":\"F32\",\"shape\":[64]},{\"name\":\"blk.0.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.0.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.0.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.0.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.0.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.0.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.0.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.0.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.0.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.1.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.1.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.1.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.1.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.1.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.1.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.1.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.1.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.1.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.2.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.2.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.2.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.2.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.2.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.2.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.2.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.2.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.2.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.3.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.3.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.3.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.3.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.3.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.3.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.3.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.3.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.3.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.4.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.4.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.4.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.4.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.4.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.4.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.4.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.4.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.4.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.5.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.5.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.5.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.5.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.5.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.5.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.5.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.5.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.5.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.6.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.6.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.6.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.6.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.6.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.6.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.6.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.6.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.6.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.7.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.7.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.7.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.7.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.7.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.7.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.7.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.7.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.7.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.8.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.8.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.8.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.8.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.8.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.8.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.8.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.8.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.8.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.10.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.10.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.10.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.10.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.10.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.10.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.10.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.10.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.10.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.11.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.11.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.11.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.11.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.11.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.11.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.11.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.11.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.11.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.12.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.12.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.12.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.12.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.12.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.12.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.12.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.12.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.12.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.13.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.13.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.13.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.13.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.13.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.13.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.13.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.13.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.13.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.14.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.14.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.14.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.14.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.14.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.14.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.14.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.14.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.14.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.15.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.15.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.15.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.15.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.15.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.15.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.15.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.15.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.15.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.16.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.16.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.16.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.16.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.16.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.16.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.16.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.16.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.16.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.17.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.17.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.17.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.17.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.17.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.17.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.17.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.17.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.17.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.18.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.18.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.18.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.18.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.18.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.18.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.18.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.18.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.18.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.19.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.19.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.19.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.19.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.19.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.19.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.19.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.19.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.19.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.20.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.20.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.20.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.20.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.20.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.9.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.9.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.9.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.9.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.9.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.9.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.9.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.9.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.9.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.20.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.20.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.20.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.20.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.21.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.21.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.21.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.21.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.21.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.21.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.21.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.21.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.21.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.22.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.22.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.22.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.22.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.22.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.22.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.22.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.22.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.22.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.23.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.23.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.23.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.23.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.23.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.23.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.23.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.23.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.23.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.24.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.24.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.24.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.24.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.24.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.24.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.24.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.24.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.24.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.25.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.25.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.25.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.25.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.25.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.25.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.25.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.25.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.25.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.26.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.26.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.26.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.26.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.26.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.26.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.26.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.26.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.26.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.27.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.27.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.27.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.27.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.27.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.27.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.27.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.27.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.27.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.28.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.28.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.28.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.28.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.28.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.28.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.28.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.28.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.28.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.29.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.29.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.29.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.29.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.29.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.29.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.29.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.29.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.29.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.30.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.30.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.30.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.30.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.30.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.30.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.30.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.30.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.30.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.31.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.31.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.31.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.31.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.31.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.31.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"output.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,128256]},{\"name\":\"blk.31.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.31.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.31.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"output_norm.weight\",\"type\":\"F32\",\"shape\":[4096]}],\"capabilities\":[\"completion\",\"tools\"],\"modified_at\":\"2025-04-11T14:41:15.05985701Z\"}"
    headers:
      Content-Type:
      - application/json; charset=utf-8
      Date:
      - Wed, 07 May 2025 01:17:28 GMT
      Transfer-Encoding:
      - chunked
    status:
      code: 200
      message: OK
- request:
    body: '{"name": "llama3.1"}'
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '20'
      content-type:
      - application/json
      host:
      - localhost:11434
      user-agent:
      - litellm/1.68.0
    method: POST
    uri: http://localhost:11434/api/show
  response:
    body:
      string: "{\"license\":\"LLAMA 3.1 COMMUNITY LICENSE AGREEMENT\\nLlama 3.1 Version
        Release Date: July 23, 2024\\n\\n\u201CAgreement\u201D means the terms and
        conditions for use, reproduction, distribution and modification of the\\nLlama
        Materials set forth herein.\\n\\n\u201CDocumentation\u201D means the specifications,
        manuals and documentation accompanying Llama 3.1\\ndistributed by Meta at
        https://llama.meta.com/doc/overview.\\n\\n\u201CLicensee\u201D or \u201Cyou\u201D
        means you, or your employer or any other person or entity (if you are entering
        into\\nthis Agreement on such person or entity\u2019s behalf), of the age
        required under applicable laws, rules or\\nregulations to provide legal consent
        and that has legal authority to bind your employer or such other\\nperson
        or entity if you are entering in this Agreement on their behalf.\\n\\n\u201CLlama
        3.1\u201D means the foundational large language models and software and algorithms,
        including\\nmachine-learning model code, trained model weights, inference-enabling
        code, training-enabling code,\\nfine-tuning enabling code and other elements
        of the foregoing distributed by Meta at\\nhttps://llama.meta.com/llama-downloads.\\n\\n\u201CLlama
        Materials\u201D means, collectively, Meta\u2019s proprietary Llama 3.1 and
        Documentation (and any\\nportion thereof) made available under this Agreement.\\n\\n\u201CMeta\u201D
        or \u201Cwe\u201D means Meta Platforms Ireland Limited (if you are located
        in or, if you are an entity, your\\nprincipal place of business is in the
        EEA or Switzerland) and Meta Platforms, Inc. (if you are located\\noutside
        of the EEA or Switzerland).\\n\\nBy clicking \u201CI Accept\u201D below or
        by using or distributing any portion or element of the Llama Materials,\\nyou
        agree to be bound by this Agreement.\\n\\n1. License Rights and Redistribution.\\n\\n
        \ a. Grant of Rights. You are granted a non-exclusive, worldwide, non-transferable
        and royalty-free\\nlimited license under Meta\u2019s intellectual property
        or other rights owned by Meta embodied in the Llama\\nMaterials to use, reproduce,
        distribute, copy, create derivative works of, and make modifications to the\\nLlama
        Materials.\\n\\n  b. Redistribution and Use.\\n\\n      i. If you distribute
        or make available the Llama Materials (or any derivative works\\nthereof),
        or a product or service (including another AI model) that contains any of
        them, you shall (A)\\nprovide a copy of this Agreement with any such Llama
        Materials; and (B) prominently display \u201CBuilt with\\nLlama\u201D on a
        related website, user interface, blogpost, about page, or product documentation.
        If you use\\nthe Llama Materials or any outputs or results of the Llama Materials
        to create, train, fine tune, or\\notherwise improve an AI model, which is
        distributed or made available, you shall also include \u201CLlama\u201D at\\nthe
        beginning of any such AI model name.\\n\\n      ii. If you receive Llama Materials,
        or any derivative works thereof, from a Licensee as part \\nof an integrated
        end user product, then Section 2 of this Agreement will not apply to you.\\n\\n
        \     iii. You must retain in all copies of the Llama Materials that you distribute
        the following\\nattribution notice within a \u201CNotice\u201D text file distributed
        as a part of such copies: \u201CLlama 3.1 is\\nlicensed under the Llama 3.1
        Community License, Copyright \xA9 Meta Platforms, Inc. All Rights\\nReserved.\u201D\\n\\n
        \     iv. Your use of the Llama Materials must comply with applicable laws
        and regulations\\n(including trade compliance laws and regulations) and adhere
        to the Acceptable Use Policy for the Llama\\nMaterials (available at https://llama.meta.com/llama3_1/use-policy),
        which is hereby incorporated by\\nreference into this Agreement.\\n\\n2. Additional
        Commercial Terms. If, on the Llama 3.1 version release date, the monthly active
        users\\nof the products or services made available by or for Licensee, or
        Licensee\u2019s affiliates, is greater than 700\\nmillion monthly active users
        in the preceding calendar month, you must request a license from Meta,\\nwhich
        Meta may grant to you in its sole discretion, and you are not authorized to
        exercise any of the\\nrights under this Agreement unless or until Meta otherwise
        expressly grants you such rights.\\n\\n3. Disclaimer of Warranty. UNLESS REQUIRED
        BY APPLICABLE LAW, THE LLAMA MATERIALS AND ANY\\nOUTPUT AND RESULTS THEREFROM
        ARE PROVIDED ON AN \u201CAS IS\u201D BASIS, WITHOUT WARRANTIES OF\\nANY KIND,
        AND META DISCLAIMS ALL WARRANTIES OF ANY KIND, BOTH EXPRESS AND IMPLIED,\\nINCLUDING,
        WITHOUT LIMITATION, ANY WARRANTIES OF TITLE, NON-INFRINGEMENT,\\nMERCHANTABILITY,
        OR FITNESS FOR A PARTICULAR PURPOSE. YOU ARE SOLELY RESPONSIBLE FOR\\nDETERMINING
        THE APPROPRIATENESS OF USING OR REDISTRIBUTING THE LLAMA MATERIALS AND\\nASSUME
        ANY RISKS ASSOCIATED WITH YOUR USE OF THE LLAMA MATERIALS AND ANY OUTPUT AND\\nRESULTS.\\n\\n4.
        Limitation of Liability. IN NO EVENT WILL META OR ITS AFFILIATES BE LIABLE
        UNDER ANY THEORY OF\\nLIABILITY, WHETHER IN CONTRACT, TORT, NEGLIGENCE, PRODUCTS
        LIABILITY, OR OTHERWISE, ARISING\\nOUT OF THIS AGREEMENT, FOR ANY LOST PROFITS
        OR ANY INDIRECT, SPECIAL, CONSEQUENTIAL,\\nINCIDENTAL, EXEMPLARY OR PUNITIVE
        DAMAGES, EVEN IF META OR ITS AFFILIATES HAVE BEEN ADVISED\\nOF THE POSSIBILITY
        OF ANY OF THE FOREGOING.\\n\\n5. Intellectual Property.\\n\\n  a. No trademark
        licenses are granted under this Agreement, and in connection with the Llama\\nMaterials,
        neither Meta nor Licensee may use any name or mark owned by or associated
        with the other\\nor any of its affiliates, except as required for reasonable
        and customary use in describing and\\nredistributing the Llama Materials or
        as set forth in this Section 5(a). Meta hereby grants you a license to\\nuse
        \u201CLlama\u201D (the \u201CMark\u201D) solely as required to comply with
        the last sentence of Section 1.b.i. You will\\ncomply with Meta\u2019s brand
        guidelines (currently accessible at\\nhttps://about.meta.com/brand/resources/meta/company-brand/
        ). All goodwill arising out of your use\\nof the Mark will inure to the benefit
        of Meta.\\n\\n  b. Subject to Meta\u2019s ownership of Llama Materials and
        derivatives made by or for Meta, with\\nrespect to any derivative works and
        modifications of the Llama Materials that are made by you, as\\nbetween you
        and Meta, you are and will be the owner of such derivative works and modifications.\\n\\n
        \ c. If you institute litigation or other proceedings against Meta or any
        entity (including a\\ncross-claim or counterclaim in a lawsuit) alleging that
        the Llama Materials or Llama 3.1 outputs or\\nresults, or any portion of any
        of the foregoing, constitutes infringement of intellectual property or other\\nrights
        owned or licensable by you, then any licenses granted to you under this Agreement
        shall\\nterminate as of the date such litigation or claim is filed or instituted.
        You will indemnify and hold\\nharmless Meta from and against any claim by
        any third party arising out of or related to your use or\\ndistribution of
        the Llama Materials.\\n\\n6. Term and Termination. The term of this Agreement
        will commence upon your acceptance of this\\nAgreement or access to the Llama
        Materials and will continue in full force and effect until terminated in\\naccordance
        with the terms and conditions herein. Meta may terminate this Agreement if
        you are in\\nbreach of any term or condition of this Agreement. Upon termination
        of this Agreement, you shall delete\\nand cease use of the Llama Materials.
        Sections 3, 4 and 7 shall survive the termination of this\\nAgreement.\\n\\n7.
        Governing Law and Jurisdiction. This Agreement will be governed and construed
        under the laws of\\nthe State of California without regard to choice of law
        principles, and the UN Convention on Contracts\\nfor the International Sale
        of Goods does not apply to this Agreement. The courts of California shall
        have\\nexclusive jurisdiction of any dispute arising out of this Agreement.\\n\\n#
        Llama 3.1 Acceptable Use Policy\\n\\nMeta is committed to promoting safe and
        fair use of its tools and features, including Llama 3.1. If you\\naccess or
        use Llama 3.1, you agree to this Acceptable Use Policy (\u201CPolicy\u201D).
        The most recent copy of\\nthis policy can be found at [https://llama.meta.com/llama3_1/use-policy](https://llama.meta.com/llama3_1/use-policy)\\n\\n##
        Prohibited Uses\\n\\nWe want everyone to use Llama 3.1 safely and responsibly.
        You agree you will not use, or allow\\nothers to use, Llama 3.1 to:\\n\\n1.
        Violate the law or others\u2019 rights, including to:\\n    1. Engage in,
        promote, generate, contribute to, encourage, plan, incite, or further illegal
        or unlawful activity or content, such as:\\n        1. Violence or terrorism\\n
        \       2. Exploitation or harm to children, including the solicitation, creation,
        acquisition, or dissemination of child exploitative content or failure to
        report Child Sexual Abuse Material\\n        3. Human trafficking, exploitation,
        and sexual violence\\n        4. The illegal distribution of information or
        materials to minors, including obscene materials, or failure to employ legally
        required age-gating in connection with such information or materials.\\n        5.
        Sexual solicitation\\n        6. Any other criminal activity\\n    3. Engage
        in, promote, incite, or facilitate the harassment, abuse, threatening, or
        bullying of individuals or groups of individuals\\n    4. Engage in, promote,
        incite, or facilitate discrimination or other unlawful or harmful conduct
        in the provision of employment, employment benefits, credit, housing, other
        economic benefits, or other essential goods and services\\n    5. Engage in
        the unauthorized or unlicensed practice of any profession including, but not
        limited to, financial, legal, medical/health, or related professional practices\\n
        \   6. Collect, process, disclose, generate, or infer health, demographic,
        or other sensitive personal or private information about individuals without
        rights and consents required by applicable laws\\n    7. Engage in or facilitate
        any action or generate any content that infringes, misappropriates, or otherwise
        violates any third-party rights, including the outputs or results of any products
        or services using the Llama Materials\\n    8. Create, generate, or facilitate
        the creation of malicious code, malware, computer viruses or do anything else
        that could disable, overburden, interfere with or impair the proper working,
        integrity, operation or appearance of a website or computer system\\n\\n2.
        Engage in, promote, incite, facilitate, or assist in the planning or development
        of activities that present a risk of death or bodily harm to individuals,
        including use of Llama 3.1 related to the following:\\n    1. Military, warfare,
        nuclear industries or applications, espionage, use for materials or activities
        that are subject to the International Traffic Arms Regulations (ITAR) maintained
        by the United States Department of State\\n    2. Guns and illegal weapons
        (including weapon development)\\n    3. Illegal drugs and regulated/controlled
        substances\\n    4. Operation of critical infrastructure, transportation technologies,
        or heavy machinery\\n    5. Self-harm or harm to others, including suicide,
        cutting, and eating disorders\\n    6. Any content intended to incite or promote
        violence, abuse, or any infliction of bodily harm to an individual\\n\\n3.
        Intentionally deceive or mislead others, including use of Llama 3.1 related
        to the following:\\n    1. Generating, promoting, or furthering fraud or the
        creation or promotion of disinformation\\n    2. Generating, promoting, or
        furthering defamatory content, including the creation of defamatory statements,
        images, or other content\\n    3. Generating, promoting, or further distributing
        spam\\n    4. Impersonating another individual without consent, authorization,
        or legal right\\n    5. Representing that the use of Llama 3.1 or outputs
        are human-generated\\n    6. Generating or facilitating false online engagement,
        including fake reviews and other means of fake online engagement\\n\\n4. Fail
        to appropriately disclose to end users any known dangers of your AI system\\n\\nPlease
        report any violation of this Policy, software \u201Cbug,\u201D or other problems
        that could lead to a violation\\nof this Policy through one of the following
        means:\\n\\n* Reporting issues with the model: [https://github.com/meta-llama/llama-models/issues](https://github.com/meta-llama/llama-models/issues)\\n*
        Reporting risky content generated by the model: developers.facebook.com/llama_output_feedback\\n*
        Reporting bugs and security concerns: facebook.com/whitehat/info\\n* Reporting
        violations of the Acceptable Use Policy or unlicensed uses of Llama 3.1: <EMAIL>\",\"modelfile\":\"#
        Modelfile generated by \\\"ollama show\\\"\\n# To build a new Modelfile based
        on this, replace FROM with:\\n# FROM llama3.1:latest\\n\\nFROM /root/.ollama/models/blobs/sha256-667b0c1932bc6ffc593ed1d03f895bf2dc8dc6df21db3042284a6f4416b06a29\\nTEMPLATE
        \\\"\\\"\\\"{{- if or .System .Tools }}\\u003c|start_header_id|\\u003esystem\\u003c|end_header_id|\\u003e\\n{{-
        if .System }}\\n\\n{{ .System }}\\n{{- end }}\\n{{- if .Tools }}\\n\\nCutting
        Knowledge Date: December 2023\\n\\nWhen you receive a tool call response,
        use the output to format an answer to the orginal user question.\\n\\nYou
        are a helpful assistant with tool calling capabilities.\\n{{- end }}\\u003c|eot_id|\\u003e\\n{{-
        end }}\\n{{- range $i, $_ := .Messages }}\\n{{- $last := eq (len (slice $.Messages
        $i)) 1 }}\\n{{- if eq .Role \\\"user\\\" }}\\u003c|start_header_id|\\u003euser\\u003c|end_header_id|\\u003e\\n{{-
        if and $.Tools $last }}\\n\\nGiven the following functions, please respond
        with a JSON for a function call with its proper arguments that best answers
        the given prompt.\\n\\nRespond in the format {\\\"name\\\": function name,
        \\\"parameters\\\": dictionary of argument name and its value}. Do not use
        variables.\\n\\n{{ range $.Tools }}\\n{{- . }}\\n{{ end }}\\nQuestion: {{
        .Content }}\\u003c|eot_id|\\u003e\\n{{- else }}\\n\\n{{ .Content }}\\u003c|eot_id|\\u003e\\n{{-
        end }}{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
        end }}\\n{{- else if eq .Role \\\"assistant\\\" }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n{{-
        if .ToolCalls }}\\n{{ range .ToolCalls }}\\n{\\\"name\\\": \\\"{{ .Function.Name
        }}\\\", \\\"parameters\\\": {{ .Function.Arguments }}}{{ end }}\\n{{- else
        }}\\n\\n{{ .Content }}\\n{{- end }}{{ if not $last }}\\u003c|eot_id|\\u003e{{
        end }}\\n{{- else if eq .Role \\\"tool\\\" }}\\u003c|start_header_id|\\u003eipython\\u003c|end_header_id|\\u003e\\n\\n{{
        .Content }}\\u003c|eot_id|\\u003e{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
        end }}\\n{{- end }}\\n{{- end }}\\\"\\\"\\\"\\nPARAMETER stop \\u003c|start_header_id|\\u003e\\nPARAMETER
        stop \\u003c|end_header_id|\\u003e\\nPARAMETER stop \\u003c|eot_id|\\u003e\\nLICENSE
        \\\"LLAMA 3.1 COMMUNITY LICENSE AGREEMENT\\nLlama 3.1 Version Release Date:
        July 23, 2024\\n\\n\u201CAgreement\u201D means the terms and conditions for
        use, reproduction, distribution and modification of the\\nLlama Materials
        set forth herein.\\n\\n\u201CDocumentation\u201D means the specifications,
        manuals and documentation accompanying Llama 3.1\\ndistributed by Meta at
        https://llama.meta.com/doc/overview.\\n\\n\u201CLicensee\u201D or \u201Cyou\u201D
        means you, or your employer or any other person or entity (if you are entering
        into\\nthis Agreement on such person or entity\u2019s behalf), of the age
        required under applicable laws, rules or\\nregulations to provide legal consent
        and that has legal authority to bind your employer or such other\\nperson
        or entity if you are entering in this Agreement on their behalf.\\n\\n\u201CLlama
        3.1\u201D means the foundational large language models and software and algorithms,
        including\\nmachine-learning model code, trained model weights, inference-enabling
        code, training-enabling code,\\nfine-tuning enabling code and other elements
        of the foregoing distributed by Meta at\\nhttps://llama.meta.com/llama-downloads.\\n\\n\u201CLlama
        Materials\u201D means, collectively, Meta\u2019s proprietary Llama 3.1 and
        Documentation (and any\\nportion thereof) made available under this Agreement.\\n\\n\u201CMeta\u201D
        or \u201Cwe\u201D means Meta Platforms Ireland Limited (if you are located
        in or, if you are an entity, your\\nprincipal place of business is in the
        EEA or Switzerland) and Meta Platforms, Inc. (if you are located\\noutside
        of the EEA or Switzerland).\\n\\nBy clicking \u201CI Accept\u201D below or
        by using or distributing any portion or element of the Llama Materials,\\nyou
        agree to be bound by this Agreement.\\n\\n1. License Rights and Redistribution.\\n\\n
        \ a. Grant of Rights. You are granted a non-exclusive, worldwide, non-transferable
        and royalty-free\\nlimited license under Meta\u2019s intellectual property
        or other rights owned by Meta embodied in the Llama\\nMaterials to use, reproduce,
        distribute, copy, create derivative works of, and make modifications to the\\nLlama
        Materials.\\n\\n  b. Redistribution and Use.\\n\\n      i. If you distribute
        or make available the Llama Materials (or any derivative works\\nthereof),
        or a product or service (including another AI model) that contains any of
        them, you shall (A)\\nprovide a copy of this Agreement with any such Llama
        Materials; and (B) prominently display \u201CBuilt with\\nLlama\u201D on a
        related website, user interface, blogpost, about page, or product documentation.
        If you use\\nthe Llama Materials or any outputs or results of the Llama Materials
        to create, train, fine tune, or\\notherwise improve an AI model, which is
        distributed or made available, you shall also include \u201CLlama\u201D at\\nthe
        beginning of any such AI model name.\\n\\n      ii. If you receive Llama Materials,
        or any derivative works thereof, from a Licensee as part \\nof an integrated
        end user product, then Section 2 of this Agreement will not apply to you.\\n\\n
        \     iii. You must retain in all copies of the Llama Materials that you distribute
        the following\\nattribution notice within a \u201CNotice\u201D text file distributed
        as a part of such copies: \u201CLlama 3.1 is\\nlicensed under the Llama 3.1
        Community License, Copyright \xA9 Meta Platforms, Inc. All Rights\\nReserved.\u201D\\n\\n
        \     iv. Your use of the Llama Materials must comply with applicable laws
        and regulations\\n(including trade compliance laws and regulations) and adhere
        to the Acceptable Use Policy for the Llama\\nMaterials (available at https://llama.meta.com/llama3_1/use-policy),
        which is hereby incorporated by\\nreference into this Agreement.\\n\\n2. Additional
        Commercial Terms. If, on the Llama 3.1 version release date, the monthly active
        users\\nof the products or services made available by or for Licensee, or
        Licensee\u2019s affiliates, is greater than 700\\nmillion monthly active users
        in the preceding calendar month, you must request a license from Meta,\\nwhich
        Meta may grant to you in its sole discretion, and you are not authorized to
        exercise any of the\\nrights under this Agreement unless or until Meta otherwise
        expressly grants you such rights.\\n\\n3. Disclaimer of Warranty. UNLESS REQUIRED
        BY APPLICABLE LAW, THE LLAMA MATERIALS AND ANY\\nOUTPUT AND RESULTS THEREFROM
        ARE PROVIDED ON AN \u201CAS IS\u201D BASIS, WITHOUT WARRANTIES OF\\nANY KIND,
        AND META DISCLAIMS ALL WARRANTIES OF ANY KIND, BOTH EXPRESS AND IMPLIED,\\nINCLUDING,
        WITHOUT LIMITATION, ANY WARRANTIES OF TITLE, NON-INFRINGEMENT,\\nMERCHANTABILITY,
        OR FITNESS FOR A PARTICULAR PURPOSE. YOU ARE SOLELY RESPONSIBLE FOR\\nDETERMINING
        THE APPROPRIATENESS OF USING OR REDISTRIBUTING THE LLAMA MATERIALS AND\\nASSUME
        ANY RISKS ASSOCIATED WITH YOUR USE OF THE LLAMA MATERIALS AND ANY OUTPUT AND\\nRESULTS.\\n\\n4.
        Limitation of Liability. IN NO EVENT WILL META OR ITS AFFILIATES BE LIABLE
        UNDER ANY THEORY OF\\nLIABILITY, WHETHER IN CONTRACT, TORT, NEGLIGENCE, PRODUCTS
        LIABILITY, OR OTHERWISE, ARISING\\nOUT OF THIS AGREEMENT, FOR ANY LOST PROFITS
        OR ANY INDIRECT, SPECIAL, CONSEQUENTIAL,\\nINCIDENTAL, EXEMPLARY OR PUNITIVE
        DAMAGES, EVEN IF META OR ITS AFFILIATES HAVE BEEN ADVISED\\nOF THE POSSIBILITY
        OF ANY OF THE FOREGOING.\\n\\n5. Intellectual Property.\\n\\n  a. No trademark
        licenses are granted under this Agreement, and in connection with the Llama\\nMaterials,
        neither Meta nor Licensee may use any name or mark owned by or associated
        with the other\\nor any of its affiliates, except as required for reasonable
        and customary use in describing and\\nredistributing the Llama Materials or
        as set forth in this Section 5(a). Meta hereby grants you a license to\\nuse
        \u201CLlama\u201D (the \u201CMark\u201D) solely as required to comply with
        the last sentence of Section 1.b.i. You will\\ncomply with Meta\u2019s brand
        guidelines (currently accessible at\\nhttps://about.meta.com/brand/resources/meta/company-brand/
        ). All goodwill arising out of your use\\nof the Mark will inure to the benefit
        of Meta.\\n\\n  b. Subject to Meta\u2019s ownership of Llama Materials and
        derivatives made by or for Meta, with\\nrespect to any derivative works and
        modifications of the Llama Materials that are made by you, as\\nbetween you
        and Meta, you are and will be the owner of such derivative works and modifications.\\n\\n
        \ c. If you institute litigation or other proceedings against Meta or any
        entity (including a\\ncross-claim or counterclaim in a lawsuit) alleging that
        the Llama Materials or Llama 3.1 outputs or\\nresults, or any portion of any
        of the foregoing, constitutes infringement of intellectual property or other\\nrights
        owned or licensable by you, then any licenses granted to you under this Agreement
        shall\\nterminate as of the date such litigation or claim is filed or instituted.
        You will indemnify and hold\\nharmless Meta from and against any claim by
        any third party arising out of or related to your use or\\ndistribution of
        the Llama Materials.\\n\\n6. Term and Termination. The term of this Agreement
        will commence upon your acceptance of this\\nAgreement or access to the Llama
        Materials and will continue in full force and effect until terminated in\\naccordance
        with the terms and conditions herein. Meta may terminate this Agreement if
        you are in\\nbreach of any term or condition of this Agreement. Upon termination
        of this Agreement, you shall delete\\nand cease use of the Llama Materials.
        Sections 3, 4 and 7 shall survive the termination of this\\nAgreement.\\n\\n7.
        Governing Law and Jurisdiction. This Agreement will be governed and construed
        under the laws of\\nthe State of California without regard to choice of law
        principles, and the UN Convention on Contracts\\nfor the International Sale
        of Goods does not apply to this Agreement. The courts of California shall
        have\\nexclusive jurisdiction of any dispute arising out of this Agreement.\\n\\n#
        Llama 3.1 Acceptable Use Policy\\n\\nMeta is committed to promoting safe and
        fair use of its tools and features, including Llama 3.1. If you\\naccess or
        use Llama 3.1, you agree to this Acceptable Use Policy (\u201CPolicy\u201D).
        The most recent copy of\\nthis policy can be found at [https://llama.meta.com/llama3_1/use-policy](https://llama.meta.com/llama3_1/use-policy)\\n\\n##
        Prohibited Uses\\n\\nWe want everyone to use Llama 3.1 safely and responsibly.
        You agree you will not use, or allow\\nothers to use, Llama 3.1 to:\\n\\n1.
        Violate the law or others\u2019 rights, including to:\\n    1. Engage in,
        promote, generate, contribute to, encourage, plan, incite, or further illegal
        or unlawful activity or content, such as:\\n        1. Violence or terrorism\\n
        \       2. Exploitation or harm to children, including the solicitation, creation,
        acquisition, or dissemination of child exploitative content or failure to
        report Child Sexual Abuse Material\\n        3. Human trafficking, exploitation,
        and sexual violence\\n        4. The illegal distribution of information or
        materials to minors, including obscene materials, or failure to employ legally
        required age-gating in connection with such information or materials.\\n        5.
        Sexual solicitation\\n        6. Any other criminal activity\\n    3. Engage
        in, promote, incite, or facilitate the harassment, abuse, threatening, or
        bullying of individuals or groups of individuals\\n    4. Engage in, promote,
        incite, or facilitate discrimination or other unlawful or harmful conduct
        in the provision of employment, employment benefits, credit, housing, other
        economic benefits, or other essential goods and services\\n    5. Engage in
        the unauthorized or unlicensed practice of any profession including, but not
        limited to, financial, legal, medical/health, or related professional practices\\n
        \   6. Collect, process, disclose, generate, or infer health, demographic,
        or other sensitive personal or private information about individuals without
        rights and consents required by applicable laws\\n    7. Engage in or facilitate
        any action or generate any content that infringes, misappropriates, or otherwise
        violates any third-party rights, including the outputs or results of any products
        or services using the Llama Materials\\n    8. Create, generate, or facilitate
        the creation of malicious code, malware, computer viruses or do anything else
        that could disable, overburden, interfere with or impair the proper working,
        integrity, operation or appearance of a website or computer system\\n\\n2.
        Engage in, promote, incite, facilitate, or assist in the planning or development
        of activities that present a risk of death or bodily harm to individuals,
        including use of Llama 3.1 related to the following:\\n    1. Military, warfare,
        nuclear industries or applications, espionage, use for materials or activities
        that are subject to the International Traffic Arms Regulations (ITAR) maintained
        by the United States Department of State\\n    2. Guns and illegal weapons
        (including weapon development)\\n    3. Illegal drugs and regulated/controlled
        substances\\n    4. Operation of critical infrastructure, transportation technologies,
        or heavy machinery\\n    5. Self-harm or harm to others, including suicide,
        cutting, and eating disorders\\n    6. Any content intended to incite or promote
        violence, abuse, or any infliction of bodily harm to an individual\\n\\n3.
        Intentionally deceive or mislead others, including use of Llama 3.1 related
        to the following:\\n    1. Generating, promoting, or furthering fraud or the
        creation or promotion of disinformation\\n    2. Generating, promoting, or
        furthering defamatory content, including the creation of defamatory statements,
        images, or other content\\n    3. Generating, promoting, or further distributing
        spam\\n    4. Impersonating another individual without consent, authorization,
        or legal right\\n    5. Representing that the use of Llama 3.1 or outputs
        are human-generated\\n    6. Generating or facilitating false online engagement,
        including fake reviews and other means of fake online engagement\\n\\n4. Fail
        to appropriately disclose to end users any known dangers of your AI system\\n\\nPlease
        report any violation of this Policy, software \u201Cbug,\u201D or other problems
        that could lead to a violation\\nof this Policy through one of the following
        means:\\n\\n* Reporting issues with the model: [https://github.com/meta-llama/llama-models/issues](https://github.com/meta-llama/llama-models/issues)\\n*
        Reporting risky content generated by the model: developers.facebook.com/llama_output_feedback\\n*
        Reporting bugs and security concerns: facebook.com/whitehat/info\\n* Reporting
        violations of the Acceptable Use Policy or unlicensed uses of Llama 3.1: <EMAIL>\\\"\\n\",\"parameters\":\"stop
        \                          \\\"\\u003c|start_header_id|\\u003e\\\"\\nstop
        \                          \\\"\\u003c|end_header_id|\\u003e\\\"\\nstop                           \\\"\\u003c|eot_id|\\u003e\\\"\",\"template\":\"{{-
        if or .System .Tools }}\\u003c|start_header_id|\\u003esystem\\u003c|end_header_id|\\u003e\\n{{-
        if .System }}\\n\\n{{ .System }}\\n{{- end }}\\n{{- if .Tools }}\\n\\nCutting
        Knowledge Date: December 2023\\n\\nWhen you receive a tool call response,
        use the output to format an answer to the orginal user question.\\n\\nYou
        are a helpful assistant with tool calling capabilities.\\n{{- end }}\\u003c|eot_id|\\u003e\\n{{-
        end }}\\n{{- range $i, $_ := .Messages }}\\n{{- $last := eq (len (slice $.Messages
        $i)) 1 }}\\n{{- if eq .Role \\\"user\\\" }}\\u003c|start_header_id|\\u003euser\\u003c|end_header_id|\\u003e\\n{{-
        if and $.Tools $last }}\\n\\nGiven the following functions, please respond
        with a JSON for a function call with its proper arguments that best answers
        the given prompt.\\n\\nRespond in the format {\\\"name\\\": function name,
        \\\"parameters\\\": dictionary of argument name and its value}. Do not use
        variables.\\n\\n{{ range $.Tools }}\\n{{- . }}\\n{{ end }}\\nQuestion: {{
        .Content }}\\u003c|eot_id|\\u003e\\n{{- else }}\\n\\n{{ .Content }}\\u003c|eot_id|\\u003e\\n{{-
        end }}{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
        end }}\\n{{- else if eq .Role \\\"assistant\\\" }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n{{-
        if .ToolCalls }}\\n{{ range .ToolCalls }}\\n{\\\"name\\\": \\\"{{ .Function.Name
        }}\\\", \\\"parameters\\\": {{ .Function.Arguments }}}{{ end }}\\n{{- else
        }}\\n\\n{{ .Content }}\\n{{- end }}{{ if not $last }}\\u003c|eot_id|\\u003e{{
        end }}\\n{{- else if eq .Role \\\"tool\\\" }}\\u003c|start_header_id|\\u003eipython\\u003c|end_header_id|\\u003e\\n\\n{{
        .Content }}\\u003c|eot_id|\\u003e{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
        end }}\\n{{- end }}\\n{{- end }}\",\"details\":{\"parent_model\":\"\",\"format\":\"gguf\",\"family\":\"llama\",\"families\":[\"llama\"],\"parameter_size\":\"8.0B\",\"quantization_level\":\"Q4_K_M\"},\"model_info\":{\"general.architecture\":\"llama\",\"general.basename\":\"Meta-Llama-3.1\",\"general.file_type\":15,\"general.finetune\":\"Instruct\",\"general.languages\":[\"en\",\"de\",\"fr\",\"it\",\"pt\",\"hi\",\"es\",\"th\"],\"general.license\":\"llama3.1\",\"general.parameter_count\":**********,\"general.quantization_version\":2,\"general.size_label\":\"8B\",\"general.tags\":[\"facebook\",\"meta\",\"pytorch\",\"llama\",\"llama-3\",\"text-generation\"],\"general.type\":\"model\",\"llama.attention.head_count\":32,\"llama.attention.head_count_kv\":8,\"llama.attention.layer_norm_rms_epsilon\":0.00001,\"llama.block_count\":32,\"llama.context_length\":131072,\"llama.embedding_length\":4096,\"llama.feed_forward_length\":14336,\"llama.rope.dimension_count\":128,\"llama.rope.freq_base\":500000,\"llama.vocab_size\":128256,\"tokenizer.ggml.bos_token_id\":128000,\"tokenizer.ggml.eos_token_id\":128009,\"tokenizer.ggml.merges\":null,\"tokenizer.ggml.model\":\"gpt2\",\"tokenizer.ggml.pre\":\"llama-bpe\",\"tokenizer.ggml.token_type\":null,\"tokenizer.ggml.tokens\":null},\"tensors\":[{\"name\":\"token_embd.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,128256]},{\"name\":\"rope_freqs.weight\",\"type\":\"F32\",\"shape\":[64]},{\"name\":\"blk.0.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.0.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.0.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.0.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.0.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.0.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.0.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.0.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.0.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.1.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.1.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.1.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.1.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.1.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.1.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.1.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.1.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.1.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.2.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.2.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.2.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.2.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.2.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.2.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.2.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.2.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.2.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.3.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.3.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.3.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.3.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.3.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.3.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.3.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.3.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.3.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.4.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.4.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.4.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.4.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.4.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.4.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.4.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.4.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.4.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.5.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.5.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.5.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.5.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.5.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.5.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.5.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.5.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.5.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.6.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.6.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.6.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.6.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.6.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.6.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.6.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.6.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.6.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.7.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.7.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.7.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.7.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.7.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.7.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.7.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.7.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.7.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.8.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.8.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.8.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.8.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.8.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.8.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.8.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.8.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.8.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.10.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.10.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.10.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.10.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.10.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.10.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.10.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.10.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.10.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.11.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.11.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.11.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.11.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.11.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.11.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.11.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.11.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.11.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.12.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.12.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.12.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.12.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.12.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.12.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.12.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.12.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.12.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.13.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.13.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.13.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.13.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.13.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.13.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.13.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.13.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.13.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.14.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.14.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.14.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.14.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.14.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.14.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.14.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.14.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.14.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.15.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.15.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.15.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.15.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.15.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.15.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.15.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.15.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.15.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.16.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.16.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.16.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.16.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.16.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.16.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.16.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.16.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.16.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.17.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.17.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.17.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.17.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.17.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.17.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.17.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.17.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.17.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.18.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.18.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.18.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.18.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.18.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.18.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.18.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.18.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.18.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.19.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.19.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.19.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.19.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.19.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.19.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.19.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.19.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.19.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.20.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.20.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.20.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.20.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.20.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.9.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.9.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.9.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.9.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.9.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.9.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.9.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.9.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.9.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.20.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.20.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.20.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.20.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.21.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.21.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.21.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.21.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.21.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.21.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.21.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.21.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.21.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.22.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.22.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.22.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.22.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.22.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.22.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.22.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.22.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.22.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.23.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.23.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.23.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.23.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.23.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.23.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.23.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.23.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.23.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.24.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.24.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.24.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.24.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.24.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.24.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.24.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.24.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.24.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.25.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.25.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.25.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.25.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.25.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.25.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.25.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.25.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.25.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.26.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.26.ffn_down.weight\",\"type\":\"Q3_K_M\",\"shape\":[14336,4096]},{\"name\":\"blk.26.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.26.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.26.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.26.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.26.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.26.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.26.attn_v.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.27.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.27.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.27.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.27.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.27.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.27.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.27.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.27.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.27.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.28.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.28.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.28.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.28.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.28.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.28.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.28.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.28.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.28.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.29.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.29.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.29.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.29.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.29.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.29.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.29.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.29.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.29.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.30.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.30.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.30.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.30.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.30.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.30.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.30.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.30.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.30.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"blk.31.ffn_gate.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.31.ffn_up.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,14336]},{\"name\":\"blk.31.attn_k.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,1024]},{\"name\":\"blk.31.attn_output.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.31.attn_q.weight\",\"type\":\"Q3_K_M\",\"shape\":[4096,4096]},{\"name\":\"blk.31.attn_v.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,1024]},{\"name\":\"output.weight\",\"type\":\"Q4_K_S\",\"shape\":[4096,128256]},{\"name\":\"blk.31.attn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"blk.31.ffn_down.weight\",\"type\":\"Q4_K_S\",\"shape\":[14336,4096]},{\"name\":\"blk.31.ffn_norm.weight\",\"type\":\"F32\",\"shape\":[4096]},{\"name\":\"output_norm.weight\",\"type\":\"F32\",\"shape\":[4096]}],\"capabilities\":[\"completion\",\"tools\"],\"modified_at\":\"2025-04-11T14:41:15.05985701Z\"}"
    headers:
      Content-Type:
      - application/json; charset=utf-8
      Date:
      - Wed, 07 May 2025 01:17:28 GMT
      Transfer-Encoding:
      - chunked
    status:
      code: 200
      message: OK
version: 1
