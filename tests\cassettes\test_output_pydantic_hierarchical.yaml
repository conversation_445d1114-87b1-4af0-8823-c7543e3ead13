interactions:
- request:
    body: '{"messages": [{"role": "system", "content": "You are Crew Manager. You
      are a seasoned manager with a knack for getting the best out of your team.\nYou
      are also known for your ability to delegate work to the right people, and to
      ask the right questions to get the best out of your team.\nEven though you don''t
      perform tasks by yourself, you have a lot of experience in the field, which
      allows you to properly evaluate the work of your team members.\nYour personal
      goal is: Manage the team to complete the task in the best way possible.\nYou
      ONLY have access to the following tools, and should NEVER make up tools that
      are not listed here:\n\nTool Name: Delegate work to coworker(task: str, context:
      str, coworker: Optional[str] = None, **kwargs)\nTool Description: Delegate a
      specific task to one of the following coworkers: Scorer\nThe input to this tool
      should be the coworker, the task you want them to do, and ALL necessary context
      to execute the task, they know nothing about the task, so share absolute everything
      you know, don''t reference things but instead explain them.\nTool Arguments:
      {''task'': {''title'': ''Task'', ''type'': ''string''}, ''context'': {''title'':
      ''Context'', ''type'': ''string''}, ''coworker'': {''title'': ''Coworker'',
      ''type'': ''string''}, ''kwargs'': {''title'': ''Kwargs'', ''type'': ''object''}}\nTool
      Name: Ask question to coworker(question: str, context: str, coworker: Optional[str]
      = None, **kwargs)\nTool Description: Ask a specific question to one of the following
      coworkers: Scorer\nThe input to this tool should be the coworker, the question
      you have for them, and ALL necessary context to ask the question properly, they
      know nothing about the question, so share absolute everything you know, don''t
      reference things but instead explain them.\nTool Arguments: {''question'': {''title'':
      ''Question'', ''type'': ''string''}, ''context'': {''title'': ''Context'', ''type'':
      ''string''}, ''coworker'': {''title'': ''Coworker'', ''type'': ''string''},
      ''kwargs'': {''title'': ''Kwargs'', ''type'': ''object''}}\n\nUse the following
      format:\n\nThought: you should always think about what to do\nAction: the action
      to take, only one name of [Delegate work to coworker, Ask question to coworker],
      just the name, exactly as it''s written.\nAction Input: the input to the action,
      just a simple python dictionary, enclosed in curly braces, using \" to wrap
      keys and values.\nObservation: the result of the action\n\nOnce all necessary
      information is gathered:\n\nThought: I now know the final answer\nFinal Answer:
      the final answer to the original input question\n"}, {"role": "user", "content":
      "\nCurrent Task: Give me an integer score between 1-5 for the following title:
      ''The impact of AI in the future of work''\n\nThis is the expect criteria for
      your final answer: The score of the title.\nyou MUST return the actual complete
      content as the final answer, not a summary.\n\nBegin! This is VERY important
      to you, use the tools available and give your best Final Answer, your job depends
      on it!\n\nThought:"}], "model": "gpt-4o"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '2986'
      content-type:
      - application/json
      cookie:
      - __cf_bm=9.8sBYBkvBR8R1K_bVF7xgU..80XKlEIg3N2OBbTSCU-**********-*******-.qiTLXbPamYUMSuyNsOEB9jhGu.jOifujOrx9E2JZvStbIZ9RTIiE44xKKNfLPxQkOi6qAT3h6htK8lPDGV_5g;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-*************-*******-*********
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7ftPrKueGANoTk2jisJQy28SMQZ\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727214473,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"I need to get an integer score between
      1-5 for the given title \\\"The impact of AI in the future of work.\\\" I will
      delegate this task to Scorer, providing them with all necessary context.\\n\\nAction:
      Delegate work to coworker\\nAction Input: {\\\"coworker\\\": \\\"Scorer\\\",
      \\\"task\\\": \\\"Provide an integer score between 1-5 for the following title:
      'The impact of AI in the future of work'\\\", \\\"context\\\": \\\"This is for
      evaluating the impact of AI on the future of work. The score must be an integer
      between 1-5.\\\"}\",\n        \"refusal\": null\n      },\n      \"logprobs\":
      null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      664,\n    \"completion_tokens\": 123,\n    \"total_tokens\": 787,\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0\n    }\n  },\n  \"system_fingerprint\": \"fp_e375328146\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85f9b85bf71cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:47:55 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '1941'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999269'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 1ms
      x-request-id:
      - req_9ed13e6054d3e4d6b7b600e20891fb25
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are Scorer. You''re an
      expert scorer, specialized in scoring titles.\nYour personal goal is: Score
      the title\nTo give my best complete final answer to the task use the exact following
      format:\n\nThought: I now can give a great answer\nFinal Answer: Your final
      answer must be the great and the most complete as possible, it must be outcome
      described.\n\nI MUST use these formats, my job depends on it!"}, {"role": "user",
      "content": "\nCurrent Task: Provide an integer score between 1-5 for the following
      title: ''The impact of AI in the future of work''\n\nThis is the expect criteria
      for your final answer: Your best answer to your coworker asking you this, accounting
      for the context shared.\nyou MUST return the actual complete content as the
      final answer, not a summary.\n\nThis is the context you''re working with:\nThis
      is for evaluating the impact of AI on the future of work. The score must be
      an integer between 1-5.\n\nBegin! This is VERY important to you, use the tools
      available and give your best Final Answer, your job depends on it!\n\nThought:"}],
      "model": "gpt-4o"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1127'
      content-type:
      - application/json
      cookie:
      - __cf_bm=9.8sBYBkvBR8R1K_bVF7xgU..80XKlEIg3N2OBbTSCU-**********-*******-.qiTLXbPamYUMSuyNsOEB9jhGu.jOifujOrx9E2JZvStbIZ9RTIiE44xKKNfLPxQkOi6qAT3h6htK8lPDGV_5g;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-*************-*******-*********
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7fvCyQD7z7StdDBs7M1mDlMsW6l\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727214475,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I now can give a great answer\\nFinal
      Answer: 4\",\n        \"refusal\": null\n      },\n      \"logprobs\": null,\n
      \     \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      230,\n    \"completion_tokens\": 15,\n    \"total_tokens\": 245,\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0\n    }\n  },\n  \"system_fingerprint\": \"fp_e375328146\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85f9c6df9b1cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:47:56 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '350'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999729'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_a88c60d514500d2b940c40ad4d553e73
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are Crew Manager. You
      are a seasoned manager with a knack for getting the best out of your team.\nYou
      are also known for your ability to delegate work to the right people, and to
      ask the right questions to get the best out of your team.\nEven though you don''t
      perform tasks by yourself, you have a lot of experience in the field, which
      allows you to properly evaluate the work of your team members.\nYour personal
      goal is: Manage the team to complete the task in the best way possible.\nYou
      ONLY have access to the following tools, and should NEVER make up tools that
      are not listed here:\n\nTool Name: Delegate work to coworker(task: str, context:
      str, coworker: Optional[str] = None, **kwargs)\nTool Description: Delegate a
      specific task to one of the following coworkers: Scorer\nThe input to this tool
      should be the coworker, the task you want them to do, and ALL necessary context
      to execute the task, they know nothing about the task, so share absolute everything
      you know, don''t reference things but instead explain them.\nTool Arguments:
      {''task'': {''title'': ''Task'', ''type'': ''string''}, ''context'': {''title'':
      ''Context'', ''type'': ''string''}, ''coworker'': {''title'': ''Coworker'',
      ''type'': ''string''}, ''kwargs'': {''title'': ''Kwargs'', ''type'': ''object''}}\nTool
      Name: Ask question to coworker(question: str, context: str, coworker: Optional[str]
      = None, **kwargs)\nTool Description: Ask a specific question to one of the following
      coworkers: Scorer\nThe input to this tool should be the coworker, the question
      you have for them, and ALL necessary context to ask the question properly, they
      know nothing about the question, so share absolute everything you know, don''t
      reference things but instead explain them.\nTool Arguments: {''question'': {''title'':
      ''Question'', ''type'': ''string''}, ''context'': {''title'': ''Context'', ''type'':
      ''string''}, ''coworker'': {''title'': ''Coworker'', ''type'': ''string''},
      ''kwargs'': {''title'': ''Kwargs'', ''type'': ''object''}}\n\nUse the following
      format:\n\nThought: you should always think about what to do\nAction: the action
      to take, only one name of [Delegate work to coworker, Ask question to coworker],
      just the name, exactly as it''s written.\nAction Input: the input to the action,
      just a simple python dictionary, enclosed in curly braces, using \" to wrap
      keys and values.\nObservation: the result of the action\n\nOnce all necessary
      information is gathered:\n\nThought: I now know the final answer\nFinal Answer:
      the final answer to the original input question\n"}, {"role": "user", "content":
      "\nCurrent Task: Give me an integer score between 1-5 for the following title:
      ''The impact of AI in the future of work''\n\nThis is the expect criteria for
      your final answer: The score of the title.\nyou MUST return the actual complete
      content as the final answer, not a summary.\n\nBegin! This is VERY important
      to you, use the tools available and give your best Final Answer, your job depends
      on it!\n\nThought:"}, {"role": "assistant", "content": "I need to get an integer
      score between 1-5 for the given title \"The impact of AI in the future of work.\"
      I will delegate this task to Scorer, providing them with all necessary context.\n\nAction:
      Delegate work to coworker\nAction Input: {\"coworker\": \"Scorer\", \"task\":
      \"Provide an integer score between 1-5 for the following title: ''The impact
      of AI in the future of work''\", \"context\": \"This is for evaluating the impact
      of AI on the future of work. The score must be an integer between 1-5.\"}\nObservation:
      4"}], "model": "gpt-4o"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '3546'
      content-type:
      - application/json
      cookie:
      - __cf_bm=9.8sBYBkvBR8R1K_bVF7xgU..80XKlEIg3N2OBbTSCU-**********-*******-.qiTLXbPamYUMSuyNsOEB9jhGu.jOifujOrx9E2JZvStbIZ9RTIiE44xKKNfLPxQkOi6qAT3h6htK8lPDGV_5g;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-*************-*******-*********
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7fwZ4WSniWh4YMRrip2x1Bx03b7\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727214476,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I now know the final answer.\\n\\nFinal
      Answer: 4\",\n        \"refusal\": null\n      },\n      \"logprobs\": null,\n
      \     \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      795,\n    \"completion_tokens\": 14,\n    \"total_tokens\": 809,\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0\n    }\n  },\n  \"system_fingerprint\": \"fp_e375328146\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85f9cd08fb1cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:47:56 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '285'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999141'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 1ms
      x-request-id:
      - req_7afa4cb902ce1bca02dcd7dbf2e36970
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: !!binary |
      CsUWCiQKIgoMc2VydmljZS5uYW1lEhIKEGNyZXdBSS10ZWxlbWV0cnkSnBYKEgoQY3Jld2FpLnRl
      bGVtZXRyeRKWBwoQ7hmoiN2Q853yaKv7s8v78xII1Z7nWICk2xQqDENyZXcgQ3JlYXRlZDABORiE
      ZSRpTPgXQZDhZyRpTPgXShoKDmNyZXdhaV92ZXJzaW9uEggKBjAuNjEuMEoaCg5weXRob25fdmVy
      c2lvbhIICgYzLjExLjdKLgoIY3Jld19rZXkSIgogNWU2ZWZmZTY4MGE1ZDk3ZGMzODczYjE0ODI1
      Y2NmYTNKMQoHY3Jld19pZBImCiQwNmFkMjAzYS1lYzY3LTQ5NzgtOWUwYy02N2FmOTgwNTExNjlK
      HAoMY3Jld19wcm9jZXNzEgwKCnNlcXVlbnRpYWxKEQoLY3Jld19tZW1vcnkSAhAAShoKFGNyZXdf
      bnVtYmVyX29mX3Rhc2tzEgIYAUobChVjcmV3X251bWJlcl9vZl9hZ2VudHMSAhgBSsgCCgtjcmV3
      X2FnZW50cxK4Agq1Alt7ImtleSI6ICI5MmU3ZWIxOTE2NjRjOTM1Nzg1ZWQ3ZDQyNDBhMjk0ZCIs
      ICJpZCI6ICJlYzQwNTViMC1hMjJhLTQ0NDYtODMyZS1jMmY5MzBjMWM0OGYiLCAicm9sZSI6ICJT
      Y29yZXIiLCAidmVyYm9zZT8iOiBmYWxzZSwgIm1heF9pdGVyIjogMTUsICJtYXhfcnBtIjogbnVs
      bCwgImZ1bmN0aW9uX2NhbGxpbmdfbGxtIjogIiIsICJsbG0iOiAiZ3B0LTRvIiwgImRlbGVnYXRp
      b25fZW5hYmxlZD8iOiBmYWxzZSwgImFsbG93X2NvZGVfZXhlY3V0aW9uPyI6IGZhbHNlLCAibWF4
      X3JldHJ5X2xpbWl0IjogMiwgInRvb2xzX25hbWVzIjogW119XUr7AQoKY3Jld190YXNrcxLsAQrp
      AVt7ImtleSI6ICIyN2VmMzhjYzk5ZGE0YThkZWQ3MGVkNDA2ZTQ0YWI4NiIsICJpZCI6ICIyM2Mw
      YzVkMC1kOWJhLTQwODAtOTVlNy1hNTVhMDI4Zjc5YmIiLCAiYXN5bmNfZXhlY3V0aW9uPyI6IGZh
      bHNlLCAiaHVtYW5faW5wdXQ/IjogZmFsc2UsICJhZ2VudF9yb2xlIjogIlNjb3JlciIsICJhZ2Vu
      dF9rZXkiOiAiOTJlN2ViMTkxNjY0YzkzNTc4NWVkN2Q0MjQwYTI5NGQiLCAidG9vbHNfbmFtZXMi
      OiBbXX1degIYAYUBAAEAABKOAgoQJ9OdH2Oo8Rb6Phj24GNRNxIIJdpH3NFfg4kqDFRhc2sgQ3Jl
      YXRlZDABOQCHeiRpTPgXQThOeyRpTPgXSi4KCGNyZXdfa2V5EiIKIDVlNmVmZmU2ODBhNWQ5N2Rj
      Mzg3M2IxNDgyNWNjZmEzSjEKB2NyZXdfaWQSJgokMDZhZDIwM2EtZWM2Ny00OTc4LTllMGMtNjdh
      Zjk4MDUxMTY5Si4KCHRhc2tfa2V5EiIKIDI3ZWYzOGNjOTlkYTRhOGRlZDcwZWQ0MDZlNDRhYjg2
      SjEKB3Rhc2tfaWQSJgokMjNjMGM1ZDAtZDliYS00MDgwLTk1ZTctYTU1YTAyOGY3OWJiegIYAYUB
      AAEAABKQAgoQz7ebW98sQ+bPLeiKpGCv/hIIzmkkC3T0j0wqDlRhc2sgRXhlY3V0aW9uMAE5iJR7
      JGlM+BdB2HgueWlM+BdKLgoIY3Jld19rZXkSIgogNWU2ZWZmZTY4MGE1ZDk3ZGMzODczYjE0ODI1
      Y2NmYTNKMQoHY3Jld19pZBImCiQwNmFkMjAzYS1lYzY3LTQ5NzgtOWUwYy02N2FmOTgwNTExNjlK
      LgoIdGFza19rZXkSIgogMjdlZjM4Y2M5OWRhNGE4ZGVkNzBlZDQwNmU0NGFiODZKMQoHdGFza19p
      ZBImCiQyM2MwYzVkMC1kOWJhLTQwODAtOTVlNy1hNTVhMDI4Zjc5YmJ6AhgBhQEAAQAAEpgHChD6
      So0yENrsTtbKKm1BpmVCEghSKU/H68ezCioMQ3JldyBDcmVhdGVkMAE5aKdGemlM+BdBoFZLemlM
      +BdKGgoOY3Jld2FpX3ZlcnNpb24SCAoGMC42MS4wShoKDnB5dGhvbl92ZXJzaW9uEggKBjMuMTEu
      N0ouCghjcmV3X2tleRIiCiA1ZTZlZmZlNjgwYTVkOTdkYzM4NzNiMTQ4MjVjY2ZhM0oxCgdjcmV3
      X2lkEiYKJDA4NzM1YWQ3LWJlMWEtNDRkMy05NTc3LWEzMzRmMjY1M2Y2MUoeCgxjcmV3X3Byb2Nl
      c3MSDgoMaGllcmFyY2hpY2FsShEKC2NyZXdfbWVtb3J5EgIQAEoaChRjcmV3X251bWJlcl9vZl90
      YXNrcxICGAFKGwoVY3Jld19udW1iZXJfb2ZfYWdlbnRzEgIYAUrIAgoLY3Jld19hZ2VudHMSuAIK
      tQJbeyJrZXkiOiAiOTJlN2ViMTkxNjY0YzkzNTc4NWVkN2Q0MjQwYTI5NGQiLCAiaWQiOiAiM2Ex
      ZWQwYjAtN2MyMy00YzI0LWJkNzEtYzllZGEzYTRhOTE2IiwgInJvbGUiOiAiU2NvcmVyIiwgInZl
      cmJvc2U/IjogZmFsc2UsICJtYXhfaXRlciI6IDE1LCAibWF4X3JwbSI6IG51bGwsICJmdW5jdGlv
      bl9jYWxsaW5nX2xsbSI6ICIiLCAibGxtIjogImdwdC00byIsICJkZWxlZ2F0aW9uX2VuYWJsZWQ/
      IjogZmFsc2UsICJhbGxvd19jb2RlX2V4ZWN1dGlvbj8iOiBmYWxzZSwgIm1heF9yZXRyeV9saW1p
      dCI6IDIsICJ0b29sc19uYW1lcyI6IFtdfV1K+wEKCmNyZXdfdGFza3MS7AEK6QFbeyJrZXkiOiAi
      MjdlZjM4Y2M5OWRhNGE4ZGVkNzBlZDQwNmU0NGFiODYiLCAiaWQiOiAiOWIxNmIxN2EtMGU4OS00
      ZjBiLTg3NzQtZmY5MTg1NzRmMzY2IiwgImFzeW5jX2V4ZWN1dGlvbj8iOiBmYWxzZSwgImh1bWFu
      X2lucHV0PyI6IGZhbHNlLCAiYWdlbnRfcm9sZSI6ICJTY29yZXIiLCAiYWdlbnRfa2V5IjogIjky
      ZTdlYjE5MTY2NGM5MzU3ODVlZDdkNDI0MGEyOTRkIiwgInRvb2xzX25hbWVzIjogW119XXoCGAGF
      AQABAAASjgIKEERPBzklAtGSN2WJ/m32bsYSCCh9XOn+rkCQKgxUYXNrIENyZWF0ZWQwATlgnTR7
      aUz4F0HgWDV7aUz4F0ouCghjcmV3X2tleRIiCiA1ZTZlZmZlNjgwYTVkOTdkYzM4NzNiMTQ4MjVj
      Y2ZhM0oxCgdjcmV3X2lkEiYKJDA4NzM1YWQ3LWJlMWEtNDRkMy05NTc3LWEzMzRmMjY1M2Y2MUou
      Cgh0YXNrX2tleRIiCiAyN2VmMzhjYzk5ZGE0YThkZWQ3MGVkNDA2ZTQ0YWI4NkoxCgd0YXNrX2lk
      EiYKJDliMTZiMTdhLTBlODktNGYwYi04Nzc0LWZmOTE4NTc0ZjM2NnoCGAGFAQABAAASnAEKEPU4
      SNHw9hxH/s8bG9c91koSCDcUNUW+hkT9KgpUb29sIFVzYWdlMAE5UDVuQGpM+BdBEG90QGpM+BdK
      GgoOY3Jld2FpX3ZlcnNpb24SCAoGMC42MS4wSigKCXRvb2xfbmFtZRIbChlEZWxlZ2F0ZSB3b3Jr
      IHRvIGNvd29ya2VySg4KCGF0dGVtcHRzEgIYAXoCGAGFAQABAAA=
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
      Content-Length:
      - '2888'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.27.0
    method: POST
    uri: https://telemetry.crewai.com:4319/v1/traces
  response:
    body:
      string: "\n\0"
    headers:
      Content-Length:
      - '2'
      Content-Type:
      - application/x-protobuf
      Date:
      - Tue, 24 Sep 2024 21:47:57 GMT
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "user", "content": "4"}, {"role": "system", "content":
      "I''m gonna convert this raw text into valid JSON.\n\nThe json should have the
      following structure, with the following keys:\n{\n    score: int\n}"}], "model":
      "gpt-4o", "tool_choice": {"type": "function", "function": {"name": "ScoreOutput"}},
      "tools": [{"type": "function", "function": {"name": "ScoreOutput", "description":
      "Correctly extracted `ScoreOutput` with all the required parameters with correct
      types", "parameters": {"properties": {"score": {"title": "Score", "type": "integer"}},
      "required": ["score"], "type": "object"}}}]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '615'
      content-type:
      - application/json
      cookie:
      - __cf_bm=9.8sBYBkvBR8R1K_bVF7xgU..80XKlEIg3N2OBbTSCU-**********-*******-.qiTLXbPamYUMSuyNsOEB9jhGu.jOifujOrx9E2JZvStbIZ9RTIiE44xKKNfLPxQkOi6qAT3h6htK8lPDGV_5g;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-*************-*******-*********
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7fxFJVaaPLV8eezP1LWgZes9p8t\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727214477,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": null,\n        \"tool_calls\": [\n          {\n
      \           \"id\": \"call_TuUA4HNG1rH6GOq9nrx94Fp7\",\n            \"type\":
      \"function\",\n            \"function\": {\n              \"name\": \"ScoreOutput\",\n
      \             \"arguments\": \"{\\\"score\\\":4}\"\n            }\n          }\n
      \       ],\n        \"refusal\": null\n      },\n      \"logprobs\": null,\n
      \     \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      100,\n    \"completion_tokens\": 5,\n    \"total_tokens\": 105,\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0\n    }\n  },\n  \"system_fingerprint\": \"fp_3537616b13\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85f9d13ee71cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:47:57 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '267'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999947'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_c8bb2af8f4a63658c3c9316045a413e1
    http_version: HTTP/1.1
    status_code: 200
version: 1
