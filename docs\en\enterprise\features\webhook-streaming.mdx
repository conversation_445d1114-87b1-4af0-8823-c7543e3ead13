---
title: Webhook Streaming
description: "Using Webhook Streaming to stream events to your webhook"
icon: "webhook"
---

## Overview

Enterprise Event Streaming lets you receive real-time webhook updates about your crews and flows deployed to 
CrewAI Enterprise, such as model calls, tool usage, and flow steps.

## Usage

When using the Kickoff API, include a `webhooks` object to your request, for example:

```json
{
  "inputs": {"foo": "bar"},
  "webhooks": {
    "events": ["crew_kickoff_started", "llm_call_started"],
    "url": "https://your.endpoint/webhook",
    "realtime": false,
    "authentication": {
      "strategy": "bearer",
      "token": "my-secret-token"
    }
  }
}
```

If `realtime` is set to `true`, each event is delivered individually and immediately, at the cost of crew/flow performance.

## Webhook Format

Each webhook sends a list of events:

```json
{
  "events": [
    {
      "id": "event-id",
      "execution_id": "crew-run-id",
      "timestamp": "2025-02-16T10:58:44.965Z",
      "type": "llm_call_started",
      "data": {
        "model": "gpt-4",
        "messages": [
          {"role": "system", "content": "You are an assistant."},
          {"role": "user", "content": "Summarize this article."}
        ]
      }
    }
  ]
}
```

The `data` object structure varies by event type. Refer to the [event list](https://github.com/crewAIInc/crewAI/tree/main/src/crewai/utilities/events) on GitHub.

As requests are sent over HTTP, the order of events can't be guaranteed. If you need ordering, use the `timestamp` field.

## Supported Events

CrewAI supports both system events and custom events in Enterprise Event Streaming. These events are sent to your configured webhook endpoint during crew and flow execution.

- `crew_kickoff_started`
- `crew_step_started`
- `crew_step_completed`
- `crew_execution_completed`
- `llm_call_started`
- `llm_call_completed`
- `tool_usage_started`
- `tool_usage_completed`
- `crew_test_failed`
- *...and others*

Event names match the internal event bus. See [GitHub source](https://github.com/crewAIInc/crewAI/tree/main/src/crewai/utilities/events) for the full list.

You can emit your own custom events, and they will be delivered through the webhook stream alongside system events.

<Card title="Need Help?" icon="headset" href="mailto:<EMAIL>">
  Contact our support team for assistance with webhook integration or troubleshooting.
</Card>