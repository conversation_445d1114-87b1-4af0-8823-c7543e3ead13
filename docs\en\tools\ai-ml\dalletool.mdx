---
title: DALL-E Tool
description: The `DallETool` is a powerful tool designed for generating images from textual descriptions.
icon: image
---

# `Dal<PERSON>ETool`

## Description

This tool is used to give the Agent the ability to generate images using the DALL-E model. It is a transformer-based model that generates images from textual descriptions. 
This tool allows the Agent to generate images based on the text input provided by the user.

## Installation

Install the crewai_tools package
```shell
pip install 'crewai[tools]'
```

## Example

Remember that when using this tool, the text must be generated by the Agent itself. The text must be a description of the image you want to generate.

```python Code
from crewai_tools import DallETool

Agent(
    ...
    tools=[DallETool()],
)
```

If needed you can also tweak the parameters of the DALL-E model by passing them as arguments to the `DallETool` class. For example:

```python Code
from crewai_tools import DallETool

dalle_tool = DallETool(model="dall-e-3",
                       size="1024x1024",
                       quality="standard",
                       n=1)

Agent(
    ...
    tools=[dalle_tool]
)
```

The parameters are based on the `client.images.generate` method from the OpenAI API. For more information on the parameters, 
please refer to the [OpenAI API documentation](https://platform.openai.com/docs/guides/images/introduction?lang=python).