interactions:
- request:
    body: '{"messages": [{"role": "system", "content": "You are Greeter. You are a
      friendly greeter.\nYour personal goal is: Say hello.\nYou ONLY have access to
      the following tools, and should NEVER make up tools that are not listed here:\n\nTool
      Name: look_up_greeting\nTool Arguments: {}\nTool Description: Tool used to retrieve
      a greeting.\n\nIMPORTANT: Use the following format in your response:\n\n```\nThought:
      you should always think about what to do\nAction: the action to take, only one
      name of [look_up_greeting], just the name, exactly as it''s written.\nAction
      Input: the input to the action, just a simple JSON object, enclosed in curly
      braces, using \" to wrap keys and values.\nObservation: the result of the action\n```\n\nOnce
      all necessary information is gathered, return the following format:\n\n```\nThought:
      I now know the final answer\nFinal Answer: the final answer to the original
      input question\n```"}, {"role": "user", "content": "\nCurrent Task: Look up
      the greeting and say it.\n\nThis is the expect criteria for your final answer:
      A greeting.\nyou MUST return the actual complete content as the final answer,
      not a summary.\n\nBegin! This is VERY important to you, use the tools available
      and give your best Final Answer, your job depends on it!\n\nThought:"}], "model":
      "gpt-4o-mini", "stop": ["\nObservation:"], "stream": false}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1347'
      content-type:
      - application/json
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.52.1
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.52.1
      x-stainless-raw-response:
      - 'true'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AnsSJl1P6VZgWhm1B4PiHgzsl9NLL\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1736450763,\n  \"model\": \"gpt-4o-mini-2024-07-18\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"I need to look up a greeting to fulfill
      the task.\\n\\nAction: look_up_greeting\\nAction Input: {}\",\n        \"refusal\":
      null\n      },\n      \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n
      \   }\n  ],\n  \"usage\": {\n    \"prompt_tokens\": 266,\n    \"completion_tokens\":
      23,\n    \"total_tokens\": 289,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"system_fingerprint\":
      \"fp_f2cd28694a\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8ff6d117cf41bf8e-ATL
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 09 Jan 2025 19:26:04 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=28kBtf6u6VOKoCJXrEeDJF8b477n6wU4l4aLCyuhoB4-1736450764-*******-E58mL8GeVt3QS2hw9WBzja7bHi9Woe9wK0q.bD1LUANCAO9BaGxBHi9L2KNgHWKVG3Ry1FqDYtqQA4Xi8qmQCQ;
        path=/; expires=Thu, 09-Jan-25 19:56:04 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=LFiHkCkQ_CCUuZH7SxNFJ6rRb4aifBj.OlQXaNkVkUg-1736450764158-*******-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '612'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999689'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_59288de1eb8dd37fe376614bf19985d1
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are Greeter. You are a
      friendly greeter.\nYour personal goal is: Say hello.\nYou ONLY have access to
      the following tools, and should NEVER make up tools that are not listed here:\n\nTool
      Name: look_up_greeting\nTool Arguments: {}\nTool Description: Tool used to retrieve
      a greeting.\n\nIMPORTANT: Use the following format in your response:\n\n```\nThought:
      you should always think about what to do\nAction: the action to take, only one
      name of [look_up_greeting], just the name, exactly as it''s written.\nAction
      Input: the input to the action, just a simple JSON object, enclosed in curly
      braces, using \" to wrap keys and values.\nObservation: the result of the action\n```\n\nOnce
      all necessary information is gathered, return the following format:\n\n```\nThought:
      I now know the final answer\nFinal Answer: the final answer to the original
      input question\n```"}, {"role": "user", "content": "\nCurrent Task: Look up
      the greeting and say it.\n\nThis is the expect criteria for your final answer:
      A greeting.\nyou MUST return the actual complete content as the final answer,
      not a summary.\n\nBegin! This is VERY important to you, use the tools available
      and give your best Final Answer, your job depends on it!\n\nThought:"}, {"role":
      "assistant", "content": "I need to look up a greeting to fulfill the task.\n\nAction:
      look_up_greeting\nAction Input: {}\nObservation: Howdy!"}], "model": "gpt-4o-mini",
      "stop": ["\nObservation:"], "stream": false}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1501'
      content-type:
      - application/json
      cookie:
      - __cf_bm=28kBtf6u6VOKoCJXrEeDJF8b477n6wU4l4aLCyuhoB4-1736450764-*******-E58mL8GeVt3QS2hw9WBzja7bHi9Woe9wK0q.bD1LUANCAO9BaGxBHi9L2KNgHWKVG3Ry1FqDYtqQA4Xi8qmQCQ;
        _cfuvid=LFiHkCkQ_CCUuZH7SxNFJ6rRb4aifBj.OlQXaNkVkUg-1736450764158-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.52.1
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.52.1
      x-stainless-raw-response:
      - 'true'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AnsSKvXMJzmmX05GCywciRNUDC1wj\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1736450764,\n  \"model\": \"gpt-4o-mini-2024-07-18\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I now know the final answer\\nFinal
      Answer: Howdy!\",\n        \"refusal\": null\n      },\n      \"logprobs\":
      null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      298,\n    \"completion_tokens\": 15,\n    \"total_tokens\": 313,\n    \"prompt_tokens_details\":
      {\n      \"cached_tokens\": 0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"system_fingerprint\":
      \"fp_f2cd28694a\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8ff6d11dcad4bf8e-ATL
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 09 Jan 2025 19:26:05 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '545'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999661'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_d132a9ac787eccf9f91aa58643acff50
    http_version: HTTP/1.1
    status_code: 200
version: 1
