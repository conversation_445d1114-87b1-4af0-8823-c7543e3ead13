interactions:
- request:
    body: '{"messages": [{"role": "system", "content": "You are Crew Manager. You
      are a seasoned manager with a knack for getting the best out of your team.\nYou
      are also known for your ability to delegate work to the right people, and to
      ask the right questions to get the best out of your team.\nEven though you don''t
      perform tasks by yourself, you have a lot of experience in the field, which
      allows you to properly evaluate the work of your team members.\nYour personal
      goal is: Manage the team to complete the task in the best way possible.\nYou
      ONLY have access to the following tools, and should NEVER make up tools that
      are not listed here:\n\nTool Name: Delegate work to coworker(task: str, context:
      str, coworker: Optional[str] = None, **kwargs)\nTool Description: Delegate a
      specific task to one of the following coworkers: Scorer\nThe input to this tool
      should be the coworker, the task you want them to do, and ALL necessary context
      to execute the task, they know nothing about the task, so share absolute everything
      you know, don''t reference things but instead explain them.\nTool Arguments:
      {''task'': {''title'': ''Task'', ''type'': ''string''}, ''context'': {''title'':
      ''Context'', ''type'': ''string''}, ''coworker'': {''title'': ''Coworker'',
      ''type'': ''string''}, ''kwargs'': {''title'': ''Kwargs'', ''type'': ''object''}}\nTool
      Name: Ask question to coworker(question: str, context: str, coworker: Optional[str]
      = None, **kwargs)\nTool Description: Ask a specific question to one of the following
      coworkers: Scorer\nThe input to this tool should be the coworker, the question
      you have for them, and ALL necessary context to ask the question properly, they
      know nothing about the question, so share absolute everything you know, don''t
      reference things but instead explain them.\nTool Arguments: {''question'': {''title'':
      ''Question'', ''type'': ''string''}, ''context'': {''title'': ''Context'', ''type'':
      ''string''}, ''coworker'': {''title'': ''Coworker'', ''type'': ''string''},
      ''kwargs'': {''title'': ''Kwargs'', ''type'': ''object''}}\n\nUse the following
      format:\n\nThought: you should always think about what to do\nAction: the action
      to take, only one name of [Delegate work to coworker, Ask question to coworker],
      just the name, exactly as it''s written.\nAction Input: the input to the action,
      just a simple python dictionary, enclosed in curly braces, using \" to wrap
      keys and values.\nObservation: the result of the action\n\nOnce all necessary
      information is gathered:\n\nThought: I now know the final answer\nFinal Answer:
      the final answer to the original input question\n"}, {"role": "user", "content":
      "\nCurrent Task: Give me an integer score between 1-5 for the following title:
      ''The impact of AI in the future of work''\n\nThis is the expect criteria for
      your final answer: The score of the title.\nyou MUST return the actual complete
      content as the final answer, not a summary.\n\nBegin! This is VERY important
      to you, use the tools available and give your best Final Answer, your job depends
      on it!\n\nThought:"}], "model": "gpt-4o"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '2986'
      content-type:
      - application/json
      cookie:
      - __cf_bm=9.8sBYBkvBR8R1K_bVF7xgU..80XKlEIg3N2OBbTSCU-**********-*******-.qiTLXbPamYUMSuyNsOEB9jhGu.jOifujOrx9E2JZvStbIZ9RTIiE44xKKNfLPxQkOi6qAT3h6htK8lPDGV_5g;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-1727213194587-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7g8URGcdob5DuebCrqcqCaXJczj\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727214488,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"To give an accurate score between 1-5
      for the title \\\"The impact of AI in the future of work,\\\" I need to engage
      Scorer to evaluate it based on the criteria they have.\\n\\nAction: Ask question
      to coworker\\nAction Input: {\\\"question\\\": \\\"Can you provide an integer
      score between 1-5 for the title 'The impact of AI in the future of work'?\\\",
      \\\"context\\\": \\\"We need to evaluate the title 'The impact of AI in the
      future of work' according to its quality, relevance, and appeal. Please provide
      a score between 1-5, where 1 is the lowest and 5 is the highest.\\\", \\\"coworker\\\":
      \\\"Scorer\\\"}\\n\\nObservation:\",\n        \"refusal\": null\n      },\n
      \     \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n
      \ \"usage\": {\n    \"prompt_tokens\": 664,\n    \"completion_tokens\": 146,\n
      \   \"total_tokens\": 810,\n    \"completion_tokens_details\": {\n      \"reasoning_tokens\":
      0\n    }\n  },\n  \"system_fingerprint\": \"fp_e375328146\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85fa1909cf1cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:48:10 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '2091'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999269'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 1ms
      x-request-id:
      - req_9aa3183c20ac41471d1d107854e4e502
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: !!binary |
      CvwLCiQKIgoMc2VydmljZS5uYW1lEhIKEGNyZXdBSS10ZWxlbWV0cnkS0wsKEgoQY3Jld2FpLnRl
      bGVtZXRyeRKQAgoQaiW7ExV7PFmNK7y6QbFQCBIImZxnjiAjQxAqDlRhc2sgRXhlY3V0aW9uMAE5
      SAbHe2xM+BdBsAAxE21M+BdKLgoIY3Jld19rZXkSIgogNWU2ZWZmZTY4MGE1ZDk3ZGMzODczYjE0
      ODI1Y2NmYTNKMQoHY3Jld19pZBImCiQ5MGEwOTY1Ny0xNDY3LTQzMmMtYjQwZS02M2QzYTRhNzNl
      ZmJKLgoIdGFza19rZXkSIgogMjdlZjM4Y2M5OWRhNGE4ZGVkNzBlZDQwNmU0NGFiODZKMQoHdGFz
      a19pZBImCiRkOTdlMDUyOS02NTY0LTQ4YmUtYjllZC0xOGJjNjdhMmE2OTJ6AhgBhQEAAQAAEpgH
      ChD2aXxrYF5W85yKqjVQe0rTEgiizow4hqpQaSoMQ3JldyBDcmVhdGVkMAE5kM3nE21M+BdBgAvs
      E21M+BdKGgoOY3Jld2FpX3ZlcnNpb24SCAoGMC42MS4wShoKDnB5dGhvbl92ZXJzaW9uEggKBjMu
      MTEuN0ouCghjcmV3X2tleRIiCiA1ZTZlZmZlNjgwYTVkOTdkYzM4NzNiMTQ4MjVjY2ZhM0oxCgdj
      cmV3X2lkEiYKJDFjZWJhZTk5LWYwNmQtNDEzYS05N2ExLWRlZWU1NjU3ZWFjNkoeCgxjcmV3X3By
      b2Nlc3MSDgoMaGllcmFyY2hpY2FsShEKC2NyZXdfbWVtb3J5EgIQAEoaChRjcmV3X251bWJlcl9v
      Zl90YXNrcxICGAFKGwoVY3Jld19udW1iZXJfb2ZfYWdlbnRzEgIYAUrIAgoLY3Jld19hZ2VudHMS
      uAIKtQJbeyJrZXkiOiAiOTJlN2ViMTkxNjY0YzkzNTc4NWVkN2Q0MjQwYTI5NGQiLCAiaWQiOiAi
      NjU5N2NhNWMtYzIyMS00ZDM4LTg1MTItMjMwNTFjYjMxZThlIiwgInJvbGUiOiAiU2NvcmVyIiwg
      InZlcmJvc2U/IjogZmFsc2UsICJtYXhfaXRlciI6IDE1LCAibWF4X3JwbSI6IG51bGwsICJmdW5j
      dGlvbl9jYWxsaW5nX2xsbSI6ICIiLCAibGxtIjogImdwdC00byIsICJkZWxlZ2F0aW9uX2VuYWJs
      ZWQ/IjogZmFsc2UsICJhbGxvd19jb2RlX2V4ZWN1dGlvbj8iOiBmYWxzZSwgIm1heF9yZXRyeV9s
      aW1pdCI6IDIsICJ0b29sc19uYW1lcyI6IFtdfV1K+wEKCmNyZXdfdGFza3MS7AEK6QFbeyJrZXki
      OiAiMjdlZjM4Y2M5OWRhNGE4ZGVkNzBlZDQwNmU0NGFiODYiLCAiaWQiOiAiNTUyNDkzYjAtYWY0
      Ny00ZWYwLTgzY2MtYjBiZGMzNTFlZjY3IiwgImFzeW5jX2V4ZWN1dGlvbj8iOiBmYWxzZSwgImh1
      bWFuX2lucHV0PyI6IGZhbHNlLCAiYWdlbnRfcm9sZSI6ICJTY29yZXIiLCAiYWdlbnRfa2V5Ijog
      IjkyZTdlYjE5MTY2NGM5MzU3ODVlZDdkNDI0MGEyOTRkIiwgInRvb2xzX25hbWVzIjogW119XXoC
      GAGFAQABAAASjgIKEBqy7yzvVJE6v2HuRtisU48SCIGXqca8/TSDKgxUYXNrIENyZWF0ZWQwATkA
      hAYVbUz4F0EQKAcVbUz4F0ouCghjcmV3X2tleRIiCiA1ZTZlZmZlNjgwYTVkOTdkYzM4NzNiMTQ4
      MjVjY2ZhM0oxCgdjcmV3X2lkEiYKJDFjZWJhZTk5LWYwNmQtNDEzYS05N2ExLWRlZWU1NjU3ZWFj
      NkouCgh0YXNrX2tleRIiCiAyN2VmMzhjYzk5ZGE0YThkZWQ3MGVkNDA2ZTQ0YWI4NkoxCgd0YXNr
      X2lkEiYKJDU1MjQ5M2IwLWFmNDctNGVmMC04M2NjLWIwYmRjMzUxZWY2N3oCGAGFAQABAAA=
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
      Content-Length:
      - '1535'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.27.0
    method: POST
    uri: https://telemetry.crewai.com:4319/v1/traces
  response:
    body:
      string: "\n\0"
    headers:
      Content-Length:
      - '2'
      Content-Type:
      - application/x-protobuf
      Date:
      - Tue, 24 Sep 2024 21:48:12 GMT
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "system", "content": "You are Scorer. You''re an
      expert scorer, specialized in scoring titles.\nYour personal goal is: Score
      the title\nTo give my best complete final answer to the task use the exact following
      format:\n\nThought: I now can give a great answer\nFinal Answer: Your final
      answer must be the great and the most complete as possible, it must be outcome
      described.\n\nI MUST use these formats, my job depends on it!"}, {"role": "user",
      "content": "\nCurrent Task: Can you provide an integer score between 1-5 for
      the title ''The impact of AI in the future of work''?\n\nThis is the expect
      criteria for your final answer: Your best answer to your coworker asking you
      this, accounting for the context shared.\nyou MUST return the actual complete
      content as the final answer, not a summary.\n\nThis is the context you''re working
      with:\nWe need to evaluate the title ''The impact of AI in the future of work''
      according to its quality, relevance, and appeal. Please provide a score between
      1-5, where 1 is the lowest and 5 is the highest.\n\nBegin! This is VERY important
      to you, use the tools available and give your best Final Answer, your job depends
      on it!\n\nThought:"}], "model": "gpt-4o"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1220'
      content-type:
      - application/json
      cookie:
      - __cf_bm=9.8sBYBkvBR8R1K_bVF7xgU..80XKlEIg3N2OBbTSCU-**********-*******-.qiTLXbPamYUMSuyNsOEB9jhGu.jOifujOrx9E2JZvStbIZ9RTIiE44xKKNfLPxQkOi6qAT3h6htK8lPDGV_5g;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-1727213194587-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7gBKY8RwpfJc5mSVrH1WmeT5L5z\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727214491,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I now can give a great answer\\nFinal
      Answer: I would score the title 'The impact of AI in the future of work' a 4
      out of 5. The title is clear, relevant, and appealing as it addresses a highly
      topical subject with significant interest in both the academic and professional
      communities. It implies a forward-looking analysis which can attract various
      stakeholders interested in the future trends of employment, technology, and
      society. The only improvement could be to make it slightly more specific or
      engaging to ensure maximum impact.\",\n        \"refusal\": null\n      },\n
      \     \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n
      \ \"usage\": {\n    \"prompt_tokens\": 255,\n    \"completion_tokens\": 106,\n
      \   \"total_tokens\": 361,\n    \"completion_tokens_details\": {\n      \"reasoning_tokens\":
      0\n    }\n  },\n  \"system_fingerprint\": \"fp_e375328146\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85fa2878d21cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:48:12 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '1460'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999705'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_5d6460275a26c2b5a187d8e8cec85215
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are Crew Manager. You
      are a seasoned manager with a knack for getting the best out of your team.\nYou
      are also known for your ability to delegate work to the right people, and to
      ask the right questions to get the best out of your team.\nEven though you don''t
      perform tasks by yourself, you have a lot of experience in the field, which
      allows you to properly evaluate the work of your team members.\nYour personal
      goal is: Manage the team to complete the task in the best way possible.\nYou
      ONLY have access to the following tools, and should NEVER make up tools that
      are not listed here:\n\nTool Name: Delegate work to coworker(task: str, context:
      str, coworker: Optional[str] = None, **kwargs)\nTool Description: Delegate a
      specific task to one of the following coworkers: Scorer\nThe input to this tool
      should be the coworker, the task you want them to do, and ALL necessary context
      to execute the task, they know nothing about the task, so share absolute everything
      you know, don''t reference things but instead explain them.\nTool Arguments:
      {''task'': {''title'': ''Task'', ''type'': ''string''}, ''context'': {''title'':
      ''Context'', ''type'': ''string''}, ''coworker'': {''title'': ''Coworker'',
      ''type'': ''string''}, ''kwargs'': {''title'': ''Kwargs'', ''type'': ''object''}}\nTool
      Name: Ask question to coworker(question: str, context: str, coworker: Optional[str]
      = None, **kwargs)\nTool Description: Ask a specific question to one of the following
      coworkers: Scorer\nThe input to this tool should be the coworker, the question
      you have for them, and ALL necessary context to ask the question properly, they
      know nothing about the question, so share absolute everything you know, don''t
      reference things but instead explain them.\nTool Arguments: {''question'': {''title'':
      ''Question'', ''type'': ''string''}, ''context'': {''title'': ''Context'', ''type'':
      ''string''}, ''coworker'': {''title'': ''Coworker'', ''type'': ''string''},
      ''kwargs'': {''title'': ''Kwargs'', ''type'': ''object''}}\n\nUse the following
      format:\n\nThought: you should always think about what to do\nAction: the action
      to take, only one name of [Delegate work to coworker, Ask question to coworker],
      just the name, exactly as it''s written.\nAction Input: the input to the action,
      just a simple python dictionary, enclosed in curly braces, using \" to wrap
      keys and values.\nObservation: the result of the action\n\nOnce all necessary
      information is gathered:\n\nThought: I now know the final answer\nFinal Answer:
      the final answer to the original input question\n"}, {"role": "user", "content":
      "\nCurrent Task: Give me an integer score between 1-5 for the following title:
      ''The impact of AI in the future of work''\n\nThis is the expect criteria for
      your final answer: The score of the title.\nyou MUST return the actual complete
      content as the final answer, not a summary.\n\nBegin! This is VERY important
      to you, use the tools available and give your best Final Answer, your job depends
      on it!\n\nThought:"}, {"role": "assistant", "content": "To give an accurate
      score between 1-5 for the title \"The impact of AI in the future of work,\"
      I need to engage Scorer to evaluate it based on the criteria they have.\n\nAction:
      Ask question to coworker\nAction Input: {\"question\": \"Can you provide an
      integer score between 1-5 for the title ''The impact of AI in the future of
      work''?\", \"context\": \"We need to evaluate the title ''The impact of AI in
      the future of work'' according to its quality, relevance, and appeal. Please
      provide a score between 1-5, where 1 is the lowest and 5 is the highest.\",
      \"coworker\": \"Scorer\"}\n\nObservation:\nObservation: I would score the title
      ''The impact of AI in the future of work'' a 4 out of 5. The title is clear,
      relevant, and appealing as it addresses a highly topical subject with significant
      interest in both the academic and professional communities. It implies a forward-looking
      analysis which can attract various stakeholders interested in the future trends
      of employment, technology, and society. The only improvement could be to make
      it slightly more specific or engaging to ensure maximum impact."}], "model":
      "gpt-4o"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '4129'
      content-type:
      - application/json
      cookie:
      - __cf_bm=9.8sBYBkvBR8R1K_bVF7xgU..80XKlEIg3N2OBbTSCU-**********-*******-.qiTLXbPamYUMSuyNsOEB9jhGu.jOifujOrx9E2JZvStbIZ9RTIiE44xKKNfLPxQkOi6qAT3h6htK8lPDGV_5g;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-1727213194587-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7gClsVUQLww5J3bHB40NunZxQTa\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727214492,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I now know the final answer.\\n\\nFinal
      Answer: 4\",\n        \"refusal\": null\n      },\n      \"logprobs\": null,\n
      \     \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      909,\n    \"completion_tokens\": 14,\n    \"total_tokens\": 923,\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0\n    }\n  },\n  \"system_fingerprint\": \"fp_e375328146\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85fa3348301cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:48:13 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '298'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29998996'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 2ms
      x-request-id:
      - req_78324a4c53dc4f87327b0e1060b0b445
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "user", "content": "4"}, {"role": "system", "content":
      "I''m gonna convert this raw text into valid JSON.\n\nThe json should have the
      following structure, with the following keys:\n{\n    score: int\n}"}], "model":
      "gpt-4o", "tool_choice": {"type": "function", "function": {"name": "ScoreOutput"}},
      "tools": [{"type": "function", "function": {"name": "ScoreOutput", "description":
      "Correctly extracted `ScoreOutput` with all the required parameters with correct
      types", "parameters": {"properties": {"score": {"title": "Score", "type": "integer"}},
      "required": ["score"], "type": "object"}}}]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '615'
      content-type:
      - application/json
      cookie:
      - __cf_bm=9.8sBYBkvBR8R1K_bVF7xgU..80XKlEIg3N2OBbTSCU-**********-*******-.qiTLXbPamYUMSuyNsOEB9jhGu.jOifujOrx9E2JZvStbIZ9RTIiE44xKKNfLPxQkOi6qAT3h6htK8lPDGV_5g;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-1727213194587-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7gDkv4V1cMJ6IVFldUfx5aJ54mS\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727214493,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": null,\n        \"tool_calls\": [\n          {\n
      \           \"id\": \"call_E79eAVdZeKx6iitt2tQLfiT6\",\n            \"type\":
      \"function\",\n            \"function\": {\n              \"name\": \"ScoreOutput\",\n
      \             \"arguments\": \"{\\\"score\\\":4}\"\n            }\n          }\n
      \       ],\n        \"refusal\": null\n      },\n      \"logprobs\": null,\n
      \     \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      100,\n    \"completion_tokens\": 5,\n    \"total_tokens\": 105,\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0\n    }\n  },\n  \"system_fingerprint\": \"fp_3537616b13\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85fa377e391cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:48:13 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '220'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999947'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_d13285e05176425b72094c7e74370ab6
    http_version: HTTP/1.1
    status_code: 200
version: 1
