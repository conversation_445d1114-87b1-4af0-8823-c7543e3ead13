---
title: GitHub Integration
description: "Repository and issue management with GitHub integration for CrewAI."
icon: "github"
---

## Overview

Enable your agents to manage repositories, issues, and releases through GitHub. Create and update issues, manage releases, track project development, and streamline your software development workflow with AI-powered automation.

## Prerequisites

Before using the GitHub integration, ensure you have:

- A [CrewAI Enterprise](https://app.crewai.com) account with an active subscription
- A GitHub account with appropriate repository permissions
- Connected your GitHub account through the [Integrations page](https://app.crewai.com/crewai_plus/connectors)

## Setting Up GitHub Integration

### 1. Connect Your GitHub Account

1. Navigate to [CrewAI Enterprise Integrations](https://app.crewai.com/crewai_plus/connectors)
2. Find **GitHub** in the Authentication Integrations section
3. Click **Connect** and complete the OAuth flow
4. Grant the necessary permissions for repository and issue management
5. Copy your Enterprise Token from [Account Settings](https://app.crewai.com/crewai_plus/settings/account)

### 2. Install Required Package

```bash
uv add crewai-tools
```

## Available Actions

<AccordionGroup>
  <Accordion title="GITHUB_CREATE_ISSUE">
    **Description:** Create an issue in GitHub.

    **Parameters:**
    - `owner` (string, required): Owner - Specify the name of the account owner of the associated repository for this Issue. (example: "abc").
    - `repo` (string, required): Repository - Specify the name of the associated repository for this Issue.
    - `title` (string, required): Issue Title - Specify the title of the issue to create.
    - `body` (string, optional): Issue Body - Specify the body contents of the issue to create.
    - `assignees` (string, optional): Assignees - Specify the assignee(s)' GitHub login as an array of strings for this issue. (example: `["octocat"]`).
  </Accordion>

  <Accordion title="GITHUB_UPDATE_ISSUE">
    **Description:** Update an issue in GitHub.

    **Parameters:**
    - `owner` (string, required): Owner - Specify the name of the account owner of the associated repository for this Issue. (example: "abc").
    - `repo` (string, required): Repository - Specify the name of the associated repository for this Issue.
    - `issue_number` (string, required): Issue Number - Specify the number of the issue to update.
    - `title` (string, required): Issue Title - Specify the title of the issue to update.
    - `body` (string, optional): Issue Body - Specify the body contents of the issue to update.
    - `assignees` (string, optional): Assignees - Specify the assignee(s)' GitHub login as an array of strings for this issue. (example: `["octocat"]`).
    - `state` (string, optional): State - Specify the updated state of the issue.
      - Options: `open`, `closed`
  </Accordion>

  <Accordion title="GITHUB_GET_ISSUE_BY_NUMBER">
    **Description:** Get an issue by number in GitHub.

    **Parameters:**
    - `owner` (string, required): Owner - Specify the name of the account owner of the associated repository for this Issue. (example: "abc").
    - `repo` (string, required): Repository - Specify the name of the associated repository for this Issue.
    - `issue_number` (string, required): Issue Number - Specify the number of the issue to fetch.
  </Accordion>

  <Accordion title="GITHUB_LOCK_ISSUE">
    **Description:** Lock an issue in GitHub.

    **Parameters:**
    - `owner` (string, required): Owner - Specify the name of the account owner of the associated repository for this Issue. (example: "abc").
    - `repo` (string, required): Repository - Specify the name of the associated repository for this Issue.
    - `issue_number` (string, required): Issue Number - Specify the number of the issue to lock.
    - `lock_reason` (string, required): Lock Reason - Specify a reason for locking the issue or pull request conversation.
      - Options: `off-topic`, `too heated`, `resolved`, `spam`
  </Accordion>

  <Accordion title="GITHUB_SEARCH_ISSUE">
    **Description:** Search for issues in GitHub.

    **Parameters:**
    - `owner` (string, required): Owner - Specify the name of the account owner of the associated repository for this Issue. (example: "abc").
    - `repo` (string, required): Repository - Specify the name of the associated repository for this Issue.
    - `filter` (object, required): A filter in disjunctive normal form - OR of AND groups of single conditions.
      ```json
      {
        "operator": "OR",
        "conditions": [
          {
            "operator": "AND",
            "conditions": [
              {
                "field": "assignee",
                "operator": "$stringExactlyMatches",
                "value": "octocat"
              }
            ]
          }
        ]
      }
      ```
      Available fields: `assignee`, `creator`, `mentioned`, `labels`
  </Accordion>

  <Accordion title="GITHUB_CREATE_RELEASE">
    **Description:** Create a release in GitHub.

    **Parameters:**
    - `owner` (string, required): Owner - Specify the name of the account owner of the associated repository for this Release. (example: "abc").
    - `repo` (string, required): Repository - Specify the name of the associated repository for this Release.
    - `tag_name` (string, required): Name - Specify the name of the release tag to be created. (example: "v1.0.0").
    - `target_commitish` (string, optional): Target - Specify the target of the release. This can either be a branch name or a commit SHA. Defaults to the main branch. (example: "master").
    - `body` (string, optional): Body - Specify a description for this release.
    - `draft` (string, optional): Draft - Specify whether the created release should be a draft (unpublished) release.
      - Options: `true`, `false`
    - `prerelease` (string, optional): Prerelease - Specify whether the created release should be a prerelease.
      - Options: `true`, `false`
    - `discussion_category_name` (string, optional): Discussion Category Name - If specified, a discussion of the specified category is created and linked to the release. The value must be a category that already exists in the repository.
    - `generate_release_notes` (string, optional): Release Notes - Specify whether the created release should automatically create release notes using the provided name and body specified.
      - Options: `true`, `false`
  </Accordion>

  <Accordion title="GITHUB_UPDATE_RELEASE">
    **Description:** Update a release in GitHub.

    **Parameters:**
    - `owner` (string, required): Owner - Specify the name of the account owner of the associated repository for this Release. (example: "abc").
    - `repo` (string, required): Repository - Specify the name of the associated repository for this Release.
    - `id` (string, required): Release ID - Specify the ID of the release to update.
    - `tag_name` (string, optional): Name - Specify the name of the release tag to be updated. (example: "v1.0.0").
    - `target_commitish` (string, optional): Target - Specify the target of the release. This can either be a branch name or a commit SHA. Defaults to the main branch. (example: "master").
    - `body` (string, optional): Body - Specify a description for this release.
    - `draft` (string, optional): Draft - Specify whether the created release should be a draft (unpublished) release.
      - Options: `true`, `false`
    - `prerelease` (string, optional): Prerelease - Specify whether the created release should be a prerelease.
      - Options: `true`, `false`
    - `discussion_category_name` (string, optional): Discussion Category Name - If specified, a discussion of the specified category is created and linked to the release. The value must be a category that already exists in the repository.
    - `generate_release_notes` (string, optional): Release Notes - Specify whether the created release should automatically create release notes using the provided name and body specified.
      - Options: `true`, `false`
  </Accordion>

  <Accordion title="GITHUB_GET_RELEASE_BY_ID">
    **Description:** Get a release by ID in GitHub.

    **Parameters:**
    - `owner` (string, required): Owner - Specify the name of the account owner of the associated repository for this Release. (example: "abc").
    - `repo` (string, required): Repository - Specify the name of the associated repository for this Release.
    - `id` (string, required): Release ID - Specify the release ID of the release to fetch.
  </Accordion>

  <Accordion title="GITHUB_GET_RELEASE_BY_TAG_NAME">
    **Description:** Get a release by tag name in GitHub.

    **Parameters:**
    - `owner` (string, required): Owner - Specify the name of the account owner of the associated repository for this Release. (example: "abc").
    - `repo` (string, required): Repository - Specify the name of the associated repository for this Release.
    - `tag_name` (string, required): Name - Specify the tag of the release to fetch. (example: "v1.0.0").
  </Accordion>

  <Accordion title="GITHUB_DELETE_RELEASE">
    **Description:** Delete a release in GitHub.

    **Parameters:**
    - `owner` (string, required): Owner - Specify the name of the account owner of the associated repository for this Release. (example: "abc").
    - `repo` (string, required): Repository - Specify the name of the associated repository for this Release.
    - `id` (string, required): Release ID - Specify the ID of the release to delete.
  </Accordion>
</AccordionGroup>

## Usage Examples

### Basic GitHub Agent Setup

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

# Get enterprise tools (GitHub tools will be included)
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

# Create an agent with GitHub capabilities
github_agent = Agent(
    role="Repository Manager",
    goal="Manage GitHub repositories, issues, and releases efficiently",
    backstory="An AI assistant specialized in repository management and issue tracking.",
    tools=[enterprise_tools]
)

# Task to create a new issue
create_issue_task = Task(
    description="Create a bug report issue for the login functionality in the main repository",
    agent=github_agent,
    expected_output="Issue created successfully with issue number"
)

# Run the task
crew = Crew(
    agents=[github_agent],
    tasks=[create_issue_task]
)

crew.kickoff()
```

### Filtering Specific GitHub Tools

```python
from crewai_tools import CrewaiEnterpriseTools

# Get only specific GitHub tools
enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token",
    actions_list=["github_create_issue", "github_update_issue", "github_search_issue"]
)

issue_manager = Agent(
    role="Issue Manager",
    goal="Create and manage GitHub issues efficiently",
    backstory="An AI assistant that focuses on issue tracking and management.",
    tools=enterprise_tools
)

# Task to manage issue workflow
issue_workflow = Task(
    description="Create a feature request issue and assign it to the development team",
    agent=issue_manager,
    expected_output="Feature request issue created and assigned successfully"
)

crew = Crew(
    agents=[issue_manager],
    tasks=[issue_workflow]
)

crew.kickoff()
```

### Release Management

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

release_manager = Agent(
    role="Release Manager",
    goal="Manage software releases and versioning",
    backstory="An experienced release manager who handles version control and release processes.",
    tools=[enterprise_tools]
)

# Task to create a new release
release_task = Task(
    description="""
    Create a new release v2.1.0 for the project with:
    - Auto-generated release notes
    - Target the main branch
    - Include a description of new features and bug fixes
    """,
    agent=release_manager,
    expected_output="Release v2.1.0 created successfully with release notes"
)

crew = Crew(
    agents=[release_manager],
    tasks=[release_task]
)

crew.kickoff()
```

### Issue Tracking and Management

```python
from crewai import Agent, Task, Crew
from crewai_tools import CrewaiEnterpriseTools

enterprise_tools = CrewaiEnterpriseTools(
    enterprise_token="your_enterprise_token"
)

project_coordinator = Agent(
    role="Project Coordinator",
    goal="Track and coordinate project issues and development progress",
    backstory="An AI assistant that helps coordinate development work and track project progress.",
    tools=[enterprise_tools]
)

# Complex task involving multiple GitHub operations
coordination_task = Task(
    description="""
    1. Search for all open issues assigned to the current milestone
    2. Identify overdue issues and update their priority labels
    3. Create a weekly progress report issue
    4. Lock resolved issues that have been inactive for 30 days
    """,
    agent=project_coordinator,
    expected_output="Project coordination completed with progress report and issue management"
)

crew = Crew(
    agents=[project_coordinator],
    tasks=[coordination_task]
)

crew.kickoff()
```

### Getting Help

<Card title="Need Help?" icon="headset" href="mailto:<EMAIL>">
  Contact our support team for assistance with GitHub integration setup or troubleshooting.
</Card>
