---
title: S3 Writer Tool
description: The `S3WriterTool` enables CrewAI agents to write content to files in Amazon S3 buckets.
icon: aws
---

# `S3WriterTool`

## Description

The `S3WriterTool` is designed to write content to files in Amazon S3 buckets. This tool allows CrewAI agents to create or update files in S3, making it ideal for workflows that require storing data, saving configuration files, or persisting any other content to AWS S3 storage.

## Installation

To use this tool, you need to install the required dependencies:

```shell
uv add boto3
```

## Steps to Get Started

To effectively use the `S3WriterTool`, follow these steps:

1. **Install Dependencies**: Install the required packages using the command above.
2. **Configure AWS Credentials**: Set up your AWS credentials as environment variables.
3. **Initialize the Tool**: Create an instance of the tool.
4. **Specify S3 Path and Content**: Provide the S3 path where you want to write the file and the content to be written.

## Example

The following example demonstrates how to use the `S3WriterTool` to write content to a file in an S3 bucket:

```python Code
from crewai import Agent, Task, Crew
from crewai_tools.aws.s3 import S3WriterTool

# Initialize the tool
s3_writer_tool = S3WriterTool()

# Define an agent that uses the tool
file_writer_agent = Agent(
    role="File Writer",
    goal="Write content to files in S3 buckets",
    backstory="An expert in storing and managing files in cloud storage.",
    tools=[s3_writer_tool],
    verbose=True,
)

# Example task to write a report
write_task = Task(
    description="Generate a summary report of the quarterly sales data and save it to {my_bucket}.",
    expected_output="Confirmation that the report was successfully saved to S3.",
    agent=file_writer_agent,
)

# Create and run the crew
crew = Crew(agents=[file_writer_agent], tasks=[write_task])
result = crew.kickoff(inputs={"my_bucket": "s3://my-bucket/reports/quarterly-summary.txt"})
```

## Parameters

The `S3WriterTool` accepts the following parameters when used by an agent:

- **file_path**: Required. The S3 file path in the format `s3://bucket-name/file-name`.
- **content**: Required. The content to write to the file.

## AWS Credentials

The tool requires AWS credentials to access S3 buckets. You can configure these credentials using environment variables:

- **CREW_AWS_REGION**: The AWS region where your S3 bucket is located. Default is `us-east-1`.
- **CREW_AWS_ACCESS_KEY_ID**: Your AWS access key ID.
- **CREW_AWS_SEC_ACCESS_KEY**: Your AWS secret access key.

## Usage

When using the `S3WriterTool` with an agent, the agent will need to provide both the S3 file path and the content to write:

```python Code
# Example of using the tool with an agent
file_writer_agent = Agent(
    role="File Writer",
    goal="Write content to files in S3 buckets",
    backstory="An expert in storing and managing files in cloud storage.",
    tools=[s3_writer_tool],
    verbose=True,
)

# Create a task for the agent to write a specific file
write_config_task = Task(
    description="""
    Create a configuration file with the following database settings:
    - host: db.example.com
    - port: 5432
    - username: app_user
    - password: secure_password
    
    Save this configuration as JSON to {my_bucket}.
    """,
    expected_output="Confirmation that the configuration file was successfully saved to S3.",
    agent=file_writer_agent,
)

# Run the task
crew = Crew(agents=[file_writer_agent], tasks=[write_config_task])
result = crew.kickoff(inputs={"my_bucket": "s3://my-bucket/config/db-config.json"})
```

## Error Handling

The `S3WriterTool` includes error handling for common S3 issues:

- Invalid S3 path format
- Permission issues (e.g., no write access to the bucket)
- AWS credential problems
- Bucket does not exist

When an error occurs, the tool will return an error message that includes details about the issue.

## Implementation Details

The `S3WriterTool` uses the AWS SDK for Python (boto3) to interact with S3:

```python Code
class S3WriterTool(BaseTool):
    name: str = "S3 Writer Tool"
    description: str = "Writes content to a file in Amazon S3 given an S3 file path"
    
    def _run(self, file_path: str, content: str) -> str:
        try:
            bucket_name, object_key = self._parse_s3_path(file_path)

            s3 = boto3.client(
                's3',
                region_name=os.getenv('CREW_AWS_REGION', 'us-east-1'),
                aws_access_key_id=os.getenv('CREW_AWS_ACCESS_KEY_ID'),
                aws_secret_access_key=os.getenv('CREW_AWS_SEC_ACCESS_KEY')
            )

            s3.put_object(Bucket=bucket_name, Key=object_key, Body=content.encode('utf-8'))
            return f"Successfully wrote content to {file_path}"
        except ClientError as e:
            return f"Error writing file to S3: {str(e)}"
```

## Conclusion

The `S3WriterTool` provides a straightforward way to write content to files in Amazon S3 buckets. By enabling agents to create and update files in S3, it facilitates workflows that require cloud-based file storage. This tool is particularly useful for data persistence, configuration management, report generation, and any task that involves storing information in AWS S3 storage. 