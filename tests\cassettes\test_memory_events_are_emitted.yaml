interactions:
- request:
    body: null
    headers: {}
    method: GET
    uri: https://pypi.org/pypi/agentops/json
  response:
    body:
      string: '{"info":{"author":null,"author_email":"<PERSON> <<EMAIL>>,
        <PERSON> <siyang<PERSON><EMAIL>>, <PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <<EMAIL>>,
        <PERSON><PERSON> <<EMAIL>>","bugtrack_url":null,"classifiers":["License
        :: OSI Approved :: MIT License","Operating System :: OS Independent","Programming
        Language :: Python :: 3","Programming Language :: Python :: 3.10","Programming
        Language :: Python :: 3.11","Programming Language :: Python :: 3.12","Programming
        Language :: Python :: 3.13","Programming Language :: Python :: 3.9"],"description":"","description_content_type":null,"docs_url":null,"download_url":null,"downloads":{"last_day":-1,"last_month":-1,"last_week":-1},"dynamic":null,"home_page":null,"keywords":null,"license":null,"license_expression":null,"license_files":["LICENSE"],"maintainer":null,"maintainer_email":null,"name":"agentops","package_url":"https://pypi.org/project/agentops/","platform":null,"project_url":"https://pypi.org/project/agentops/","project_urls":{"Homepage":"https://github.com/AgentOps-AI/agentops","Issues":"https://github.com/AgentOps-AI/agentops/issues"},"provides_extra":null,"release_url":"https://pypi.org/project/agentops/0.4.16/","requires_dist":["httpx<0.29.0,>=0.24.0","opentelemetry-api==1.29.0;
        python_version < \"3.10\"","opentelemetry-api>1.29.0; python_version >= \"3.10\"","opentelemetry-exporter-otlp-proto-http==1.29.0;
        python_version < \"3.10\"","opentelemetry-exporter-otlp-proto-http>1.29.0;
        python_version >= \"3.10\"","opentelemetry-instrumentation==0.50b0; python_version
        < \"3.10\"","opentelemetry-instrumentation>=0.50b0; python_version >= \"3.10\"","opentelemetry-sdk==1.29.0;
        python_version < \"3.10\"","opentelemetry-sdk>1.29.0; python_version >= \"3.10\"","opentelemetry-semantic-conventions==0.50b0;
        python_version < \"3.10\"","opentelemetry-semantic-conventions>=0.50b0; python_version
        >= \"3.10\"","ordered-set<5.0.0,>=4.0.0","packaging<25.0,>=21.0","psutil<7.0.1,>=5.9.8","pyyaml<7.0,>=5.3","requests<3.0.0,>=2.0.0","termcolor<2.5.0,>=2.3.0","wrapt<2.0.0,>=1.0.0"],"requires_python":">=3.9","summary":"Observability
        and DevTool Platform for AI Agents","version":"0.4.16","yanked":false,"yanked_reason":null},"last_serial":29695949,"releases":{"0.0.1":[{"comment_text":"","digests":{"blake2b_256":"9b4641d084346e88671acc02e3a0049d3e0925fe99edd88c8b82700dc3c04d01","md5":"2b491f3b3dd01edd4ee37c361087bb46","sha256":"f2cb9d59a0413e7977a44a23dbd6a9d89cda5309b63ed08f5c346c7488acf645"},"downloads":-1,"filename":"agentops-0.0.1-py3-none-any.whl","has_sig":false,"md5_digest":"2b491f3b3dd01edd4ee37c361087bb46","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":10328,"upload_time":"2023-08-21T18:33:47","upload_time_iso_8601":"2023-08-21T18:33:47.827866Z","url":"https://files.pythonhosted.org/packages/9b/46/41d084346e88671acc02e3a0049d3e0925fe99edd88c8b82700dc3c04d01/agentops-0.0.1-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"b280bf609d98778499bd42df723100a8e910d9b9827cbd00b804cf0b13bb3c87","md5":"ff218fc16d45cf72f73d50ee9a0afe82","sha256":"5c3d4311b9dde0c71cb475ec99d2963a71604c78d468b333f55e81364f4fe79e"},"downloads":-1,"filename":"agentops-0.0.1.tar.gz","has_sig":false,"md5_digest":"ff218fc16d45cf72f73d50ee9a0afe82","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":11452,"upload_time":"2023-08-21T18:33:49","upload_time_iso_8601":"2023-08-21T18:33:49.613830Z","url":"https://files.pythonhosted.org/packages/b2/80/bf609d98778499bd42df723100a8e910d9b9827cbd00b804cf0b13bb3c87/agentops-0.0.1.tar.gz","yanked":false,"yanked_reason":null}],"0.0.10":[{"comment_text":"","digests":{"blake2b_256":"92933862af53105332cb524db237138d3284b5d6abcc7df5fd4406e382372d94","md5":"8bdea319b5579775eb88efac72e70cd6","sha256":"e8a333567458c1df35538d626bc596f3ba7b8fa2aac5015bc378f3f7f8850669"},"downloads":-1,"filename":"agentops-0.0.10-py3-none-any.whl","has_sig":false,"md5_digest":"8bdea319b5579775eb88efac72e70cd6","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":14752,"upload_time":"2023-12-16T01:40:40","upload_time_iso_8601":"2023-12-16T01:40:40.867657Z","url":"https://files.pythonhosted.org/packages/92/93/3862af53105332cb524db237138d3284b5d6abcc7df5fd4406e382372d94/agentops-0.0.10-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"c63136b1f2e508b67f92ddb5f51f2acf5abdf2bf4b32d5b355d8018b368dc854","md5":"87bdcd4d7469d22ce922234d4f0b2b98","sha256":"5fbc567bece7b218fc35ce70d208e88e89bb399a9dbf84ab7ad59a2aa559648c"},"downloads":-1,"filename":"agentops-0.0.10.tar.gz","has_sig":false,"md5_digest":"87bdcd4d7469d22ce922234d4f0b2b98","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":15099,"upload_time":"2023-12-16T01:40:42","upload_time_iso_8601":"2023-12-16T01:40:42.281826Z","url":"https://files.pythonhosted.org/packages/c6/31/36b1f2e508b67f92ddb5f51f2acf5abdf2bf4b32d5b355d8018b368dc854/agentops-0.0.10.tar.gz","yanked":false,"yanked_reason":null}],"0.0.11":[{"comment_text":"","digests":{"blake2b_256":"7125ed114f918332cda824092f620b1002fd76ab6b538dd83711b31c93907139","md5":"83ba7e621f01412144aa38306fc1e04c","sha256":"cb80823e065d17dc26bdc8fe951ea7e04b23677ef2b4da939669c6fe1b2502bf"},"downloads":-1,"filename":"agentops-0.0.11-py3-none-any.whl","has_sig":false,"md5_digest":"83ba7e621f01412144aa38306fc1e04c","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":16627,"upload_time":"2023-12-21T19:50:28","upload_time_iso_8601":"2023-12-21T19:50:28.595886Z","url":"https://files.pythonhosted.org/packages/71/25/ed114f918332cda824092f620b1002fd76ab6b538dd83711b31c93907139/agentops-0.0.11-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"9e037750b04398cda2548bbf3d84ce554c4009592095c060c4904e773f3a43da","md5":"5bbb120cc9a5f5ff6fb5dd45691ba279","sha256":"cbf0f39768d47e32be448a3ff3ded665fce64ff8a90c0e10692fd7a3ab4790ee"},"downloads":-1,"filename":"agentops-0.0.11.tar.gz","has_sig":false,"md5_digest":"5bbb120cc9a5f5ff6fb5dd45691ba279","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":16794,"upload_time":"2023-12-21T19:50:29","upload_time_iso_8601":"2023-12-21T19:50:29.881561Z","url":"https://files.pythonhosted.org/packages/9e/03/7750b04398cda2548bbf3d84ce554c4009592095c060c4904e773f3a43da/agentops-0.0.11.tar.gz","yanked":false,"yanked_reason":null}],"0.0.12":[{"comment_text":"","digests":{"blake2b_256":"adf5cc3e93b2328532ea80b8b36450b8b48a8199ebbe1f75ebb490e57a926b88","md5":"694ba49ca8841532039bdf8dc0250b85","sha256":"9a2c773efbe3353f60d1b86da12333951dad288ba54839615a53b57e5965bea8"},"downloads":-1,"filename":"agentops-0.0.12-py3-none-any.whl","has_sig":false,"md5_digest":"694ba49ca8841532039bdf8dc0250b85","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":18602,"upload_time":"2024-01-03T03:47:07","upload_time_iso_8601":"2024-01-03T03:47:07.184203Z","url":"https://files.pythonhosted.org/packages/ad/f5/cc3e93b2328532ea80b8b36450b8b48a8199ebbe1f75ebb490e57a926b88/agentops-0.0.12-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"7eb0633ecd30c74a0613c7330ececf0303286622ce429f08ce0daa9ee8cc4ecf","md5":"025daef9622472882a1fa58b6c1fddb5","sha256":"fbb4c38711a7dff3ab08004591451b5a5c33bea5e496fa71fac668c7284513d2"},"downloads":-1,"filename":"agentops-0.0.12.tar.gz","has_sig":false,"md5_digest":"025daef9622472882a1fa58b6c1fddb5","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":19826,"upload_time":"2024-01-03T03:47:08","upload_time_iso_8601":"2024-01-03T03:47:08.942790Z","url":"https://files.pythonhosted.org/packages/7e/b0/633ecd30c74a0613c7330ececf0303286622ce429f08ce0daa9ee8cc4ecf/agentops-0.0.12.tar.gz","yanked":false,"yanked_reason":null}],"0.0.13":[{"comment_text":"","digests":{"blake2b_256":"3a0f9c1500adb4191531374db4d7920c51aba92c5472d13d172108e881c36948","md5":"f0a3b78c15af3ab467778f94fb50bf4a","sha256":"3379a231f37a375bda421114a5626643263e84ce951503d0bdff8411149946e0"},"downloads":-1,"filename":"agentops-0.0.13-py3-none-any.whl","has_sig":false,"md5_digest":"f0a3b78c15af3ab467778f94fb50bf4a","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":18709,"upload_time":"2024-01-07T08:57:57","upload_time_iso_8601":"2024-01-07T08:57:57.456769Z","url":"https://files.pythonhosted.org/packages/3a/0f/9c1500adb4191531374db4d7920c51aba92c5472d13d172108e881c36948/agentops-0.0.13-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"cbf9a3824bd30d7107aaca8d409165c0a3574a879efd7ca0fea755e903623b61","md5":"0ebceb6aad82c0622adcd4c2633fc677","sha256":"5e6adf68c2a533496648ea3fabb6e791f39ce810d18dbc1354d118b195fd8556"},"downloads":-1,"filename":"agentops-0.0.13.tar.gz","has_sig":false,"md5_digest":"0ebceb6aad82c0622adcd4c2633fc677","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":19933,"upload_time":"2024-01-07T08:57:59","upload_time_iso_8601":"2024-01-07T08:57:59.146933Z","url":"https://files.pythonhosted.org/packages/cb/f9/a3824bd30d7107aaca8d409165c0a3574a879efd7ca0fea755e903623b61/agentops-0.0.13.tar.gz","yanked":false,"yanked_reason":null}],"0.0.14":[{"comment_text":"","digests":{"blake2b_256":"252b1d8ee3b4ab02215eb1a52865a9f2c209d6d4cbf4a3444fb7faf23b02ca66","md5":"a8ba77b0ec0d25072b2e0535a135cc40","sha256":"d5bb4661642daf8fc63a257ef0f04ccc5c79a73e73d57ea04190e74d9a3e6df9"},"downloads":-1,"filename":"agentops-0.0.14-py3-none-any.whl","has_sig":false,"md5_digest":"a8ba77b0ec0d25072b2e0535a135cc40","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":18710,"upload_time":"2024-01-08T21:52:28","upload_time_iso_8601":"2024-01-08T21:52:28.340899Z","url":"https://files.pythonhosted.org/packages/25/2b/1d8ee3b4ab02215eb1a52865a9f2c209d6d4cbf4a3444fb7faf23b02ca66/agentops-0.0.14-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"bf3a1fdf85563c47c2fc6571a1406aecb772f644d53a2adabf4981012971587a","md5":"1ecf7177ab57738c6663384de20887e5","sha256":"c54cee1c9ed1b5b7829fd80d5d01278b1efb50e977e5a890627f4688d0f2afb2"},"downloads":-1,"filename":"agentops-0.0.14.tar.gz","has_sig":false,"md5_digest":"1ecf7177ab57738c6663384de20887e5","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":19932,"upload_time":"2024-01-08T21:52:29","upload_time_iso_8601":"2024-01-08T21:52:29.988596Z","url":"https://files.pythonhosted.org/packages/bf/3a/1fdf85563c47c2fc6571a1406aecb772f644d53a2adabf4981012971587a/agentops-0.0.14.tar.gz","yanked":false,"yanked_reason":null}],"0.0.15":[{"comment_text":"","digests":{"blake2b_256":"0c5374cbe5c78db9faa7c939d1a91eff111c4d3f13f4d8d18920ddd48f89f335","md5":"c4528a66151e76c7b1abdcac3c3eaf52","sha256":"aa8034dc9a0e9e56014a06fac521fc2a63a968d34f73e4d4c9bef4b0e87f8241"},"downloads":-1,"filename":"agentops-0.0.15-py3-none-any.whl","has_sig":false,"md5_digest":"c4528a66151e76c7b1abdcac3c3eaf52","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":18734,"upload_time":"2024-01-23T08:43:24","upload_time_iso_8601":"2024-01-23T08:43:24.651479Z","url":"https://files.pythonhosted.org/packages/0c/53/74cbe5c78db9faa7c939d1a91eff111c4d3f13f4d8d18920ddd48f89f335/agentops-0.0.15-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"da56c7d8189f4accc182be6729bc44a8006d981173e721ff4751ab784bbadfb3","md5":"cd27bff6c943c6fcbed33ed8280ab5ea","sha256":"71b0e048d2f1b86744105509436cbb6fa51e6b418a50a8253849dc6cdeda6cca"},"downloads":-1,"filename":"agentops-0.0.15.tar.gz","has_sig":false,"md5_digest":"cd27bff6c943c6fcbed33ed8280ab5ea","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":19985,"upload_time":"2024-01-23T08:43:26","upload_time_iso_8601":"2024-01-23T08:43:26.316265Z","url":"https://files.pythonhosted.org/packages/da/56/c7d8189f4accc182be6729bc44a8006d981173e721ff4751ab784bbadfb3/agentops-0.0.15.tar.gz","yanked":false,"yanked_reason":null}],"0.0.16":[{"comment_text":"","digests":{"blake2b_256":"b694d78d43f49688829cab72b7326db1d9e3f436f71eed113f26d402fefa6856","md5":"657c2cad11b3c8b97469524bff19b916","sha256":"e9633dcbc419a47db8de13bd0dc4f5d55f0a50ef3434ffe8e1f8a3468561bd60"},"downloads":-1,"filename":"agentops-0.0.16-py3-none-any.whl","has_sig":false,"md5_digest":"657c2cad11b3c8b97469524bff19b916","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":18736,"upload_time":"2024-01-23T09:03:05","upload_time_iso_8601":"2024-01-23T09:03:05.799496Z","url":"https://files.pythonhosted.org/packages/b6/94/d78d43f49688829cab72b7326db1d9e3f436f71eed113f26d402fefa6856/agentops-0.0.16-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"ec353005c98c1e2642d61510a9977c2118d3baa72f50e3c45ef6a341bfd9a3b0","md5":"2f9b28dd0953fdd2da606e19b9131006","sha256":"469588d72734fc6e90c66cf9658613baf2a0b94c933a23cab16820435576c61f"},"downloads":-1,"filename":"agentops-0.0.16.tar.gz","has_sig":false,"md5_digest":"2f9b28dd0953fdd2da606e19b9131006","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":19986,"upload_time":"2024-01-23T09:03:07","upload_time_iso_8601":"2024-01-23T09:03:07.645949Z","url":"https://files.pythonhosted.org/packages/ec/35/3005c98c1e2642d61510a9977c2118d3baa72f50e3c45ef6a341bfd9a3b0/agentops-0.0.16.tar.gz","yanked":false,"yanked_reason":null}],"0.0.17":[{"comment_text":"","digests":{"blake2b_256":"f3b2eff27fc5373097fc4f4d3d90f4d0fad1c3be7b923a6213750fe1cb022e6e","md5":"20325afd9b9d9633b120b63967d4ae85","sha256":"1a7c8d8fc8821e2e7eedbbe2683e076bfaca3434401b0d1ca6b830bf3230e61e"},"downloads":-1,"filename":"agentops-0.0.17-py3-none-any.whl","has_sig":false,"md5_digest":"20325afd9b9d9633b120b63967d4ae85","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":18827,"upload_time":"2024-01-23T17:12:19","upload_time_iso_8601":"2024-01-23T17:12:19.300806Z","url":"https://files.pythonhosted.org/packages/f3/b2/eff27fc5373097fc4f4d3d90f4d0fad1c3be7b923a6213750fe1cb022e6e/agentops-0.0.17-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"ac2a2cb7548cce5b009bee9e6f9b46b26df1cca777830231e2d1603b83740053","md5":"4ac65e38fa45946f1d382ce290b904e9","sha256":"cc1e7f796a84c66a29b271d8f0faa4999c152c80195911b817502da002a3ae02"},"downloads":-1,"filename":"agentops-0.0.17.tar.gz","has_sig":false,"md5_digest":"4ac65e38fa45946f1d382ce290b904e9","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":20063,"upload_time":"2024-01-23T17:12:20","upload_time_iso_8601":"2024-01-23T17:12:20.558647Z","url":"https://files.pythonhosted.org/packages/ac/2a/2cb7548cce5b009bee9e6f9b46b26df1cca777830231e2d1603b83740053/agentops-0.0.17.tar.gz","yanked":false,"yanked_reason":null}],"0.0.18":[{"comment_text":"","digests":{"blake2b_256":"321102c865df2245ab8cfaeb48a72ef7011a7bbbe1553a43791d68295ff7c20d","md5":"ad10ec2bf28bf434d3d2f11500f5a396","sha256":"df241f6a62368aa645d1599bb6885688fba0d49dcc26f97f7f65ab29a6af1a2a"},"downloads":-1,"filename":"agentops-0.0.18-py3-none-any.whl","has_sig":false,"md5_digest":"ad10ec2bf28bf434d3d2f11500f5a396","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":18860,"upload_time":"2024-01-24T04:39:06","upload_time_iso_8601":"2024-01-24T04:39:06.952175Z","url":"https://files.pythonhosted.org/packages/32/11/02c865df2245ab8cfaeb48a72ef7011a7bbbe1553a43791d68295ff7c20d/agentops-0.0.18-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"7831bd4249dcf9a0cdcad5451ca62aa83187295bb9c16fd1b3034999bff7ceaf","md5":"76dc30c0a2e68f09c0411c23dd5e3a36","sha256":"47e071424247dbbb1b9aaf07ff60a7e376ae01666478d0305d62a9068d61c1c1"},"downloads":-1,"filename":"agentops-0.0.18.tar.gz","has_sig":false,"md5_digest":"76dc30c0a2e68f09c0411c23dd5e3a36","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":20094,"upload_time":"2024-01-24T04:39:09","upload_time_iso_8601":"2024-01-24T04:39:09.795862Z","url":"https://files.pythonhosted.org/packages/78/31/bd4249dcf9a0cdcad5451ca62aa83187295bb9c16fd1b3034999bff7ceaf/agentops-0.0.18.tar.gz","yanked":false,"yanked_reason":null}],"0.0.19":[{"comment_text":"","digests":{"blake2b_256":"9d48292d743b748eddc01b51747e1dac4b62dea0eb5f240877bae821c0049572","md5":"a26178cdf9d5fc5b466a30e5990c16a1","sha256":"0e663e26aad41bf0288d250685e88130430dd087d03ffc69aa7f43e587921b59"},"downloads":-1,"filename":"agentops-0.0.19-py3-none-any.whl","has_sig":false,"md5_digest":"a26178cdf9d5fc5b466a30e5990c16a1","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":18380,"upload_time":"2024-01-24T07:58:38","upload_time_iso_8601":"2024-01-24T07:58:38.440021Z","url":"https://files.pythonhosted.org/packages/9d/48/292d743b748eddc01b51747e1dac4b62dea0eb5f240877bae821c0049572/agentops-0.0.19-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"dfe6f3b3fc53b050ec70de947e27227d0ea1e7a75037d082fc5f4d914178d12f","md5":"c62a69951acd19121b059215cf0ddb8b","sha256":"3d46faabf2dad44bd4705279569c76240ab5c71f03f511ba9d363dfd033d453e"},"downloads":-1,"filename":"agentops-0.0.19.tar.gz","has_sig":false,"md5_digest":"c62a69951acd19121b059215cf0ddb8b","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":19728,"upload_time":"2024-01-24T07:58:41","upload_time_iso_8601":"2024-01-24T07:58:41.352463Z","url":"https://files.pythonhosted.org/packages/df/e6/f3b3fc53b050ec70de947e27227d0ea1e7a75037d082fc5f4d914178d12f/agentops-0.0.19.tar.gz","yanked":false,"yanked_reason":null}],"0.0.2":[{"comment_text":"","digests":{"blake2b_256":"e593e3863d3c61a75e43a347d423f754bc57559989773af6a9c7bc696ff1d6b4","md5":"8ff77b84c32a4e846ce50c6844664b49","sha256":"3bea2bdd8a26c190675aaf2775d97bc2e3c52d7da05c04ae8ec46fed959e0c6e"},"downloads":-1,"filename":"agentops-0.0.2-py3-none-any.whl","has_sig":false,"md5_digest":"8ff77b84c32a4e846ce50c6844664b49","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":10452,"upload_time":"2023-08-28T23:14:23","upload_time_iso_8601":"2023-08-28T23:14:23.488523Z","url":"https://files.pythonhosted.org/packages/e5/93/e3863d3c61a75e43a347d423f754bc57559989773af6a9c7bc696ff1d6b4/agentops-0.0.2-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"82dbea7088c3ba71d9882a8d09d896d8529100f3103d1fe58ff4b890f9d616f1","md5":"02c4fed5ca014de524e5c1dfe3ec2dd2","sha256":"dc183d28965a9514cb33d916b29b3159189f5be64c4a7d943be0cad1a00379f9"},"downloads":-1,"filename":"agentops-0.0.2.tar.gz","has_sig":false,"md5_digest":"02c4fed5ca014de524e5c1dfe3ec2dd2","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":11510,"upload_time":"2023-08-28T23:14:24","upload_time_iso_8601":"2023-08-28T23:14:24.882664Z","url":"https://files.pythonhosted.org/packages/82/db/ea7088c3ba71d9882a8d09d896d8529100f3103d1fe58ff4b890f9d616f1/agentops-0.0.2.tar.gz","yanked":false,"yanked_reason":null}],"0.0.20":[{"comment_text":"","digests":{"blake2b_256":"ad68d8cc6d631618e04ec6988d0c3f4462a74b0b5849719b8373c2470cf9d533","md5":"09b2866043abc3e5cb5dfc17b80068cb","sha256":"ba20fc48902434858f28e3c4a7febe56d275a28bd33378868e7fcde2f53f2430"},"downloads":-1,"filename":"agentops-0.0.20-py3-none-any.whl","has_sig":false,"md5_digest":"09b2866043abc3e5cb5dfc17b80068cb","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":18367,"upload_time":"2024-01-25T07:12:48","upload_time_iso_8601":"2024-01-25T07:12:48.514177Z","url":"https://files.pythonhosted.org/packages/ad/68/d8cc6d631618e04ec6988d0c3f4462a74b0b5849719b8373c2470cf9d533/agentops-0.0.20-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"0ba37435a8ce7125c7d75b931a373a188acf1c9e793be28db1b5c5e5a57d7a10","md5":"fb700178ad44a4697b696ecbd28d115c","sha256":"d50623b03b410c8c88718c29ea271304681e1305b5c05ba824edb92d18aab4f8"},"downloads":-1,"filename":"agentops-0.0.20.tar.gz","has_sig":false,"md5_digest":"fb700178ad44a4697b696ecbd28d115c","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":19707,"upload_time":"2024-01-25T07:12:49","upload_time_iso_8601":"2024-01-25T07:12:49.915462Z","url":"https://files.pythonhosted.org/packages/0b/a3/7435a8ce7125c7d75b931a373a188acf1c9e793be28db1b5c5e5a57d7a10/agentops-0.0.20.tar.gz","yanked":false,"yanked_reason":null}],"0.0.21":[{"comment_text":"","digests":{"blake2b_256":"9182ceb8c12e05c0e56ea6c5ba7395c57764ffc5a8134fd045b247793873c172","md5":"ce428cf01a0c1066d3f1f3c8ca6b4f9b","sha256":"fdefe50d945ad669b33c90bf526f9af0e7dc4792b4443aeb907b0a36de2be186"},"downloads":-1,"filename":"agentops-0.0.21-py3-none-any.whl","has_sig":false,"md5_digest":"ce428cf01a0c1066d3f1f3c8ca6b4f9b","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":18483,"upload_time":"2024-02-22T03:07:14","upload_time_iso_8601":"2024-02-22T03:07:14.032143Z","url":"https://files.pythonhosted.org/packages/91/82/ceb8c12e05c0e56ea6c5ba7395c57764ffc5a8134fd045b247793873c172/agentops-0.0.21-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"acbb361e3d7ed85fc4207ffbbe44ddfa7ee3b8f96b76c3712d4153d63ebb45e2","md5":"360f00d330fa37ad10f687906e31e219","sha256":"ec10f8e64c553a1c400f1d5c792c3daef383cd718747cabb8e5abc9ef685f25d"},"downloads":-1,"filename":"agentops-0.0.21.tar.gz","has_sig":false,"md5_digest":"360f00d330fa37ad10f687906e31e219","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":19787,"upload_time":"2024-02-22T03:07:15","upload_time_iso_8601":"2024-02-22T03:07:15.546312Z","url":"https://files.pythonhosted.org/packages/ac/bb/361e3d7ed85fc4207ffbbe44ddfa7ee3b8f96b76c3712d4153d63ebb45e2/agentops-0.0.21.tar.gz","yanked":false,"yanked_reason":null}],"0.0.22":[{"comment_text":"","digests":{"blake2b_256":"b9da29a808d5bd3045f80b5652737e94695056b4a7cf7830ed7de037b1fe941c","md5":"d9e04a68f0b143432b9e34341e4f0a17","sha256":"fbcd962ff08a2e216637341c36c558be74368fbfda0b2408e55388e4c96474ca"},"downloads":-1,"filename":"agentops-0.0.22-py3-none-any.whl","has_sig":false,"md5_digest":"d9e04a68f0b143432b9e34341e4f0a17","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":18485,"upload_time":"2024-02-29T21:16:00","upload_time_iso_8601":"2024-02-29T21:16:00.124986Z","url":"https://files.pythonhosted.org/packages/b9/da/29a808d5bd3045f80b5652737e94695056b4a7cf7830ed7de037b1fe941c/agentops-0.0.22-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"4d842d1c5d80c69e6c9b8f3fd925c2f2fd084ad6eb29d93fdeadbdeca79e5eda","md5":"8f3b286fd01c2c43f7f7b1e4aebe3594","sha256":"397544ce90474fee59f1e8561c92f4923e9034842be593f1ac41437c5fca5841"},"downloads":-1,"filename":"agentops-0.0.22.tar.gz","has_sig":false,"md5_digest":"8f3b286fd01c2c43f7f7b1e4aebe3594","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":19784,"upload_time":"2024-02-29T21:16:01","upload_time_iso_8601":"2024-02-29T21:16:01.909583Z","url":"https://files.pythonhosted.org/packages/4d/84/2d1c5d80c69e6c9b8f3fd925c2f2fd084ad6eb29d93fdeadbdeca79e5eda/agentops-0.0.22.tar.gz","yanked":false,"yanked_reason":null}],"0.0.3":[{"comment_text":"","digests":{"blake2b_256":"324eda261865c2042eeb5da9827a350760e435896855d5480b8f3136212c3f65","md5":"07a9f9f479a14e65b82054a145514e8d","sha256":"35351701e3caab900243771bda19d6613bdcb84cc9ef2e1adde431a775c09af8"},"downloads":-1,"filename":"agentops-0.0.3-py3-none-any.whl","has_sig":false,"md5_digest":"07a9f9f479a14e65b82054a145514e8d","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":11872,"upload_time":"2023-09-13T23:03:34","upload_time_iso_8601":"2023-09-13T23:03:34.300564Z","url":"https://files.pythonhosted.org/packages/32/4e/da261865c2042eeb5da9827a350760e435896855d5480b8f3136212c3f65/agentops-0.0.3-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"643485e455d4f411b56bef2a99c40e32f35f456c93deda0a3915231f1da92e56","md5":"c637ee3cfa358b65ed14cfc20d5f803f","sha256":"45a57492e4072f3f27b5e851f6e501b54c796f6ace5f65ecf70e51dbe18ca1a8"},"downloads":-1,"filename":"agentops-0.0.3.tar.gz","has_sig":false,"md5_digest":"c637ee3cfa358b65ed14cfc20d5f803f","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":12455,"upload_time":"2023-09-13T23:03:35","upload_time_iso_8601":"2023-09-13T23:03:35.513682Z","url":"https://files.pythonhosted.org/packages/64/34/85e455d4f411b56bef2a99c40e32f35f456c93deda0a3915231f1da92e56/agentops-0.0.3.tar.gz","yanked":false,"yanked_reason":null}],"0.0.4":[{"comment_text":"","digests":{"blake2b_256":"20cc12cf2391854ed588eaf6cdc87f60048f84e8dc7d15792850b7e90a0406b8","md5":"7a3c11004517e22dc7cde83cf6d8d5e8","sha256":"5a5cdcbe6e32c59237521182b83768e650b4519416b42f4e13929a115a0f20ee"},"downloads":-1,"filename":"agentops-0.0.4-py3-none-any.whl","has_sig":false,"md5_digest":"7a3c11004517e22dc7cde83cf6d8d5e8","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":13520,"upload_time":"2023-09-22T09:23:52","upload_time_iso_8601":"2023-09-22T09:23:52.896099Z","url":"https://files.pythonhosted.org/packages/20/cc/12cf2391854ed588eaf6cdc87f60048f84e8dc7d15792850b7e90a0406b8/agentops-0.0.4-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"98d2d9f9932d17711dd5d98af674c868686bdbdd9aaae9b8d69e9eecfd4c68f4","md5":"712d3bc3b28703963f8f398845b1d17a","sha256":"97743c6420bc5ba2655ac690041d5f5732fb950130cf61ab25ef6d44be6ecfb2"},"downloads":-1,"filename":"agentops-0.0.4.tar.gz","has_sig":false,"md5_digest":"712d3bc3b28703963f8f398845b1d17a","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":14050,"upload_time":"2023-09-22T09:23:54","upload_time_iso_8601":"2023-09-22T09:23:54.315467Z","url":"https://files.pythonhosted.org/packages/98/d2/d9f9932d17711dd5d98af674c868686bdbdd9aaae9b8d69e9eecfd4c68f4/agentops-0.0.4.tar.gz","yanked":false,"yanked_reason":null}],"0.0.5":[{"comment_text":"","digests":{"blake2b_256":"e900cd903074a01932ded9a05dac7849a16c5850ed20c027b954b1eccfba54c1","md5":"1bd4fd6cca14dac4947ecc6c4e3fe0a1","sha256":"e39e1051ba8c58f222f3495196eb939ccc53f04bd279372ae01e694973dd25d6"},"downloads":-1,"filename":"agentops-0.0.5-py3-none-any.whl","has_sig":false,"md5_digest":"1bd4fd6cca14dac4947ecc6c4e3fe0a1","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":14107,"upload_time":"2023-10-07T00:22:48","upload_time_iso_8601":"2023-10-07T00:22:48.714074Z","url":"https://files.pythonhosted.org/packages/e9/00/cd903074a01932ded9a05dac7849a16c5850ed20c027b954b1eccfba54c1/agentops-0.0.5-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"08d5c29068ce4df9c85865b45e1cdb7be1df06e54fce087fad18ec390a7aea54","md5":"4d8fc5553e3199fe24d6118337884a2b","sha256":"8f3662e600ba57e9a102c6bf86a6a1e16c0e53e1f38a84fa1b9c01cc07ca4990"},"downloads":-1,"filename":"agentops-0.0.5.tar.gz","has_sig":false,"md5_digest":"4d8fc5553e3199fe24d6118337884a2b","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":14724,"upload_time":"2023-10-07T00:22:50","upload_time_iso_8601":"2023-10-07T00:22:50.304226Z","url":"https://files.pythonhosted.org/packages/08/d5/c29068ce4df9c85865b45e1cdb7be1df06e54fce087fad18ec390a7aea54/agentops-0.0.5.tar.gz","yanked":false,"yanked_reason":null}],"0.0.6":[{"comment_text":"","digests":{"blake2b_256":"2f5b5f3bd8a5b2d96b6417fd4a3fc72ed484e3a4ffacac49035f17bb8df1dd5b","md5":"b7e701ff7953ecca01ceec3a6b9374b2","sha256":"05dea1d06f8f8d06a8f460d18d302febe91f4dad2e3fc0088d05b7017765f3b6"},"downloads":-1,"filename":"agentops-0.0.6-py3-none-any.whl","has_sig":false,"md5_digest":"b7e701ff7953ecca01ceec3a6b9374b2","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":14236,"upload_time":"2023-10-27T06:56:14","upload_time_iso_8601":"2023-10-27T06:56:14.029277Z","url":"https://files.pythonhosted.org/packages/2f/5b/5f3bd8a5b2d96b6417fd4a3fc72ed484e3a4ffacac49035f17bb8df1dd5b/agentops-0.0.6-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"4af43743bf40518545c8906687038e5717b1bd33db7ba300a084ec4f6c9c59e0","md5":"0a78dcafcbc6292cf0823181cdc226a7","sha256":"0057cb5d6dc0dd2c444f3371faef40c844a1510700b31824a4fccf5302713361"},"downloads":-1,"filename":"agentops-0.0.6.tar.gz","has_sig":false,"md5_digest":"0a78dcafcbc6292cf0823181cdc226a7","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":14785,"upload_time":"2023-10-27T06:56:15","upload_time_iso_8601":"2023-10-27T06:56:15.069192Z","url":"https://files.pythonhosted.org/packages/4a/f4/3743bf40518545c8906687038e5717b1bd33db7ba300a084ec4f6c9c59e0/agentops-0.0.6.tar.gz","yanked":false,"yanked_reason":null}],"0.0.7":[{"comment_text":"","digests":{"blake2b_256":"3cb1d15c39bbc95f66c64d01cca304f9b4b0c3503509ad92ef29f926c9163599","md5":"f494f6c256899103a80666be68d136ad","sha256":"6984429ca1a9013fd4386105516cb36a46dd7078f7ac81e0a4701f1700bd25b5"},"downloads":-1,"filename":"agentops-0.0.7-py3-none-any.whl","has_sig":false,"md5_digest":"f494f6c256899103a80666be68d136ad","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":14370,"upload_time":"2023-11-02T06:37:36","upload_time_iso_8601":"2023-11-02T06:37:36.480189Z","url":"https://files.pythonhosted.org/packages/3c/b1/d15c39bbc95f66c64d01cca304f9b4b0c3503509ad92ef29f926c9163599/agentops-0.0.7-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"ba709ae02fc635cab51b237dcc3657ec69aac61ee67ea5f903cfae07de19abc8","md5":"b163eaaf9cbafbbd19ec3f91b2b56969","sha256":"a6f36d94a82d8e481b406f040790cefd4d939f07108737c696327d97c0ccdaf4"},"downloads":-1,"filename":"agentops-0.0.7.tar.gz","has_sig":false,"md5_digest":"b163eaaf9cbafbbd19ec3f91b2b56969","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":14895,"upload_time":"2023-11-02T06:37:37","upload_time_iso_8601":"2023-11-02T06:37:37.698159Z","url":"https://files.pythonhosted.org/packages/ba/70/9ae02fc635cab51b237dcc3657ec69aac61ee67ea5f903cfae07de19abc8/agentops-0.0.7.tar.gz","yanked":false,"yanked_reason":null}],"0.0.8":[{"comment_text":"","digests":{"blake2b_256":"8147fa3ee8807ad961aa50a773b6567e3a624000936d3cc1a578af72d83e02e7","md5":"20cffb5534b4545fa1e8b24a6a24b1da","sha256":"5d50b2ab18a203dbb4555a2cd482dae8df5bf2aa3e771a9758ee28b540330da3"},"downloads":-1,"filename":"agentops-0.0.8-py3-none-any.whl","has_sig":false,"md5_digest":"20cffb5534b4545fa1e8b24a6a24b1da","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":14391,"upload_time":"2023-11-23T06:17:56","upload_time_iso_8601":"2023-11-23T06:17:56.154712Z","url":"https://files.pythonhosted.org/packages/81/47/fa3ee8807ad961aa50a773b6567e3a624000936d3cc1a578af72d83e02e7/agentops-0.0.8-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"707473dc640a3fecfbe84ab7da230f7c862f72f231514a2a488b43a896146ed6","md5":"bba7e74b58849f15d50f4e1270cbd23f","sha256":"3a625d2acc922d99563ce71c5032b0b3b0db57d1c6fade319cf1bb636608eca0"},"downloads":-1,"filename":"agentops-0.0.8.tar.gz","has_sig":false,"md5_digest":"bba7e74b58849f15d50f4e1270cbd23f","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":14775,"upload_time":"2023-11-23T06:17:58","upload_time_iso_8601":"2023-11-23T06:17:58.768877Z","url":"https://files.pythonhosted.org/packages/70/74/73dc640a3fecfbe84ab7da230f7c862f72f231514a2a488b43a896146ed6/agentops-0.0.8.tar.gz","yanked":false,"yanked_reason":null}],"0.1.0":[{"comment_text":"","digests":{"blake2b_256":"c2a41dc8456edc9bccc0c560967cfdce23a4d7ab8162946be288b54391d80f7c","md5":"5fb09f82b7eeb270c6644dcd3656953f","sha256":"b480fd51fbffc76ae13bb885c2adb1236a7d3b0095b4dafb4a992f6e25647433"},"downloads":-1,"filename":"agentops-0.1.0-py3-none-any.whl","has_sig":false,"md5_digest":"5fb09f82b7eeb270c6644dcd3656953f","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":25045,"upload_time":"2024-04-03T02:01:56","upload_time_iso_8601":"2024-04-03T02:01:56.936873Z","url":"https://files.pythonhosted.org/packages/c2/a4/1dc8456edc9bccc0c560967cfdce23a4d7ab8162946be288b54391d80f7c/agentops-0.1.0-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"a81756443f28de774cb7c863a2856e1b07658a9a772ba86dfb1cfbb19bc08fe3","md5":"b93c602c1d1da5d8f7a2dcdaa70f8e21","sha256":"22d3dc87dedf93b3b78a0dfdef8c685b2f3bff9fbab32016360e298a24d311dc"},"downloads":-1,"filename":"agentops-0.1.0.tar.gz","has_sig":false,"md5_digest":"b93c602c1d1da5d8f7a2dcdaa70f8e21","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":24685,"upload_time":"2024-04-03T02:01:58","upload_time_iso_8601":"2024-04-03T02:01:58.623055Z","url":"https://files.pythonhosted.org/packages/a8/17/56443f28de774cb7c863a2856e1b07658a9a772ba86dfb1cfbb19bc08fe3/agentops-0.1.0.tar.gz","yanked":false,"yanked_reason":null}],"0.1.0b1":[{"comment_text":"","digests":{"blake2b_256":"c03a329c59f001f50701e9e541775c79304a5ce4ffe34d717b1d2af555362e9e","md5":"7c7e84b3b4448580bf5a7e9c08012477","sha256":"825ab57ac5f7840f5a7f8ac195f4af75ec07a9c0972b17d1a57a595420d06208"},"downloads":-1,"filename":"agentops-0.1.0b1-py3-none-any.whl","has_sig":false,"md5_digest":"7c7e84b3b4448580bf5a7e9c08012477","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":23258,"upload_time":"2024-03-18T18:51:08","upload_time_iso_8601":"2024-03-18T18:51:08.693772Z","url":"https://files.pythonhosted.org/packages/c0/3a/329c59f001f50701e9e541775c79304a5ce4ffe34d717b1d2af555362e9e/agentops-0.1.0b1-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"026ee44f1d5a49924867475f7d101abe40170c0674b4b395f28ce88552c1ba71","md5":"9cf6699fe45f13f1893c8992405e7261","sha256":"f5ce4b34999fe4b21a4ce3643980253d30f8ea9c55f01d96cd35631355fc7ac3"},"downloads":-1,"filename":"agentops-0.1.0b1.tar.gz","has_sig":false,"md5_digest":"9cf6699fe45f13f1893c8992405e7261","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":23842,"upload_time":"2024-03-18T18:51:10","upload_time_iso_8601":"2024-03-18T18:51:10.250127Z","url":"https://files.pythonhosted.org/packages/02/6e/e44f1d5a49924867475f7d101abe40170c0674b4b395f28ce88552c1ba71/agentops-0.1.0b1.tar.gz","yanked":false,"yanked_reason":null}],"0.1.0b2":[{"comment_text":"","digests":{"blake2b_256":"6a25e9282f81c3f2615ef6543a0b5ca49dd14b03f311fc5a108ad1aff4f0b720","md5":"1d3e736ef44c0ad8829c50f036ac807b","sha256":"485362b9a68d2327da250f0681b30a9296f0b41e058672b023ae2a8ed924b4d3"},"downloads":-1,"filename":"agentops-0.1.0b2-py3-none-any.whl","has_sig":false,"md5_digest":"1d3e736ef44c0ad8829c50f036ac807b","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":23477,"upload_time":"2024-03-21T23:31:20","upload_time_iso_8601":"2024-03-21T23:31:20.022797Z","url":"https://files.pythonhosted.org/packages/6a/25/e9282f81c3f2615ef6543a0b5ca49dd14b03f311fc5a108ad1aff4f0b720/agentops-0.1.0b2-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"3165f702684da6e01f8df74a4291be2914c382ec4cb6f8ed2c3dc6d5a9f177ff","md5":"0d51a6f6bf7cb0d3651574404c9c703c","sha256":"cf9a8b54cc4f76592b6380729c03ec7adfe2256e6b200876d7595e50015f5d62"},"downloads":-1,"filename":"agentops-0.1.0b2.tar.gz","has_sig":false,"md5_digest":"0d51a6f6bf7cb0d3651574404c9c703c","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":23659,"upload_time":"2024-03-21T23:31:21","upload_time_iso_8601":"2024-03-21T23:31:21.330837Z","url":"https://files.pythonhosted.org/packages/31/65/f702684da6e01f8df74a4291be2914c382ec4cb6f8ed2c3dc6d5a9f177ff/agentops-0.1.0b2.tar.gz","yanked":false,"yanked_reason":null}],"0.1.0b3":[{"comment_text":"","digests":{"blake2b_256":"2e64bfe82911b8981ce57f86154915d53b45fffa83ccb9cd6cf4cc71af3f796b","md5":"470bc56525c114dddd908628dcb4f267","sha256":"45b5aaa9f38989cfbfcc4f64e3041050df6d417177874316839225085e60d18d"},"downloads":-1,"filename":"agentops-0.1.0b3-py3-none-any.whl","has_sig":false,"md5_digest":"470bc56525c114dddd908628dcb4f267","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":23522,"upload_time":"2024-03-25T19:34:58","upload_time_iso_8601":"2024-03-25T19:34:58.102867Z","url":"https://files.pythonhosted.org/packages/2e/64/bfe82911b8981ce57f86154915d53b45fffa83ccb9cd6cf4cc71af3f796b/agentops-0.1.0b3-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"0858e4b718e30a6bbe27d32b7128398cb3884f83f89b4121e36cbb7f979466ca","md5":"8ddb13824d3636d841739479e02a12e6","sha256":"9020daab306fe8c7ed0a98a9edcad9772eb1df0eacce7f936a5ed6bf0f7d2af1"},"downloads":-1,"filename":"agentops-0.1.0b3.tar.gz","has_sig":false,"md5_digest":"8ddb13824d3636d841739479e02a12e6","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":23641,"upload_time":"2024-03-25T19:35:01","upload_time_iso_8601":"2024-03-25T19:35:01.119334Z","url":"https://files.pythonhosted.org/packages/08/58/e4b718e30a6bbe27d32b7128398cb3884f83f89b4121e36cbb7f979466ca/agentops-0.1.0b3.tar.gz","yanked":false,"yanked_reason":null}],"0.1.0b4":[{"comment_text":"","digests":{"blake2b_256":"67f860440d18b674b06c5a9f4f334bf1f1656dca9f6763d5dd3a2be9e5d2c256","md5":"b11f47108926fb46964bbf28675c3e35","sha256":"93a1f241c3fd7880c3d29ab64baa0661d9ba84e2071092aecb3e4fc574037900"},"downloads":-1,"filename":"agentops-0.1.0b4-py3-none-any.whl","has_sig":false,"md5_digest":"b11f47108926fb46964bbf28675c3e35","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":23512,"upload_time":"2024-03-26T01:14:54","upload_time_iso_8601":"2024-03-26T01:14:54.986869Z","url":"https://files.pythonhosted.org/packages/67/f8/60440d18b674b06c5a9f4f334bf1f1656dca9f6763d5dd3a2be9e5d2c256/agentops-0.1.0b4-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"10feabb836b04b7eae44383f5616ed1c4c6e9aee9beecc3df4617f69f7e3adc5","md5":"fa4512f74baf9909544ebab021862740","sha256":"4716b4e2a627d7a3846ddee3d334c8f5e8a1a2d231ec5286379c0f22920a2a9d"},"downloads":-1,"filename":"agentops-0.1.0b4.tar.gz","has_sig":false,"md5_digest":"fa4512f74baf9909544ebab021862740","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":23668,"upload_time":"2024-03-26T01:14:56","upload_time_iso_8601":"2024-03-26T01:14:56.921017Z","url":"https://files.pythonhosted.org/packages/10/fe/abb836b04b7eae44383f5616ed1c4c6e9aee9beecc3df4617f69f7e3adc5/agentops-0.1.0b4.tar.gz","yanked":false,"yanked_reason":null}],"0.1.0b5":[{"comment_text":"","digests":{"blake2b_256":"3ac591c14d08000def551f70ccc1da9ab8b37f57561d24cf7fdf6cd3547610ee","md5":"52a2212b79870ee48f0dbdad852dbb90","sha256":"ed050e51137baa4f46769c77595e1cbe212bb86243f27a29b50218782a0d8242"},"downloads":-1,"filename":"agentops-0.1.0b5-py3-none-any.whl","has_sig":false,"md5_digest":"52a2212b79870ee48f0dbdad852dbb90","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":24597,"upload_time":"2024-04-02T00:56:17","upload_time_iso_8601":"2024-04-02T00:56:17.570921Z","url":"https://files.pythonhosted.org/packages/3a/c5/91c14d08000def551f70ccc1da9ab8b37f57561d24cf7fdf6cd3547610ee/agentops-0.1.0b5-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"84d6f0bbe5883b86e749f2f02896d94054ebd84b4d66524e4b7004263ae21a6f","md5":"89c6aa7864f45c17f42a38bb6fae904b","sha256":"6ebe6a94f0898fd47521755b6c8083c5f6c0c8bb30d43441200b9ef67998ed01"},"downloads":-1,"filename":"agentops-0.1.0b5.tar.gz","has_sig":false,"md5_digest":"89c6aa7864f45c17f42a38bb6fae904b","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":24624,"upload_time":"2024-04-02T00:56:18","upload_time_iso_8601":"2024-04-02T00:56:18.703411Z","url":"https://files.pythonhosted.org/packages/84/d6/f0bbe5883b86e749f2f02896d94054ebd84b4d66524e4b7004263ae21a6f/agentops-0.1.0b5.tar.gz","yanked":false,"yanked_reason":null}],"0.1.0b7":[{"comment_text":"","digests":{"blake2b_256":"3cc4ebdb56f0ff88ad20ddba765093aa6c1fc655a8f2bbafbcb2057f998d814f","md5":"d117591df22735d1dedbdc034c93bff6","sha256":"0d4fdb036836dddcce770cffcb2d564b0011a3307224d9a4675fc9bf80ffa5d2"},"downloads":-1,"filename":"agentops-0.1.0b7-py3-none-any.whl","has_sig":false,"md5_digest":"d117591df22735d1dedbdc034c93bff6","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":24592,"upload_time":"2024-04-02T03:20:11","upload_time_iso_8601":"2024-04-02T03:20:11.132539Z","url":"https://files.pythonhosted.org/packages/3c/c4/ebdb56f0ff88ad20ddba765093aa6c1fc655a8f2bbafbcb2057f998d814f/agentops-0.1.0b7-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"cbf0c32014a8ee12df4596ec4d90428e73e0cc5277d1b9bd2b53f815a7f0ea1f","md5":"20364eb7d493e6f9b46666f36be8fb2f","sha256":"938b29cd894ff38c7b1dee02f6422458702ccf8f3b69b69bc0e4220e42a33629"},"downloads":-1,"filename":"agentops-0.1.0b7.tar.gz","has_sig":false,"md5_digest":"20364eb7d493e6f9b46666f36be8fb2f","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":24611,"upload_time":"2024-04-02T03:20:12","upload_time_iso_8601":"2024-04-02T03:20:12.490524Z","url":"https://files.pythonhosted.org/packages/cb/f0/c32014a8ee12df4596ec4d90428e73e0cc5277d1b9bd2b53f815a7f0ea1f/agentops-0.1.0b7.tar.gz","yanked":false,"yanked_reason":null}],"0.1.1":[{"comment_text":"","digests":{"blake2b_256":"ba13ff18b4ff72805bcbe7437aa445cde854a44b4b358564ed2b044678e270b9","md5":"d4f77de8dd58468c6c307e735c1cfaa9","sha256":"8afc0b7871d17f8cbe9996cab5ca10a8a3ed33a3406e1ddc257fadc214daa79a"},"downloads":-1,"filename":"agentops-0.1.1-py3-none-any.whl","has_sig":false,"md5_digest":"d4f77de8dd58468c6c307e735c1cfaa9","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":25189,"upload_time":"2024-04-05T22:41:01","upload_time_iso_8601":"2024-04-05T22:41:01.867983Z","url":"https://files.pythonhosted.org/packages/ba/13/ff18b4ff72805bcbe7437aa445cde854a44b4b358564ed2b044678e270b9/agentops-0.1.1-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"1dec1d2af6e33dd097feaf1e41a4d34c66d4e4e59ce35c5efac85c18614b9d4b","md5":"f072d8700d4e22fc25eae8bb29a54d1f","sha256":"001582703d5e6ffe67a51f9d67a303b5344e4ef8ca315f24aa43e0dd3d19f53b"},"downloads":-1,"filename":"agentops-0.1.1.tar.gz","has_sig":false,"md5_digest":"f072d8700d4e22fc25eae8bb29a54d1f","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":24831,"upload_time":"2024-04-05T22:41:03","upload_time_iso_8601":"2024-04-05T22:41:03.677234Z","url":"https://files.pythonhosted.org/packages/1d/ec/1d2af6e33dd097feaf1e41a4d34c66d4e4e59ce35c5efac85c18614b9d4b/agentops-0.1.1.tar.gz","yanked":false,"yanked_reason":null}],"0.1.10":[{"comment_text":"","digests":{"blake2b_256":"cdf9a295ed62701dd4e56d5b57e45e0425db2bcea992c687534c9a2dd1e001f1","md5":"8d82b9cb794b4b4a1e91ddece5447bcf","sha256":"8b80800d4fa5a7a6c85c79f2bf39a50fb446ab8b209519bd51f44dee3b38517e"},"downloads":-1,"filename":"agentops-0.1.10-py3-none-any.whl","has_sig":false,"md5_digest":"8d82b9cb794b4b4a1e91ddece5447bcf","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":29769,"upload_time":"2024-05-10T20:13:39","upload_time_iso_8601":"2024-05-10T20:13:39.477237Z","url":"https://files.pythonhosted.org/packages/cd/f9/a295ed62701dd4e56d5b57e45e0425db2bcea992c687534c9a2dd1e001f1/agentops-0.1.10-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"f3788e027be4aa50f677a46bba1e0132f021e90d299c6eae093181a91679e378","md5":"4dd3d1fd8c08efb1a08ae212ed9211d7","sha256":"73fbd36cd5f3052d22e64dbea1fa9d70fb02658a901a600101801daa73f359f9"},"downloads":-1,"filename":"agentops-0.1.10.tar.gz","has_sig":false,"md5_digest":"4dd3d1fd8c08efb1a08ae212ed9211d7","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":30268,"upload_time":"2024-05-10T20:14:25","upload_time_iso_8601":"2024-05-10T20:14:25.258530Z","url":"https://files.pythonhosted.org/packages/f3/78/8e027be4aa50f677a46bba1e0132f021e90d299c6eae093181a91679e378/agentops-0.1.10.tar.gz","yanked":false,"yanked_reason":null}],"0.1.11":[{"comment_text":"","digests":{"blake2b_256":"1ebfaaa31babe3bf687312592f99fe900e3808058658577bd1367b7df0332a08","md5":"73c0b028248665a7927688fb8baa7680","sha256":"e9411981a5d0b1190b93e3e1124db3ac6f17015c65a84b92a793f34d79b694c9"},"downloads":-1,"filename":"agentops-0.1.11-py3-none-any.whl","has_sig":false,"md5_digest":"73c0b028248665a7927688fb8baa7680","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":30952,"upload_time":"2024-05-17T00:32:49","upload_time_iso_8601":"2024-05-17T00:32:49.202597Z","url":"https://files.pythonhosted.org/packages/1e/bf/aaa31babe3bf687312592f99fe900e3808058658577bd1367b7df0332a08/agentops-0.1.11-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"6ee43f71a7d1d63595058cd6945e7b9e2de1b06ace04176a6723b7bfb37bf880","md5":"36092e907e4f15a6bafd6788383df112","sha256":"4a365ee56303b5b80d9de21fc13ccb7a3fe44544a6c165327bbfd9213bfe0191"},"downloads":-1,"filename":"agentops-0.1.11.tar.gz","has_sig":false,"md5_digest":"36092e907e4f15a6bafd6788383df112","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":31256,"upload_time":"2024-05-17T00:32:50","upload_time_iso_8601":"2024-05-17T00:32:50.919974Z","url":"https://files.pythonhosted.org/packages/6e/e4/3f71a7d1d63595058cd6945e7b9e2de1b06ace04176a6723b7bfb37bf880/agentops-0.1.11.tar.gz","yanked":false,"yanked_reason":null}],"0.1.12":[{"comment_text":"","digests":{"blake2b_256":"67f5227dffbebeffd3b404db0dd71805f00814e458c0d081faf7a4e70c7e984f","md5":"2591924de6f2e5580e4733b0e8336e2c","sha256":"b4b47c990638b74810cc1c38624ada162094b46e3fdd63883642a16bc5258386"},"downloads":-1,"filename":"agentops-0.1.12-py3-none-any.whl","has_sig":false,"md5_digest":"2591924de6f2e5580e4733b0e8336e2c","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":35605,"upload_time":"2024-05-24T20:11:52","upload_time_iso_8601":"2024-05-24T20:11:52.863109Z","url":"https://files.pythonhosted.org/packages/67/f5/227dffbebeffd3b404db0dd71805f00814e458c0d081faf7a4e70c7e984f/agentops-0.1.12-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"9f9ae6dc42ad8d40ad47c6116629b2cbda443d314327ab4d33e1044cb75ba88b","md5":"4c2e76e7b6d4799ef4b464dee29e7255","sha256":"c4f762482fb240fc3503907f52498f2d8d9e4f80236ee4a12bf039317a85fcd7"},"downloads":-1,"filename":"agentops-0.1.12.tar.gz","has_sig":false,"md5_digest":"4c2e76e7b6d4799ef4b464dee29e7255","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":35103,"upload_time":"2024-05-24T20:11:54","upload_time_iso_8601":"2024-05-24T20:11:54.846567Z","url":"https://files.pythonhosted.org/packages/9f/9a/e6dc42ad8d40ad47c6116629b2cbda443d314327ab4d33e1044cb75ba88b/agentops-0.1.12.tar.gz","yanked":false,"yanked_reason":null}],"0.1.2":[{"comment_text":"","digests":{"blake2b_256":"e709193dfe68c2d23de2c60dd0af2af336cbf81d3a3f0c175705783b4c1da580","md5":"588d9877b9767546606d3d6d76d247fc","sha256":"ec79e56889eadd2bab04dfe2f6a899a1b90dc347a66cc80488297368386105b4"},"downloads":-1,"filename":"agentops-0.1.2-py3-none-any.whl","has_sig":false,"md5_digest":"588d9877b9767546606d3d6d76d247fc","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":25359,"upload_time":"2024-04-09T23:00:51","upload_time_iso_8601":"2024-04-09T23:00:51.897995Z","url":"https://files.pythonhosted.org/packages/e7/09/193dfe68c2d23de2c60dd0af2af336cbf81d3a3f0c175705783b4c1da580/agentops-0.1.2-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"8acc872aba374093481bb40ed6b7531b1500b00138baf6bfb9ca7c20fb889d58","md5":"80f8f7c56b1e1a6ff4c48877fe12dd12","sha256":"d213e1037d2d319743889c2bdbc10dc068b0591e2c6c156f69019302490336d5"},"downloads":-1,"filename":"agentops-0.1.2.tar.gz","has_sig":false,"md5_digest":"80f8f7c56b1e1a6ff4c48877fe12dd12","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":24968,"upload_time":"2024-04-09T23:00:53","upload_time_iso_8601":"2024-04-09T23:00:53.227389Z","url":"https://files.pythonhosted.org/packages/8a/cc/872aba374093481bb40ed6b7531b1500b00138baf6bfb9ca7c20fb889d58/agentops-0.1.2.tar.gz","yanked":false,"yanked_reason":null}],"0.1.3":[{"comment_text":"","digests":{"blake2b_256":"9701aad65170506dcf29606e9e619d2c0caaee565e5e8b14a791c3e0e86c6356","md5":"4dc967275c884e2a5a1de8df448ae1c6","sha256":"f1ca0f2c5156d826381e9ebd634555215c67e1cb344683abddb382e594f483e4"},"downloads":-1,"filename":"agentops-0.1.3-py3-none-any.whl","has_sig":false,"md5_digest":"4dc967275c884e2a5a1de8df448ae1c6","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":25393,"upload_time":"2024-04-09T23:24:20","upload_time_iso_8601":"2024-04-09T23:24:20.821465Z","url":"https://files.pythonhosted.org/packages/97/01/aad65170506dcf29606e9e619d2c0caaee565e5e8b14a791c3e0e86c6356/agentops-0.1.3-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"5e22afde273bcf52cfc6581fba804b44eeebea6ff2ae774f0e5917fa1dd3ee09","md5":"624c9b63dbe56c8b1dd535e1b20ada81","sha256":"dd65e80ec70accfac0692171199b6ecfa37a7d109a3c25f2191c0934b5004114"},"downloads":-1,"filename":"agentops-0.1.3.tar.gz","has_sig":false,"md5_digest":"624c9b63dbe56c8b1dd535e1b20ada81","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":24994,"upload_time":"2024-04-09T23:24:22","upload_time_iso_8601":"2024-04-09T23:24:22.610198Z","url":"https://files.pythonhosted.org/packages/5e/22/afde273bcf52cfc6581fba804b44eeebea6ff2ae774f0e5917fa1dd3ee09/agentops-0.1.3.tar.gz","yanked":false,"yanked_reason":null}],"0.1.4":[{"comment_text":"","digests":{"blake2b_256":"50313e20afb169e707941cc3342cecb88060aa8746e95d72a202fd90ac4096b6","md5":"3f64b736522ea40c35db6d2a609fc54f","sha256":"476a5e795a6cc87858a0885be61b1e05eed21e4c6ab47f20348c48717c2ac454"},"downloads":-1,"filename":"agentops-0.1.4-py3-none-any.whl","has_sig":false,"md5_digest":"3f64b736522ea40c35db6d2a609fc54f","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":25558,"upload_time":"2024-04-11T19:26:01","upload_time_iso_8601":"2024-04-11T19:26:01.162829Z","url":"https://files.pythonhosted.org/packages/50/31/3e20afb169e707941cc3342cecb88060aa8746e95d72a202fd90ac4096b6/agentops-0.1.4-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"e0688b1a21f72b85c9bdd56da4223c991bdfb5d0c2accd9ddd326616bf952795","md5":"6f4601047f3e2080b4f7363ff84f15f3","sha256":"d55e64953f84654d44557b496a3b3744a20449b854af84fa83a15be75b362b3d"},"downloads":-1,"filename":"agentops-0.1.4.tar.gz","has_sig":false,"md5_digest":"6f4601047f3e2080b4f7363ff84f15f3","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":25390,"upload_time":"2024-04-11T19:26:02","upload_time_iso_8601":"2024-04-11T19:26:02.991657Z","url":"https://files.pythonhosted.org/packages/e0/68/8b1a21f72b85c9bdd56da4223c991bdfb5d0c2accd9ddd326616bf952795/agentops-0.1.4.tar.gz","yanked":false,"yanked_reason":null}],"0.1.5":[{"comment_text":"","digests":{"blake2b_256":"641c742793fa77c803e5667830ccd34b8d313d11f361a105fe92ce68d871cc5f","md5":"964421a604c67c07b5c72b70ceee6ce8","sha256":"bc65dd4cd85d1ffcba195f2490b5a4380d0b565dd0f4a71ecc64ed96a7fe1eee"},"downloads":-1,"filename":"agentops-0.1.5-py3-none-any.whl","has_sig":false,"md5_digest":"964421a604c67c07b5c72b70ceee6ce8","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":25793,"upload_time":"2024-04-20T01:56:23","upload_time_iso_8601":"2024-04-20T01:56:23.089343Z","url":"https://files.pythonhosted.org/packages/64/1c/742793fa77c803e5667830ccd34b8d313d11f361a105fe92ce68d871cc5f/agentops-0.1.5-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"62beabcb235daf34d4740961c4ad295b8dfb8a053ac6a1e341394e36f722ea89","md5":"3ff7fa3135bc5c4254aaa99e3cc00dc8","sha256":"17f0a573362d9c4770846874a4091662304d6889e21ca6a7dd747be48b9c8597"},"downloads":-1,"filename":"agentops-0.1.5.tar.gz","has_sig":false,"md5_digest":"3ff7fa3135bc5c4254aaa99e3cc00dc8","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":25664,"upload_time":"2024-04-20T01:56:24","upload_time_iso_8601":"2024-04-20T01:56:24.303013Z","url":"https://files.pythonhosted.org/packages/62/be/abcb235daf34d4740961c4ad295b8dfb8a053ac6a1e341394e36f722ea89/agentops-0.1.5.tar.gz","yanked":false,"yanked_reason":null}],"0.1.6":[{"comment_text":"","digests":{"blake2b_256":"430b9f3fcfc2f9778dbbfc1fd68b223e9a91938505ef987e17b93a631bb6b2e4","md5":"28ce2e6aa7a4598fa1e764d9762fd030","sha256":"9dff841ef71f5fad2d897012a00f50011a706970e0e5eaae9d7b0540a637b128"},"downloads":-1,"filename":"agentops-0.1.6-py3-none-any.whl","has_sig":false,"md5_digest":"28ce2e6aa7a4598fa1e764d9762fd030","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":26154,"upload_time":"2024-04-20T03:48:58","upload_time_iso_8601":"2024-04-20T03:48:58.494391Z","url":"https://files.pythonhosted.org/packages/43/0b/9f3fcfc2f9778dbbfc1fd68b223e9a91938505ef987e17b93a631bb6b2e4/agentops-0.1.6-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"a6c2b437246ce28bad9c2bbad9a9371f7008f76a979fb19699588212f653daf9","md5":"fc81fd641ad630a17191d4a9cf77193b","sha256":"48ddb49fc01eb83ce151d3f08ae670b3d603c454aa35b4ea145f2dc15e081b36"},"downloads":-1,"filename":"agentops-0.1.6.tar.gz","has_sig":false,"md5_digest":"fc81fd641ad630a17191d4a9cf77193b","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":25792,"upload_time":"2024-04-20T03:48:59","upload_time_iso_8601":"2024-04-20T03:48:59.957150Z","url":"https://files.pythonhosted.org/packages/a6/c2/b437246ce28bad9c2bbad9a9371f7008f76a979fb19699588212f653daf9/agentops-0.1.6.tar.gz","yanked":false,"yanked_reason":null}],"0.1.7":[{"comment_text":"","digests":{"blake2b_256":"1ca529570477f62973c6b835e09dc5bbda7498c1a26ba7a428cdb08a71ae86ca","md5":"a1962d1bb72c6fd00e67e83fe56a3692","sha256":"ce7a9e89dcf17507ee6db85017bef8f87fc4e8a23745f3f73e1fbda5489fb6f9"},"downloads":-1,"filename":"agentops-0.1.7-py3-none-any.whl","has_sig":false,"md5_digest":"a1962d1bb72c6fd00e67e83fe56a3692","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.10","size":27891,"upload_time":"2024-05-03T19:21:38","upload_time_iso_8601":"2024-05-03T19:21:38.018602Z","url":"https://files.pythonhosted.org/packages/1c/a5/29570477f62973c6b835e09dc5bbda7498c1a26ba7a428cdb08a71ae86ca/agentops-0.1.7-py3-none-any.whl","yanked":true,"yanked_reason":"Introduced
        breaking bug"},{"comment_text":"","digests":{"blake2b_256":"b2447ce75e71fcc9605a609b41adc52d517eba4356d15f7ca77d46f683ca07f1","md5":"9a9bb22af4b30c454d46b9a01e8701a0","sha256":"70d22e9a71ea13af6e6ad9c1cffe63c98f9dbccf91bda199825609379b2babaf"},"downloads":-1,"filename":"agentops-0.1.7.tar.gz","has_sig":false,"md5_digest":"9a9bb22af4b30c454d46b9a01e8701a0","packagetype":"sdist","python_version":"source","requires_python":">=3.10","size":28122,"upload_time":"2024-05-03T19:21:39","upload_time_iso_8601":"2024-05-03T19:21:39.415523Z","url":"https://files.pythonhosted.org/packages/b2/44/7ce75e71fcc9605a609b41adc52d517eba4356d15f7ca77d46f683ca07f1/agentops-0.1.7.tar.gz","yanked":true,"yanked_reason":"Introduced
        breaking bug"}],"0.1.8":[{"comment_text":"","digests":{"blake2b_256":"38c63d0d19eeae4c3c9e3ff5957b10c3c16a4a9fd2be6673fbfc965f8bb4fd08","md5":"e12d3d92f51f5b2fed11a01742e5b5b5","sha256":"d49d113028a891d50900bb4fae253218cc49519f7fe39f9ea15f8f2b29d6d7ef"},"downloads":-1,"filename":"agentops-0.1.8-py3-none-any.whl","has_sig":false,"md5_digest":"e12d3d92f51f5b2fed11a01742e5b5b5","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.10","size":27977,"upload_time":"2024-05-04T03:01:53","upload_time_iso_8601":"2024-05-04T03:01:53.905081Z","url":"https://files.pythonhosted.org/packages/38/c6/3d0d19eeae4c3c9e3ff5957b10c3c16a4a9fd2be6673fbfc965f8bb4fd08/agentops-0.1.8-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"9269e51fa1714f169f692e4fad0a42ebeb77c7a27c48f62b751c869ad6441c69","md5":"07dbdb45f9ec086b1bc314d6a8264423","sha256":"5762137a84e2309e1b6ca9a0fd72c8b72c90f6f73ba49549980722221960cac8"},"downloads":-1,"filename":"agentops-0.1.8.tar.gz","has_sig":false,"md5_digest":"07dbdb45f9ec086b1bc314d6a8264423","packagetype":"sdist","python_version":"source","requires_python":">=3.10","size":28189,"upload_time":"2024-05-04T03:01:55","upload_time_iso_8601":"2024-05-04T03:01:55.328668Z","url":"https://files.pythonhosted.org/packages/92/69/e51fa1714f169f692e4fad0a42ebeb77c7a27c48f62b751c869ad6441c69/agentops-0.1.8.tar.gz","yanked":false,"yanked_reason":null}],"0.1.9":[{"comment_text":"","digests":{"blake2b_256":"eb5a920e71729bd1f06b002ee146b38b0d1862357a1f484628e6b20a7d3dcca1","md5":"6ae4929d91c4bb8025edc86b5322630c","sha256":"af7983ba4929b04a34714dd97d7e82c11384ebbe9d7d8bc7b673e1263c4c79a1"},"downloads":-1,"filename":"agentops-0.1.9-py3-none-any.whl","has_sig":false,"md5_digest":"6ae4929d91c4bb8025edc86b5322630c","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":28458,"upload_time":"2024-05-07T07:07:30","upload_time_iso_8601":"2024-05-07T07:07:30.798380Z","url":"https://files.pythonhosted.org/packages/eb/5a/920e71729bd1f06b002ee146b38b0d1862357a1f484628e6b20a7d3dcca1/agentops-0.1.9-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"df2b8fc76d629d8a83b0796612a27b966426550114c930eee5d730654fcd9fe9","md5":"43090632f87cd398ed77b57daa8c28d6","sha256":"7f428bfda2db57a994029b1c9f72b63ca7660616635c9c671b2b729d112a833e"},"downloads":-1,"filename":"agentops-0.1.9.tar.gz","has_sig":false,"md5_digest":"43090632f87cd398ed77b57daa8c28d6","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":28596,"upload_time":"2024-05-07T07:07:35","upload_time_iso_8601":"2024-05-07T07:07:35.242350Z","url":"https://files.pythonhosted.org/packages/df/2b/8fc76d629d8a83b0796612a27b966426550114c930eee5d730654fcd9fe9/agentops-0.1.9.tar.gz","yanked":false,"yanked_reason":null}],"0.2.0":[{"comment_text":"","digests":{"blake2b_256":"483560ec38a81a7e9588d32730ed4f581621169216f968771d5f611388f68a9b","md5":"bdda5480977cccd55628e117e8c8da04","sha256":"bee84bf046c9b4346c5f0f50e2087a992e8d2eae80b3fe9f01c456b49c299bcc"},"downloads":-1,"filename":"agentops-0.2.0-py3-none-any.whl","has_sig":false,"md5_digest":"bdda5480977cccd55628e117e8c8da04","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":35921,"upload_time":"2024-05-28T22:04:14","upload_time_iso_8601":"2024-05-28T22:04:14.813154Z","url":"https://files.pythonhosted.org/packages/48/35/60ec38a81a7e9588d32730ed4f581621169216f968771d5f611388f68a9b/agentops-0.2.0-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"8d7591c79141d31da4e56d6c6a00737b50dcc2f1ce8a711c1293d2a1d70478fc","md5":"71e3c3b9fe0286c9b58d81ba1c12a42d","sha256":"ca340136abff6a3727729c3eda87f0768e5ba2b672ce03320cb52ad138b05598"},"downloads":-1,"filename":"agentops-0.2.0.tar.gz","has_sig":false,"md5_digest":"71e3c3b9fe0286c9b58d81ba1c12a42d","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":35498,"upload_time":"2024-05-28T22:04:16","upload_time_iso_8601":"2024-05-28T22:04:16.598374Z","url":"https://files.pythonhosted.org/packages/8d/75/91c79141d31da4e56d6c6a00737b50dcc2f1ce8a711c1293d2a1d70478fc/agentops-0.2.0.tar.gz","yanked":false,"yanked_reason":null}],"0.2.1":[{"comment_text":"","digests":{"blake2b_256":"fa3b84032b7dca3d7315b329db6681bbfe0872c2a46d62ca992a05f2d6a078e1","md5":"ce3fc46711fa8225a3d6a9566f95f875","sha256":"7dde95db92c8306c0a17e193bfb5ee20e71e16630ccc629db685e148b3aca3f6"},"downloads":-1,"filename":"agentops-0.2.1-py3-none-any.whl","has_sig":false,"md5_digest":"ce3fc46711fa8225a3d6a9566f95f875","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":36375,"upload_time":"2024-06-03T18:40:02","upload_time_iso_8601":"2024-06-03T18:40:02.820700Z","url":"https://files.pythonhosted.org/packages/fa/3b/84032b7dca3d7315b329db6681bbfe0872c2a46d62ca992a05f2d6a078e1/agentops-0.2.1-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"d6286ad330da5736588a54575fde95502006da58c3e9f4f15933f5876c1e1482","md5":"faa972c26a3e59fb6ca04f253165da22","sha256":"9f18a36a79c04e9c06f6e96aefe75f0fb1d08e562873315d6cb945488306e515"},"downloads":-1,"filename":"agentops-0.2.1.tar.gz","has_sig":false,"md5_digest":"faa972c26a3e59fb6ca04f253165da22","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":35784,"upload_time":"2024-06-03T18:40:05","upload_time_iso_8601":"2024-06-03T18:40:05.431174Z","url":"https://files.pythonhosted.org/packages/d6/28/6ad330da5736588a54575fde95502006da58c3e9f4f15933f5876c1e1482/agentops-0.2.1.tar.gz","yanked":false,"yanked_reason":null}],"0.2.2":[{"comment_text":"","digests":{"blake2b_256":"fbe73a57dd30e354b7bcc5a86908fc92aa16378035c69eb225ce254387940b5d","md5":"c24e4656bb6de14ffb9d810fe7872829","sha256":"57aab8a5d76a0dd7b1f0b14e90e778c42444eeaf5c48f2f387719735d7d840ee"},"downloads":-1,"filename":"agentops-0.2.2-py3-none-any.whl","has_sig":false,"md5_digest":"c24e4656bb6de14ffb9d810fe7872829","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":36588,"upload_time":"2024-06-05T19:30:29","upload_time_iso_8601":"2024-06-05T19:30:29.208415Z","url":"https://files.pythonhosted.org/packages/fb/e7/3a57dd30e354b7bcc5a86908fc92aa16378035c69eb225ce254387940b5d/agentops-0.2.2-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"89c51cbd038b9d2898b7f1b05943c338aa4aa9654d7e7763d8fa8d73a25fbfb6","md5":"401bfce001638cc26d7975f6534b5bab","sha256":"d4135c96ad7ec39c81015b3e33dfa977d2d846a685aba0d1922d2d6e3dca7fff"},"downloads":-1,"filename":"agentops-0.2.2.tar.gz","has_sig":false,"md5_digest":"401bfce001638cc26d7975f6534b5bab","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":36012,"upload_time":"2024-06-05T19:30:31","upload_time_iso_8601":"2024-06-05T19:30:31.173781Z","url":"https://files.pythonhosted.org/packages/89/c5/1cbd038b9d2898b7f1b05943c338aa4aa9654d7e7763d8fa8d73a25fbfb6/agentops-0.2.2.tar.gz","yanked":false,"yanked_reason":null}],"0.2.3":[{"comment_text":"","digests":{"blake2b_256":"b66fb36e2bb7158f45b6c496ce3cec50ef861e130cfa3ec8c62e709d63fa9e94","md5":"b3f6a8d97cc0129a9e4730b7810509c6","sha256":"a1829a21301223c26464cbc9da5bfba2f3750e21238912ee1d2f3097c358859a"},"downloads":-1,"filename":"agentops-0.2.3-py3-none-any.whl","has_sig":false,"md5_digest":"b3f6a8d97cc0129a9e4730b7810509c6","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":36986,"upload_time":"2024-06-13T19:56:33","upload_time_iso_8601":"2024-06-13T19:56:33.675807Z","url":"https://files.pythonhosted.org/packages/b6/6f/b36e2bb7158f45b6c496ce3cec50ef861e130cfa3ec8c62e709d63fa9e94/agentops-0.2.3-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"f4d34aed81a4ec4251131b94fb8ed4edf0823922bfda66ba0e4c43d9452111d2","md5":"466abe04d466a950d4bcebbe9c3ccc27","sha256":"b502b83bb4954386a28c4304028ba8cd2b45303f7e1f84720477b521267a3b4e"},"downloads":-1,"filename":"agentops-0.2.3.tar.gz","has_sig":false,"md5_digest":"466abe04d466a950d4bcebbe9c3ccc27","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":37024,"upload_time":"2024-06-13T19:56:35","upload_time_iso_8601":"2024-06-13T19:56:35.481794Z","url":"https://files.pythonhosted.org/packages/f4/d3/4aed81a4ec4251131b94fb8ed4edf0823922bfda66ba0e4c43d9452111d2/agentops-0.2.3.tar.gz","yanked":false,"yanked_reason":null}],"0.2.4":[{"comment_text":"","digests":{"blake2b_256":"a4d4e91fb66bc2eb7effb53f7d9481da04e60809d10240306452a8307aca7985","md5":"f1ba1befb6bd854d5fd6f670937dcb55","sha256":"96162c28cc0391011c04e654273e5a96ec4dcf015e27a7ac12a1ea4077d38950"},"downloads":-1,"filename":"agentops-0.2.4-py3-none-any.whl","has_sig":false,"md5_digest":"f1ba1befb6bd854d5fd6f670937dcb55","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":37518,"upload_time":"2024-06-24T19:31:58","upload_time_iso_8601":"2024-06-24T19:31:58.838680Z","url":"https://files.pythonhosted.org/packages/a4/d4/e91fb66bc2eb7effb53f7d9481da04e60809d10240306452a8307aca7985/agentops-0.2.4-py3-none-any.whl","yanked":true,"yanked_reason":"Potential
        breaking change"},{"comment_text":"","digests":{"blake2b_256":"8e4b920629e08c956cdc74a31ab466d005eb13d86c2d58fa2d2bd261cf36c37b","md5":"527c82f21f01f13b879a1fca90ddb209","sha256":"d263de21eb40e15eb17adc31821fc0dee4ff4ca4501a9feb7ed376d473063208"},"downloads":-1,"filename":"agentops-0.2.4.tar.gz","has_sig":false,"md5_digest":"527c82f21f01f13b879a1fca90ddb209","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":37656,"upload_time":"2024-06-24T19:32:01","upload_time_iso_8601":"2024-06-24T19:32:01.155014Z","url":"https://files.pythonhosted.org/packages/8e/4b/920629e08c956cdc74a31ab466d005eb13d86c2d58fa2d2bd261cf36c37b/agentops-0.2.4.tar.gz","yanked":true,"yanked_reason":"Potential
        breaking change"}],"0.2.5":[{"comment_text":"","digests":{"blake2b_256":"47c73ab9d7d971b664a9bdff6e6464afb6c1de8eb0f845d8de93eb036d5dcc60","md5":"bed576cc1591da4783777920fb223761","sha256":"ff87b82d1efaf50b10624e00c6e9334f4c16ffe08ec7f9889b4417c231c31471"},"downloads":-1,"filename":"agentops-0.2.5-py3-none-any.whl","has_sig":false,"md5_digest":"bed576cc1591da4783777920fb223761","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":37529,"upload_time":"2024-06-26T22:57:15","upload_time_iso_8601":"2024-06-26T22:57:15.646328Z","url":"https://files.pythonhosted.org/packages/47/c7/3ab9d7d971b664a9bdff6e6464afb6c1de8eb0f845d8de93eb036d5dcc60/agentops-0.2.5-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"31c48f2af30ae75dbdb4697506f80f76ce786f79014deb8c6679fa62962fdd6f","md5":"42def99798edfaf201fa6f62846e77c5","sha256":"6bad7aca37af6174307769550a53ec00824049a57e97b8868a9a213b2272adb4"},"downloads":-1,"filename":"agentops-0.2.5.tar.gz","has_sig":false,"md5_digest":"42def99798edfaf201fa6f62846e77c5","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":37703,"upload_time":"2024-06-26T22:57:17","upload_time_iso_8601":"2024-06-26T22:57:17.337904Z","url":"https://files.pythonhosted.org/packages/31/c4/8f2af30ae75dbdb4697506f80f76ce786f79014deb8c6679fa62962fdd6f/agentops-0.2.5.tar.gz","yanked":false,"yanked_reason":null}],"0.2.6":[{"comment_text":"","digests":{"blake2b_256":"5af2f90538b00d887c04a5570e8a3af4aef27a600a67c058a0ee6befafd60748","md5":"8ef3ed13ed582346b71648ca9df30f7c","sha256":"59e88000a9f108931fd68056f22def7a7f4b3015906de5791e777c23ba7dee52"},"downloads":-1,"filename":"agentops-0.2.6-py3-none-any.whl","has_sig":false,"md5_digest":"8ef3ed13ed582346b71648ca9df30f7c","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":37534,"upload_time":"2024-06-28T21:41:56","upload_time_iso_8601":"2024-06-28T21:41:56.933334Z","url":"https://files.pythonhosted.org/packages/5a/f2/f90538b00d887c04a5570e8a3af4aef27a600a67c058a0ee6befafd60748/agentops-0.2.6-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"bcf412c388dccc301ad54a501843ba5b5dd359575dcef9ac24c18a619a32214d","md5":"89a6b04f12801682b53ee0133593ce74","sha256":"7906a08c9154355484deb173b82631f9acddec3775b2d5e8ca946abdee27183b"},"downloads":-1,"filename":"agentops-0.2.6.tar.gz","has_sig":false,"md5_digest":"89a6b04f12801682b53ee0133593ce74","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":37874,"upload_time":"2024-06-28T21:41:59","upload_time_iso_8601":"2024-06-28T21:41:59.143953Z","url":"https://files.pythonhosted.org/packages/bc/f4/12c388dccc301ad54a501843ba5b5dd359575dcef9ac24c18a619a32214d/agentops-0.2.6.tar.gz","yanked":false,"yanked_reason":null}],"0.3.0":[{"comment_text":"","digests":{"blake2b_256":"b8e996f12ac457f46c370c6f70f344e975d534f2c92853703ee29802f0127024","md5":"d9c6995a843b49ac7eb6f500fa1f3c2a","sha256":"22aeb3355e66b32a2b2a9f676048b81979b2488feddb088f9266034b3ed50539"},"downloads":-1,"filename":"agentops-0.3.0-py3-none-any.whl","has_sig":false,"md5_digest":"d9c6995a843b49ac7eb6f500fa1f3c2a","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":39430,"upload_time":"2024-07-17T18:38:24","upload_time_iso_8601":"2024-07-17T18:38:24.763919Z","url":"https://files.pythonhosted.org/packages/b8/e9/96f12ac457f46c370c6f70f344e975d534f2c92853703ee29802f0127024/agentops-0.3.0-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"7e2d6fda9613562c0394d7ef3dd8f0cb9fc4ebaa8d413862fce33940c73564d6","md5":"8fa67ca01ca726e3bfcd66898313f33f","sha256":"6c0c08a57410fa5e826a7bafa1deeba9f7b3524709427d9e1abbd0964caaf76b"},"downloads":-1,"filename":"agentops-0.3.0.tar.gz","has_sig":false,"md5_digest":"8fa67ca01ca726e3bfcd66898313f33f","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":41734,"upload_time":"2024-07-17T18:38:26","upload_time_iso_8601":"2024-07-17T18:38:26.447237Z","url":"https://files.pythonhosted.org/packages/7e/2d/6fda9613562c0394d7ef3dd8f0cb9fc4ebaa8d413862fce33940c73564d6/agentops-0.3.0.tar.gz","yanked":false,"yanked_reason":null}],"0.3.10":[{"comment_text":"","digests":{"blake2b_256":"eb5e3ac36b33d3e95747d64effd509f66a9b3b76b47216b16f492e27d8d90b0c","md5":"6fade0b81fc65b2c79a869b5f240590b","sha256":"b304d366691281e08c1f02307aabdd551ae4f68b0de82bbbb4cf6f651af2dd16"},"downloads":-1,"filename":"agentops-0.3.10-py3-none-any.whl","has_sig":false,"md5_digest":"6fade0b81fc65b2c79a869b5f240590b","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":41201,"upload_time":"2024-08-19T20:51:49","upload_time_iso_8601":"2024-08-19T20:51:49.487947Z","url":"https://files.pythonhosted.org/packages/eb/5e/3ac36b33d3e95747d64effd509f66a9b3b76b47216b16f492e27d8d90b0c/agentops-0.3.10-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"8367ca0cb01df6b529f0127d23ec661e92c95ff68faf544439d86ec2331f3a52","md5":"639da9c2a3381cb3f62812bfe48a5e57","sha256":"40f895019f29bc5a6c023110cbec32870e5edb3e3926f8100974db8d3e299e2a"},"downloads":-1,"filename":"agentops-0.3.10.tar.gz","has_sig":false,"md5_digest":"639da9c2a3381cb3f62812bfe48a5e57","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":45332,"upload_time":"2024-08-19T20:51:50","upload_time_iso_8601":"2024-08-19T20:51:50.714217Z","url":"https://files.pythonhosted.org/packages/83/67/ca0cb01df6b529f0127d23ec661e92c95ff68faf544439d86ec2331f3a52/agentops-0.3.10.tar.gz","yanked":false,"yanked_reason":null}],"0.3.11":[{"comment_text":"","digests":{"blake2b_256":"0b078e6a74f084463def9d79d2c84d79475adc0229bbfb2e57401b0616ba6d6a","md5":"e760d867d9431d1bc13798024237ab99","sha256":"75fe10b8fc86c7f5c2633139ac1c06959611f22434fc1aaa8688c3c223fde8b5"},"downloads":-1,"filename":"agentops-0.3.11-py3-none-any.whl","has_sig":false,"md5_digest":"e760d867d9431d1bc13798024237ab99","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":50252,"upload_time":"2024-09-17T21:57:23","upload_time_iso_8601":"2024-09-17T21:57:23.085964Z","url":"https://files.pythonhosted.org/packages/0b/07/8e6a74f084463def9d79d2c84d79475adc0229bbfb2e57401b0616ba6d6a/agentops-0.3.11-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"3746057c552ea7ded5c954bdcbaf8a7dca07b6109633e040bf33de5f97a1289b","md5":"3b661fb76d343ec3bdef5b70fc9e5cc3","sha256":"38a2ffeeac1d722cb72c32d70e1c840424902b57934c647ef10de15478fe8f27"},"downloads":-1,"filename":"agentops-0.3.11.tar.gz","has_sig":false,"md5_digest":"3b661fb76d343ec3bdef5b70fc9e5cc3","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":48018,"upload_time":"2024-09-17T21:57:24","upload_time_iso_8601":"2024-09-17T21:57:24.699442Z","url":"https://files.pythonhosted.org/packages/37/46/057c552ea7ded5c954bdcbaf8a7dca07b6109633e040bf33de5f97a1289b/agentops-0.3.11.tar.gz","yanked":false,"yanked_reason":null}],"0.3.12":[{"comment_text":"","digests":{"blake2b_256":"ac0a9004d7a8c2865ed804ddd6968095ef100ac554bc51ada7a2f3c0b4e9142b","md5":"be18cdad4333c6013d9584b84b4c7875","sha256":"4767def30de5dd97397728efcb50398a4f6d6823c1b534846f0a9b0cb85a6d45"},"downloads":-1,"filename":"agentops-0.3.12-py3-none-any.whl","has_sig":false,"md5_digest":"be18cdad4333c6013d9584b84b4c7875","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":50794,"upload_time":"2024-09-23T19:30:49","upload_time_iso_8601":"2024-09-23T19:30:49.050650Z","url":"https://files.pythonhosted.org/packages/ac/0a/9004d7a8c2865ed804ddd6968095ef100ac554bc51ada7a2f3c0b4e9142b/agentops-0.3.12-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"2c6d4f640d9fadd22f8cd7cb9857eed1f56d422f11b130ba226b947454eb0f0b","md5":"91aa981d4199ac73b4d7407547667e2f","sha256":"11ce3048656b5d146d02a4890dd50c8d2801ca5ad5caccab17d573cd8eea6e83"},"downloads":-1,"filename":"agentops-0.3.12.tar.gz","has_sig":false,"md5_digest":"91aa981d4199ac73b4d7407547667e2f","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":48525,"upload_time":"2024-09-23T19:30:50","upload_time_iso_8601":"2024-09-23T19:30:50.568151Z","url":"https://files.pythonhosted.org/packages/2c/6d/4f640d9fadd22f8cd7cb9857eed1f56d422f11b130ba226b947454eb0f0b/agentops-0.3.12.tar.gz","yanked":false,"yanked_reason":null}],"0.3.13":[{"comment_text":"","digests":{"blake2b_256":"68efa3b8adc0de2e7daa1e6e2734af9a0e37c90e3346b8a804e3fdc322c82b6c","md5":"948e9278dfc02e1a6ba2ec563296779a","sha256":"81bfdfedd990fbc3064ee42a67422ddbee07b6cd96c5fca7e124eb8c1e0cebdc"},"downloads":-1,"filename":"agentops-0.3.13-py3-none-any.whl","has_sig":false,"md5_digest":"948e9278dfc02e1a6ba2ec563296779a","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":50813,"upload_time":"2024-10-02T18:32:59","upload_time_iso_8601":"2024-10-02T18:32:59.208892Z","url":"https://files.pythonhosted.org/packages/68/ef/a3b8adc0de2e7daa1e6e2734af9a0e37c90e3346b8a804e3fdc322c82b6c/agentops-0.3.13-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"3511fb06b4cee96285a5f745809d0f4efddef70d2a82112a633ed53834d6fc64","md5":"27a923eaceb4ae35abe2cf1aed1b8241","sha256":"319b7325fb79004ce996191aa21f0982489be22cc1acc2f3f6d02cdff1db2429"},"downloads":-1,"filename":"agentops-0.3.13.tar.gz","has_sig":false,"md5_digest":"27a923eaceb4ae35abe2cf1aed1b8241","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":48559,"upload_time":"2024-10-02T18:33:00","upload_time_iso_8601":"2024-10-02T18:33:00.614409Z","url":"https://files.pythonhosted.org/packages/35/11/fb06b4cee96285a5f745809d0f4efddef70d2a82112a633ed53834d6fc64/agentops-0.3.13.tar.gz","yanked":false,"yanked_reason":null}],"0.3.14":[{"comment_text":"","digests":{"blake2b_256":"1c2775ab5bf99341a6a02775e3858f54a18cbcda0f35b5c6c0f114a829d62b8e","md5":"ad2d676d293c4baa1f9afecc61654e50","sha256":"f4a2fcf1a7caf1d5383bfb66d8a9d567f3cb88fc7495cfd81ade167b0c06a4ea"},"downloads":-1,"filename":"agentops-0.3.14-py3-none-any.whl","has_sig":false,"md5_digest":"ad2d676d293c4baa1f9afecc61654e50","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":50825,"upload_time":"2024-10-14T23:53:48","upload_time_iso_8601":"2024-10-14T23:53:48.464714Z","url":"https://files.pythonhosted.org/packages/1c/27/75ab5bf99341a6a02775e3858f54a18cbcda0f35b5c6c0f114a829d62b8e/agentops-0.3.14-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"46cb183fdaf40ae97ac1806ba91f6f23d55dc0a1a5cdf0881a5c834c8ca7175a","md5":"b90053253770c8e1c385b18e7172d58f","sha256":"fcb515e5743d73efee851b687692bed74797dc88e29a8327b2bbfb21d73a7447"},"downloads":-1,"filename":"agentops-0.3.14.tar.gz","has_sig":false,"md5_digest":"b90053253770c8e1c385b18e7172d58f","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":48548,"upload_time":"2024-10-14T23:53:50","upload_time_iso_8601":"2024-10-14T23:53:50.306080Z","url":"https://files.pythonhosted.org/packages/46/cb/183fdaf40ae97ac1806ba91f6f23d55dc0a1a5cdf0881a5c834c8ca7175a/agentops-0.3.14.tar.gz","yanked":false,"yanked_reason":null}],"0.3.15":[{"comment_text":"","digests":{"blake2b_256":"eadebed95f173bd304abe219b2b0a6f4e1f8e38b6733b19f2444a30fe2e731e1","md5":"7a46ccd127ffcd52eff26edaf5721bd9","sha256":"d5617108bbd9871a4250415f4e536ba33c2a6a2d2bec9342046303fb9e839f9d"},"downloads":-1,"filename":"agentops-0.3.15-py3-none-any.whl","has_sig":false,"md5_digest":"7a46ccd127ffcd52eff26edaf5721bd9","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":55349,"upload_time":"2024-11-09T01:18:40","upload_time_iso_8601":"2024-11-09T01:18:40.622134Z","url":"https://files.pythonhosted.org/packages/ea/de/bed95f173bd304abe219b2b0a6f4e1f8e38b6733b19f2444a30fe2e731e1/agentops-0.3.15-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"33a40ef511dc3f23bba2d345b464223b1e7acc3c2a29230a93abb8fbcb6faebf","md5":"7af7abcf01e8d3ef64ac287e9300528f","sha256":"4358f85929d55929002cae589323d36b68fc4d12d0ea5010a80bfc4c7addc0ec"},"downloads":-1,"filename":"agentops-0.3.15.tar.gz","has_sig":false,"md5_digest":"7af7abcf01e8d3ef64ac287e9300528f","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":51296,"upload_time":"2024-11-09T01:18:42","upload_time_iso_8601":"2024-11-09T01:18:42.358185Z","url":"https://files.pythonhosted.org/packages/33/a4/0ef511dc3f23bba2d345b464223b1e7acc3c2a29230a93abb8fbcb6faebf/agentops-0.3.15.tar.gz","yanked":false,"yanked_reason":null}],"0.3.15rc1":[{"comment_text":"","digests":{"blake2b_256":"0978ac2f89ccb7b3a31742f5b70434953faff168da6cab67c0836f432919c762","md5":"7f805adf76594ac4bc169b1a111817f4","sha256":"86069387a265bc6c5fa00ffbb3f8a131254a51ee3a9b8b35af4aca823dee76f1"},"downloads":-1,"filename":"agentops-0.3.15rc1-py3-none-any.whl","has_sig":false,"md5_digest":"7f805adf76594ac4bc169b1a111817f4","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":50798,"upload_time":"2024-10-31T04:36:11","upload_time_iso_8601":"2024-10-31T04:36:11.059082Z","url":"https://files.pythonhosted.org/packages/09/78/ac2f89ccb7b3a31742f5b70434953faff168da6cab67c0836f432919c762/agentops-0.3.15rc1-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"4317d6950ad32c33317509ea05a64d01ab661515165ffbd4e120148826b69ffb","md5":"5f131294c10c9b60b33ec93edc106f4f","sha256":"897ab94ae4fca8f1711216f9317dbf6f14e5d018c866086ef0b8831dc125e4ad"},"downloads":-1,"filename":"agentops-0.3.15rc1.tar.gz","has_sig":false,"md5_digest":"5f131294c10c9b60b33ec93edc106f4f","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":48739,"upload_time":"2024-10-31T04:36:12","upload_time_iso_8601":"2024-10-31T04:36:12.630857Z","url":"https://files.pythonhosted.org/packages/43/17/d6950ad32c33317509ea05a64d01ab661515165ffbd4e120148826b69ffb/agentops-0.3.15rc1.tar.gz","yanked":false,"yanked_reason":null}],"0.3.16":[{"comment_text":"","digests":{"blake2b_256":"b876e1c933480ec9ad093a841321e5c9f7f16a0af59f339ba2c840851b1af01d","md5":"d57593bb32704fae1163656f03355a71","sha256":"7763e65efe053fa81cea2a2e16f015c7603365280972e0c0709eec32c3c8569e"},"downloads":-1,"filename":"agentops-0.3.16-py3-none-any.whl","has_sig":false,"md5_digest":"d57593bb32704fae1163656f03355a71","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":55351,"upload_time":"2024-11-09T18:44:21","upload_time_iso_8601":"2024-11-09T18:44:21.626158Z","url":"https://files.pythonhosted.org/packages/b8/76/e1c933480ec9ad093a841321e5c9f7f16a0af59f339ba2c840851b1af01d/agentops-0.3.16-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"aa748e77e654b37a5e0c977eca4f7e92740c1e24be39c827815e7bd8da429003","md5":"23078e1dc78ef459a667feeb904345c1","sha256":"564163eb048939d64e848c7e6caf25d6c0aee31200623ef97efe492f090f8939"},"downloads":-1,"filename":"agentops-0.3.16.tar.gz","has_sig":false,"md5_digest":"23078e1dc78ef459a667feeb904345c1","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":51308,"upload_time":"2024-11-09T18:44:23","upload_time_iso_8601":"2024-11-09T18:44:23.037514Z","url":"https://files.pythonhosted.org/packages/aa/74/8e77e654b37a5e0c977eca4f7e92740c1e24be39c827815e7bd8da429003/agentops-0.3.16.tar.gz","yanked":false,"yanked_reason":null}],"0.3.17":[{"comment_text":"","digests":{"blake2b_256":"6c3038a659671eec20fcae759bd69655ec45b08c4e875627b33e3b05bd46f299","md5":"93bbe3bd4ee492e7e73780c07897b017","sha256":"0d24dd082270a76c98ad0391101d5b5c3d01e389c5032389ecd551285e4b0662"},"downloads":-1,"filename":"agentops-0.3.17-py3-none-any.whl","has_sig":false,"md5_digest":"93bbe3bd4ee492e7e73780c07897b017","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":55503,"upload_time":"2024-11-10T02:39:28","upload_time_iso_8601":"2024-11-10T02:39:28.884052Z","url":"https://files.pythonhosted.org/packages/6c/30/38a659671eec20fcae759bd69655ec45b08c4e875627b33e3b05bd46f299/agentops-0.3.17-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"2131d9a3747df04b7915ee1cffaa4a5636f8ed0e1385e5236b0da085ccce936a","md5":"49e8cf186203cadaa39301c4ce5fda42","sha256":"a893cc7c37eda720ab59e8facaa2774cc23d125648aa00539ae485ff592e8b77"},"downloads":-1,"filename":"agentops-0.3.17.tar.gz","has_sig":false,"md5_digest":"49e8cf186203cadaa39301c4ce5fda42","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":51469,"upload_time":"2024-11-10T02:39:30","upload_time_iso_8601":"2024-11-10T02:39:30.636907Z","url":"https://files.pythonhosted.org/packages/21/31/d9a3747df04b7915ee1cffaa4a5636f8ed0e1385e5236b0da085ccce936a/agentops-0.3.17.tar.gz","yanked":false,"yanked_reason":null}],"0.3.18":[{"comment_text":"","digests":{"blake2b_256":"978dbd4cad95dad722dc2d3e4179feab1058ef846828c0e15e51e8bfaea373ee","md5":"d9afc3636cb969c286738ce02ed12196","sha256":"8b48d8a1662f276653430fd541c77fa4f9a15a43e881b518ff88ea56925afcf7"},"downloads":-1,"filename":"agentops-0.3.18-py3-none-any.whl","has_sig":false,"md5_digest":"d9afc3636cb969c286738ce02ed12196","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":58032,"upload_time":"2024-11-19T19:06:19","upload_time_iso_8601":"2024-11-19T19:06:19.068511Z","url":"https://files.pythonhosted.org/packages/97/8d/bd4cad95dad722dc2d3e4179feab1058ef846828c0e15e51e8bfaea373ee/agentops-0.3.18-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"c55246bb2f29b9e5f2e1d8b124296b7794934a9048de635d9e7d6a95e791ad7b","md5":"02a4fc081499360aac58485a94a6ca33","sha256":"4d509754df7be52579597cc9f53939c5218131a0379463e0ff6f6f40cde9fcc4"},"downloads":-1,"filename":"agentops-0.3.18.tar.gz","has_sig":false,"md5_digest":"02a4fc081499360aac58485a94a6ca33","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":55394,"upload_time":"2024-11-19T19:06:21","upload_time_iso_8601":"2024-11-19T19:06:21.306448Z","url":"https://files.pythonhosted.org/packages/c5/52/46bb2f29b9e5f2e1d8b124296b7794934a9048de635d9e7d6a95e791ad7b/agentops-0.3.18.tar.gz","yanked":false,"yanked_reason":null}],"0.3.19":[{"comment_text":"","digests":{"blake2b_256":"fc1e48616d2db40717d560a561e13521009655d447388f944f12f2b3811e6d7d","md5":"a9e23f1d31821585017e97633b058233","sha256":"1888a47dd3d9b92c5f246cdeeab333def5acbd26833d3148c63e8793457405b3"},"downloads":-1,"filename":"agentops-0.3.19-py3-none-any.whl","has_sig":false,"md5_digest":"a9e23f1d31821585017e97633b058233","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":38648,"upload_time":"2024-12-04T00:54:00","upload_time_iso_8601":"2024-12-04T00:54:00.173948Z","url":"https://files.pythonhosted.org/packages/fc/1e/48616d2db40717d560a561e13521009655d447388f944f12f2b3811e6d7d/agentops-0.3.19-py3-none-any.whl","yanked":true,"yanked_reason":"Broken
        dependency, please install 0.3.18"},{"comment_text":"","digests":{"blake2b_256":"b319bb0e9895cb6da29f764f8d7b95b10ac8fde400bc17028f9bd486e9574dbe","md5":"f6424c41464d438007e9628748a0bea6","sha256":"ca0d4ba35ae699169ae20f74f72ca6a5780a8768ba2a2c32589fc5292ed81674"},"downloads":-1,"filename":"agentops-0.3.19.tar.gz","has_sig":false,"md5_digest":"f6424c41464d438007e9628748a0bea6","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":48360,"upload_time":"2024-12-04T00:54:01","upload_time_iso_8601":"2024-12-04T00:54:01.418776Z","url":"https://files.pythonhosted.org/packages/b3/19/bb0e9895cb6da29f764f8d7b95b10ac8fde400bc17028f9bd486e9574dbe/agentops-0.3.19.tar.gz","yanked":true,"yanked_reason":"Broken
        dependency, please install 0.3.18"}],"0.3.2":[{"comment_text":"","digests":{"blake2b_256":"9d2c23b745a61d48df788b8020e5ea37e94f9da59b322a17accafe18d8cb4006","md5":"62d576d9518a627fe4232709c0721eff","sha256":"b35988e04378624204572bb3d7a454094f879ea573f05b57d4e75ab0bfbb82af"},"downloads":-1,"filename":"agentops-0.3.2-py3-none-any.whl","has_sig":false,"md5_digest":"62d576d9518a627fe4232709c0721eff","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":39527,"upload_time":"2024-07-21T03:09:56","upload_time_iso_8601":"2024-07-21T03:09:56.844372Z","url":"https://files.pythonhosted.org/packages/9d/2c/23b745a61d48df788b8020e5ea37e94f9da59b322a17accafe18d8cb4006/agentops-0.3.2-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"d2a1cc21406646c065e83435fe30fa205b99b2204d8074eca31926a5f8ef4381","md5":"30b247bcae25b181485a89213518241c","sha256":"55559ac4a43634831dfa8937c2597c28e332809dc7c6bb3bc3c8b233442e224c"},"downloads":-1,"filename":"agentops-0.3.2.tar.gz","has_sig":false,"md5_digest":"30b247bcae25b181485a89213518241c","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":41894,"upload_time":"2024-07-21T03:09:58","upload_time_iso_8601":"2024-07-21T03:09:58.409826Z","url":"https://files.pythonhosted.org/packages/d2/a1/cc21406646c065e83435fe30fa205b99b2204d8074eca31926a5f8ef4381/agentops-0.3.2.tar.gz","yanked":false,"yanked_reason":null}],"0.3.20":[{"comment_text":"","digests":{"blake2b_256":"a854ae9147a490dd9bd03ab7bfc5af47f40ff675840a9aa143896b385a8f8d3a","md5":"a13af8737ddff8a0c7c0f05cee70085f","sha256":"b5396e11b0bfef46b85604e8e36ab17668057711edd56f1edb0a067b8676fdcc"},"downloads":-1,"filename":"agentops-0.3.20-py3-none-any.whl","has_sig":false,"md5_digest":"a13af8737ddff8a0c7c0f05cee70085f","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":38674,"upload_time":"2024-12-07T00:06:31","upload_time_iso_8601":"2024-12-07T00:06:31.901162Z","url":"https://files.pythonhosted.org/packages/a8/54/ae9147a490dd9bd03ab7bfc5af47f40ff675840a9aa143896b385a8f8d3a/agentops-0.3.20-py3-none-any.whl","yanked":true,"yanked_reason":"Wrong
        release"},{"comment_text":"","digests":{"blake2b_256":"c1eb19d04c801854ba75e235eb87c51a6a9c5b1a89e8579cb745c83f8bf84e08","md5":"11754497191d8340eda7a831720d9b74","sha256":"c71406294804a82795310a4afc492064a8884b1ba47e12607230975bc1291ce3"},"downloads":-1,"filename":"agentops-0.3.20.tar.gz","has_sig":false,"md5_digest":"11754497191d8340eda7a831720d9b74","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":48332,"upload_time":"2024-12-07T00:06:33","upload_time_iso_8601":"2024-12-07T00:06:33.568362Z","url":"https://files.pythonhosted.org/packages/c1/eb/19d04c801854ba75e235eb87c51a6a9c5b1a89e8579cb745c83f8bf84e08/agentops-0.3.20.tar.gz","yanked":true,"yanked_reason":"Wrong
        release"}],"0.3.20rc1":[{"comment_text":"","digests":{"blake2b_256":"073de7eba58e2a60c0136eee2760b20f99607001d372de26505feee891e0976b","md5":"73c6ac515ee9d555e27a7ba7e26e3a46","sha256":"079ea8138938e27a3e1319a235a6f4cf98c0d6846731d854aa83b8422d570bda"},"downloads":-1,"filename":"agentops-0.3.20rc1-py3-none-any.whl","has_sig":false,"md5_digest":"73c6ac515ee9d555e27a7ba7e26e3a46","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":38718,"upload_time":"2024-12-07T00:10:18","upload_time_iso_8601":"2024-12-07T00:10:18.796963Z","url":"https://files.pythonhosted.org/packages/07/3d/e7eba58e2a60c0136eee2760b20f99607001d372de26505feee891e0976b/agentops-0.3.20rc1-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"02ff111d618c21aad946caedb666030f1f374a0d558228b9061ea2b46acb6bcd","md5":"17062e985b931dc85b4855922d7842ce","sha256":"ef48447e07a3eded246b2f7e10bba74422a34563ffdc667ac16b2d3383475a3f"},"downloads":-1,"filename":"agentops-0.3.20rc1.tar.gz","has_sig":false,"md5_digest":"17062e985b931dc85b4855922d7842ce","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":48329,"upload_time":"2024-12-07T00:10:20","upload_time_iso_8601":"2024-12-07T00:10:20.510407Z","url":"https://files.pythonhosted.org/packages/02/ff/111d618c21aad946caedb666030f1f374a0d558228b9061ea2b46acb6bcd/agentops-0.3.20rc1.tar.gz","yanked":false,"yanked_reason":null}],"0.3.20rc10":[{"comment_text":"","digests":{"blake2b_256":"a7274706d8d9c8f4abecc1dda2b9b02cd02ffe895220bd39f58322a46ccc7254","md5":"2c66a93c691c6b8cac2f2dc8fab9efae","sha256":"3c10d77f2fe88b61d97ad007820c1ba968c62f692986ea2b2cbfd8b22ec9e5bc"},"downloads":-1,"filename":"agentops-0.3.20rc10-py3-none-any.whl","has_sig":false,"md5_digest":"2c66a93c691c6b8cac2f2dc8fab9efae","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":57423,"upload_time":"2024-12-10T03:41:04","upload_time_iso_8601":"2024-12-10T03:41:04.579814Z","url":"https://files.pythonhosted.org/packages/a7/27/4706d8d9c8f4abecc1dda2b9b02cd02ffe895220bd39f58322a46ccc7254/agentops-0.3.20rc10-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"efe9e304f465945f57e4c6d35cd35fff53dc2a2e36b9b32793fa57017467b0c2","md5":"9882d32866b94d925ba36ac376c30bea","sha256":"f0c72c20e7fe41054c22c6257420314863549dd91428a892ac9b47b81cdfcc8c"},"downloads":-1,"filename":"agentops-0.3.20rc10.tar.gz","has_sig":false,"md5_digest":"9882d32866b94d925ba36ac376c30bea","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":57564,"upload_time":"2024-12-10T03:41:06","upload_time_iso_8601":"2024-12-10T03:41:06.899043Z","url":"https://files.pythonhosted.org/packages/ef/e9/e304f465945f57e4c6d35cd35fff53dc2a2e36b9b32793fa57017467b0c2/agentops-0.3.20rc10.tar.gz","yanked":false,"yanked_reason":null}],"0.3.20rc11":[{"comment_text":"","digests":{"blake2b_256":"8dbf598ec2532b713a228f4041c9b2c10358cd43e6aecf6128d0988a0b5f103e","md5":"d9ab67a850aefcb5bf9467b48f74675d","sha256":"3e5d4c19de6c58ae684693f47a2f03db35eaf4cd6d8aafc1e804a134462c2b55"},"downloads":-1,"filename":"agentops-0.3.20rc11-py3-none-any.whl","has_sig":false,"md5_digest":"d9ab67a850aefcb5bf9467b48f74675d","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":60280,"upload_time":"2024-12-10T22:45:05","upload_time_iso_8601":"2024-12-10T22:45:05.280119Z","url":"https://files.pythonhosted.org/packages/8d/bf/598ec2532b713a228f4041c9b2c10358cd43e6aecf6128d0988a0b5f103e/agentops-0.3.20rc11-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"210642e51fff6a4537fb811a15bc22d00343145285c6246dc069433d61436e1b","md5":"ca5279f4cb6ad82e06ef542a2d08d06e","sha256":"9211489c6a01bc9cda4061826f8b80d0989cfcd7fbabe1dd2ed5a5cb76b3d6f0"},"downloads":-1,"filename":"agentops-0.3.20rc11.tar.gz","has_sig":false,"md5_digest":"ca5279f4cb6ad82e06ef542a2d08d06e","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":59718,"upload_time":"2024-12-10T22:45:09","upload_time_iso_8601":"2024-12-10T22:45:09.616947Z","url":"https://files.pythonhosted.org/packages/21/06/42e51fff6a4537fb811a15bc22d00343145285c6246dc069433d61436e1b/agentops-0.3.20rc11.tar.gz","yanked":false,"yanked_reason":null}],"0.3.20rc12":[{"comment_text":"","digests":{"blake2b_256":"dc281db6f49f10ac849683de1d7f5b5ef492be2a996325302167b8388f375d51","md5":"8b2611d2510f0d4fac7ab824d7658ff7","sha256":"9237652d28db89315c49c0705829b291c17280e07d41272f909e2609acec650b"},"downloads":-1,"filename":"agentops-0.3.20rc12-py3-none-any.whl","has_sig":false,"md5_digest":"8b2611d2510f0d4fac7ab824d7658ff7","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":60282,"upload_time":"2024-12-10T23:10:54","upload_time_iso_8601":"2024-12-10T23:10:54.516317Z","url":"https://files.pythonhosted.org/packages/dc/28/1db6f49f10ac849683de1d7f5b5ef492be2a996325302167b8388f375d51/agentops-0.3.20rc12-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"10c073cb9a55592f55bb44c9206f50f41d7b7a8a8d6fd67d42f40c8f9f184b0e","md5":"02b3a68f3491564af2e29f0f216eea1e","sha256":"d4d3a73ac34b2a00edb6e6b5b220cbb031bb76ff58d85e2096b536be24aee4fe"},"downloads":-1,"filename":"agentops-0.3.20rc12.tar.gz","has_sig":false,"md5_digest":"02b3a68f3491564af2e29f0f216eea1e","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":59731,"upload_time":"2024-12-10T23:10:56","upload_time_iso_8601":"2024-12-10T23:10:56.822803Z","url":"https://files.pythonhosted.org/packages/10/c0/73cb9a55592f55bb44c9206f50f41d7b7a8a8d6fd67d42f40c8f9f184b0e/agentops-0.3.20rc12.tar.gz","yanked":false,"yanked_reason":null}],"0.3.20rc13":[{"comment_text":"","digests":{"blake2b_256":"4ed48a97563074235f266281167c70ab90833c195e2b704087e414509ae3ec32","md5":"c86fe22044483f94bc044a3bf7b054b7","sha256":"2fbb3b55701d9aea64f622e7e29aa417772e897e2414f74ed3954d99009d224f"},"downloads":-1,"filename":"agentops-0.3.20rc13-py3-none-any.whl","has_sig":false,"md5_digest":"c86fe22044483f94bc044a3bf7b054b7","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":64724,"upload_time":"2024-12-10T23:27:50","upload_time_iso_8601":"2024-12-10T23:27:50.895316Z","url":"https://files.pythonhosted.org/packages/4e/d4/8a97563074235f266281167c70ab90833c195e2b704087e414509ae3ec32/agentops-0.3.20rc13-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"767e59c6f34e9a067d9152021de7e3146e5c0f69f36434dcb3026ff03f382489","md5":"152a70647d5ff28fe851e4cc406d8fb4","sha256":"b7a6d1d7f603bbb2605cc747762ae866bdee53941c4c76087c9f0f0a5efad03b"},"downloads":-1,"filename":"agentops-0.3.20rc13.tar.gz","has_sig":false,"md5_digest":"152a70647d5ff28fe851e4cc406d8fb4","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":63242,"upload_time":"2024-12-10T23:27:53","upload_time_iso_8601":"2024-12-10T23:27:53.657606Z","url":"https://files.pythonhosted.org/packages/76/7e/59c6f34e9a067d9152021de7e3146e5c0f69f36434dcb3026ff03f382489/agentops-0.3.20rc13.tar.gz","yanked":false,"yanked_reason":null}],"0.3.20rc2":[{"comment_text":"","digests":{"blake2b_256":"cebbbca58531e21f4c1c92cbe6ba15d0f308ff8f3b27083cd0ce6358c7d1d117","md5":"5a9fcd99e0b6e3b24e721b22c3ee5907","sha256":"ada95d42e82abef16c1e83443dc42d02bb470ee48b1fa8f2d58a20703511a7be"},"downloads":-1,"filename":"agentops-0.3.20rc2-py3-none-any.whl","has_sig":false,"md5_digest":"5a9fcd99e0b6e3b24e721b22c3ee5907","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":38716,"upload_time":"2024-12-07T00:20:01","upload_time_iso_8601":"2024-12-07T00:20:01.561074Z","url":"https://files.pythonhosted.org/packages/ce/bb/bca58531e21f4c1c92cbe6ba15d0f308ff8f3b27083cd0ce6358c7d1d117/agentops-0.3.20rc2-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"124aec14492566949b7383ae321cb40c1edc18940712b277c08d32392566f7a8","md5":"ff8db0075584474e35784b080fb9b6b1","sha256":"60462b82390e78fd21312c5db45f0f48dfcc9c9ab354e6bf232db557ccf57c13"},"downloads":-1,"filename":"agentops-0.3.20rc2.tar.gz","has_sig":false,"md5_digest":"ff8db0075584474e35784b080fb9b6b1","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":48341,"upload_time":"2024-12-07T00:20:02","upload_time_iso_8601":"2024-12-07T00:20:02.519240Z","url":"https://files.pythonhosted.org/packages/12/4a/ec14492566949b7383ae321cb40c1edc18940712b277c08d32392566f7a8/agentops-0.3.20rc2.tar.gz","yanked":false,"yanked_reason":null}],"0.3.20rc4":[{"comment_text":"","digests":{"blake2b_256":"a1551125b2b3823fcb3f3afa3c6b9621541799ac329622ee21038babbfbedf39","md5":"a82f1b73347d3a2fe33f31cec01ca376","sha256":"72253950b46a11b5b1163b13bbb9d5b769e6cdb7b102acf46efac8cf02f7eaac"},"downloads":-1,"filename":"agentops-0.3.20rc4-py3-none-any.whl","has_sig":false,"md5_digest":"a82f1b73347d3a2fe33f31cec01ca376","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":38719,"upload_time":"2024-12-07T00:53:45","upload_time_iso_8601":"2024-12-07T00:53:45.212239Z","url":"https://files.pythonhosted.org/packages/a1/55/1125b2b3823fcb3f3afa3c6b9621541799ac329622ee21038babbfbedf39/agentops-0.3.20rc4-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"a180420ef26926052b12d1c2010360b4037f6765321055ce7e09c6bfaeac3480","md5":"1a314ff81d87a774e5e1cf338151a353","sha256":"4218fcfa42644dd86ee50ac7806d08783e4629db30b127bc8011c9c3523eeb5c"},"downloads":-1,"filename":"agentops-0.3.20rc4.tar.gz","has_sig":false,"md5_digest":"1a314ff81d87a774e5e1cf338151a353","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":48332,"upload_time":"2024-12-07T00:53:47","upload_time_iso_8601":"2024-12-07T00:53:47.581677Z","url":"https://files.pythonhosted.org/packages/a1/80/420ef26926052b12d1c2010360b4037f6765321055ce7e09c6bfaeac3480/agentops-0.3.20rc4.tar.gz","yanked":false,"yanked_reason":null}],"0.3.20rc5":[{"comment_text":"","digests":{"blake2b_256":"7747e61c5387124f53a3095261427888ab88e192828e3bb8be92660bf4e008d0","md5":"fd7343ddf99f077d1a159b87d84ed79c","sha256":"97df38116ec7fe337fc04b800e423aa8b5e69681565c02dc4af3e9c60764827e"},"downloads":-1,"filename":"agentops-0.3.20rc5-py3-none-any.whl","has_sig":false,"md5_digest":"fd7343ddf99f077d1a159b87d84ed79c","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":44545,"upload_time":"2024-12-07T01:38:17","upload_time_iso_8601":"2024-12-07T01:38:17.177125Z","url":"https://files.pythonhosted.org/packages/77/47/e61c5387124f53a3095261427888ab88e192828e3bb8be92660bf4e008d0/agentops-0.3.20rc5-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"145fa0bf5ee5b56dacf63b9712ac62169c585c6222efe043cc77f3148f709965","md5":"20a32d514b5d51851dbcbdfb2c189491","sha256":"48111083dab1fc30f0545e0812c4aab00fc9e9d48de42de95d254699396992a8"},"downloads":-1,"filename":"agentops-0.3.20rc5.tar.gz","has_sig":false,"md5_digest":"20a32d514b5d51851dbcbdfb2c189491","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":53243,"upload_time":"2024-12-07T01:38:18","upload_time_iso_8601":"2024-12-07T01:38:18.772880Z","url":"https://files.pythonhosted.org/packages/14/5f/a0bf5ee5b56dacf63b9712ac62169c585c6222efe043cc77f3148f709965/agentops-0.3.20rc5.tar.gz","yanked":false,"yanked_reason":null}],"0.3.20rc6":[{"comment_text":"","digests":{"blake2b_256":"85f3a5ae3d8d47aa5160a5c805551d75077cad61bff9626abe44079d29d1c299","md5":"30f87c628c530e82e27b8bc2d2a46d8a","sha256":"d03f16832b3a5670d9c3273b95c9d9def772c203b2cd4ac52ae0e7f6d3b1b9e4"},"downloads":-1,"filename":"agentops-0.3.20rc6-py3-none-any.whl","has_sig":false,"md5_digest":"30f87c628c530e82e27b8bc2d2a46d8a","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":61844,"upload_time":"2024-12-07T01:49:11","upload_time_iso_8601":"2024-12-07T01:49:11.801219Z","url":"https://files.pythonhosted.org/packages/85/f3/a5ae3d8d47aa5160a5c805551d75077cad61bff9626abe44079d29d1c299/agentops-0.3.20rc6-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"060e24f42ed1de3d892355f3ba90f0b7f659855fafd18851e59aa7174fa30615","md5":"384c60ee11b827b8bad31cef20a35a17","sha256":"45aa4797269214d41858537d95050964f330651da5c7412b2895e714a81f30f5"},"downloads":-1,"filename":"agentops-0.3.20rc6.tar.gz","has_sig":false,"md5_digest":"384c60ee11b827b8bad31cef20a35a17","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":61004,"upload_time":"2024-12-07T01:49:13","upload_time_iso_8601":"2024-12-07T01:49:13.917920Z","url":"https://files.pythonhosted.org/packages/06/0e/24f42ed1de3d892355f3ba90f0b7f659855fafd18851e59aa7174fa30615/agentops-0.3.20rc6.tar.gz","yanked":false,"yanked_reason":null}],"0.3.20rc7":[{"comment_text":"","digests":{"blake2b_256":"d502edf7ba8aff1a994176da4c95688c9ba0428ac3bd9a0db2392fe5009162a9","md5":"9b43c5e2df12abac01ffc5262e991825","sha256":"95972115c5c753ceee477834de902afaf0664107048e44eee2c65e74e05656a2"},"downloads":-1,"filename":"agentops-0.3.20rc7-py3-none-any.whl","has_sig":false,"md5_digest":"9b43c5e2df12abac01ffc5262e991825","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":40117,"upload_time":"2024-12-07T02:12:48","upload_time_iso_8601":"2024-12-07T02:12:48.512036Z","url":"https://files.pythonhosted.org/packages/d5/02/edf7ba8aff1a994176da4c95688c9ba0428ac3bd9a0db2392fe5009162a9/agentops-0.3.20rc7-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"5d7029d8d02fcf6db627c6b20ceab974c455e23a25fc0e991c0a8d0eaebda523","md5":"9de760856bed3f7adbd1d0ab7ba0a63a","sha256":"7c793b7b199a61ca61366ddb8fd94986fac262ef6514918c3baaa08184b86669"},"downloads":-1,"filename":"agentops-0.3.20rc7.tar.gz","has_sig":false,"md5_digest":"9de760856bed3f7adbd1d0ab7ba0a63a","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":49661,"upload_time":"2024-12-07T02:12:50","upload_time_iso_8601":"2024-12-07T02:12:50.120388Z","url":"https://files.pythonhosted.org/packages/5d/70/29d8d02fcf6db627c6b20ceab974c455e23a25fc0e991c0a8d0eaebda523/agentops-0.3.20rc7.tar.gz","yanked":false,"yanked_reason":null}],"0.3.20rc8":[{"comment_text":"","digests":{"blake2b_256":"6d0f66418c0b20f40fe11de50f29481abdb266ff641ac6166eab9eac3d7364d2","md5":"52a2cea48e48d1818169c07507a6c7a9","sha256":"8cf2e9fe6400a4fb4367a039cacc5d76339a8fd2749a44243389547e928e545c"},"downloads":-1,"filename":"agentops-0.3.20rc8-py3-none-any.whl","has_sig":false,"md5_digest":"52a2cea48e48d1818169c07507a6c7a9","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":57414,"upload_time":"2024-12-07T02:17:51","upload_time_iso_8601":"2024-12-07T02:17:51.404804Z","url":"https://files.pythonhosted.org/packages/6d/0f/66418c0b20f40fe11de50f29481abdb266ff641ac6166eab9eac3d7364d2/agentops-0.3.20rc8-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"4d18250b066f23ccbb22f2bba8df101361abd5724ddcef59a4d63d4539c7cd82","md5":"f7887176e88d4434e38e237850363b80","sha256":"a06e7939dd4d59c9880ded1b129fd4548b34be5530a46cf043326740bdfeca56"},"downloads":-1,"filename":"agentops-0.3.20rc8.tar.gz","has_sig":false,"md5_digest":"f7887176e88d4434e38e237850363b80","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":57521,"upload_time":"2024-12-07T02:17:53","upload_time_iso_8601":"2024-12-07T02:17:53.055737Z","url":"https://files.pythonhosted.org/packages/4d/18/250b066f23ccbb22f2bba8df101361abd5724ddcef59a4d63d4539c7cd82/agentops-0.3.20rc8.tar.gz","yanked":false,"yanked_reason":null}],"0.3.21":[{"comment_text":"","digests":{"blake2b_256":"c4cb3b6cc5a08d11d9e56501f980222da0fa41814b7d6948a7f6354f31739af6","md5":"c7592f9e7993dbe307fbffd7e4da1e51","sha256":"4f98beecdce4c7cbee80ec26658a9657ba307a1fb2910b589f85325d3259b75b"},"downloads":-1,"filename":"agentops-0.3.21-py3-none-any.whl","has_sig":false,"md5_digest":"c7592f9e7993dbe307fbffd7e4da1e51","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":64701,"upload_time":"2024-12-11T12:24:00","upload_time_iso_8601":"2024-12-11T12:24:00.934724Z","url":"https://files.pythonhosted.org/packages/c4/cb/3b6cc5a08d11d9e56501f980222da0fa41814b7d6948a7f6354f31739af6/agentops-0.3.21-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"83f6bfd27fa4b948c353eaff579dafdf4eb54833f5c526e00c6f2faee4b467a8","md5":"83d7666511cccf3b0d4354cebd99b110","sha256":"d8e8d1f6d154554dba64ec5b139905bf76c68f21575af9fa2ca1697277fe36f2"},"downloads":-1,"filename":"agentops-0.3.21.tar.gz","has_sig":false,"md5_digest":"83d7666511cccf3b0d4354cebd99b110","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":63185,"upload_time":"2024-12-11T12:24:02","upload_time_iso_8601":"2024-12-11T12:24:02.068404Z","url":"https://files.pythonhosted.org/packages/83/f6/bfd27fa4b948c353eaff579dafdf4eb54833f5c526e00c6f2faee4b467a8/agentops-0.3.21.tar.gz","yanked":false,"yanked_reason":null}],"0.3.22":[{"comment_text":"","digests":{"blake2b_256":"11e721b42168ecfd0a9fff9dea51201646b6e62c4f52c8cd9c2a6400125d7234","md5":"26061ab467e358b63251f9547275bbbd","sha256":"992f4f31d80e8b0b2098abf58ae2707c60538e4b66e5aec8cf49fb269d5a2adc"},"downloads":-1,"filename":"agentops-0.3.22-py3-none-any.whl","has_sig":false,"md5_digest":"26061ab467e358b63251f9547275bbbd","packagetype":"bdist_wheel","python_version":"py3","requires_python":"<3.14,>=3.9","size":39539,"upload_time":"2025-01-11T03:21:39","upload_time_iso_8601":"2025-01-11T03:21:39.093169Z","url":"https://files.pythonhosted.org/packages/11/e7/21b42168ecfd0a9fff9dea51201646b6e62c4f52c8cd9c2a6400125d7234/agentops-0.3.22-py3-none-any.whl","yanked":true,"yanked_reason":"Broken
        dependency"},{"comment_text":"","digests":{"blake2b_256":"e067e61aa4c2e329da10b5e95d325091e599d8a00a28843a54bdcefa7a2eef8d","md5":"bcf45b6c4c56884ed2409f835571af62","sha256":"705d772b6994f8bab0cd163b24602009353f7906c72d9db008af11683f6e9341"},"downloads":-1,"filename":"agentops-0.3.22.tar.gz","has_sig":false,"md5_digest":"bcf45b6c4c56884ed2409f835571af62","packagetype":"sdist","python_version":"source","requires_python":"<3.14,>=3.9","size":52845,"upload_time":"2025-01-11T03:21:41","upload_time_iso_8601":"2025-01-11T03:21:41.762282Z","url":"https://files.pythonhosted.org/packages/e0/67/e61aa4c2e329da10b5e95d325091e599d8a00a28843a54bdcefa7a2eef8d/agentops-0.3.22.tar.gz","yanked":true,"yanked_reason":"Broken
        dependency"}],"0.3.23":[{"comment_text":null,"digests":{"blake2b_256":"e67de1434765cf0a3d62372b74f47919aa17c0b01909823f7d3ee705edf821a9","md5":"1f0f02509b8ba713db72e57a072f01a6","sha256":"ecfff77d8f9006361ef2a2e8593271e97eb54b7b504abfb8abd6504006baca56"},"downloads":-1,"filename":"agentops-0.3.23-py3-none-any.whl","has_sig":false,"md5_digest":"1f0f02509b8ba713db72e57a072f01a6","packagetype":"bdist_wheel","python_version":"py3","requires_python":"<3.14,>=3.9","size":70098,"upload_time":"2025-01-12T02:11:56","upload_time_iso_8601":"2025-01-12T02:11:56.319763Z","url":"https://files.pythonhosted.org/packages/e6/7d/e1434765cf0a3d62372b74f47919aa17c0b01909823f7d3ee705edf821a9/agentops-0.3.23-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":null,"digests":{"blake2b_256":"5c7fa4fd91f8fd819e1ecfdc608d1c7ade83de0f9dddd868e2c2c139a2fdae25","md5":"b7922399f81fb26517eb69fc7fef97c9","sha256":"4e4de49caeaf567b8746082f84a8cdd65afe2c698720f6f40251bbc4fdffe4c9"},"downloads":-1,"filename":"agentops-0.3.23.tar.gz","has_sig":false,"md5_digest":"b7922399f81fb26517eb69fc7fef97c9","packagetype":"sdist","python_version":"source","requires_python":"<3.14,>=3.9","size":64225,"upload_time":"2025-01-12T02:11:59","upload_time_iso_8601":"2025-01-12T02:11:59.360077Z","url":"https://files.pythonhosted.org/packages/5c/7f/a4fd91f8fd819e1ecfdc608d1c7ade83de0f9dddd868e2c2c139a2fdae25/agentops-0.3.23.tar.gz","yanked":false,"yanked_reason":null}],"0.3.24":[{"comment_text":null,"digests":{"blake2b_256":"254ea7d131802bac2ece5302ebf78dcef1ba1ba2f8b3a51fbe44c7f52bae6a53","md5":"39c39d8a7f1285add0fec21830a89a4a","sha256":"c5dfc8098b0dd49ddd819aa55280d07f8bfbf2f8fa088fc51ff5849b65062b10"},"downloads":-1,"filename":"agentops-0.3.24-py3-none-any.whl","has_sig":false,"md5_digest":"39c39d8a7f1285add0fec21830a89a4a","packagetype":"bdist_wheel","python_version":"py3","requires_python":"<3.14,>=3.9","size":71957,"upload_time":"2025-01-18T19:08:02","upload_time_iso_8601":"2025-01-18T19:08:02.053316Z","url":"https://files.pythonhosted.org/packages/25/4e/a7d131802bac2ece5302ebf78dcef1ba1ba2f8b3a51fbe44c7f52bae6a53/agentops-0.3.24-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":null,"digests":{"blake2b_256":"71fee96e22c4bf762f34cd5ba435880470dad4576ab357ee61742fe053752322","md5":"3e1b7e0a31197936e099a7509128f794","sha256":"c97a3af959b728bcfbfb1ac2494cef82d8804defc9dac858648b39a9ecdcd2e4"},"downloads":-1,"filename":"agentops-0.3.24.tar.gz","has_sig":false,"md5_digest":"3e1b7e0a31197936e099a7509128f794","packagetype":"sdist","python_version":"source","requires_python":"<3.14,>=3.9","size":233974,"upload_time":"2025-01-18T19:08:04","upload_time_iso_8601":"2025-01-18T19:08:04.121618Z","url":"https://files.pythonhosted.org/packages/71/fe/e96e22c4bf762f34cd5ba435880470dad4576ab357ee61742fe053752322/agentops-0.3.24.tar.gz","yanked":false,"yanked_reason":null}],"0.3.25":[{"comment_text":null,"digests":{"blake2b_256":"e6e39cff4ed65c5deac34f427ed60cd7af3604ec7ed8a999c351f6411e190d3b","md5":"328dedc417be02fc28f8a4c7ed7b52e9","sha256":"4faebf73a62aa0bcac8578428277ca5b9af5e828f49f2cb03a9695b8426e6b9d"},"downloads":-1,"filename":"agentops-0.3.25-py3-none-any.whl","has_sig":false,"md5_digest":"328dedc417be02fc28f8a4c7ed7b52e9","packagetype":"bdist_wheel","python_version":"py3","requires_python":"<3.14,>=3.9","size":71971,"upload_time":"2025-01-22T10:43:16","upload_time_iso_8601":"2025-01-22T10:43:16.070593Z","url":"https://files.pythonhosted.org/packages/e6/e3/9cff4ed65c5deac34f427ed60cd7af3604ec7ed8a999c351f6411e190d3b/agentops-0.3.25-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":null,"digests":{"blake2b_256":"2fdfeb00eaabebb51feae0724a5928f25df4d71d1c8392204f4f849351fd748c","md5":"a40bc7037baf6dbba92d63331f561a28","sha256":"868d855b6531d1fa2d1047db2cb03ddb1121062fd51c44b564dc626f15cc1e40"},"downloads":-1,"filename":"agentops-0.3.25.tar.gz","has_sig":false,"md5_digest":"a40bc7037baf6dbba92d63331f561a28","packagetype":"sdist","python_version":"source","requires_python":"<3.14,>=3.9","size":234024,"upload_time":"2025-01-22T10:43:17","upload_time_iso_8601":"2025-01-22T10:43:17.986230Z","url":"https://files.pythonhosted.org/packages/2f/df/eb00eaabebb51feae0724a5928f25df4d71d1c8392204f4f849351fd748c/agentops-0.3.25.tar.gz","yanked":false,"yanked_reason":null}],"0.3.26":[{"comment_text":null,"digests":{"blake2b_256":"f521671c458951850bd3a445aa09eafd2793aae1104fa68351a5c3976cdf762b","md5":"c3f8fa92ff5a94a37516e774c7f58b9a","sha256":"20948f52e3ffb4ba1d52301c3a82e59490182c4dad22774ad831dce0181eb5c2"},"downloads":-1,"filename":"agentops-0.3.26-py3-none-any.whl","has_sig":false,"md5_digest":"c3f8fa92ff5a94a37516e774c7f58b9a","packagetype":"bdist_wheel","python_version":"py3","requires_python":"<3.14,>=3.9","size":72090,"upload_time":"2025-01-24T23:44:06","upload_time_iso_8601":"2025-01-24T23:44:06.828461Z","url":"https://files.pythonhosted.org/packages/f5/21/671c458951850bd3a445aa09eafd2793aae1104fa68351a5c3976cdf762b/agentops-0.3.26-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":null,"digests":{"blake2b_256":"76a1b03c6348a77798e750bde4eec03b4af620d71b9e4b64ff7dcf0860025a2d","md5":"ba4d0f2411ec72828677b38a395465cc","sha256":"bc824bf8727332f59bf803cf84440d13e9e398406222ab29f45909ac1e39f815"},"downloads":-1,"filename":"agentops-0.3.26.tar.gz","has_sig":false,"md5_digest":"ba4d0f2411ec72828677b38a395465cc","packagetype":"sdist","python_version":"source","requires_python":"<3.14,>=3.9","size":234235,"upload_time":"2025-01-24T23:44:08","upload_time_iso_8601":"2025-01-24T23:44:08.541961Z","url":"https://files.pythonhosted.org/packages/76/a1/b03c6348a77798e750bde4eec03b4af620d71b9e4b64ff7dcf0860025a2d/agentops-0.3.26.tar.gz","yanked":false,"yanked_reason":null}],"0.3.4":[{"comment_text":"","digests":{"blake2b_256":"52f32bd714234ec345153c0fcbc9e4896c306c347f3fb66a3aa6d6fc109a7243","md5":"c7a975a86900f7dbe6861a21fdd3c2d8","sha256":"126f7aed4ba43c1399b5488d67a03d10cb4c531e619c650776f826ca00c1aa24"},"downloads":-1,"filename":"agentops-0.3.4-py3-none-any.whl","has_sig":false,"md5_digest":"c7a975a86900f7dbe6861a21fdd3c2d8","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":39915,"upload_time":"2024-07-24T23:15:03","upload_time_iso_8601":"2024-07-24T23:15:03.892439Z","url":"https://files.pythonhosted.org/packages/52/f3/2bd714234ec345153c0fcbc9e4896c306c347f3fb66a3aa6d6fc109a7243/agentops-0.3.4-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"d28b88a2c9c2df655de806adbb5deebb12c64d19d6aa3cfa759da642953525e0","md5":"f48a2ab7fcaf9cf11a25805ac5300e26","sha256":"a92c9cb7c511197f0ecb8cb5aca15d35022c15a3d2fd2aaaa34cd7e5dc59393f"},"downloads":-1,"filename":"agentops-0.3.4.tar.gz","has_sig":false,"md5_digest":"f48a2ab7fcaf9cf11a25805ac5300e26","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":42063,"upload_time":"2024-07-24T23:15:05","upload_time_iso_8601":"2024-07-24T23:15:05.586475Z","url":"https://files.pythonhosted.org/packages/d2/8b/88a2c9c2df655de806adbb5deebb12c64d19d6aa3cfa759da642953525e0/agentops-0.3.4.tar.gz","yanked":false,"yanked_reason":null}],"0.3.5":[{"comment_text":"","digests":{"blake2b_256":"f253f9672c6aa3c79b6a5b64321e93d2316f126add867ceb2e3e95ea8b4bf1b0","md5":"bd45dc8100fd3974dff11014d12424ff","sha256":"687cb938ecf9d1bf7650afc910e2b2e1b8b6d9e969215aeb49e57f1555a2a756"},"downloads":-1,"filename":"agentops-0.3.5-py3-none-any.whl","has_sig":false,"md5_digest":"bd45dc8100fd3974dff11014d12424ff","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":39177,"upload_time":"2024-08-01T19:32:19","upload_time_iso_8601":"2024-08-01T19:32:19.765946Z","url":"https://files.pythonhosted.org/packages/f2/53/f9672c6aa3c79b6a5b64321e93d2316f126add867ceb2e3e95ea8b4bf1b0/agentops-0.3.5-py3-none-any.whl","yanked":true,"yanked_reason":"Introduces
        FileNotFoundError impacting OpenAI and LiteLLM integrations"},{"comment_text":"","digests":{"blake2b_256":"235508ce5915f1ceb86ea6f7a6e8c8dc025b34981408a1b638316b5140fad525","md5":"53ef2f5230de09260f4ead09633dde62","sha256":"ae98540355ce9b892a630e61a7224a9175657cad1b7e799269238748ca7bc0ea"},"downloads":-1,"filename":"agentops-0.3.5.tar.gz","has_sig":false,"md5_digest":"53ef2f5230de09260f4ead09633dde62","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":42699,"upload_time":"2024-08-01T19:32:21","upload_time_iso_8601":"2024-08-01T19:32:21.259555Z","url":"https://files.pythonhosted.org/packages/23/55/08ce5915f1ceb86ea6f7a6e8c8dc025b34981408a1b638316b5140fad525/agentops-0.3.5.tar.gz","yanked":true,"yanked_reason":"Introduces
        FileNotFoundError impacting OpenAI and LiteLLM integrations"}],"0.3.6":[{"comment_text":"","digests":{"blake2b_256":"be89412afc864df3715d377cff9fe15deadaccdc0902b0a242f742f286e6d84b","md5":"149922f5cd986a8641b6e88c991af0cc","sha256":"413f812eb015fb31175a507784afe08123adfa9e227870e315899b059f42b443"},"downloads":-1,"filename":"agentops-0.3.6-py3-none-any.whl","has_sig":false,"md5_digest":"149922f5cd986a8641b6e88c991af0cc","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":39431,"upload_time":"2024-08-02T06:48:19","upload_time_iso_8601":"2024-08-02T06:48:19.594149Z","url":"https://files.pythonhosted.org/packages/be/89/412afc864df3715d377cff9fe15deadaccdc0902b0a242f742f286e6d84b/agentops-0.3.6-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"c3bf85f1439c3951ef69c81dbd7ef6df8a11df957e8d1180d835d71c11fa5131","md5":"b68d3124e365867f891bec4fb211a398","sha256":"0941f2486f3a561712ba6f77d560b49e2df55be141f243da0f9dc97ed43e6968"},"downloads":-1,"filename":"agentops-0.3.6.tar.gz","has_sig":false,"md5_digest":"b68d3124e365867f891bec4fb211a398","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":42933,"upload_time":"2024-08-02T06:48:21","upload_time_iso_8601":"2024-08-02T06:48:21.508300Z","url":"https://files.pythonhosted.org/packages/c3/bf/85f1439c3951ef69c81dbd7ef6df8a11df957e8d1180d835d71c11fa5131/agentops-0.3.6.tar.gz","yanked":false,"yanked_reason":null}],"0.3.7":[{"comment_text":"","digests":{"blake2b_256":"a34d05ba61e4fbd976dabe736d74fb2bb14d064ca758f05f084c0dadb6ac5cb1","md5":"551df1e89278270e0f5522d41f5c28ae","sha256":"7eeec5bef41e9ba397b3d880bcec8cd0818209ab31665c85e8b97615011a23d9"},"downloads":-1,"filename":"agentops-0.3.7-py3-none-any.whl","has_sig":false,"md5_digest":"551df1e89278270e0f5522d41f5c28ae","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":39816,"upload_time":"2024-08-08T23:21:45","upload_time_iso_8601":"2024-08-08T23:21:45.035395Z","url":"https://files.pythonhosted.org/packages/a3/4d/05ba61e4fbd976dabe736d74fb2bb14d064ca758f05f084c0dadb6ac5cb1/agentops-0.3.7-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"9f31034c3e062287f4fe9f57f2448e9508617a26bbb8a16b11c77cda9b28e1c0","md5":"1c48a797903a25988bae9b72559307ec","sha256":"048ee3caa5edf01b98c994e4e3ff90c09d83f820a43a70f07db96032c3386750"},"downloads":-1,"filename":"agentops-0.3.7.tar.gz","has_sig":false,"md5_digest":"1c48a797903a25988bae9b72559307ec","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":43495,"upload_time":"2024-08-08T23:21:46","upload_time_iso_8601":"2024-08-08T23:21:46.798531Z","url":"https://files.pythonhosted.org/packages/9f/31/034c3e062287f4fe9f57f2448e9508617a26bbb8a16b11c77cda9b28e1c0/agentops-0.3.7.tar.gz","yanked":false,"yanked_reason":null}],"0.3.9":[{"comment_text":"","digests":{"blake2b_256":"660ce931f892e0cedd40d861c3deff4134e1af1d226d6dc9762b32514d6dbc9f","md5":"82792de7bccabed058a24d3bd47443db","sha256":"582c9ddb30a9bb951b4d3ee2fd0428ba77d4a4367950b9cc6043f45b10bf12d8"},"downloads":-1,"filename":"agentops-0.3.9-py3-none-any.whl","has_sig":false,"md5_digest":"82792de7bccabed058a24d3bd47443db","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.7","size":40235,"upload_time":"2024-08-15T21:21:33","upload_time_iso_8601":"2024-08-15T21:21:33.468748Z","url":"https://files.pythonhosted.org/packages/66/0c/e931f892e0cedd40d861c3deff4134e1af1d226d6dc9762b32514d6dbc9f/agentops-0.3.9-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":"","digests":{"blake2b_256":"e17b68cef3aaf44d423046b7779e9325e4feef5257e6d784a55c9dadf84bd61a","md5":"470f3b2663b71eb2f1597903bf8922e7","sha256":"7c999edbc64196924acdb06da09ec664a09d9fec8e73ba4e0f89e5f3dafc79e5"},"downloads":-1,"filename":"agentops-0.3.9.tar.gz","has_sig":false,"md5_digest":"470f3b2663b71eb2f1597903bf8922e7","packagetype":"sdist","python_version":"source","requires_python":">=3.7","size":43796,"upload_time":"2024-08-15T21:21:34","upload_time_iso_8601":"2024-08-15T21:21:34.591272Z","url":"https://files.pythonhosted.org/packages/e1/7b/68cef3aaf44d423046b7779e9325e4feef5257e6d784a55c9dadf84bd61a/agentops-0.3.9.tar.gz","yanked":false,"yanked_reason":null}],"0.4.0":[{"comment_text":null,"digests":{"blake2b_256":"060e66184fab1fc3bdd955ac20ea7bdef78f5b9aecc4080ea3e054c2a2436991","md5":"250de44e3599992c75625cef67682ecd","sha256":"b4821b8ec69c05a4d13b34eaad4762bb06a4f14e1241d57c16fdd28de5c8c929"},"downloads":-1,"filename":"agentops-0.4.0-py3-none-any.whl","has_sig":false,"md5_digest":"250de44e3599992c75625cef67682ecd","packagetype":"bdist_wheel","python_version":"py3","requires_python":"<3.14,>=3.9","size":171419,"upload_time":"2025-03-13T11:24:15","upload_time_iso_8601":"2025-03-13T11:24:15.042606Z","url":"https://files.pythonhosted.org/packages/06/0e/66184fab1fc3bdd955ac20ea7bdef78f5b9aecc4080ea3e054c2a2436991/agentops-0.4.0-py3-none-any.whl","yanked":true,"yanked_reason":"broken
        dependencies"},{"comment_text":null,"digests":{"blake2b_256":"ff7f8a57d060489c780db3e15c4d9ff8c670e5db583549c74dd2d32ae6ec10c0","md5":"ea0932849a7311750c6ac0e567c90182","sha256":"45f5367cecd8a0b648055b6ec76e8a6a2801425e80dede8f86b39e9c6cfe1d98"},"downloads":-1,"filename":"agentops-0.4.0.tar.gz","has_sig":false,"md5_digest":"ea0932849a7311750c6ac0e567c90182","packagetype":"sdist","python_version":"source","requires_python":"<3.14,>=3.9","size":248757,"upload_time":"2025-03-13T11:24:16","upload_time_iso_8601":"2025-03-13T11:24:16.866033Z","url":"https://files.pythonhosted.org/packages/ff/7f/8a57d060489c780db3e15c4d9ff8c670e5db583549c74dd2d32ae6ec10c0/agentops-0.4.0.tar.gz","yanked":true,"yanked_reason":"broken
        dependencies"}],"0.4.1":[{"comment_text":null,"digests":{"blake2b_256":"736e7ab03c56260ec59bfaeeb08efb76f55ec6153861ad2a9cf20b38b222e4e7","md5":"3fcebe0141ca19b2fbcb53e918003ce9","sha256":"69c944e22628bc0f52c534007d2453da2a1988a7fd1f993720c4a15b0f70465a"},"downloads":-1,"filename":"agentops-0.4.1-py3-none-any.whl","has_sig":false,"md5_digest":"3fcebe0141ca19b2fbcb53e918003ce9","packagetype":"bdist_wheel","python_version":"py3","requires_python":"<3.14,>=3.9","size":171402,"upload_time":"2025-03-13T16:29:26","upload_time_iso_8601":"2025-03-13T16:29:26.477091Z","url":"https://files.pythonhosted.org/packages/73/6e/7ab03c56260ec59bfaeeb08efb76f55ec6153861ad2a9cf20b38b222e4e7/agentops-0.4.1-py3-none-any.whl","yanked":true,"yanked_reason":"Broken
        dependencies"},{"comment_text":null,"digests":{"blake2b_256":"ca303217cd3480ad099ffa92848ccbc8672e5232c22918c95a4b99e49c0ef31e","md5":"ec421fa88b575b827fc0d3fd02f45515","sha256":"fec044f0346dca6aba17e458e669ac1f52f1b618a4a15b43342615096c5e7d56"},"downloads":-1,"filename":"agentops-0.4.1.tar.gz","has_sig":false,"md5_digest":"ec421fa88b575b827fc0d3fd02f45515","packagetype":"sdist","python_version":"source","requires_python":"<3.14,>=3.9","size":248747,"upload_time":"2025-03-13T16:29:27","upload_time_iso_8601":"2025-03-13T16:29:27.905694Z","url":"https://files.pythonhosted.org/packages/ca/30/3217cd3480ad099ffa92848ccbc8672e5232c22918c95a4b99e49c0ef31e/agentops-0.4.1.tar.gz","yanked":true,"yanked_reason":"Broken
        dependencies"}],"0.4.10":[{"comment_text":null,"digests":{"blake2b_256":"301e0fe4fb617a5a69a8692b571d726f03e713a37d94d6a43c595a08fc33cff3","md5":"5ac7ec12e80bae6946dc10e46ef768f7","sha256":"917ad7ad51af0ca00cace2a3ae1d1d36e0d65dc813e030fcd377ff98535002bd"},"downloads":-1,"filename":"agentops-0.4.10-py3-none-any.whl","has_sig":false,"md5_digest":"5ac7ec12e80bae6946dc10e46ef768f7","packagetype":"bdist_wheel","python_version":"py3","requires_python":"<3.14,>=3.9","size":198777,"upload_time":"2025-05-08T20:37:29","upload_time_iso_8601":"2025-05-08T20:37:29.322288Z","url":"https://files.pythonhosted.org/packages/30/1e/0fe4fb617a5a69a8692b571d726f03e713a37d94d6a43c595a08fc33cff3/agentops-0.4.10-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":null,"digests":{"blake2b_256":"a0ef0a56be3981bd464ad5a22fa3a859421f4b5560cbbb082f3ef9aca9cdb1a7","md5":"1954d07bfa38ba5c5ce0e516b7dbfdc9","sha256":"b66a48b4ec50c9cb34abc6ff1df873f0dcddbbb528d8a8c0527cb97b24c91b36"},"downloads":-1,"filename":"agentops-0.4.10.tar.gz","has_sig":false,"md5_digest":"1954d07bfa38ba5c5ce0e516b7dbfdc9","packagetype":"sdist","python_version":"source","requires_python":"<3.14,>=3.9","size":284727,"upload_time":"2025-05-08T20:37:30","upload_time_iso_8601":"2025-05-08T20:37:30.744217Z","url":"https://files.pythonhosted.org/packages/a0/ef/0a56be3981bd464ad5a22fa3a859421f4b5560cbbb082f3ef9aca9cdb1a7/agentops-0.4.10.tar.gz","yanked":false,"yanked_reason":null}],"0.4.11":[{"comment_text":null,"digests":{"blake2b_256":"35cde66dea05d2d8070f886e8f4ce86905cf1cce2f89622e041f26e39f717c9e","md5":"20424d54ba76517d586d4bcc92dda3bf","sha256":"b08c84fd69f36fcd5d6f2b14d16ff88b977a9a417d92448c9709f3c7990d6438"},"downloads":-1,"filename":"agentops-0.4.11-py3-none-any.whl","has_sig":false,"md5_digest":"20424d54ba76517d586d4bcc92dda3bf","packagetype":"bdist_wheel","python_version":"py3","requires_python":"<3.14,>=3.9","size":198789,"upload_time":"2025-05-12T20:38:29","upload_time_iso_8601":"2025-05-12T20:38:29.202046Z","url":"https://files.pythonhosted.org/packages/35/cd/e66dea05d2d8070f886e8f4ce86905cf1cce2f89622e041f26e39f717c9e/agentops-0.4.11-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":null,"digests":{"blake2b_256":"349df76fc1760cb21788967db3dd22ff2e6521c42b8ecee152e6ac4278e7cade","md5":"b7affd8b15834e4f9cb63066d7d160d1","sha256":"6eb80ee4a0653f9bdc9fc7641bf60cb7546cd34ff1c04dfbc4fca77dbb07edda"},"downloads":-1,"filename":"agentops-0.4.11.tar.gz","has_sig":false,"md5_digest":"b7affd8b15834e4f9cb63066d7d160d1","packagetype":"sdist","python_version":"source","requires_python":"<3.14,>=3.9","size":284735,"upload_time":"2025-05-12T20:38:30","upload_time_iso_8601":"2025-05-12T20:38:30.393540Z","url":"https://files.pythonhosted.org/packages/34/9d/f76fc1760cb21788967db3dd22ff2e6521c42b8ecee152e6ac4278e7cade/agentops-0.4.11.tar.gz","yanked":false,"yanked_reason":null}],"0.4.12":[{"comment_text":null,"digests":{"blake2b_256":"eb86772ed94e4e55433e8014933dab08aa6dfbcd8072f7fd74ffcad335ba0e73","md5":"831a3d54bccce09cc6c2a352776d02e6","sha256":"7c2685ae9c9de1a1071f6a29d395444191744d5ee58e33c020a69e2388dc2f7c"},"downloads":-1,"filename":"agentops-0.4.12-py3-none-any.whl","has_sig":false,"md5_digest":"831a3d54bccce09cc6c2a352776d02e6","packagetype":"bdist_wheel","python_version":"py3","requires_python":"<3.14,>=3.9","size":198319,"upload_time":"2025-05-15T19:59:27","upload_time_iso_8601":"2025-05-15T19:59:27.609093Z","url":"https://files.pythonhosted.org/packages/eb/86/772ed94e4e55433e8014933dab08aa6dfbcd8072f7fd74ffcad335ba0e73/agentops-0.4.12-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":null,"digests":{"blake2b_256":"0cf664cea8e916a305d2dc2f3f3840a1d4cae40e1927892e1fcc11f83ec7ebee","md5":"7e97e0612a6e8544b37a2fa2e1633166","sha256":"530f15d428a4c78db918fa766366c8f11105c4d1d3b1a56de027747d805a573f"},"downloads":-1,"filename":"agentops-0.4.12.tar.gz","has_sig":false,"md5_digest":"7e97e0612a6e8544b37a2fa2e1633166","packagetype":"sdist","python_version":"source","requires_python":"<3.14,>=3.9","size":284309,"upload_time":"2025-05-15T19:59:28","upload_time_iso_8601":"2025-05-15T19:59:28.955745Z","url":"https://files.pythonhosted.org/packages/0c/f6/64cea8e916a305d2dc2f3f3840a1d4cae40e1927892e1fcc11f83ec7ebee/agentops-0.4.12.tar.gz","yanked":false,"yanked_reason":null}],"0.4.13":[{"comment_text":null,"digests":{"blake2b_256":"637f1514550d55e8ba0e2aef4f652678413e9979f4f6c019d8032cfd9fade10e","md5":"ddea9230651973616b50a1f089657999","sha256":"256cfcd4eb257d0a3c9538bd461ffe1dceb15cd0627b405b45d99661d8925247"},"downloads":-1,"filename":"agentops-0.4.13-py3-none-any.whl","has_sig":false,"md5_digest":"ddea9230651973616b50a1f089657999","packagetype":"bdist_wheel","python_version":"py3","requires_python":"<3.14,>=3.9","size":214973,"upload_time":"2025-05-27T22:32:40","upload_time_iso_8601":"2025-05-27T22:32:40.986531Z","url":"https://files.pythonhosted.org/packages/63/7f/1514550d55e8ba0e2aef4f652678413e9979f4f6c019d8032cfd9fade10e/agentops-0.4.13-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":null,"digests":{"blake2b_256":"cee05df9380bcf206dbdf70a7df161ffb406b1060dd06f489f3bdf8765b5463a","md5":"ab39a8b926330602f8930e73a1671245","sha256":"942832fa1a8c728abf4097878316da9e2739e35f1d7b0de6d60422144d34d961"},"downloads":-1,"filename":"agentops-0.4.13.tar.gz","has_sig":false,"md5_digest":"ab39a8b926330602f8930e73a1671245","packagetype":"sdist","python_version":"source","requires_python":"<3.14,>=3.9","size":298357,"upload_time":"2025-05-27T22:32:43","upload_time_iso_8601":"2025-05-27T22:32:43.002489Z","url":"https://files.pythonhosted.org/packages/ce/e0/5df9380bcf206dbdf70a7df161ffb406b1060dd06f489f3bdf8765b5463a/agentops-0.4.13.tar.gz","yanked":false,"yanked_reason":null}],"0.4.14":[{"comment_text":null,"digests":{"blake2b_256":"f23ffbbb6b6f81f82943e1d19dd38dc7eda566b630b5f2fd02205d0c1a05f491","md5":"a081592d2b27897042bdba8fc375bba4","sha256":"5efa6b2c7a0e5b854b2c0aa8248b49e865dac83e5404332bf2eab4d226a0d3bd"},"downloads":-1,"filename":"agentops-0.4.14-py3-none-any.whl","has_sig":false,"md5_digest":"a081592d2b27897042bdba8fc375bba4","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.9","size":214837,"upload_time":"2025-05-30T20:46:55","upload_time_iso_8601":"2025-05-30T20:46:55.103050Z","url":"https://files.pythonhosted.org/packages/f2/3f/fbbb6b6f81f82943e1d19dd38dc7eda566b630b5f2fd02205d0c1a05f491/agentops-0.4.14-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":null,"digests":{"blake2b_256":"502593c81d2860a122a92091d5e8cd960beafa354bd37d3a796d45db5d2c071d","md5":"6041cd38a5160f5a27276e17ee6efb1b","sha256":"041cfc93280f6ea4639808d383442a5b70e148c0c357719315b8330768b9a3f0"},"downloads":-1,"filename":"agentops-0.4.14.tar.gz","has_sig":false,"md5_digest":"6041cd38a5160f5a27276e17ee6efb1b","packagetype":"sdist","python_version":"source","requires_python":">=3.9","size":298334,"upload_time":"2025-05-30T20:46:56","upload_time_iso_8601":"2025-05-30T20:46:56.560116Z","url":"https://files.pythonhosted.org/packages/50/25/93c81d2860a122a92091d5e8cd960beafa354bd37d3a796d45db5d2c071d/agentops-0.4.14.tar.gz","yanked":false,"yanked_reason":null}],"0.4.15":[{"comment_text":null,"digests":{"blake2b_256":"5de724df0613409f8f8f949b2acdf5d52aa6ac7f7e798e40af31117ef9bb3494","md5":"caa1ceb85a1cbaaecf71374df4eefb7d","sha256":"5881cc64c6d93a52a8e434788b11febf72bf14db4d5898d9ae5cc90c7ae74a6e"},"downloads":-1,"filename":"agentops-0.4.15-py3-none-any.whl","has_sig":false,"md5_digest":"caa1ceb85a1cbaaecf71374df4eefb7d","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.9","size":249524,"upload_time":"2025-06-17T00:00:33","upload_time_iso_8601":"2025-06-17T00:00:33.763125Z","url":"https://files.pythonhosted.org/packages/5d/e7/24df0613409f8f8f949b2acdf5d52aa6ac7f7e798e40af31117ef9bb3494/agentops-0.4.15-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":null,"digests":{"blake2b_256":"259b9040a5dc9b2dac5891aa5b93b325c8aea3b8eced3e4ea0b74937d4fa2724","md5":"8ee09660a4cc856eb482e3e36023796c","sha256":"03db71a80bafa808cec24a825b4b23a3c06a3e49b62b6e789c6796c5ec04c21b"},"downloads":-1,"filename":"agentops-0.4.15.tar.gz","has_sig":false,"md5_digest":"8ee09660a4cc856eb482e3e36023796c","packagetype":"sdist","python_version":"source","requires_python":">=3.9","size":322997,"upload_time":"2025-06-17T00:00:35","upload_time_iso_8601":"2025-06-17T00:00:35.227273Z","url":"https://files.pythonhosted.org/packages/25/9b/9040a5dc9b2dac5891aa5b93b325c8aea3b8eced3e4ea0b74937d4fa2724/agentops-0.4.15.tar.gz","yanked":false,"yanked_reason":null}],"0.4.16":[{"comment_text":null,"digests":{"blake2b_256":"76a6fff94368ad5c04128c37bb9c6a7b3cbb4956aed19fb566796900afba9440","md5":"acf57b34328c7d464d8f405e3c0d48a5","sha256":"04f78d3996e03be2716476c25316b99d765f31a78b5352bd8d28f4cb425d9458"},"downloads":-1,"filename":"agentops-0.4.16-py3-none-any.whl","has_sig":false,"md5_digest":"acf57b34328c7d464d8f405e3c0d48a5","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.9","size":268341,"upload_time":"2025-06-19T00:52:07","upload_time_iso_8601":"2025-06-19T00:52:07.933214Z","url":"https://files.pythonhosted.org/packages/76/a6/fff94368ad5c04128c37bb9c6a7b3cbb4956aed19fb566796900afba9440/agentops-0.4.16-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":null,"digests":{"blake2b_256":"c6e8ca6c289a2af9af2140ddf97271b6060cd052dfdfd44c438667d379c3f95a","md5":"60214a3ffc818ce3cbfc3123d8c354f3","sha256":"0d2dff064be938b355522c25907538b331e2049188027275b4fd4840187f283e"},"downloads":-1,"filename":"agentops-0.4.16.tar.gz","has_sig":false,"md5_digest":"60214a3ffc818ce3cbfc3123d8c354f3","packagetype":"sdist","python_version":"source","requires_python":">=3.9","size":335321,"upload_time":"2025-06-19T00:52:09","upload_time_iso_8601":"2025-06-19T00:52:09.730961Z","url":"https://files.pythonhosted.org/packages/c6/e8/ca6c289a2af9af2140ddf97271b6060cd052dfdfd44c438667d379c3f95a/agentops-0.4.16.tar.gz","yanked":false,"yanked_reason":null}],"0.4.2":[{"comment_text":null,"digests":{"blake2b_256":"b13fcb38831e86502e3a30460a27e72a254df39cc2f223d1952e063e2d0b1f70","md5":"c958500ff1e2b600064e980d526f3ad8","sha256":"4c376e3a95d1c65a864e8a5ab6f4bdb62f76abf2271b3c9a1cda2a0ad33b2b1a"},"downloads":-1,"filename":"agentops-0.4.2-py3-none-any.whl","has_sig":false,"md5_digest":"c958500ff1e2b600064e980d526f3ad8","packagetype":"bdist_wheel","python_version":"py3","requires_python":"<3.14,>=3.9","size":171420,"upload_time":"2025-03-13T16:56:31","upload_time_iso_8601":"2025-03-13T16:56:31.589623Z","url":"https://files.pythonhosted.org/packages/b1/3f/cb38831e86502e3a30460a27e72a254df39cc2f223d1952e063e2d0b1f70/agentops-0.4.2-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":null,"digests":{"blake2b_256":"4bd0f2c1951661617febfd14c3e98a58fbd805e48f453356e912dc8efc950490","md5":"7a125604d2bb3494714462442f0ac47c","sha256":"42cbc30a0eecee5db468d01dcbe398d57f080cbf8bb09aecc2ce40c5a21509a5"},"downloads":-1,"filename":"agentops-0.4.2.tar.gz","has_sig":false,"md5_digest":"7a125604d2bb3494714462442f0ac47c","packagetype":"sdist","python_version":"source","requires_python":"<3.14,>=3.9","size":248754,"upload_time":"2025-03-13T16:56:33","upload_time_iso_8601":"2025-03-13T16:56:33.062966Z","url":"https://files.pythonhosted.org/packages/4b/d0/f2c1951661617febfd14c3e98a58fbd805e48f453356e912dc8efc950490/agentops-0.4.2.tar.gz","yanked":false,"yanked_reason":null}],"0.4.3":[{"comment_text":null,"digests":{"blake2b_256":"398892f5a663cf616607e92a0499f5b636fe4e5ae8a6b7febc436077cd02ecd5","md5":"e739880fc1b0cf1e15a816277ca1e8d9","sha256":"c69cf884fc20cd3b44dd07bc9bca9ecec72e44fd2b12c50523670e3743fbbe6c"},"downloads":-1,"filename":"agentops-0.4.3-py3-none-any.whl","has_sig":false,"md5_digest":"e739880fc1b0cf1e15a816277ca1e8d9","packagetype":"bdist_wheel","python_version":"py3","requires_python":"<3.14,>=3.9","size":111111,"upload_time":"2025-03-14T17:35:53","upload_time_iso_8601":"2025-03-14T17:35:53.978325Z","url":"https://files.pythonhosted.org/packages/39/88/92f5a663cf616607e92a0499f5b636fe4e5ae8a6b7febc436077cd02ecd5/agentops-0.4.3-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":null,"digests":{"blake2b_256":"c296f6f5268ffd68079185c6b21190a6ab5b35997678ce89af211d3c3683cc16","md5":"8df7f60a4346721caf9a4a74b0ba2e32","sha256":"48379801976e5e6c830ee40b247d7e7834fb79fb18d2cec926a8c06bdf767090"},"downloads":-1,"filename":"agentops-0.4.3.tar.gz","has_sig":false,"md5_digest":"8df7f60a4346721caf9a4a74b0ba2e32","packagetype":"sdist","python_version":"source","requires_python":"<3.14,>=3.9","size":209668,"upload_time":"2025-03-14T17:35:55","upload_time_iso_8601":"2025-03-14T17:35:55.387572Z","url":"https://files.pythonhosted.org/packages/c2/96/f6f5268ffd68079185c6b21190a6ab5b35997678ce89af211d3c3683cc16/agentops-0.4.3.tar.gz","yanked":false,"yanked_reason":null}],"0.4.4":[{"comment_text":null,"digests":{"blake2b_256":"e230799eb1a6b63e6f072611e4d6c5f7d70d969b1c2d14735100a5295eb794fd","md5":"76de08f25b0f1765ec9b3ce200f2273c","sha256":"a33f32e0d09e942b501a4066460b77bc1f6be960bdbd8dfed1cfc5950702f87c"},"downloads":-1,"filename":"agentops-0.4.4-py3-none-any.whl","has_sig":false,"md5_digest":"76de08f25b0f1765ec9b3ce200f2273c","packagetype":"bdist_wheel","python_version":"py3","requires_python":"<3.14,>=3.9","size":115456,"upload_time":"2025-03-17T21:08:16","upload_time_iso_8601":"2025-03-17T21:08:16.149499Z","url":"https://files.pythonhosted.org/packages/e2/30/799eb1a6b63e6f072611e4d6c5f7d70d969b1c2d14735100a5295eb794fd/agentops-0.4.4-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":null,"digests":{"blake2b_256":"65e969c80c4c8fbf27826644c2bbcaf657bf9882a7974b115bff5021c683560d","md5":"2c34c20f9b785c60ea1cc6011b50684b","sha256":"509daf197bb27f8e5b1ac87e516487883178335c70328fd74897b1a5fadbf0bd"},"downloads":-1,"filename":"agentops-0.4.4.tar.gz","has_sig":false,"md5_digest":"2c34c20f9b785c60ea1cc6011b50684b","packagetype":"sdist","python_version":"source","requires_python":"<3.14,>=3.9","size":209971,"upload_time":"2025-03-17T21:08:17","upload_time_iso_8601":"2025-03-17T21:08:17.396763Z","url":"https://files.pythonhosted.org/packages/65/e9/69c80c4c8fbf27826644c2bbcaf657bf9882a7974b115bff5021c683560d/agentops-0.4.4.tar.gz","yanked":false,"yanked_reason":null}],"0.4.5":[{"comment_text":null,"digests":{"blake2b_256":"5cf1848e02d7233e3bfe74119e28a4fb7cf9dd3363eb215cf8bb8ca835317cc7","md5":"e70f8b49cbbbf5b6a56bbfc51938581c","sha256":"ec45a775dd5f494fe137620ce3e43aa06a6858495bed31c4b9019b343a34d092"},"downloads":-1,"filename":"agentops-0.4.5-py3-none-any.whl","has_sig":false,"md5_digest":"e70f8b49cbbbf5b6a56bbfc51938581c","packagetype":"bdist_wheel","python_version":"py3","requires_python":"<3.14,>=3.9","size":148034,"upload_time":"2025-03-25T00:05:57","upload_time_iso_8601":"2025-03-25T00:05:57.075368Z","url":"https://files.pythonhosted.org/packages/5c/f1/848e02d7233e3bfe74119e28a4fb7cf9dd3363eb215cf8bb8ca835317cc7/agentops-0.4.5-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":null,"digests":{"blake2b_256":"cc2c243f2e01dae6cc2583bca8009c735bb08267c9f51f0e916154b91329e08f","md5":"16781e2f18e40444f869c38b3b27c70c","sha256":"d82d908072c8ffea1b90d63d651ccb73dec8597ef830e60b4311efb4f5593e8e"},"downloads":-1,"filename":"agentops-0.4.5.tar.gz","has_sig":false,"md5_digest":"16781e2f18e40444f869c38b3b27c70c","packagetype":"sdist","python_version":"source","requires_python":"<3.14,>=3.9","size":232839,"upload_time":"2025-03-25T00:05:58","upload_time_iso_8601":"2025-03-25T00:05:58.270348Z","url":"https://files.pythonhosted.org/packages/cc/2c/243f2e01dae6cc2583bca8009c735bb08267c9f51f0e916154b91329e08f/agentops-0.4.5.tar.gz","yanked":false,"yanked_reason":null}],"0.4.6":[{"comment_text":null,"digests":{"blake2b_256":"316124fa78f759c68e1484ed04ed6d0d60ad4b6b58d02570a65dc670975fd954","md5":"36d7d7e64cde9ed73d4ced26e9ee4fb0","sha256":"283929b8f7a1bc79693a6c982e012ccceac4645c6a35709603e7ff83332ec00d"},"downloads":-1,"filename":"agentops-0.4.6-py3-none-any.whl","has_sig":false,"md5_digest":"36d7d7e64cde9ed73d4ced26e9ee4fb0","packagetype":"bdist_wheel","python_version":"py3","requires_python":"<3.14,>=3.9","size":160863,"upload_time":"2025-04-07T22:18:58","upload_time_iso_8601":"2025-04-07T22:18:58.881418Z","url":"https://files.pythonhosted.org/packages/31/61/24fa78f759c68e1484ed04ed6d0d60ad4b6b58d02570a65dc670975fd954/agentops-0.4.6-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":null,"digests":{"blake2b_256":"d0073869f9b99dbc45ac55bc0dbfd8cf6b22de850a716004135ec96a29c3d81e","md5":"1390e3bc3185a4e97492958c1c4e549c","sha256":"78179a0d2c02217445fb7315bb963496bb338c96bcc126bebfb45a5733fea23e"},"downloads":-1,"filename":"agentops-0.4.6.tar.gz","has_sig":false,"md5_digest":"1390e3bc3185a4e97492958c1c4e549c","packagetype":"sdist","python_version":"source","requires_python":"<3.14,>=3.9","size":254164,"upload_time":"2025-04-07T22:19:00","upload_time_iso_8601":"2025-04-07T22:19:00.589814Z","url":"https://files.pythonhosted.org/packages/d0/07/3869f9b99dbc45ac55bc0dbfd8cf6b22de850a716004135ec96a29c3d81e/agentops-0.4.6.tar.gz","yanked":false,"yanked_reason":null}],"0.4.7":[{"comment_text":null,"digests":{"blake2b_256":"a4be6d708281bd3a282879859231fb7d2ab1d0fec6ee421ec6b02d08a3726670","md5":"3bb2171ad2809a49c43935f1d249aa02","sha256":"b1c4acda70ef45a3c7deac01a695b922a14bb762826ba68fb2b8c3859f4e87da"},"downloads":-1,"filename":"agentops-0.4.7-py3-none-any.whl","has_sig":false,"md5_digest":"3bb2171ad2809a49c43935f1d249aa02","packagetype":"bdist_wheel","python_version":"py3","requires_python":"<3.14,>=3.9","size":182708,"upload_time":"2025-04-24T00:39:39","upload_time_iso_8601":"2025-04-24T00:39:39.403616Z","url":"https://files.pythonhosted.org/packages/a4/be/6d708281bd3a282879859231fb7d2ab1d0fec6ee421ec6b02d08a3726670/agentops-0.4.7-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":null,"digests":{"blake2b_256":"20a5d142e98481d82912280e29b5b73dc5a5deea4d34c132045333b5201c1209","md5":"62c78776d059798f2e6a74bf1b03932f","sha256":"ad6dca62ff88d4c09eda34e3393c138880a5126682b53cf0c881a7dbb61dcc0d"},"downloads":-1,"filename":"agentops-0.4.7.tar.gz","has_sig":false,"md5_digest":"62c78776d059798f2e6a74bf1b03932f","packagetype":"sdist","python_version":"source","requires_python":"<3.14,>=3.9","size":272982,"upload_time":"2025-04-24T00:39:40","upload_time_iso_8601":"2025-04-24T00:39:40.931148Z","url":"https://files.pythonhosted.org/packages/20/a5/d142e98481d82912280e29b5b73dc5a5deea4d34c132045333b5201c1209/agentops-0.4.7.tar.gz","yanked":false,"yanked_reason":null}],"0.4.8":[{"comment_text":null,"digests":{"blake2b_256":"96d32cee2a94f2917be9c7575238dfff3088a51a6376168a2c7287da0e8b654c","md5":"a02a327b4620a909e831fbd6889bf25e","sha256":"86f439d47c0fdfcb3525859528300b19bb96c105875d0b5b3d205260aedc3f24"},"downloads":-1,"filename":"agentops-0.4.8-py3-none-any.whl","has_sig":false,"md5_digest":"a02a327b4620a909e831fbd6889bf25e","packagetype":"bdist_wheel","python_version":"py3","requires_python":"<3.14,>=3.9","size":182678,"upload_time":"2025-04-27T09:10:39","upload_time_iso_8601":"2025-04-27T09:10:39.925403Z","url":"https://files.pythonhosted.org/packages/96/d3/2cee2a94f2917be9c7575238dfff3088a51a6376168a2c7287da0e8b654c/agentops-0.4.8-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":null,"digests":{"blake2b_256":"ba64732ebe57c77123058cbc03eec0795267fac65aa6032b8906b1dfe80ff837","md5":"f947ace32256ff3ee6b2a6c716ef3543","sha256":"c299ca067298f568ae2885e4d21951b0bdb7067692d930b57ff1f19bd447ae5a"},"downloads":-1,"filename":"agentops-0.4.8.tar.gz","has_sig":false,"md5_digest":"f947ace32256ff3ee6b2a6c716ef3543","packagetype":"sdist","python_version":"source","requires_python":"<3.14,>=3.9","size":272951,"upload_time":"2025-04-27T09:10:41","upload_time_iso_8601":"2025-04-27T09:10:41.806172Z","url":"https://files.pythonhosted.org/packages/ba/64/732ebe57c77123058cbc03eec0795267fac65aa6032b8906b1dfe80ff837/agentops-0.4.8.tar.gz","yanked":false,"yanked_reason":null}],"0.4.9":[{"comment_text":null,"digests":{"blake2b_256":"5814e40def8897f404273f69d6841793b3dbdcbb8f2948fb6bd9c50087239b37","md5":"f49c139fbf17affaa3e8165743971a50","sha256":"622b9ecdc1b5e91c5ac3aa92d2f756d083c4e0ba830d8e94c3785f7290587a97"},"downloads":-1,"filename":"agentops-0.4.9-py3-none-any.whl","has_sig":false,"md5_digest":"f49c139fbf17affaa3e8165743971a50","packagetype":"bdist_wheel","python_version":"py3","requires_python":"<3.14,>=3.9","size":198463,"upload_time":"2025-05-02T23:51:48","upload_time_iso_8601":"2025-05-02T23:51:48.502905Z","url":"https://files.pythonhosted.org/packages/58/14/e40def8897f404273f69d6841793b3dbdcbb8f2948fb6bd9c50087239b37/agentops-0.4.9-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":null,"digests":{"blake2b_256":"32efa2af9802799b3d26c570b8dd18669e3577fb58fa093a3c9cfafbf179376c","md5":"5eb22fdc989748711f0252c3679388e9","sha256":"c69a0c912a75367850036c20368d4722462b5769eb86bdebabb0695f8be4c8bd"},"downloads":-1,"filename":"agentops-0.4.9.tar.gz","has_sig":false,"md5_digest":"5eb22fdc989748711f0252c3679388e9","packagetype":"sdist","python_version":"source","requires_python":"<3.14,>=3.9","size":284471,"upload_time":"2025-05-02T23:51:49","upload_time_iso_8601":"2025-05-02T23:51:49.781274Z","url":"https://files.pythonhosted.org/packages/32/ef/a2af9802799b3d26c570b8dd18669e3577fb58fa093a3c9cfafbf179376c/agentops-0.4.9.tar.gz","yanked":false,"yanked_reason":null}]},"urls":[{"comment_text":null,"digests":{"blake2b_256":"76a6fff94368ad5c04128c37bb9c6a7b3cbb4956aed19fb566796900afba9440","md5":"acf57b34328c7d464d8f405e3c0d48a5","sha256":"04f78d3996e03be2716476c25316b99d765f31a78b5352bd8d28f4cb425d9458"},"downloads":-1,"filename":"agentops-0.4.16-py3-none-any.whl","has_sig":false,"md5_digest":"acf57b34328c7d464d8f405e3c0d48a5","packagetype":"bdist_wheel","python_version":"py3","requires_python":">=3.9","size":268341,"upload_time":"2025-06-19T00:52:07","upload_time_iso_8601":"2025-06-19T00:52:07.933214Z","url":"https://files.pythonhosted.org/packages/76/a6/fff94368ad5c04128c37bb9c6a7b3cbb4956aed19fb566796900afba9440/agentops-0.4.16-py3-none-any.whl","yanked":false,"yanked_reason":null},{"comment_text":null,"digests":{"blake2b_256":"c6e8ca6c289a2af9af2140ddf97271b6060cd052dfdfd44c438667d379c3f95a","md5":"60214a3ffc818ce3cbfc3123d8c354f3","sha256":"0d2dff064be938b355522c25907538b331e2049188027275b4fd4840187f283e"},"downloads":-1,"filename":"agentops-0.4.16.tar.gz","has_sig":false,"md5_digest":"60214a3ffc818ce3cbfc3123d8c354f3","packagetype":"sdist","python_version":"source","requires_python":">=3.9","size":335321,"upload_time":"2025-06-19T00:52:09","upload_time_iso_8601":"2025-06-19T00:52:09.730961Z","url":"https://files.pythonhosted.org/packages/c6/e8/ca6c289a2af9af2140ddf97271b6060cd052dfdfd44c438667d379c3f95a/agentops-0.4.16.tar.gz","yanked":false,"yanked_reason":null}],"vulnerabilities":[]}

        '
    headers:
      Accept-Ranges:
      - bytes
      Connection:
      - keep-alive
      Content-Length:
      - '147037'
      Date:
      - Tue, 01 Jul 2025 17:36:00 GMT
      Permissions-Policy:
      - publickey-credentials-create=(self),publickey-credentials-get=(self),accelerometer=(),ambient-light-sensor=(),autoplay=(),battery=(),camera=(),display-capture=(),document-domain=(),encrypted-media=(),execution-while-not-rendered=(),execution-while-out-of-viewport=(),fullscreen=(),gamepad=(),geolocation=(),gyroscope=(),hid=(),identity-credentials-get=(),idle-detection=(),local-fonts=(),magnetometer=(),microphone=(),midi=(),otp-credentials=(),payment=(),picture-in-picture=(),screen-wake-lock=(),serial=(),speaker-selection=(),storage-access=(),usb=(),web-share=(),xr-spatial-tracking=()
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains; preload
      Vary:
      - Accept-Encoding
      X-Cache:
      - MISS, HIT, HIT
      X-Cache-Hits:
      - 0, 5234, 0
      X-Content-Type-Options:
      - nosniff
      X-Frame-Options:
      - deny
      X-Permitted-Cross-Domain-Policies:
      - none
      X-Served-By:
      - cache-iad-kjyo7100059-IAD, cache-iad-kjyo7100044-IAD, cache-gru-sbgr1930071-GRU
      X-Timer:
      - S1751391361.550252,VS0,VE128
      X-XSS-Protection:
      - 1; mode=block
      access-control-allow-headers:
      - Content-Type, If-Match, If-Modified-Since, If-None-Match, If-Unmodified-Since
      access-control-allow-methods:
      - GET
      access-control-allow-origin:
      - '*'
      access-control-expose-headers:
      - X-PyPI-Last-Serial
      access-control-max-age:
      - '86400'
      cache-control:
      - max-age=900, public
      content-security-policy:
      - base-uri 'self'; connect-src 'self' https://api.github.com/repos/ https://api.github.com/search/issues
        https://gitlab.com/api/ https://analytics.python.org fastly-insights.com *.fastly-insights.com
        *.ethicalads.io https://api.pwnedpasswords.com https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/sre/mathmaps/
        https://2p66nmmycsj3.statuspage.io; default-src 'none'; font-src 'self' fonts.gstatic.com;
        form-action 'self' https://checkout.stripe.com; frame-ancestors 'none'; frame-src
        'none'; img-src 'self' https://pypi-camo.freetls.fastly.net/ *.fastly-insights.com
        *.ethicalads.io ethicalads.blob.core.windows.net; script-src 'self' https://analytics.python.org
        *.fastly-insights.com *.ethicalads.io 'sha256-U3hKDidudIaxBDEzwGJApJgPEf2mWk6cfMWghrAa6i0='
        https://cdn.jsdelivr.net/npm/mathjax@3.2.2/ 'sha256-1CldwzdEg2k1wTmf7s5RWVd7NMXI/7nxxjJM2C4DqII='
        'sha256-0POaN8stWYQxhzjKS+/eOfbbJ/u4YHO5ZagJvLpMypo='; style-src 'self' fonts.googleapis.com
        *.ethicalads.io 'sha256-2YHqZokjiizkHi1Zt+6ar0XJ0OeEy/egBnlm+MDMtrM=' 'sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU='
        'sha256-JLEjeN9e5dGsz5475WyRaoA4eQOdNPxDIeUhclnJDCE=' 'sha256-mQyxHEuwZJqpxCw3SLmc4YOySNKXunyu2Oiz1r3/wAE='
        'sha256-OCf+kv5Asiwp++8PIevKBYSgnNLNUZvxAp4a7wMLuKA=' 'sha256-h5LOiLhk6wiJrGsG5ItM0KimwzWQH/yAcmoJDJL//bY=';
        worker-src *.fastly-insights.com
      content-type:
      - application/json
      etag:
      - '"mVxu5Y9b1sgh2CIUoXK8BQ"'
      referrer-policy:
      - origin-when-cross-origin
      x-pypi-last-serial:
      - '29695949'
    status:
      code: 200
      message: OK
- request:
    body: '{"input": ["Research a topic to teach a kid aged 6 about math."], "model":
      "text-embedding-3-small", "encoding_format": "base64"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '129'
      content-type:
      - application/json
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.78.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.78.0
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.12
    method: POST
    uri: https://api.openai.com/v1/embeddings
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1R6yxKyzLLlfD/FF//UPiH3KvYMARG5FQIKdnR0gCACInKpAurEefcO/Dr6MnGA
        BFBZmSvXWln/+a8/f/7psrp4TP/8+88/72qc/vlv27U8ndJ//v3nv//rz58/f/7z9/v/3Vm0WZHn
        1af83f77s/rkxfLPv/8w/+fK/73p33/+0evljruXAzL6sYcC1kvoeuy8l7K5Hq0UHPr5RUwpC0IG
        8WsDE3FWkT2+HID1/aWSD9VVI9n4YrJFP4mljHIVIGc9JeHCZnkLboUlYnEeLuFsOIICR9MrsSBU
        l3q6p/cSnkueQYdG9zI+WnVfPhWV5UnKgavnuNobUtAKAbqIr3pYrpp1k88lyyBbg3NISbb0oFNg
        TJ7IqcNVfbm6FLD2g+jf8hpSjQMFGM57j9hC+NGoyvEeZMfKJMect8GyVg8MdFefUb4HusZ5ZlVC
        sV4DdK6VfFhsLuxlCFeX6Jx8r/m8rLD8+D5acroAZng/0K4Bx744I/PemfQ7BooElyZpkFuNrrY2
        7E6SXrf6QA63z5nOOJwKAA39hUIvz8BsNWcDyuGDQzeJujV3ZoQdNI/eFWn+DYLxanepXMDeJ945
        MetxxjddrpeLS651daV8EPIMLAa6YO6D7vbCi7UgN/yxIM99bYJZx99e3l1wS47ey6jnBT8cyfB9
        Ea9kqUKGhC8szzLmias1j3rx1jmSX3h3ICj53sC6e2IT9JH5RMWNjbLWM5tE/vSBg6IdASEVDMaX
        0aTmJDUPijYbnzaQc5w9kDvHCmVT1hXE2PV1onqVlG3Ps2D0RYC4x8Gh/CVdbnJuNogEz/1uwLaD
        MCiu0ECqw5t0KZYSy+/W+ZKYg29K5yTV5ZY2JcrnuATLsRpVuI/LO3ra6WFYvnyqwyr3EHGk0zlj
        Pu9zAqkiKuS85R8dZaJA/Vghbx32ZU2Uh9HIvLwXPEmwuHC01NCRq2heUODNF43ZBSGU+4woGHCp
        YTO6A1YgvewjOb4zjq7hTpuhwXMFURbmRL+QaDc5uEkz0YfPO1xdR9VlTtRULIPpEa7I7HrIqdcb
        Ct/UGpj3+6TCUn3o5O7uDbAYCuPDx2wjoqWHZ8j61smAsc61SAHTQSOHh8VBr40ROkzyqSbJOU8g
        uhYpsTXI0yVs6E4+rNEbQzfKKe1MdwUxqAJkRCctY+FASngUyILcvviENb08OMicWg/FEj7VNApX
        R/4Irxux0PtOeYPASCrGDhOnFHpAjUsfwHOX8cjB9ZtSLL9SsNUzOkSlCGg7ZQ5Qmp2Ppy6OM6bV
        4kC+EnBGuqa+hlUH8gjk6p15rEqcjD18sSSBmFuIcka6vbIBX8KPOn7QXTdYexbK1AFzLJjI+qpK
        yKxO38CSvydIKWNbY/IkmCHP+xGKjBbUo/FpfTnABUVObryG9UYxhG9UKigLyzHEZmhw8nL18x9+
        ZPMgwBnkN/aODq19BEt311bAFD5HVOd7H9i7MDRQHCJKnlV0z9ZKuQgyvBkaMqXvq178ARjwWCmD
        J7NMNjCZXBkQtEdAsr4LQ55eUgi3fCJ2rBeA5mpawVx+1UQVPRaMXXGS5DhiTHIRX1rdpRm/g8dA
        MpA2jENGmhn2gDfPH6IcUajRvtql0Ff5gZys+DwwC448OCaOun2vWK+eOSaSa2GPKE1cD3Q6HBR4
        Mz4R0q1EDqkHWwzNTJDRFRgVHREvNeB69zR8OVqTPYsPpoSuX0sbPi8D9WG/g+CZMuh0nzBYi3ez
        g55SqcQWjjxdpans5Mg6MsiMyQnQQa8KmHo4Jed23YfjIWRmsNq7M0KL4IJJioJRbnbfF+avwNV4
        oCUQ8rIsIHQOe225vZIObvtHUOOm2er1gS9fhE7/m18YtnUKhksdkNOzNTXeoKkKc47LkancF7o+
        NdaDcl0kyPC8JVxZZqigXTg+uWdeBlY+WCQ4Px82iR2VglXL3xX8MmcRBcfjx8bz3t3BurINz/ek
        68B3X9rLjsgTgsTetLkmbrFs1vlh2w97YONcglDDjE68SQbh8qVcIF9mr8X7U/q1KZa/CYQA+Mg9
        DsdsNW44AaYtZuRSBmO9Xp5sAXffwPZEJCCbGDRQoZhdLyh384/9gemnk69d1BGnr92MP5G1kBvy
        Skn8TBI66o7cwebgE3RmBjWjysNr4T1RMDGR7A7jXf5g8EJMgtcNT1noiwGcd01AbNzZYDlVXSBL
        i/VBD/s7as1+8h5i6ExPvE79mi0JPPsgmN4J+uUf96yOifyxLqYXfbE+LGsVYbnydBMlzU7WaKbe
        /F88PSGUWxvnX6sH5NxxJFxitl6CLizl4jH0nsgMVbiSkp3hcHkFqCjL2J5kci5gnNUjlsIY1NPn
        8Srk0xtdNvxeslV9HQ3I77G/5a8KOH1/KWHx1l3iLwwJ8db/4YgPEzF3mUXZJM19Ocz41purohpW
        7WU7cIsHSfD3WfM7eJllBK0GWdyzqVczNBg4WJxNHmHWhvip0Uj+skqCHu2iazNT7xNwthUTFejy
        zUg8HE1Ypx8VuafGHah+IYKExSHyqLOqdPCKWyWDmFk2PiEP8+r0rSRXn4w4bnWs2evrkYqnmz4i
        5RGn9t/48X7Tod/7l0eYJoIWBwfcqeU3XDV1MeV2VB9e+DA7be5hFMnZ4HB4+vUXpVMjueF3GTKv
        nR/yj+PsyHL9SMhVEkbtb354H89BJ9Nfbfqe1xnyh0JD1mW0bPZ7bXXZk0Hg8R8rHdjSuSTweH/3
        JNs5SkiHg9fB9Jq9vIC/RoBTOuUhX8/ei1hza2ezGooKvPNmjDRDnCnFyRPDu5Kf8bo8GA2/I2TC
        U6t75MaM+TAzNZ/I7uFO8XisoN0ve8uBVe4gou8IyOh7FgS5jQqe6NHlA7b4SrCcx8CTzqNo363k
        eINvJqdI1XmekvtnauA1iY2/+LcewbKDN3Z8EtOxm3o6yVEHQ0meSOpKCsXAvBryLrmPRDeYfuMX
        GYRvw3171XNfDKQzNUH+2NaReKcnspkxdlXYlXqBrNw3tImcPhxk3fSJeSNetJUVzg748dVsDjmb
        rlRvIPW1tyeH5sWea3e+yTajFUg9HG9b/3r0cMe/qNddu5jijR+AJHBybykDp56pU2J5408o0tRD
        TYdK6iGP+YVYHXP+4bsKtVMge/z3ooUrMDMPTtc2xhKTTNpqLrEi/fDCQZkLZjVcVGnbD3KDo1cv
        h28rwGP/OJPDlzzp6vVJAl6enJHzxzuGI2TcFrbhR8JyZqo2p+/vFXi1VU1sxTqFLC9WirzFCxmw
        eGbLOAc7+cdfz+e9l63s7fKQt/5K9Bfss1l35B5eP7lL7u/TBPAH86t83u8lpFaGR2c3N3WouHcT
        2Tbu6y1e0Y8fepBR7iF+Vm7yW5+3OPAULobu6/LpdrLwwr5se+5ewwiZU+OR4BC+Q3xM3R1cqz5C
        DqOIIXny+xIiIwqJ71NiL7eX34OR/SwIBTunHrNnosL7DguetOHBwmbXFo5KnxNLOdyGeR8kDmj2
        ZUEuN7YcqHU6PqR4dXlknz/pQFGRrhCYo4Nis3iF1CRCC1nqIrLxj6xv4naE+ujxyGtKU+MiqXHk
        KChZkrn3R0Z88WRBp7qM6LT1p97tI0sOAdNjKVeLjIqGlMBnNsck+ApnbX5k6w0+ureN+R4+skXi
        zAYyT/NCwh1z3/jfcYXQHVRkfG/njDinwwjeqFKQbrXOwLYSm4Kt/xBTDYJ6ttT7Df70iXK6WcNc
        RKwOXQE+sMg1/TB6IPHhvu12JMlIma30vkrAyT4ank+3vl5u+x7D66FXvM+QqBnt2nwHDeXC/uWL
        s3mUZmiCg+6Jo9hpNFodXxQfnoelHpjDPBRKCvIYMyTuIczWrT7hhV9OG96hYR7SCoO6OhskmIRX
        TbuvOAPjYK3IesZHe+LE9wxNZ+U8eVXvYFHtuw5Kwae/9dTLlw90eatX5AHuOMzZ8y4BnCUdOQ6V
        AeZM7g347IWCHByc2NhcnioEio9J1sgHyi15dAM3hq02PZOG668/nLzawEuEv/ZCvhkDmfwuEmun
        8fbSSmwCS594yKMctBchXgP5YC05Uh7vA5ij786B0Ctz9GyjPpzQsGNgcwkuRG2NT7Yu5WzJM25m
        lMX6jo5P7qXLZzG4IW/UpW39oAPPMBJJ2jEXmxEfTAWx47+Q6dh6za66U8JNr3lzec7ounu2Fsj0
        ckTn/PSgKxvsK/CxQhPzFxDVswj0Uf4ytoj5wRrqJYZHFWB9VNBdj/SQe0FOgXXfNFh4pZq2Qp0t
        wa/fKoWf0ilJw0TO9NIjLstkNRXeRgSJql/Rk6/32eAVRSkNX4Pf9OJp4Nr4Zcgb3/XwrA81fe7E
        v/hKog+rDeuvfn9+wsbHa/7bPnRYHtovcs+5V09Bl5Vw1aoBnTKmzdav/9rJDX8q8LT6X5ty3SRJ
        cT9QZF2Lgc4anlIIgegj856KA9WSVyoXx9MHnaxzQHlY2AVoLv4FV/w1orjzBw4uI1cSj+9y0K1n
        b/7hATKbXW7TKJQ8MGJtwtKmf9myrx8ykL87ct/6/VR+UAcu51JE6sPF9gT9JYBfLxpQQJYvGN5T
        rsPKiWfigDUFY2SPJgwM4CGjzvmQvpEjwLSpO+JKqhbyxmW0YKA4JnoeXyX48X34CcYzck+rHs6v
        y2WVZ868kYsYRuFAJBvDJHAAMr5mqi2e2/bQeGbgl78ZbWamgxfzCz15DxptXs/e+sNDYu/5e0bE
        rgwkST08UI79ucYxb0oQso6FjEwcQ1oHYwHgFRmexF3Y7BdvuLuMJoqv0y1c+UAUoOJmpifFby1k
        dDyUsHfajDiNoGVdX1g+PLEOJcm14+kqIr+AB82BeG5irZ7382jBz4V3iHe6POnCx4wJ1bK5kmDY
        KzX7vWIDmkfnShLp+xr+8rf7ITJQtOEbZUe3AK6554iRDWy9TPNdh/udVxMVXN9gfb6/Dox6XUAO
        d1XsJQsjX1a/yoUcLJbXltHDEERBxRLtM660nz5AgbWm7MlBDJlsLiLZgNz3UCKn6Rltbg1gQHcu
        dygOGTZcLpYWSfOuDTy5GJmaxA4Dwd5MA8zQ+0dbBZRG8PX+tli0Ba5efPerSIycygghRwuZ1+W+
        gl+/1j5jAOZcsipQUSHd/ICXjeldEqDvWAZxmXNVs1ZyjMAzeEJPVLM6m3czVmB5+Jy8Yus3a3zZ
        e8Cdy5I8GM+sF++iPuSc+Hdy8Fg08J1pC3D/NCSP3vJ3uN4vYgtfp0RC7rb/dPt+6Kvs4MmSWocU
        aD6Ut3ii08jK4RiiAwe7+Jtj9sXrIUfUAf/wj5xnsayp91pv8HyYpq2exWFmVdQB4ZZmHqODqqZa
        UvvyUZgWEr2glS0SOelwRMsTswqn2xOxzyssGGcl1yA0Q9q3qQfiMbXJ8TlHNrvvHA928ZAjdzyL
        GrnEcQISQTwindo8GPvCCmBFNYA09hyHfIsOloT72kXKIT8DJucaBr52RkuOjvMEc1cgCRrn1keW
        cuCG1bx1M+T91iK36fYCP/z8qzfd/bOwl8KJVWCRnYp0i9Eyrso8HzJyIiMbp6w2S495lc3103ri
        ++QCFn+fDeg+/QuXydsHDHd8tXJUlTHSb9ZsL03pzIBPrIGcYmaia5UZAXi8Dib58YPpfZiZnx/o
        8St0w0Vc2x42Cbd63+353EvJZsiOpUny/KDZDP3cIJxyUyCbIKXfJM0DKLxfCjkqnD3MxD8KcP6o
        CLNDxA+zdI928vd6WD2qiknWLf2uBEOQIFRo0xEsgXUo5MbkfSzUL9Xmr/6jhPR8g0Q1diRbCuep
        gOFV3TwaIHnrLw6GwnTUSNoDs16XverI8OoaKD+t+sYP6Qyd2vySfIy7bGRV1IMrEc/eckhqsERG
        qf79/qOeFhpVH8pO1tiXTfThfrPnm95KEB3zG7IuhUTpeUyFjR8/iX356DanGNEDSIt5JuefH1AV
        ww62D1Xa8OFaLz3vezB1+RydP947pI9G9WX/+2KRCatqmPeTV0hhdDkSS0FHwKsc7wCs8RoxmDGv
        p/dBYKBYgau37B1WW1+QU+HNs2Nievoxo877IsE6VF/EvaJXOLv3JpDTidOQtfE79hfPr/yI0DFx
        XnRpSn2GW7/3aDDU9foYewdWw5PHiXJfAE0bQ4XNZydu+nwYxu9eUsHmVyP7ZZf2fDoHN8gOXxul
        l7GqmbhNJZjH4w15XbCEy6bvpGHuAUKjq/3W40nbfhFLukx0xcY5AcVUnZFeaqo91qOVwKDVrsRQ
        H0W9SFHCQXLvEmStTzoMmx4GPz172PwNanrWCMoSz+g8i8pAx2qe5Z8fdb7UYob9Oldh+bU8pHyZ
        Ilw2/1ze4ouedD9SfDASDEshuOK9e7QBH+MOgl3JTH/3d26stIehQ55b/e20KQtvFiy78IJ//gJd
        qdNA/jn1RGNjo15OVenD+8diCbKdNZt896v++h+mEIgaWRLfkQ+6EXt1dTApO9C1lNuHcEWXg8XU
        HVeD7q+frJJFDZdUeI2wM0uBHCb5M6z72jTAxlfwfDIpWNZTF0lbviJjpY+BahwtpDXTsQdwN9D5
        s9938FIinbjhTQlZ82k1P//Nk8Pbh87LIqiQ1fcHssUrHHPJ7GE8VDPRApTXv/eDQU0ncv4oeFjl
        2sXgbgofFMpnhXKkubSQkvOw+WNWthT9x4MPoZlQeqKCht9xvwNbveLdsW3B+hZ9TtwXrkyO2/xn
        0lutlal/eHsMsA8hK3cIQkDS26ZPQrqcnk4Bi8bF5PS63Okct4Eg2w2IibNeP8PM1+IKIi31//4/
        SvcIwunasH/xjV5S4MHgtNronPUHOhPflf7qK9XhX+H63mSAJh895GPVsOnQJhEcTWeHbqmia8y5
        /FhAfHjjXzxdrw8/+PkDyJFO33AittbJGx8hz9vuaLPt7uL85g9/+Te3qw87mfHC9W9+8sVulaTL
        yzhgJkhwSHmDM+VzbyFvPuZqxt2StZFZaQnQ6XuxKB2eBQMj68Tg9XDk7BmHdQNzxVz+zseocRlN
        OEVEQAfCmdk8CYsis85L3/x3DywvE4y/eQ9x0LfZ+uGpl9/eV0PnuP9mvdenwe97vdWbF5tG7HcH
        6WHO8FLdG/uzE6ABN/+KaMGgDdxbTLjf/APL18NqL75bScDaIYf89O28zY+g3Uiix18nLlvMrJHg
        T48Y75sQ4lBqdXmhrzNKz+PdXsyn2sLxc/sQXeCkgfzmTRufQZaQvOsZ8JMpOY9CQXnvJ9qkV2QG
        m/7BLzkc6VJ/FBMegvjlsZMrDCQ5X1O48UVySD9qxv/4/mHJO6JBINoUP+8eTG9BQVTNcQeu1Jbi
        Nw8i95//J059BZ9VrpCk53M6z4soSHfSLp7cxYSS62qlcNNLJKL3j42Pws2E7iXhvefhhShrZqMA
        mTcjkfyYy8N6UBkBho+x3uoT1uumx6GcCRE6hcw1nIAbRzDRjBtmcriAxRKXCH7dHGOGDUyNNRQY
        wL5nTx7/en7tlRE0Xf7xDzWYVY1jb/cCUmIPyNF4K2POjbWDYeN/SDaHN3vmatD/9I4nxORDySgT
        Faifi+iVRWFpaw1XH67B10fuxs8XthBT8OMvDnJ3w9Ic/RS2o/Ig/iOW7G0/LHBj8ROLO/NaU5Xb
        ezC5rzuiHo8ne/N7JLimKPz1m5D7+W2usHsge8+LGRENKYW1/cjJdeMDfFGWjMyCDnlw9uJhFaYm
        gS/EJciYbgfAavqzBXuiP9DNeJCa9KZiQRHcruSodDDDipG2wImUCnm9LQ7z+40U2A9jRTa/2H6f
        s0GF13I9Ec1c+3A1bm0if1T8Qd7GL9ZNf/Pn7s4T3XzCer0lUgvN4VCRQ1TewbzN50AgNxI5n/c4
        2+rfl0u10Inb+4I9375CC89B6xDUZKrG6UDGP7/Ta+/rlBF2PBY//MUS99TrryUCD6QPuUHuQdRq
        7uc3QSZ2iQGLfYiP+XeGDX9SkIm/+3q9HIYUOpFaYSiLg0Z+euOzMD45dHoLlmzBEfg7b97mXWvm
        BYqsu8bszZ7E1quRqh74zRd3oqwD3tHeElR3PCTmFh96EbUECvlpj7Z+DP7ODx8v4Ug2PwAs8jCs
        MM5eoyc/aAgopSYH03fyxpgluc36biXISgQx0oH9ylZo3m/wo05HlOTdK5wLrtqBf36nAv7rX3/+
        /I/fCYO2y4v3djBgKpbpP/7PUYH/4P9jbNP3++8xBDymZfHPv//3CYR/vkPXfqf/OXVN8Rn/+fcf
        lv971uCfqZvS9/97/V/bq/7rX/8LAAD//wMA/z6h5eAgAAA=
    headers:
      CF-RAY:
      - 9587a9c37b02a3f6-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 01 Jul 2025 17:36:01 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=MbKL2xE2KKstdR6eiZ_bFoVcv3TbBWfXYQMOcdDxJGw-1751391361-*******-P7iB6ruG.6Ire362NgpifzBpobaJ7XT8VAZZ0a12f0OKbkuw9Yu3OROWxxWHebqozezKSnumCbnjSwKI80Xfh7xoX8JFI4am4YSpaiPnF.0;
        path=/; expires=Tue, 01-Jul-25 18:06:01 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=gvypJd6pfIYst9Fe_P5G7.xTd5AdD0r5RsT8X4f4zlQ-1751391361098-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-allow-origin:
      - '*'
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-model:
      - text-embedding-3-small
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '45'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      via:
      - envoy-router-7dff84575b-ffwp6
      x-envoy-upstream-service-time:
      - '47'
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '10000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '9999987'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_1e31c95af2c3499634f265711cc85a69
    status:
      code: 200
      message: OK
- request:
    body: '{"input": ["Research a topic to teach a kid aged 6 about math."], "model":
      "text-embedding-3-small", "encoding_format": "base64"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '129'
      content-type:
      - application/json
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.78.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.78.0
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.12
    method: POST
    uri: https://api.openai.com/v1/embeddings
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1R6yxKyzLLlfD/FF//UPiH3KvYMARG5FQIKdnR0gCACInKpAurEefcO/Dr6MnGA
        BFBZmSvXWln/+a8/f/7psrp4TP/8+88/72qc/vlv27U8ndJ//v3nv//rz58/f/7z9/v/3Vm0WZHn
        1af83f77s/rkxfLPv/8w/+fK/73p33/+0evljruXAzL6sYcC1kvoeuy8l7K5Hq0UHPr5RUwpC0IG
        8WsDE3FWkT2+HID1/aWSD9VVI9n4YrJFP4mljHIVIGc9JeHCZnkLboUlYnEeLuFsOIICR9MrsSBU
        l3q6p/cSnkueQYdG9zI+WnVfPhWV5UnKgavnuNobUtAKAbqIr3pYrpp1k88lyyBbg3NISbb0oFNg
        TJ7IqcNVfbm6FLD2g+jf8hpSjQMFGM57j9hC+NGoyvEeZMfKJMect8GyVg8MdFefUb4HusZ5ZlVC
        sV4DdK6VfFhsLuxlCFeX6Jx8r/m8rLD8+D5acroAZng/0K4Bx744I/PemfQ7BooElyZpkFuNrrY2
        7E6SXrf6QA63z5nOOJwKAA39hUIvz8BsNWcDyuGDQzeJujV3ZoQdNI/eFWn+DYLxanepXMDeJ945
        MetxxjddrpeLS651daV8EPIMLAa6YO6D7vbCi7UgN/yxIM99bYJZx99e3l1wS47ey6jnBT8cyfB9
        Ea9kqUKGhC8szzLmias1j3rx1jmSX3h3ICj53sC6e2IT9JH5RMWNjbLWM5tE/vSBg6IdASEVDMaX
        0aTmJDUPijYbnzaQc5w9kDvHCmVT1hXE2PV1onqVlG3Ps2D0RYC4x8Gh/CVdbnJuNogEz/1uwLaD
        MCiu0ECqw5t0KZYSy+/W+ZKYg29K5yTV5ZY2JcrnuATLsRpVuI/LO3ra6WFYvnyqwyr3EHGk0zlj
        Pu9zAqkiKuS85R8dZaJA/Vghbx32ZU2Uh9HIvLwXPEmwuHC01NCRq2heUODNF43ZBSGU+4woGHCp
        YTO6A1YgvewjOb4zjq7hTpuhwXMFURbmRL+QaDc5uEkz0YfPO1xdR9VlTtRULIPpEa7I7HrIqdcb
        Ct/UGpj3+6TCUn3o5O7uDbAYCuPDx2wjoqWHZ8j61smAsc61SAHTQSOHh8VBr40ROkzyqSbJOU8g
        uhYpsTXI0yVs6E4+rNEbQzfKKe1MdwUxqAJkRCctY+FASngUyILcvviENb08OMicWg/FEj7VNApX
        R/4Irxux0PtOeYPASCrGDhOnFHpAjUsfwHOX8cjB9ZtSLL9SsNUzOkSlCGg7ZQ5Qmp2Ppy6OM6bV
        4kC+EnBGuqa+hlUH8gjk6p15rEqcjD18sSSBmFuIcka6vbIBX8KPOn7QXTdYexbK1AFzLJjI+qpK
        yKxO38CSvydIKWNbY/IkmCHP+xGKjBbUo/FpfTnABUVObryG9UYxhG9UKigLyzHEZmhw8nL18x9+
        ZPMgwBnkN/aODq19BEt311bAFD5HVOd7H9i7MDRQHCJKnlV0z9ZKuQgyvBkaMqXvq178ARjwWCmD
        J7NMNjCZXBkQtEdAsr4LQ55eUgi3fCJ2rBeA5mpawVx+1UQVPRaMXXGS5DhiTHIRX1rdpRm/g8dA
        MpA2jENGmhn2gDfPH6IcUajRvtql0Ff5gZys+DwwC448OCaOun2vWK+eOSaSa2GPKE1cD3Q6HBR4
        Mz4R0q1EDqkHWwzNTJDRFRgVHREvNeB69zR8OVqTPYsPpoSuX0sbPi8D9WG/g+CZMuh0nzBYi3ez
        g55SqcQWjjxdpans5Mg6MsiMyQnQQa8KmHo4Jed23YfjIWRmsNq7M0KL4IJJioJRbnbfF+avwNV4
        oCUQ8rIsIHQOe225vZIObvtHUOOm2er1gS9fhE7/m18YtnUKhksdkNOzNTXeoKkKc47LkancF7o+
        NdaDcl0kyPC8JVxZZqigXTg+uWdeBlY+WCQ4Px82iR2VglXL3xX8MmcRBcfjx8bz3t3BurINz/ek
        68B3X9rLjsgTgsTetLkmbrFs1vlh2w97YONcglDDjE68SQbh8qVcIF9mr8X7U/q1KZa/CYQA+Mg9
        DsdsNW44AaYtZuRSBmO9Xp5sAXffwPZEJCCbGDRQoZhdLyh384/9gemnk69d1BGnr92MP5G1kBvy
        Skn8TBI66o7cwebgE3RmBjWjysNr4T1RMDGR7A7jXf5g8EJMgtcNT1noiwGcd01AbNzZYDlVXSBL
        i/VBD/s7as1+8h5i6ExPvE79mi0JPPsgmN4J+uUf96yOifyxLqYXfbE+LGsVYbnydBMlzU7WaKbe
        /F88PSGUWxvnX6sH5NxxJFxitl6CLizl4jH0nsgMVbiSkp3hcHkFqCjL2J5kci5gnNUjlsIY1NPn
        8Srk0xtdNvxeslV9HQ3I77G/5a8KOH1/KWHx1l3iLwwJ8db/4YgPEzF3mUXZJM19Ocz41purohpW
        7WU7cIsHSfD3WfM7eJllBK0GWdyzqVczNBg4WJxNHmHWhvip0Uj+skqCHu2iazNT7xNwthUTFejy
        zUg8HE1Ypx8VuafGHah+IYKExSHyqLOqdPCKWyWDmFk2PiEP8+r0rSRXn4w4bnWs2evrkYqnmz4i
        5RGn9t/48X7Tod/7l0eYJoIWBwfcqeU3XDV1MeV2VB9e+DA7be5hFMnZ4HB4+vUXpVMjueF3GTKv
        nR/yj+PsyHL9SMhVEkbtb354H89BJ9Nfbfqe1xnyh0JD1mW0bPZ7bXXZk0Hg8R8rHdjSuSTweH/3
        JNs5SkiHg9fB9Jq9vIC/RoBTOuUhX8/ei1hza2ezGooKvPNmjDRDnCnFyRPDu5Kf8bo8GA2/I2TC
        U6t75MaM+TAzNZ/I7uFO8XisoN0ve8uBVe4gou8IyOh7FgS5jQqe6NHlA7b4SrCcx8CTzqNo363k
        eINvJqdI1XmekvtnauA1iY2/+LcewbKDN3Z8EtOxm3o6yVEHQ0meSOpKCsXAvBryLrmPRDeYfuMX
        GYRvw3171XNfDKQzNUH+2NaReKcnspkxdlXYlXqBrNw3tImcPhxk3fSJeSNetJUVzg748dVsDjmb
        rlRvIPW1tyeH5sWea3e+yTajFUg9HG9b/3r0cMe/qNddu5jijR+AJHBybykDp56pU2J5408o0tRD
        TYdK6iGP+YVYHXP+4bsKtVMge/z3ooUrMDMPTtc2xhKTTNpqLrEi/fDCQZkLZjVcVGnbD3KDo1cv
        h28rwGP/OJPDlzzp6vVJAl6enJHzxzuGI2TcFrbhR8JyZqo2p+/vFXi1VU1sxTqFLC9WirzFCxmw
        eGbLOAc7+cdfz+e9l63s7fKQt/5K9Bfss1l35B5eP7lL7u/TBPAH86t83u8lpFaGR2c3N3WouHcT
        2Tbu6y1e0Y8fepBR7iF+Vm7yW5+3OPAULobu6/LpdrLwwr5se+5ewwiZU+OR4BC+Q3xM3R1cqz5C
        DqOIIXny+xIiIwqJ71NiL7eX34OR/SwIBTunHrNnosL7DguetOHBwmbXFo5KnxNLOdyGeR8kDmj2
        ZUEuN7YcqHU6PqR4dXlknz/pQFGRrhCYo4Nis3iF1CRCC1nqIrLxj6xv4naE+ujxyGtKU+MiqXHk
        KChZkrn3R0Z88WRBp7qM6LT1p97tI0sOAdNjKVeLjIqGlMBnNsck+ApnbX5k6w0+ureN+R4+skXi
        zAYyT/NCwh1z3/jfcYXQHVRkfG/njDinwwjeqFKQbrXOwLYSm4Kt/xBTDYJ6ttT7Df70iXK6WcNc
        RKwOXQE+sMg1/TB6IPHhvu12JMlIma30vkrAyT4ank+3vl5u+x7D66FXvM+QqBnt2nwHDeXC/uWL
        s3mUZmiCg+6Jo9hpNFodXxQfnoelHpjDPBRKCvIYMyTuIczWrT7hhV9OG96hYR7SCoO6OhskmIRX
        TbuvOAPjYK3IesZHe+LE9wxNZ+U8eVXvYFHtuw5Kwae/9dTLlw90eatX5AHuOMzZ8y4BnCUdOQ6V
        AeZM7g347IWCHByc2NhcnioEio9J1sgHyi15dAM3hq02PZOG668/nLzawEuEv/ZCvhkDmfwuEmun
        8fbSSmwCS594yKMctBchXgP5YC05Uh7vA5ij786B0Ctz9GyjPpzQsGNgcwkuRG2NT7Yu5WzJM25m
        lMX6jo5P7qXLZzG4IW/UpW39oAPPMBJJ2jEXmxEfTAWx47+Q6dh6za66U8JNr3lzec7ounu2Fsj0
        ckTn/PSgKxvsK/CxQhPzFxDVswj0Uf4ytoj5wRrqJYZHFWB9VNBdj/SQe0FOgXXfNFh4pZq2Qp0t
        wa/fKoWf0ilJw0TO9NIjLstkNRXeRgSJql/Rk6/32eAVRSkNX4Pf9OJp4Nr4Zcgb3/XwrA81fe7E
        v/hKog+rDeuvfn9+wsbHa/7bPnRYHtovcs+5V09Bl5Vw1aoBnTKmzdav/9rJDX8q8LT6X5ty3SRJ
        cT9QZF2Lgc4anlIIgegj856KA9WSVyoXx9MHnaxzQHlY2AVoLv4FV/w1orjzBw4uI1cSj+9y0K1n
        b/7hATKbXW7TKJQ8MGJtwtKmf9myrx8ykL87ct/6/VR+UAcu51JE6sPF9gT9JYBfLxpQQJYvGN5T
        rsPKiWfigDUFY2SPJgwM4CGjzvmQvpEjwLSpO+JKqhbyxmW0YKA4JnoeXyX48X34CcYzck+rHs6v
        y2WVZ868kYsYRuFAJBvDJHAAMr5mqi2e2/bQeGbgl78ZbWamgxfzCz15DxptXs/e+sNDYu/5e0bE
        rgwkST08UI79ucYxb0oQso6FjEwcQ1oHYwHgFRmexF3Y7BdvuLuMJoqv0y1c+UAUoOJmpifFby1k
        dDyUsHfajDiNoGVdX1g+PLEOJcm14+kqIr+AB82BeG5irZ7382jBz4V3iHe6POnCx4wJ1bK5kmDY
        KzX7vWIDmkfnShLp+xr+8rf7ITJQtOEbZUe3AK6554iRDWy9TPNdh/udVxMVXN9gfb6/Dox6XUAO
        d1XsJQsjX1a/yoUcLJbXltHDEERBxRLtM660nz5AgbWm7MlBDJlsLiLZgNz3UCKn6Rltbg1gQHcu
        dygOGTZcLpYWSfOuDTy5GJmaxA4Dwd5MA8zQ+0dbBZRG8PX+tli0Ba5efPerSIycygghRwuZ1+W+
        gl+/1j5jAOZcsipQUSHd/ICXjeldEqDvWAZxmXNVs1ZyjMAzeEJPVLM6m3czVmB5+Jy8Yus3a3zZ
        e8Cdy5I8GM+sF++iPuSc+Hdy8Fg08J1pC3D/NCSP3vJ3uN4vYgtfp0RC7rb/dPt+6Kvs4MmSWocU
        aD6Ut3ii08jK4RiiAwe7+Jtj9sXrIUfUAf/wj5xnsayp91pv8HyYpq2exWFmVdQB4ZZmHqODqqZa
        UvvyUZgWEr2glS0SOelwRMsTswqn2xOxzyssGGcl1yA0Q9q3qQfiMbXJ8TlHNrvvHA928ZAjdzyL
        GrnEcQISQTwindo8GPvCCmBFNYA09hyHfIsOloT72kXKIT8DJucaBr52RkuOjvMEc1cgCRrn1keW
        cuCG1bx1M+T91iK36fYCP/z8qzfd/bOwl8KJVWCRnYp0i9Eyrso8HzJyIiMbp6w2S495lc3103ri
        ++QCFn+fDeg+/QuXydsHDHd8tXJUlTHSb9ZsL03pzIBPrIGcYmaia5UZAXi8Dib58YPpfZiZnx/o
        8St0w0Vc2x42Cbd63+353EvJZsiOpUny/KDZDP3cIJxyUyCbIKXfJM0DKLxfCjkqnD3MxD8KcP6o
        CLNDxA+zdI928vd6WD2qiknWLf2uBEOQIFRo0xEsgXUo5MbkfSzUL9Xmr/6jhPR8g0Q1diRbCuep
        gOFV3TwaIHnrLw6GwnTUSNoDs16XverI8OoaKD+t+sYP6Qyd2vySfIy7bGRV1IMrEc/eckhqsERG
        qf79/qOeFhpVH8pO1tiXTfThfrPnm95KEB3zG7IuhUTpeUyFjR8/iX356DanGNEDSIt5JuefH1AV
        ww62D1Xa8OFaLz3vezB1+RydP947pI9G9WX/+2KRCatqmPeTV0hhdDkSS0FHwKsc7wCs8RoxmDGv
        p/dBYKBYgau37B1WW1+QU+HNs2Nievoxo877IsE6VF/EvaJXOLv3JpDTidOQtfE79hfPr/yI0DFx
        XnRpSn2GW7/3aDDU9foYewdWw5PHiXJfAE0bQ4XNZydu+nwYxu9eUsHmVyP7ZZf2fDoHN8gOXxul
        l7GqmbhNJZjH4w15XbCEy6bvpGHuAUKjq/3W40nbfhFLukx0xcY5AcVUnZFeaqo91qOVwKDVrsRQ
        H0W9SFHCQXLvEmStTzoMmx4GPz172PwNanrWCMoSz+g8i8pAx2qe5Z8fdb7UYob9Oldh+bU8pHyZ
        Ilw2/1ze4ouedD9SfDASDEshuOK9e7QBH+MOgl3JTH/3d26stIehQ55b/e20KQtvFiy78IJ//gJd
        qdNA/jn1RGNjo15OVenD+8diCbKdNZt896v++h+mEIgaWRLfkQ+6EXt1dTApO9C1lNuHcEWXg8XU
        HVeD7q+frJJFDZdUeI2wM0uBHCb5M6z72jTAxlfwfDIpWNZTF0lbviJjpY+BahwtpDXTsQdwN9D5
        s9938FIinbjhTQlZ82k1P//Nk8Pbh87LIqiQ1fcHssUrHHPJ7GE8VDPRApTXv/eDQU0ncv4oeFjl
        2sXgbgofFMpnhXKkubSQkvOw+WNWthT9x4MPoZlQeqKCht9xvwNbveLdsW3B+hZ9TtwXrkyO2/xn
        0lutlal/eHsMsA8hK3cIQkDS26ZPQrqcnk4Bi8bF5PS63Okct4Eg2w2IibNeP8PM1+IKIi31//4/
        SvcIwunasH/xjV5S4MHgtNronPUHOhPflf7qK9XhX+H63mSAJh895GPVsOnQJhEcTWeHbqmia8y5
        /FhAfHjjXzxdrw8/+PkDyJFO33AittbJGx8hz9vuaLPt7uL85g9/+Te3qw87mfHC9W9+8sVulaTL
        yzhgJkhwSHmDM+VzbyFvPuZqxt2StZFZaQnQ6XuxKB2eBQMj68Tg9XDk7BmHdQNzxVz+zseocRlN
        OEVEQAfCmdk8CYsis85L3/x3DywvE4y/eQ9x0LfZ+uGpl9/eV0PnuP9mvdenwe97vdWbF5tG7HcH
        6WHO8FLdG/uzE6ABN/+KaMGgDdxbTLjf/APL18NqL75bScDaIYf89O28zY+g3Uiix18nLlvMrJHg
        T48Y75sQ4lBqdXmhrzNKz+PdXsyn2sLxc/sQXeCkgfzmTRufQZaQvOsZ8JMpOY9CQXnvJ9qkV2QG
        m/7BLzkc6VJ/FBMegvjlsZMrDCQ5X1O48UVySD9qxv/4/mHJO6JBINoUP+8eTG9BQVTNcQeu1Jbi
        Nw8i95//J059BZ9VrpCk53M6z4soSHfSLp7cxYSS62qlcNNLJKL3j42Pws2E7iXhvefhhShrZqMA
        mTcjkfyYy8N6UBkBho+x3uoT1uumx6GcCRE6hcw1nIAbRzDRjBtmcriAxRKXCH7dHGOGDUyNNRQY
        wL5nTx7/en7tlRE0Xf7xDzWYVY1jb/cCUmIPyNF4K2POjbWDYeN/SDaHN3vmatD/9I4nxORDySgT
        Faifi+iVRWFpaw1XH67B10fuxs8XthBT8OMvDnJ3w9Ic/RS2o/Ig/iOW7G0/LHBj8ROLO/NaU5Xb
        ezC5rzuiHo8ne/N7JLimKPz1m5D7+W2usHsge8+LGRENKYW1/cjJdeMDfFGWjMyCDnlw9uJhFaYm
        gS/EJciYbgfAavqzBXuiP9DNeJCa9KZiQRHcruSodDDDipG2wImUCnm9LQ7z+40U2A9jRTa/2H6f
        s0GF13I9Ec1c+3A1bm0if1T8Qd7GL9ZNf/Pn7s4T3XzCer0lUgvN4VCRQ1TewbzN50AgNxI5n/c4
        2+rfl0u10Inb+4I9375CC89B6xDUZKrG6UDGP7/Ta+/rlBF2PBY//MUS99TrryUCD6QPuUHuQdRq
        7uc3QSZ2iQGLfYiP+XeGDX9SkIm/+3q9HIYUOpFaYSiLg0Z+euOzMD45dHoLlmzBEfg7b97mXWvm
        BYqsu8bszZ7E1quRqh74zRd3oqwD3tHeElR3PCTmFh96EbUECvlpj7Z+DP7ODx8v4Ug2PwAs8jCs
        MM5eoyc/aAgopSYH03fyxpgluc36biXISgQx0oH9ylZo3m/wo05HlOTdK5wLrtqBf36nAv7rX3/+
        /I/fCYO2y4v3djBgKpbpP/7PUYH/4P9jbNP3++8xBDymZfHPv//3CYR/vkPXfqf/OXVN8Rn/+fcf
        lv971uCfqZvS9/97/V/bq/7rX/8LAAD//wMA/z6h5eAgAAA=
    headers:
      CF-RAY:
      - 9587a9c75d10a46c-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 01 Jul 2025 17:36:01 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=7XHLdnaCxW3mhu.O7RaJ8t1a4uAHI6mlHu3VfEYs0Ew-1751391361-*******-mpMlI8wLN_fK7ILCCmaOem0t8eR6cQqjkZvXcm.vhuuoAoCMNuwhHaX2830nq83AFFZRB3Y0tFuUFY9OsHyafJRpkk437K7NztFUVBWvNUs;
        path=/; expires=Tue, 01-Jul-25 18:06:01 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=vLbBcLMQoKUtOCugPnUg_H9aADRheAVHbrMDJqmikBA-1751391361577-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-allow-origin:
      - '*'
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-model:
      - text-embedding-3-small
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '77'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      via:
      - envoy-router-765899b89d-52ntf
      x-envoy-upstream-service-time:
      - '79'
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '10000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '9999987'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_987562aabb7a12fe7efb7594aea6631d
    status:
      code: 200
      message: OK
- request:
    body: !!binary |
      CuEMCiQKIgoMc2VydmljZS5uYW1lEhIKEGNyZXdBSS10ZWxlbWV0cnkSuAwKEgoQY3Jld2FpLnRl
      bGVtZXRyeRKeCAoQKb/1Px66HLUv0xIy+b3iGxII9DEX/rzNCJ0qDENyZXcgQ3JlYXRlZDABOUg9
      N74pMU4YQUDBPb4pMU4YShsKDmNyZXdhaV92ZXJzaW9uEgkKBzAuMTM0LjBKGwoOcHl0aG9uX3Zl
      cnNpb24SCQoHMy4xMS4xMkouCghjcmV3X2tleRIiCiBjOTdiNWZlYjVkMWI2NmJiNTkwMDZhYWEw
      MWEyOWNkNkoxCgdjcmV3X2lkEiYKJGZkMzBjMzE0LTI1NjgtNDVjZi1hNDhhLTE3OTBkMzlmYzk2
      ZUocCgxjcmV3X3Byb2Nlc3MSDAoKc2VxdWVudGlhbEoRCgtjcmV3X21lbW9yeRICEAFKGgoUY3Jl
      d19udW1iZXJfb2ZfdGFza3MSAhgBShsKFWNyZXdfbnVtYmVyX29mX2FnZW50cxICGAFKOgoQY3Jl
      d19maW5nZXJwcmludBImCiQyNGNkMjhlNi01NDY5LTRiMjMtODlmNi1kZGM0ZTMzMzZjN2NKOwob
      Y3Jld19maW5nZXJwcmludF9jcmVhdGVkX2F0EhwKGjIwMjUtMDctMDFUMTQ6MzY6MDAuMjgzOTk0
      StECCgtjcmV3X2FnZW50cxLBAgq+Alt7ImtleSI6ICIwN2Q5OWI2MzA0MTFkMzVmZDkwNDdhNTMy
      ZDUzZGRhNyIsICJpZCI6ICJmMmVmNzBmNC04MjkwLTRlZmItYjY3Ny05ZDM1YjIyZDIxZDkiLCAi
      cm9sZSI6ICJSZXNlYXJjaGVyIiwgInZlcmJvc2U/IjogZmFsc2UsICJtYXhfaXRlciI6IDI1LCAi
      bWF4X3JwbSI6IG51bGwsICJmdW5jdGlvbl9jYWxsaW5nX2xsbSI6ICIiLCAibGxtIjogImdwdC00
      by1taW5pIiwgImRlbGVnYXRpb25fZW5hYmxlZD8iOiBmYWxzZSwgImFsbG93X2NvZGVfZXhlY3V0
      aW9uPyI6IGZhbHNlLCAibWF4X3JldHJ5X2xpbWl0IjogMiwgInRvb2xzX25hbWVzIjogW119XUr/
      AQoKY3Jld190YXNrcxLwAQrtAVt7ImtleSI6ICI2Mzk5NjUxN2YzZjNmMWM5NGQ2YmI2MTdhYTBi
      MWM0ZiIsICJpZCI6ICJlOTk3Y2MxNi1hZDdkLTRhNTctYTcxMy00MmI2NjZiMDExNzIiLCAiYXN5
      bmNfZXhlY3V0aW9uPyI6IGZhbHNlLCAiaHVtYW5faW5wdXQ/IjogZmFsc2UsICJhZ2VudF9yb2xl
      IjogIlJlc2VhcmNoZXIiLCAiYWdlbnRfa2V5IjogIjA3ZDk5YjYzMDQxMWQzNWZkOTA0N2E1MzJk
      NTNkZGE3IiwgInRvb2xzX25hbWVzIjogW119XXoCGAGFAQABAAASgAQKEHhfZurmQDikCGuBgKoI
      n1MSCO02sWXcMVceKgxUYXNrIENyZWF0ZWQwATnQuk++KTFOGEFAT1C+KTFOGEouCghjcmV3X2tl
      eRIiCiBjOTdiNWZlYjVkMWI2NmJiNTkwMDZhYWEwMWEyOWNkNkoxCgdjcmV3X2lkEiYKJGZkMzBj
      MzE0LTI1NjgtNDVjZi1hNDhhLTE3OTBkMzlmYzk2ZUouCgh0YXNrX2tleRIiCiA2Mzk5NjUxN2Yz
      ZjNmMWM5NGQ2YmI2MTdhYTBiMWM0ZkoxCgd0YXNrX2lkEiYKJGU5OTdjYzE2LWFkN2QtNGE1Ny1h
      NzEzLTQyYjY2NmIwMTE3Mko6ChBjcmV3X2ZpbmdlcnByaW50EiYKJDI0Y2QyOGU2LTU0NjktNGIy
      My04OWY2LWRkYzRlMzMzNmM3Y0o6ChB0YXNrX2ZpbmdlcnByaW50EiYKJDQ0ZDYwMjU1LTFjY2Mt
      NDQ0OS1hNmQ0LTVkNjQwY2ZmZGIyOUo7Cht0YXNrX2ZpbmdlcnByaW50X2NyZWF0ZWRfYXQSHAoa
      MjAyNS0wNy0wMVQxNDozNjowMC4yODM5NDdKOwoRYWdlbnRfZmluZ2VycHJpbnQSJgokZWY4MWEz
      ZmEtY2Y5Ny00NTFhLTk2ODgtYmM2MzI5NTFiYjQ2egIYAYUBAAEAAA==
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate, zstd
      Connection:
      - keep-alive
      Content-Length:
      - '1636'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.31.1
    method: POST
    uri: https://telemetry.crewai.com:4319/v1/traces
  response:
    body:
      string: "\n\0"
    headers:
      Content-Length:
      - '2'
      Content-Type:
      - application/x-protobuf
      Date:
      - Tue, 01 Jul 2025 17:36:04 GMT
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "system", "content": "You are Researcher. You''re
      an expert in research and you love to learn new things.\nYour personal goal
      is: You research about math.\nTo give my best complete final answer to the task
      respond using the exact following format:\n\nThought: I now can give a great
      answer\nFinal Answer: Your final answer must be the great and the most complete
      as possible, it must be outcome described.\n\nI MUST use these formats, my job
      depends on it!"}, {"role": "user", "content": "\nCurrent Task: Research a topic
      to teach a kid aged 6 about math.\n\nThis is the expected criteria for your
      final answer: A topic, explanation, angle, and examples.\nyou MUST return the
      actual complete content as the final answer, not a summary.\n\nBegin! This is
      VERY important to you, use the tools available and give your best Final Answer,
      your job depends on it!\n\nThought:"}], "model": "gpt-4o-mini", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '947'
      content-type:
      - application/json
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.78.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.78.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.12
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAAwAAAP//jFZtbxM5EP7eX/GwX4CQROSlUCKhqiAQ1cGJE3AILqia2JNdt157sb1N
        c4j/fhrvpglwJ92Xvng843meeWZ2vh0BhdHFAoWqKKm6saNn/vOzy0/Hf9Sv35ysP7Pb1I1//va3
        y0/h9fxrMRQPv7pklXZeY+XrxnIy3nVmFZgSS9TJ4+PJ7Mlk9miWDbXXbMWtbNJo7ke1cWY0fTid
        jx4+Hk1Oeu/KG8WxWOCvIwD4ln9Knk7zTbHAw+HupOYYqeRicXsJKIK3clJQjCYmcqkY7o3Ku8Qu
        p34O5zdQ5FCaawahlLRBLm44AEv30jiyOMv/L7B0SzcYvPeNUYvBAOcuBa9bJaiRPM60NvJ3d+3F
        TWPJkRzI5aXbmWEivGP4NVLFiEaYiwm+4ZCvRxiHmlI1xnm6G0HWgla+TWjalIwrkSrjyojkS04V
        hzE+VuywYZDWQ/mtfL0yjpE2Hj6g9oHh2nrFQbywNk5DAlZ+g5rcVnwqumZ5OflEdoyXPoDwaLRl
        CiNv9RAmZapWjGsTW7Lmb9ag2D8meWmzXnNgl1AG3zZRMHZCiWO84sB3Y35SMiSHxKQqmLQQxiZj
        DAbPKBqF594pbtJgsEBmUZKqpC47BmsmF7EKxpWZjo0H1b51ac+JwCw5geB4s8P0Ie74hiVXtlQy
        rLniIZbF+Rpb33YsTEGNFAXkNM47cYhx1jHZGYd79vrb2u9DOL85XRYCbCrA/syM4czoKLAkkaba
        RqPI7hjKmWBlvbqKQ6yYdBxK8dahNWmMd/KaCEZVxuqsoEwyNiZVmPaOOWNyPlNwYJ/19jF+55s0
        3Auk4jr7KKEvx89c7bUlEGYC4d22Xnmba7qTskDZdUGOhca2EdGUDvce3M+B5ZS/tmR350/vd+Iy
        TlpT8TCTJnrgw2IviykeYIanOF4WfcWzYSNaFym4UgrlNGa32Q7luS1qumIcj7tGPHOl5a4F38i5
        SVi3LjsalziQSuaa7+SalFT3VY/JB8O5W66YmwPm2ZVUsu5A8A2JnHoMvrUa3egD5RDbvnMJ1qRk
        GbV3MXHApvJQ3tpceUVOG45jvLjmsEUyNaNi1MwpgrAOhp0eyhFpHTsR9j6SYGUiGmN5jN3oyUnF
        xWCwdCMMBh+ikPUsK0Bq9i5RSJ0w5rfC6BuAtMY0P3Eg8F5cBwI/xb05HmCKp3h0v3vlpXElBzwX
        JRlXykOvpBX21HUiW+d7MhG81WgbzHZH8C4Lu+pULInIaK0YnaDlfIyzeCUN+2qX3M6ZAqNtTpdF
        dq3YNp28I/OBcrJAukRksErwXYCfVCTjQ0Qk2N7lWr4NfmW5FmTL4tOu1SdIfgtFYZgf3vo29CXL
        kyMejo7+Zgbfp//r1MDHYFIWqvYbh7UPGcgCk74jTnH2A8K+efvBLpD6T5iJmOc2yMqPbc4A7JRv
        gwy/fWkodqClB/OM61v8Dp6z5VUQSaeKTUBsleIY+z5pgr82mrtkKMIxa2mO9yLKjbG2s6x9Vj2h
        8dGk/LFNyaRWS0IbCqJqStVQWle0ahKcT7hso3yOQYo010YhtnlYDrFqs4Hdpd/SyjJyG5u0HR9+
        6wOv20iyb7jW2gMDOedT97WVLeNLb/l+u1dYXzbBr+JPrsXaOBOri8AUvZMdIibfFNn6/Qj4kveX
        9oeVpGiCr5t0kfwV5+cmJ9MuXrFfm/bW+cmkt+ZJvDc86laonwNeaE5kbDxYgQpFqmK9d93vS9Rq
        4w8MRwewf03n32J30I0r/0/4vUHJJ531RRNYG/Uj5P21wFLe/7p2S3NOuIgcro3ii2Q4SCk0r6m1
        3bJXxG1MXF90vd0E02186+ZiNqfjOfGTmSqOvh/9AwAA//8DACVMBRP/CgAA
    headers:
      CF-RAY:
      - 9587a9ca7f34a45a-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 01 Jul 2025 17:36:17 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=S0h_qrp2vk.hhTaEqJB2x0uRprBHvu1mFGgv5InQnlA-**********-*******-ratAx6im.Yhy8EoS_S6aGSqbAueXlKE2STdZypVe1lTh0m23xPoA__4kVmmhuJfKED0F5pph0ic2_hC1a82dnrwiL41fq1yhObGTZ4_WXHM;
        path=/; expires=Tue, 01-Jul-25 18:06:17 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=Dmt0PIbI1kDJNEjEUHh50Vv4Y9ZcWX6w2Uku_CIohuk-*************-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '13758'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-envoy-upstream-service-time:
      - '13768'
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '*********'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999797'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_4de276f7626d08935d6b7b9a3b17fd17
    status:
      code: 200
      message: OK
- request:
    body: '{"input": ["I now can give a great answer   Final Answer:   **Topic:**
      Introduction to Addition  **Explanation:**  Addition is one of the simplest
      operations in math. It''s all about putting things together. When we add, we
      combine two or more numbers to find out how many we have in total. For a 6-year-old,
      it can be visualized as combining different groups of objects. Here''s how we
      can teach it:  1. **Basic Concept**: Explain that addition means bringing two
      amounts together to get a new total. Use simple language like, \"If you have
      2 apples and I give you 3 more apples, how many apples do you have now?\"  2.
      **Visual Aids**: Use physical objects like blocks, beads, or fruit. Show the
      child one group with 2 blocks and another group with 3 blocks. Next, combine
      them and count the total together.  3. **Symbols of Addition**: Introduce the
      plus sign (+) and the equals sign (=). For instance, you can explain that \"2
      + 3 = 5\" means that when adding 2 and 3 together, they make 5.  **Angle:**  Make
      it fun and interactive! Use games and stories to keep the child engaged. For
      example, you could create a story about a little monster who collects candies.
      Every time he meets a friend, he adds more candies to his pile.   **Examples:**
      - **Using Blocks**: Start with 4 blocks. If you add 2 more, how many blocks
      do you have? (4 + 2 = 6) - **Finger Counting**: Have the child count fingers.
      Hold up 3 fingers on one hand and 2 on the other hand. Ask, \"How many fingers
      are up?\" and help them see that when they count all the fingers together, they
      get 5. - **Story Problem**: \"You have 1 toy car, and your friend gives you
      3 more toy cars. How many do you have now?\" Write it down for them: 1 + 3 =
      ? And help them count to find the answer is 4.  Make sure to encourage the child
      as they explore addition! Celebrate their successes and provide help as needed.
      This will help foster a positive attitude towards math, making it not just an
      academic subject, but an enjoyable activity."], "model": "text-embedding-3-small",
      "encoding_format": "base64"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '2062'
      content-type:
      - application/json
      cookie:
      - __cf_bm=MbKL2xE2KKstdR6eiZ_bFoVcv3TbBWfXYQMOcdDxJGw-1751391361-*******-P7iB6ruG.6Ire362NgpifzBpobaJ7XT8VAZZ0a12f0OKbkuw9Yu3OROWxxWHebqozezKSnumCbnjSwKI80Xfh7xoX8JFI4am4YSpaiPnF.0;
        _cfuvid=gvypJd6pfIYst9Fe_P5G7.xTd5AdD0r5RsT8X4f4zlQ-1751391361098-*******-*********
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.78.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.78.0
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.12
    method: POST
    uri: https://api.openai.com/v1/embeddings
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1R6SROzupLl/v2KG3dLVxgzSbo7JjOYQRg84I6ODsAYg40xkwSqqP/eYX8vqqM2
        XgBhJDLz5DlH+Z//+uuvv7u8KYvp73/++vtVj9Pf/+t77ZZN2d///PW///XXX3/99Z+/3//xZNnm
        5e1Wv6vf47+b9ftWLn//8xf/31f+/0P//PW3TC6b0Nrfo0EQA8WBwWIjHDxsxMb4YZ6hb8U6tlQx
        NgRdv/jgUZg5jhjZDSuLtRq1l/BMNO+yBTR4JSNyL5ZFfCcECVE+WgZ82RKwvT0NbDSuYgsVdejC
        bTRWxqxU3AileP/GwdnPBtFaPkcwZpdLuNROwJZs51uQM9Yi3HKHjk3n11ZB/mB4WM9BmbCrXgiS
        NN8qYrzkG2Pldelg34od0V8NMvpjsJMg3ulb4i7B7NHODPeARbsP1tdg8db8s4RopdVK4hIlbGu7
        7gprd0TYr+ansbw0wEN6zyoSem/Jm+I650B8qq7YCa5Oziu3R4hez7cV8jJwPIFvlQ5qsu1iXFUK
        m8sp34NDLLtEPexUY3seJQiPru5gTfH3+Yqv4RMe7cMFq9EnYbzEb/ewnHke30mOANXy5x6VcnAl
        huwCo/9YrxSdje5Orna3M/jk8Jnh2u4DrPoHldGUmxR4RumZFCdWgaW/pj26elFP7CoxGZ3ymoPr
        81pgNwzDhIdH7wxVxdmQeA0OBl+MVQG++5/fbj2yZel2Aioi7jGD6KMCPnDkCNYfM8JnNrbJ+CYF
        BN/1knv6Djx+c+hqNBXwjS37BowxPJOzcu4vCNsc2ufsE0oW7AF/JmdJqwbacpsWsuV6wDpz9IEf
        rUWA4uE2zlKd42Q1i1KA4fh8EdW7aflaTomDlLdXY1O+A0YRerUwO1yKEL5ZnLPAPvQw3u4ikl3v
        cc5YrhfwYdsTMRzONQQXPCGqrCAkJT05jG65XIXlBSyhyFoFrOdZfaJtynocfIoLY29eyqBt37Tw
        VY9LMx2CXgDXqEAkX9Mj275VHEOoxhMJxexiCGP10mGuBwCHq702tHB1DmyzKv/Vjzd5x5MOLfwp
        8a1EFRBWlJ6hT3E5M4acQdCmWVVypWxnxT3sh1XXKg4tenDCt5dpArEJJA6pWesQ9/0iyUe+v00Y
        n+orsdhoJXxz0RQkzfcKBzsuZqL1oTqaWFETg5VGvp7jYwrdJlfDagw9Tzg+Njp0NF/GJzPbGOuY
        ghH625wSrcML43et5kB4KjfY/RyrfHlwVx50s9di8wkzY+vlqQW9UjvP9Jw+h7UxJR0iuE0xPkvQ
        W8+z0yJbEI4hk1/vXLC3nQ+UZNSIRZsSCAEXdWi3jRWCZaNo2N0LYmha6Iadw5LnCxWUEBatw/B5
        o4TGNAK3hY1w+GBNbDVDyD+LD/sks7GjNcnAvFfVoQFbScgZMeetKVAz+HLudC4M3WFLcFh94Bq8
        S47GOBgf/uykEPafAz4e5zVZHb2z4G9/BnvAnDqqw8Onnm7I+Xm6eOy24yisFjkn5riGjP/iGeqM
        RZ/b82mXC2oKFVhVL0buN+XQiNZH0uF3f/PzvafGEszHHrqoVUnyEUwgiLLDIYUzX+T2dJNE+L4f
        PLmdhyNVOeSLmaUQDpYPQnDBH9aqKS8h33wcSLntppzuWm2PwrlWiMcGOaG3dfLhnZCE4BZ7A5tQ
        JaDv98bFOm8bljyZD/uJDvM6SaK3vp0DRKa4hdhoiArWhx0LMMFxRHaqPhr0QKQZSjbVCY43YbK+
        DKNEv/zViZc3fGdaDppOvIo1cc+DKSH5CoQ8VebtrMjJIplyCF/3jM3KoTUbsQmJD+/8rGOvv9uG
        eLy5EoyTp4lzW9MNQdc6CLD8zEJRC2K2FkleAyQOFjElX0zoIC/wD/4bc28aWxTeFeB8Bgerj+QN
        VtU0KESa52P1428bptI+RGXQ+ERrYeAJ7/E1ggJcdthTQncQg00VwyyN7vhyEC1AmyjkgW7cZpzW
        OUnYJEwr5FJuJi4fFwOPzGpG+aaTwjZ3zYHW7bNCyZZ6+OLWPhPXTRnCPknt+e6hlFE/TSQwxM0W
        71UYNeuhvFnwYLWY3M+aAcaT4/FQsjct9lvNGdipd0f4cm4Un1mgeVv9pBdIClyJeMo0g/XR0zMS
        zJuIVRtaA6+uGo8g2TZ/6o3We91CI3l8QtRePsmqwjgF3Ms08Z5/PRNqXt0jzBIzwDf+AgwKxUoA
        9qkecbpJBu+jL09J+ubXLOh7YVg3Ul5DuvdDfELboukE0nby5SS02BBMf/jlAyzyNZx7iC7J1l1H
        FX6sNMamf5W+8ZI5YOsRIcebMeaUXxIf2d7exvqruRnsc35kYNpmIt6pum8w7Hf7P+txslprBFA5
        BRRt2ya2HF6HZUf2FpwWsuAw/EzNaDGHg9tpA4mGpp1H20/qA7dv3kQV+KO32Hz6RPapGgn+1iMr
        lqCFF/GyfvNhMpa4TiDaGu8U27khG7P6/kTIfnT6DGg7GEyprSNKp94JF+psvv1KbAHhLCnsnJdr
        8Ng2Z8j10ZnkRQ6TcS/0CnrL2Y3snS2XjL9+KX84lxwFOW7W7uZZ0D5sW3wQBjzwd5GNcHpffOz0
        1M0FsCU8lFFoYSPjlGG9AK6D9yTLiC8fdmzdMqWE5z2z8O5dUbDksafAY9dusKE5x3y5F50ExCS8
        zYt3OTH+Vy/uoxhIUc2mx/du0St8RJ84eei7hmoWU5E2z09sbkJ/WD4diODb29yJa/spEN0q20Nz
        /fRElS/PZDlYb4r8Emzn53GOc9EcYx68diomv3pa3psIItweb/gA16Thk8NjRhfRCYnZDnu2yHsE
        4TuCIXbfL5yzB1sihPKGzrU3CcMyL8OIisy4zfzhYwABnesncl09J1hfrGFrWKUJT8fEx+66EcGa
        5z4PRP9wxYF0rL01HQsVLkh+kNvQT4C+hVQHxySdcKxYJVsSMTBhzBxAsntoeNsVOiOY9NYg3qG6
        Ggt/W2b4LJZmRjx7J2sd3J9wuzk8iVntHoC+yZGDmnlU8f4zZd5iQVdB9/A64CCxfYPfzHsItXHF
        2HSPH7C0IehgGyUJCQ68PbDOfbSoWCMXxxJ9JEPiRCs8C/eE7KRa8tZe1EfobuoT1m89TtbGpDq8
        KzsJ78+y64m5PZyhxQcmueYXe2jjUCkAfLSnsGeLybZNtedhVaWYGHysMzGaryY0p9sl3Pz6K3hE
        JmB7cCY759J6JB82JugOwjRz9NQx1rW+DoFz3xNdqT1vW0zUQnaZmiTjX2a+9lkVKyPm1lDg3m5D
        abwzkZlDD8dU8QfhZr73UKfKGCLhdTBoDU4d+vYvon66B1sao8uQDG6neSnfibec7ssebY1XivfP
        7NQsXB1T4NOgDEGVGvkPv6C4siOx4pTLyfsQS+BKNR77J6aC5aZ/Ikh6AcybwejB9OQBBycuxsQW
        G42t0K0p+OIxDo5ZBCYfbWLQbroXNl4yAqt7OYTg2w9xuXUdYz1vFwU627UMN5ejk4i6MfPgeT3q
        5ISlalg+WSj9+AQxY+fjzeF75SAcUx2H1V7xqDBLKRwcN8IBq+qGMptCuHQEE0NoDoxmBiwAXycU
        O+tHNEitLg4qIvggt6Rd8nWhaQU6olxmNPQBE/eH14qWbsIEb1p/4G/rKwTffJm5J1BzPjwtFgRI
        J8ScDkdP2BwFFcjW7JOdxm/B+rGmDGTy0yb6q3p6S/3mHbQZ9luyH0DkCWM1qUAVbvK8Dbxdwhb5
        WkJ93WrYbDHXjJ90EwP5iHLsW+Dc0FvPJLgYnEU8lKTeDNvAh3GKlC8QBs1SGPdaCc5Tii01HcHs
        3PUafuhpJipvS2zKY0NCVrWxSaDLZkJZUZuK8K4inCzRlE+zmu7hA73fJHMP/UDbPiyhuQ49cXzF
        8EQgbEown8c7Uccpyhd+rXl0T9IMX+uFGpP3sRz5s5r3L5/PGImIfYT35M3NZafOjN56ICnnx0Gb
        R3g+JmM+nWcIXWdHTl88Xe+vBw+NEJ7J7rQxm7FunzWM7XImmkYuCWvNxISzVgU4aU+9R2efe/7+
        P9wE4wesYEuEP/X67V+DiO5NBfdccSdu4dme4OwPGeomqBNb6GhC2TNJocIiCZ/61yZh+/YsgV0H
        1Rm0nJ/zjqryoLJwOLOzrSfMSC8Q3kb8DMVk7yU0ud8pJF5k4i9+eezLZ8DelRTsvfy6+SilxMOA
        StcZ4ogOs/V4HWG68Y7z6IR50mmbjQKWpkLEqFr5y38rC8KtIWB3fj8Z7TzXB3Up45Dr7kdj7Snu
        leD+pCTcRB4Q8LSZ4Yz8K4m/fHB6nUcI9mJT4ODLL758x4TqnWY4Da5dMu28ZwvvT7klmXAJv3rc
        2iMZLw4JUal747RRS7T0hUpSNcLGant2j8bCcrB+abzkl+/o8OF9EjBTyGlUUwnU23yPdReMjAaa
        EKIfX7S5R+nNC01reLmsYH598XWK1EGHX/6Po0+nAbq1zAw9HL4Mlcfpbaza1OrwGZV9yPhYB+zU
        hD00A6kKucttZFNYrioMdr6PLbxuPUIuVQ3zt/8hVqEig+3bUoK+2RxImN31nDcPqQphN0KsybQ0
        Fo0dCrgJqgTfu+0RLEm8HNFhOkzk7llqw6q7s4d7mm5Dan1qY0GnGKKOOSf8yz+a84cVeaVxxnss
        JgNFZjWipTz2OCzFU04nKchABScnlJcg9IZXgmbgl+NhVpJ0Tn79CrpusJlrEWpgmw5+Cod33WCH
        U9tmfcat+eNz5KcHyHNj1rD6ggEOItQszk2RYNsPIg41qDZi+eg5+J4O9vyZ3yZgFehbqchpiHcf
        rDGe7nMK7qczIfaXHy/3290B3RKqxN91sSGmMFLRy0jKH/7kn/yKfCkPjC682r7E6FMdfbhV7Jj4
        U74YqwqzFC7luZ95xpvGH72VHU4FuQDAg2VHdAs8ksDAmoGmZHwI9h4wv7OJ1ihis57fuxoGw2jN
        ou8I3vjEUQvPpWbg0/f9vLbZSNCZxYyYnpUMQuN/jrCcBT5E1zDyGJ9cHeCdFULUJ68PIrl0Nby9
        tTjckByxLtC48KefcZjA1WA//X26mzIuBBzlFTK7EXK6NsxAmUJAf/womLqc6DJumyG7BBwwu8Nu
        Fl+26fHXuBNgVWUYO1v/nRD6Kp+wwWwOH3TXDjSI+uevX+L7WSoMnl5VB561cR/W5G0z4g9TqkgF
        PJLzhLZDf46LFHz9hG+9gubTzy5FX39jFtho5Vv2alSQB1qH8T0QjKm/Rj0qTNELuceNa5Z7UUkI
        6oFOtI36aVY5UTt0W3KR7Mzsbgybp6JD0d7ZBMPdaox1O9awvMhLePzGi3cDpweBf2Thwt/kYZme
        gwL7w+dMdrg4Jr/+B1c53ZOM71DOHO+hIse5VNizXls2mbntg36FHCl9sQErnJwInKXQJLtB8wZR
        OUYxOnGFj23kVcMf/fpunQof56c00NfpdgQmCR2sVWfTED79XgFf/+zrP8BhBfzyhFWeV8Q5zXVC
        x7DZI693xrCn0T6hcnkvgGSvOinupZTT07ONkV1mJtE+tZD3kqUKf/htFNYY8ATlCtyG+S5UuChn
        rMyIAo1XcQjB+3ZoWJquFeyycYcThrqBiexUw/3g1/iaFWeP7QopBF9/kPh6Wwx0XJ0e1ke7Jrt6
        XIbFfo3+Tx/NisftwJJkdQ+/+U9wXH2MsRi7Aql+fSM/PTxtYmkPlWJFxG6zrllQ71uws0snrMVq
        29CvvgOm7xKMv3yK0de5RWvrBOT0ijMwdfTZQuWxibDnSUpOs/TVogYvM/a//sL0XY94XNGTaOdY
        9rqtZabg64/OnHVMmmmWTzoQondAdrtTNDBhSilM6irE/put+af2bQiJWl5IWNmat7VfYwjnD1cQ
        k/FPb52VsQPuHlISHDPK1p8f8vVj/813f/G3Wu+KPQnsjDFwlgh+gkomrlw7xvpqKw5UC8ix1x0W
        g0rRI4XN/bn/1cPQG2G3gq//PQOyDgO5dDcH7gqhx1//1RvFylR/+nRebz3J2bcewXe98wo0mqzr
        zh1hDNXk3/6EKYAM7jgnJslYzMb8jrwYVtWbzfDrR331ugm+eBFORbQbeD9NFMROCSD2lfLNH/9W
        Fe4y3hc70HTuQ6Z/9Ii7y4hRPQTbgerE77B1Pr3yqv1EIbLeyj6kYqTm2ySWz/Bbr7g4CVcmyP7u
        T/zCRfH3Cf/1O+BXP2DTWuRhLSNphs2R3sjZy2qPgmbifvmFdcUqAdPixIdel4OfH5uIl+62B2Li
        33BustFghi49oQ31dgbd4WCs1U2q/vjh6lbvkw5KdwtcChTgU/qa2Hrzeh/Sc+PMq8SUfHTk3QhT
        SZ3wwe+zhg4rp4BDMAvznMDVox5899C4nsEsXcwNoze5i2H+io44WJaFLYIon0Gq1eyrn0iyZj0p
        oCeFF2KYe5ctl3MSgw9w87BbP6LXbR+0hUZ+iIjmvkZvTGuvgHc7MUNwfxpMuFXJGRxy+gg5s3YB
        pS3nQ3orvZA53MdgD6cvQZdujuTHB5jkWDO87SqL2NLlkYzbDNUAh7uFeNOOJtOIFQqpyNNZUNOR
        UYuJIfjy95BTA8JWQVWO8MtP8bF42p7wel/CP/wqm9GuoVIaz+j0uZdzk3FKQxfwPsrD9hliXEHb
        49nprcKvvsJxiRgYDV+rwJevz4p3r4bV6roMNoK1++HtV891MQwOjfXleyYg+HGIYEolC5uexQZq
        mFGIKoWy8GGUjsG/s4GH7MBnOH/fDgN7vHwF/vi3YQ/PfBGpo0Lm9zbR6NNjZN+eFfh5IZeURlwa
        7BiearCbT0LIIf3EJheHFfjhScApnjFx2acEpyHEX/zZsyWvpAjq95eBD+RgG2wjDw647aY+3Hz9
        nN/6QZAWLlH1EXijxVSIrubpPTM+rtmvf0A5V8q52ySeJ2BeodDOZg57F/wB9NxQHTVGuZ0f/lsb
        Vh40K4xyu53Z9/vOhj2NILEyaX7d340xyXNSoG88sTYZB2N6TvsO9mRSiT86/jC+s4aHe31N8P6m
        TsZaB5cn9BvlSSxJUweGYMABp3r6xK02bc5scbagaW1uMwsPwdAq72X9nZ9hz317Cf/rP8ckm7D2
        2jf5UtVTBzqD6WHtdE7OVFqHMNcxCNeedcMsJ2oPnds5nJf8dWtIbyMd3Px+Ihp17s14jSseYJlx
        IZUvZr6Ee1WCp8+tJM76uRgUSncTHoWux9ZJqP70H6hdq5jYJHyD3/4URwtljO+fOqEPpPtwygMB
        54XoDJQ98wyIobrHP31DnhldwWNlyh+/niI/VqE67o5/+BP5+mugttculIniJuPpTENkiiIk3jbb
        5yJ9dmeoJ/GeBEs8DgwjMALlgSK8fwoaEF/koUDTGUNcaptrQ51AUKBfzgei5hiCdeWsJ/z16903
        foyLNg6cz/N95pC+BSt5hhDcXhYiPz6waBpu4cbsAEkZBANpzdxSwstLDLng7Hq8D+Tsh2/YHay9
        IdwMKsExO13mpzF6BgW2UaFijV2CX9RKtgdp6QBxH0m4hBJtup+eaz4cId7dEQdGNV0AVoVsrJO3
        DYTzVpbgIGskvLy4Il9sPnoiAVsqCTdkMZZnVIUwb5pmTusc54tVnUa40nr9w6fZzy+Iu/Ucvr/+
        ubCi6IzmxLhic2lnY7EjZsFm/y6wh4iZ/Pg5PDvC8tP7A5vfnqMsl9AgFl/tgEjuVx6+6FxgFzqH
        fM65/gjRLX58z3M9RgNeNaH5cEpc6I0x0NlXj8iI/SM5mdnd+/ppI/yeb8yUkdewnF9IAuk+9Ujy
        9TfZ5ubMcH9zRHKaHDJQb16eSGsk8as3bg2reFIpv/jbABzBsunuNfzxCQX08bAFA+3RXXVu5KYO
        c04EF0RQ3O1XvJveR8BfgviofM8jQypnt4RZVlbD7e7oEsv1R28MT7KppE9KQ/l1kXOS280ROJ+P
        g3/8dakTQ/3lI3bUiPz0oQqJ2yQk/OL1Ap6ohVHBX3BQPo7D7OiVKf/8gWLKD97gYx2iH56gr/8n
        Vvy7Bq/nyyIuOC5skQR/VcaTu8M+f2uH9WaSPYyVpA65r55jWpz7sAF3gP+cx8ivuP6dV5KvHhuo
        QOYetuUZzdvbdTK6fCpnGEUHOQTGZhyEnoMqVLOnQzxJTYYp8YQIbp5qiI2rHzHqvbkK/v2bCviv
        f/311//5TRi03a18fQcDpnKZ/uO/RwX+Q/yPsc1erz9jCPOYVeXf//x7AuHvz9C1n+n/Tt2zfI9/
        //OXBMGfYYO/p27KXv/jxr++L/uvf/0/AAAA//8DAOCnhzbiIAAA
    headers:
      CF-RAY:
      - 9587aa302e706294-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 01 Jul 2025 17:36:18 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-allow-origin:
      - '*'
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-model:
      - text-embedding-3-small
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '101'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      via:
      - envoy-router-6795fbb876-6tth4
      x-envoy-upstream-service-time:
      - '106'
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '10000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '9999506'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 2ms
      x-request-id:
      - req_ec6c88987bd8e8fbef4e97a850fe821a
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "user", "content": "Assess the quality of the task
      completed based on the description, expected output, and actual results.\n\nTask
      Description:\nResearch a topic to teach a kid aged 6 about math.\n\nExpected
      Output:\nA topic, explanation, angle, and examples.\n\nActual Output:\nI now
      can give a great answer  \nFinal Answer: \n\n**Topic:** Introduction to Addition\n\n**Explanation:**
      \nAddition is one of the simplest operations in math. It''s all about putting
      things together. When we add, we combine two or more numbers to find out how
      many we have in total. For a 6-year-old, it can be visualized as combining different
      groups of objects. Here''s how we can teach it:\n\n1. **Basic Concept**: Explain
      that addition means bringing two amounts together to get a new total. Use simple
      language like, \"If you have 2 apples and I give you 3 more apples, how many
      apples do you have now?\"\n\n2. **Visual Aids**: Use physical objects like blocks,
      beads, or fruit. Show the child one group with 2 blocks and another group with
      3 blocks. Next, combine them and count the total together.\n\n3. **Symbols of
      Addition**: Introduce the plus sign (+) and the equals sign (=). For instance,
      you can explain that \"2 + 3 = 5\" means that when adding 2 and 3 together,
      they make 5.\n\n**Angle:** \nMake it fun and interactive! Use games and stories
      to keep the child engaged. For example, you could create a story about a little
      monster who collects candies. Every time he meets a friend, he adds more candies
      to his pile. \n\n**Examples:**\n- **Using Blocks**: Start with 4 blocks. If
      you add 2 more, how many blocks do you have? (4 + 2 = 6)\n- **Finger Counting**:
      Have the child count fingers. Hold up 3 fingers on one hand and 2 on the other
      hand. Ask, \"How many fingers are up?\" and help them see that when they count
      all the fingers together, they get 5.\n- **Story Problem**: \"You have 1 toy
      car, and your friend gives you 3 more toy cars. How many do you have now?\"
      Write it down for them: 1 + 3 = ? And help them count to find the answer is
      4.\n\nMake sure to encourage the child as they explore addition! Celebrate their
      successes and provide help as needed. This will help foster a positive attitude
      towards math, making it not just an academic subject, but an enjoyable activity.\n\nPlease
      provide:\n- Bullet points suggestions to improve future similar tasks\n- A score
      from 0 to 10 evaluating on completion, quality, and overall performance- Entities
      extracted from the task output, if any, their type, description, and relationships"}],
      "model": "gpt-4o-mini", "tool_choice": {"type": "function", "function": {"name":
      "TaskEvaluation"}}, "tools": [{"type": "function", "function": {"name": "TaskEvaluation",
      "description": "Correctly extracted `TaskEvaluation` with all the required parameters
      with correct types", "parameters": {"$defs": {"Entity": {"properties": {"name":
      {"description": "The name of the entity.", "title": "Name", "type": "string"},
      "type": {"description": "The type of the entity.", "title": "Type", "type":
      "string"}, "description": {"description": "Description of the entity.", "title":
      "Description", "type": "string"}, "relationships": {"description": "Relationships
      of the entity.", "items": {"type": "string"}, "title": "Relationships", "type":
      "array"}}, "required": ["name", "type", "description", "relationships"], "title":
      "Entity", "type": "object"}}, "properties": {"suggestions": {"description":
      "Suggestions to improve future similar tasks.", "items": {"type": "string"},
      "title": "Suggestions", "type": "array"}, "quality": {"description": "A score
      from 0 to 10 evaluating on completion, quality, and overall performance, all
      taking into account the task description, expected output, and the result of
      the task.", "title": "Quality", "type": "number"}, "entities": {"description":
      "Entities extracted from the task output.", "items": {"$ref": "#/$defs/Entity"},
      "title": "Entities", "type": "array"}}, "required": ["entities", "quality",
      "suggestions"], "type": "object"}}}]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '4044'
      content-type:
      - application/json
      cookie:
      - __cf_bm=S0h_qrp2vk.hhTaEqJB2x0uRprBHvu1mFGgv5InQnlA-**********-*******-ratAx6im.Yhy8EoS_S6aGSqbAueXlKE2STdZypVe1lTh0m23xPoA__4kVmmhuJfKED0F5pph0ic2_hC1a82dnrwiL41fq1yhObGTZ4_WXHM;
        _cfuvid=Dmt0PIbI1kDJNEjEUHh50Vv4Y9ZcWX6w2Uku_CIohuk-*************-*******-*********
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.78.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.78.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.12
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA3RU227bOBB971cM+CwHuS3S6K3Z7SIFNr2gaRdoVRRjciRyTZEsh5SjGv33BanY
        cRddP8jWXM85c+DdMwBhlGhBSI1JjsGubvynm+9/XN5sgvp7Or/ttdu8vtzqb+auv/1LNKXDr/8h
        mfZdJ9KPwVIy3i1pGQkTlalnV7+dXVyfXVxd18ToFdnSNoS0uvSr0TizOj89v1ydXq3Onj92a28k
        sWjh8zMAgF19FpxO0YNo4bTZR0ZixoFEeygCENHbEhHIbDihS6J5SkrvErkC3WVrjxLJe/tVorVP
        i5fP7uj3k1ho7de3D5/eXd/m9e8fb9/Frc9yunh4/ie9Ptq3jJ5DBdRnJw8iHeUP8fY/ywCEw7H2
        3iNvXk5oM/5iAoDAOOSRXCroxa4TnIeBuNRyJ9rPnXjlpM2KYPSRwLhEEWUyE0H9MskQg48w4EgM
        ycOIGwJLGJ1xA9BEbmklN+BQQr2PsDGKTzrRdOL9sg+U6XuK5BJgCNGj1MSHUtiapEGZiSIfDec0
        W+IGOEsNyDAZzmgbwKxM8nFuoPY74qQpGQkjJe33m99GPxlFgEqZwhctREK72vpoFdADFm8yJI0J
        IllMT6WFJ00UZ4UzWNMTsCSH0fgFM8KaUqII2SmKxUvKuGFZ+8pJH4OPZV4iqZ23fpgbsGZDQCpL
        fASDIXCzrNdkA0QyrvdREiRNIL2TFBL4/giVjj4PGoLF+aQTX5pOfMtoTZo70V43nSCX6sHKZXdd
        9Ugn2k68eJxQARbX1egdJg1vAkU85BSxjCYs720n7jVBiF4Sc0Ei/bg29TRp64v49fIuj2uK1Ry9
        cQoQkk9oFzmqssVt2oS94xgQ+uwUFmOihbEA2RMu+kptrIq0YPrApMA4mIr+mZcj1qNgCNYsenIn
        vvxojjl/rGaBF0bxz7TvCaUuHO6qW37J+wNT4Rv0zEaiheV/rVKstzLWZk71xgU8jZhq3SMJ/l/u
        L51GJ4l/dg74/l8AAAD//4xWTWvDMAy951cEnzvY+rHRYw/dKGMw2HUleImSiia2sZyOHvLfh+1g
        J00KuwUUy3rSe34KU3YnP/gZKAphIMsJzjcnTC6K9MtI7YY/i3bXy24W72Gg+15ETgNW0xDGkf6e
        sLas7lMO53Yf8juAopjD5yzSotUhRw1EboaLb/bKc6zRcAOUAie8VZnnob9z0gzbgmv6qeVPDc0M
        2/de9bM92AWRp0oDgTCed+RyllI3YfwBjGXgddwH+1OkKKHxL/P9/hwCl+ie8t3Rvchlq3kFlOYa
        Pd/MCcXZNsXOX3nYDyTrC4rKNufYsZEhdMnc93FgdxrKlng99UEuhDS+dmuExz7SBc+tZWUroJuj
        rESBdMo0cHJWxshI5cuyJbjLWTuya6a0bJTJjDyDu+7leevzsbhSxOhys+qj7uGJgafH5XIxkzEr
        wHB0jh6WiNxaUhHPxmXC2o0cBJIB7mk9c7k9dhTVf9LHQG55AEWmNBSYjzHH3zTYt+neb6HPrmBG
        oC+YQ2YQtJ1FASVva78JMbqSgSYrUVSglUa3DrFSZas136w5bFc5S7rkDwAA//8DAJNYZOUcCgAA
    headers:
      CF-RAY:
      - 9587aa361a1ca45a-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 01 Jul 2025 17:36:23 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '3804'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-envoy-upstream-service-time:
      - '3806'
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '*********'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999378'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_473e8ad6aa6f1a610701acde3ffa7e4a
    status:
      code: 200
      message: OK
- request:
    body: '{"input": ["Addition(Math Operation): The process of combining two or more
      numbers to find a total."], "model": "text-embedding-3-small", "encoding_format":
      "base64"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '166'
      content-type:
      - application/json
      cookie:
      - __cf_bm=7XHLdnaCxW3mhu.O7RaJ8t1a4uAHI6mlHu3VfEYs0Ew-1751391361-*******-mpMlI8wLN_fK7ILCCmaOem0t8eR6cQqjkZvXcm.vhuuoAoCMNuwhHaX2830nq83AFFZRB3Y0tFuUFY9OsHyafJRpkk437K7NztFUVBWvNUs;
        _cfuvid=vLbBcLMQoKUtOCugPnUg_H9aADRheAVHbrMDJqmikBA-1751391361577-*******-*********
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.78.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.78.0
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.12
    method: POST
    uri: https://api.openai.com/v1/embeddings
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1SaWQ+6Srvl799PsbNv7TcySVXtOyaZpZBBsNPpAKICIjIVUCfnu3f0f3K6+8ZE
        JMEq6nnWb62q//jXX3/93eV1WUx///PX369qnP7+H99rt2zK/v7nr//5r7/++uuv//h9/n93lm1e
        3m7V+/G7/fdj9b6V69///MX895X/e9M/f/2NoiggqnPunXX5yBDdm6rF6jit9QY5t4FrZ0Qzt7s3
        9Sj51wLY74vjVQyMBgoPpg3LudSIfjOlelOHl4/GQEm9w54T8nVRkQ1VH9czWs5z2L9MKROL47In
        RxdbIWsxxQIPq+FgtSkNwJc666FnaVrktE/3dL0V2g6trYexdlc++ZKcxwcQc2nDUVfONS2SKoXi
        Fu+x514onabAHOHe+byJMTdu/kqVXoN0YlfivLVjyI/dNYDrOhJ8UjUvZ7iy6tF7nz+xzWWZyszn
        doG2dryQOG4Hlfqq3KGCzO0Mgvt+2N6v2BaVF5qxpRElXPlhtOFUSU/svQVtYLEepijNvQF74+sO
        PrsRVdDhLxG2CsagnPQWOXBtrwbxca87PE92Ixzqs4r1bXZV1s62Ai5DJOEMvON8u/f3AI7bbj8f
        uPulpsmxZlDP4CfBc1CpS82JJSSfmMNxJnzqLnKfAdrrvEi8K1847NGkIrpKe4m4iKfOJ7/WERT8
        hcV5aOnhYipeAdoqksk1Jc2w9O2RObwaNsHhjJ718kZ6hRrxVeG0ODQ53+yvmVi+bB67z6nPqWI6
        HiT8siPypYroeJreFziak02cxLOGDe+QB9/XfYblly8MY3QXNJj1wcX7BM47p9d9GqBQ6vG8NLwK
        uB1ZNRSxxZGE4OwNxby7C+AZWxaR9LDJKScNEFpJh7F/sSSVs4WPDarLFWFVmk4O+12/8HBhLyTY
        hVy4caaiofmsqSR41E2+dMbDR9/xEycPXcpRh0LkTnpHlLynw3qeLQ6MJ6/HVtXw9SIAPkOfQNLJ
        lVZDvlQD3sHsPQDi6c9HzUnKrMDJQjtyshe1ZhNbTyGfjSEJ/dqks1kJM6S3U4LPQ0+c1R9XCTz9
        uMC3lNxyloaBBgQNmiRe6nRYO3PvH/gtnshZZc7O4uyiCuFimUiwXB8OayiPDPH3h0TC73g5gFuI
        LKRdPArO8/DZt5WCOEdJ8Fl5s3SbwvsMPjc3xedVXtXVYqIFHcPyiRWlOoTdk/EZcLX9Ey5lXKnL
        y60gQGA/eDveKijH8wUDNXV54d94yCD1DCwaz5iXgQw1rz9uPpy2y0wsJXaGzWlrE7oKw5AC33i6
        elSrUHwGrtcDTQIc+xxTgLCXzA9yCJylqjIGstWeJTjurs6oXaYK1t0cYeN1sofFf5oREPYPC8eo
        +KjLbK49Or27Hge25YTsPsxdmDgPgkONDwcufaQL3M9iMe8+XTSw10QeUZuOLT5210Dd6o8hwba6
        yNgR9004r5xaID+JCL4RLQAjky0u0iJtw8fXPs/Z/iOnSPVPtSfsb2rN3uQzA87mqyY2eLPhbPni
        +Gc9OGc4OPTWrx46oswkCgOjejEC0EH1wyFiItMPGS86KfAYFk8SbcjM+TTdeoSNjpLb9aqpbMLt
        H9BJ2z128S0By0naueC4f2fzoXWseoHrpoBrst8Te3yoYCK3pITLeW5nahh1zW4jTCHH30tijuTh
        0DQVe3TNmgynkp2qU6n03a9+SGm3KqXwdbiItGU2YmwfUk+XDLSQPwotUfaPc81+3kuL5utHxEq0
        DXTyM6pA2zyoJDmYVs21gR4hRarfHuOXY0g/O9lG1UNNifvaTjWB+b2DpT4REtQXE6yv/tqBb7+d
        uaWE6vqi3gK22WI9Ludkun5IJKBkDTWsBZYAxgtWBITOhJ8F23nUzON+cJH6Un0PeqIarkui7hCW
        djK29HugdsfKVxCd+HX+TGYEHlUVcCjdnsJ3POuw6GMM0Z49FMQGVuOMMYASnJ2dOSNxMdXFim8+
        nI2y+6NPS+ikJfr2e6LKYpfTuj8ssFxQQxIyauG25n6EQlFUsZndT/VKX76OgLO/zki+PimJrHgG
        6FO4WKaXJlzb61jAG2fERP7V4299iufpRXS3K9WhPjY7GPqwIG5jyOp2EXof+g9okzQOtpCGnM6J
        2jS6WMH5ByzzLhGQa7GUYD15gGVm+w36hV0Q9bh3am4rniaC0a4m9qQK+XKoYg0NQffBeXR5gv4p
        GZuA7syKS3m71Ev8lAQ4XpgjVpFcDlv4fCro8zYlfFHP0KGRvGbwroou1rA7Dessh9Wv3rCyVYgu
        /aHoYHDcJGK40qQuWL0zoKtgQ+KDKOaL53YjKoaT7tmhp9B110gZ0vLjir1n6TmkPo4Qwl6/YS9Y
        7JBzYxai3fGtY0OQ1oFctFwCV5IZOLqJa7jIU+uh9ZlVBLetS9mDXHR/+rFe0pPDfPsZvMRLTsyX
        agN6zSMfWqFJsEGU27Dy6S2CzzrQiN0cVkqSXuhgHlUOVp4yW7/X+RWh6yJV+PgqWrA5CrHh00+K
        ebk5Xs0dDlwJFaG1SFpzJKRKY9goeIoicU68AX76LfJPEhK7ugN1W3RhhpH5YnHmMm7Oq/bIwa9e
        Y3UnPAb6OfsSYrjHjDFBcz25t0KDna/CGcEXyNdJsmf4nQ9i2o40cOIjssF61uOZl3BU8+eT0QDN
        9I842D/WelnXRUB6LFek/PIIG8ZCBLVpdmcgxqgeH90cwY96cDxUh1m+fvspyAz1Oc+3aw9YPwMS
        HARt8ZZsTej8fsUmun8uIVHkfUPn6sB4MFvPl+/64Yap1W8dmJUO4LC1mJysb1mBu3NOsZ6Dl7OM
        HSlFd9I6ctRqxln0/bhBeC1VbHK6DGb5o3YoPewkYohxAxb3XKfILPcBljr4dqhqjwzcO8PbWz97
        ZeCtYsjAbs+JxJALDNYWPQq0qO+BGJe7DuY7mjXwpM0Zu7Dn6cfPqATtaFrI2XKoswx3MYI+3Dji
        4CrKlxhABaiWV2PF7V7hsji5D180uBP38dryEa6iAtezFmMPHZWc697c9uvfWFaZs8rWNn4A9k2r
        +XChe2eFfSgA3cfsLF7Na74pR6OHIMueWKL5A9DL5aqAufBj8uNR6vF3HZIBnom3PY8q56VJAyl/
        +zYjLgSM0doltIxIIWfFagYK9nkPpTyv571wBnT9KPUC57GpiGv7sF6FbAfhp4pCrFn7LSTJVsDD
        l6ewV5w3sEkHgYGJpPjEvXWGs2pGrSM6dHdy14vGeWI9zw6+IVXYiS4yYONrtUEvfZ1xdF16Os5s
        tSDt8Dh4/O3J5jSOJReO0Hfx5cReQ8pNsYaOmRPiEyfv1EmlaoYKhnmS0xSpzqTcYQsbz84xFs4A
        rMq6NYhblhEnIuUc2saWAEHxpt6he0k5kRkm+sN/DrtZ6kKNT/rzAzPrPO7qaCc7EfLouZ8Z9DzW
        DFPIClha7vHn+XRb9R386UN7TkyVi/lrcfBPJPryweZ8pMK2IZdHzfx+mkbIWf42Q6suIMmY3g7X
        MLHMP7yjhI+TMx8OuxJegFth3QAV2GzhaUNO9/qZVaxbuD04eYE//4D8F0dnlF/Lw0P6PLzpWffD
        B6F+BAiggVzcbudswk18HGo7dD3Ee4dw/c4//H4ntpq+nbW2jQd8ahXBp0C0nQWdOAVE5pslx1Mi
        5wxjih40GlLOnCm1+VbSzkTn29WfP5EkDgtrqxJ6HG8nbNDVGHgneYzIMi4KcTddrLet+JjwxwvX
        sVfrr38zIZXTAUtl+qFzZvkarFvj4DGGYqireVQkBHW2w06UH51FblYd5raweKuT1yrts5MNu89w
        8bZJbB1qFXWK+uesEPXQVuqInhoDHLmRcATcTZ3dk8TBxVsRSU6nLp8HU9rBHRPl3/nv841570U4
        12FMcILcnAb05SKuGlZsoJNLpz1JTHgH7DaDPBzBqF1eDwjFtMeJds3VadttGdA/LCZe+VQAnVMo
        QlktdBIvkuaw427l0NV1gtlIdw9AX3mcQk3dXsQ71VhdC8mSoHFKzzizs/hP/YkX15KI9O2PI0+V
        AK0jkD1+++Dh64cDeCyRhKMvzxCajD28R+6VFNH5GPLZXOvonD8cnEf5UeW7pK6g9nFHctXvm7MQ
        wU3htRfe5GbVXUjeXToeogD782um3TDodNTFTtBKr6XAyGm+ajMU+hQT7RF1Dn1eMhPeGY5iSdYt
        lf0owwJfNyaZL7bgOV8eKeHl+TbJMW4ddc3oqQcbv/pYtTWPvo2KE0BjRfPPv9XLk/E59FZgTu60
        eQwrud1LaJL3OPNyQSj1V6cF94+k4zQ6Js5cP64ClF7SDkfvc+hMQ1q4QNVSjlzXIA//8EXacDp2
        pLwCVOVvEH798cyX81LPJX3YKOKHBEuMKtDFe4ccpA9TItr7TJ05buxOjFlbxjchZ8DcBt4F3u1m
        h/Xnkoab/QouqLpKKQ7RcMz5QssbuJ2euQfgWXU+/rgqSIwukldjZlcvd7fK4Fbta+IV5wCQ7/tA
        t/EuesxFUOg6R30E5YOXYRmJz2Hi/YME4Xq8zUetjlRWuz51mLyHDznujXigxwPfAG1jDJxLuyRf
        3Vuhg6LNr1iG22fYsh0XQe6EP1995cEaOJcefPVk3lfPT70ZMJTQgW0HLJ2zi7MOx2OGMkN+4sAe
        9z+9KyHzJDJxpUMGVjamI7zfs2Eel8M9nN6BacIb4/nkuNRCvTSCGiAMHgpO2rga6MFb9B+PYuy1
        dr18+Ro4IQ08QW/mmkrHTwOux/KBdesk1zzMkx7qlscS57m1lI6EbaE7Ljk5eWxLt0fjLACHtwc+
        3vNSpWFmC8BKevynX/JfPwLpUp7IyfEiMLc7vkDXeJlINnCQzskB6HAMpPRPPkOx/drBzlIgOXKI
        ONMvn1Kv1eIJxqjXXHWAHrjFBSAJc70P46t87KBaKj7xjldz4J37UQHoYe+JMom6SuzVUeCNaDG5
        37b9sO1uVIfsmiIcUFcbWO6oP6CkaQnxRsXJueQk7+B3PF+enNTRCEAPyLA7EyccRfoxE0n5w/+u
        +Rjpgi9zD388bQmHKVxK29ygUm8FLnLPzxdueXToYiSht4KHDZZXhIM//Vowd698tE7CDhh8p+Mj
        Ns4Owx1uG/y+D2wHuk0/Z5OVoH+aIpK9Ry6kZp41MPelAf/xY16atNA3lMpbKRPX/Gdn2cAKbeIV
        a906VCOHDpxgkRBbYHy6dmEKUd2NEfEtnwzrqz93IJcPyzx9eZDMUBChp66NJ9LbTKckeuhQXMyJ
        lO4lpF89T6G663oSffWWXfXLBuu7fiBm/j7WmwyDEi4fdfO4MYrrtSznFtiwOM4l13b58npfNJjL
        YJkpGl7hbPaxAkXsHkm8+zQ5tYV3BKWre8a/eqdYFhj49YPEvm4P9XORpBLMY1sRU05oPnz9CbqS
        MfL693jJtx8/Hetlxiqn0XBJGxDBhN0aYkuvql5aKrUIF9tEZC+d882NEYQ/ftRIfh3mPDh6iM13
        7Ne/aSHnDW0Kf/72tF71fJOUVoEqLVKSHp67cEl2cQ9H9XAjpziSAP/js+aKTKyZuKqXYRUiMdX1
        lchOXjuTfX126MdL3/7v8O1tr0D5+Elxbpk3sB1W6wJW139jW/Gxw2jY8iDciTZR0CID5hHfK3Fq
        HzLOESQ1veaFD8+z2hOvGqx8WhIVovfRmYi2dLG6pLbpwvh8cMmlb4716lfJBYZry3nKy4Hq6t3Z
        GXDLNs6feg3oZvP+Bf3xq4om5wt/ByWIt7rFZiCAYa5uS4C++jE/R74fxkysFzjeHjz56gcg+eqO
        MABV5NF02wbqq1YH5Uu4ff/fJyfajfZgeyxnrJ7kzFntcSlB6cc2+ea5YJVv2Q7ybS3NK+eN+faq
        QHUwb/oJW+9QHpgfv6cHKBGrapJ6WcVZgUCiBj728ytcFfe1iZO3Dtg2bn2+MsooiopqKvjqjKeQ
        DRPLhojZjRhzhTlw3/HCuj0eyOmyP4XbL48sGO6J5ZHv61kLbAjTMyhnRkE1+OkX5C68RpzST1V2
        1csNnJdexy6xcMhpgbJD9imtST7mRk5R7NpwEPSFWN88h9duoIO9hUaixqyRr8dsFVHaMDr5rTd6
        MrXLL5+eebbNALsb0QNmUkRI8Xhtv/EtUKdFj/Xp6YMl7iYPNPPGYkXea4BmNtHE/EJu3350Clmy
        zAFYWuaBv/kRZQtJlpCxiwRv8WEDtg/9BIAZQUgkLO3zbbodssP5lvsYi88BTPf+rMFvvkF0xCJK
        k37pobSwg8cb7r5eu9CHsLf2o4fuVghoe+MVMXn0/Vcv5ZrXyKGH3lm4/5e/H0wJokMEz7P49cPd
        0Q8kNFyHDsulZzvjTj/P8HWq7D/+bl7wxEEL6RdvCd7n/MvXGzQ25orl/hZSur4tBQRuuXn7fqHh
        51w4DKjDJiOB2x1zalpRK8p3fyPSQJxhU/3NBPfhlJITfIFwvthdBPtNwnP/9l7q9/7moNn3BHsW
        RCo5n3Aj2tQUZubcaiFjib0Oz5exJCETqQPz/uxnELOmjJ29ZgxslF6huFQKxT+enO79Vf/5VW/9
        5rMT0z5EMG5w7/G+TeulDJAAjsekxW5HUkqoFjN//OBJXDpn/eX5oB9norrlq14ljg3+6IEVOEbO
        AVpBEJyb6cuPgbPWtzKCYlsH8xY+Tuoo7cLs11+9DbFHZ8zcJwdzPN+mUn9K9SYUzxGhITl70O2/
        NHn9aCB+SMbMPAtJ5Q1dhPC7PmZw7aSQRnNfQWBtBvnt31C+IAJY3HbAtsAs9Pv8C7gYcUgK1bLp
        1w+Y4Jv3Y4Up1by/tKMPdfH28eB3f2Lbve4ZyMZbh2Ux3cD6uu8hPCXRDecuwXTJL2SDIm+NWP3o
        +3qsG6aE0elaEe1QKCH3AafmT3523qyPSkDTPaD7bC3ibYUM+IO3aOj3vENye1HKwEMJD2wz4G/+
        6rDy4SLCPzzx1dMVDw8P9QqcyGktP/Vsh0UEjtLzgFXMlPWqpqkP97NQeJQapsMk3OECRd4ZsXF7
        xjltRqUBLT46xKSjoy4TPtrow5eneUFZNjAFkwaQmteeeN/8ZG1UqMCbeFCwbkp6uL2FYQeqiTt6
        26N8On/6tdBn+Mu/obo9fLgTzZBK3qdeNzqfo+lP//Ygx2o1Z10JA/Gki+THJ9v2uvigkZIZezFu
        QrqHWwNDh3uSUx1m4ZT1/gMRcyqwlVcfddmoPKM3cjE5AaSGrOHED/hdv9haA5CTNRRdSE5PCyf7
        SlI5jzN18PXP2Md9q876SQqQze9eGLORX5NYVVz01WuMGSMO6Wk6myL/nEKvqrwFTK/E5qDj7Rsi
        1a+jw2y63yLRnl/ktGOHnHBSDcGivgbiTpBxRiVyBLCW94P3+uX7kU85ZPNd+uWR17Ce3FaAScXD
        eXWnJ+2sOA5gyzzDrx/OauJ6dwbWanAiYf5K1DU6Ojbsgi4hvvOq6PbNH+HUnCg2LvcW0GeX2nDi
        GA/nnCir/BpuLlrD85scB5er/+QXv3r/5i/D8KuPzS81jFOFULr3hA4an9qZGXPjnV+9HI6C0GHj
        pmoOv8rQFbe8uxANu6eBJe+DCQ8nDL5+ZKNfHrqI8nFIibx/ec4UkKwT38lielvKY3Ud0siDHn9p
        58oe75TgG7PATGzqP/PPWky0wRJaHPa+9c77WlBAp8nHWViTBHy++cwv/yCR1PkDb2TWAqpdccfh
        T59xek7huHPOGH9qQZ0S+ezD4p5dZyE6JipVi56BX72Y1/NeCtvVlB9wvFX8PAqK5DDEVgoIWWAT
        aR45sILlncIhKS8EX+03GEew8+F3/9FbGr6m3aGKdfjb7/jut+Yshx8emirlOe+zRHX4D32XsDV1
        99e/8+nzFhpQSPsAm+RtAHbf9hIY32aA4yfLgLWanQqaXTth1egFZ9nXcwlHvrj/yRdXPwMK1GO1
        wkdUWCr3tnsJ+quf4JA8OHXmTFuDd6yXxJpjX/3xqlg0ruHtaSMNfH5jBRglCsLusX7Wm1etPioK
        uvO2L6+x1ew84PmtDFhvUVgzbOtBGA/uFfuWj4fpbCIFBml4x0cCpOG33mETOzm29qFKaawqHuwi
        /jM3WXjP6WLiEXq3bIeN4S07657cbXhdlMo78OMZ8Nh+QXTZz4q3JmRRlx+fMa33JHrfvOr+t1//
        9+9UwH/+66+//tfvhEHb3crX92DAVK7Tv//7qMC/+X+PbfZ6/TmGMI/Zo/z7n/86gfD3Z+jaz/S/
        p64p3+Pf//zFwj9nDf6euil7/b/X//V91H/+6/8AAAD//wMAyKWAHuAgAAA=
    headers:
      CF-RAY:
      - 9587aa53d98bf233-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 01 Jul 2025 17:36:24 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-allow-origin:
      - '*'
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-model:
      - text-embedding-3-small
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '77'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      via:
      - envoy-router-65d94d6b4c-n7nf8
      x-envoy-upstream-service-time:
      - '80'
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '10000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '9999979'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_5966c0e1962f1cc17c66e19a2a5c873c
    status:
      code: 200
      message: OK
- request:
    body: '{"input": ["Visual Aids(Teaching Method): Use of physical objects to help
      illustrate mathematical concepts."], "model": "text-embedding-3-small", "encoding_format":
      "base64"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '174'
      content-type:
      - application/json
      cookie:
      - __cf_bm=7XHLdnaCxW3mhu.O7RaJ8t1a4uAHI6mlHu3VfEYs0Ew-1751391361-*******-mpMlI8wLN_fK7ILCCmaOem0t8eR6cQqjkZvXcm.vhuuoAoCMNuwhHaX2830nq83AFFZRB3Y0tFuUFY9OsHyafJRpkk437K7NztFUVBWvNUs;
        _cfuvid=vLbBcLMQoKUtOCugPnUg_H9aADRheAVHbrMDJqmikBA-1751391361577-*******-*********
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.78.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.78.0
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.12
    method: POST
    uri: https://api.openai.com/v1/embeddings
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1SaW8+6zLbl79enePPe2iscpSbrDikVpJRSUNROpwOKIoIghyqonf3dO/hf2b33
        zZM8WJwnY/7GqPqPf/z1199Vkqe37u9//fV38Wq7v//XtO0ed/Hf//rrf//jr7/++us/fn//x8i0
        TNL7/fV5/ob/fnx97unw97/+kv9ry/8fNO3h2iVbVfGRcD+UVMQFHamTuSs8Oia6QGplQy+69z4Z
        yNqoIFDbjm6SvsG8FY8SnIeuUHs0w0aJrVdlko3yov46S8lHcvTW0K/vkHrHUxaKgH+2QPbqnVKT
        4XyUlYVsxqfySqm9BzQWm/QIcVI4lNrWW3DE6B4V7dWgOzJWREjIeKFMmg3MTTUt7+Igd02yl+9s
        r9MbHgsarBHdcosd5PU2HDxDvqGocUsWfTKSq8UmilCdnBR6vc5owiH3ngZ+poT565rmqpxtAUlC
        yqjzrL5YZMvQB9xJF1+Cxz0RktDTeRyUmj9bZYbo5TP24S7nM0oX7jMco66uIKWvPT1VIyZKvbzF
        BuAnYcl+aZFRrt3Y7LaHm29EJwWzYNtvwbkZqW8clU2uFs0xgsDwJbojxhrJ7SdywTfErJ8tDwYZ
        IyVvTbnyBoaHN8ZyiqoKuhUXzDutHKLE328PHQlLRvbHCxGSwj0zChrkc4PxcHQUCSBQWo3uNl8b
        CcyHpwk+Vtl2S9xE9jXlaUry7UPjk2Lg0dnxGURhM/hcd/aEBW4pm0WmU3qNZ6tQ0WWvhLuRaezu
        eQSr8m6Rms5JZ+xYESNsgr4yIF3kH7ousyoR2esqQ1B2L5auFyehBWkTm05lHJh3Xu0ajuiuAq7s
        UrZHzjbXAv4sTblwY3ZNoizvi/pYmXK5WdFlTe7N6BSSh3A2G6itFCXqZbrwQD+ViOF+e07UggZL
        M/iwC3Nv2gFr0mG8mfLbe7P1a/7A8l3Kj6Yevzd02YkvEdkyeiJq7UOarjdOqNTWtUe+GGIWR/ey
        4amW7aG+nRoWldcIjRGuS3De8Ya56ZmFY3T4tkhSTcxWpWfioX6GkVknR0JJKLOm9R/KDCQkren6
        U2tNH5kFB4ddVuz0jS3CwdyMwKEP/VEUu3CI3Sw2YWs/qe8MqRiLR6yD3G8ebD+nARFBlakoPr8N
        ttD7NVadgzoDcLFJlx2qkAjk8g31PqK++ryaTS9v7KXpXOZrtvPGEbfpp0qBz2lDVx/jItQiPNxM
        pzOObP2+7kOR2XvflDOi0VVlLJCMGqpD9Kk3bHOJxpC3zkOFjvDW1+8PD3EfgY4wApveSUzE6FD+
        NeNkeZ70RSZjdM44Av/psNXH0NEYdS2gztsj5uT6IdSCZx9Dusg+9Hrps0R1CiSjTLvd6OVxDhoh
        ERRD8TSu1Ml0D2l4nG8RpfxMN0m6azRp0DmA9awZlWgmqk5cwHQO+oum60OJv07BX2Yg+ojuXHuF
        VGfHAfiwO9Dr735+1y+hW8QWmuo3WqYfWpNstTmjthXgoYZgD9QKQuq8dBRyWLmlSWxFsHDcfsko
        S26AwLL6HmiIG9U5zAA42p190W/P4VC9ogiBswjY5a4dw6E2ToYJa/vDqCQdhaLbzhoy8+5RLLa7
        RCFf/Whyo/3QHfEWWO4WgWvCDhPqnTqfDPG+8kAuv4RS/NyHarELLtA5+ze9+eIkBL6YAUhjiiY9
        znOBl8No4kwqqaU/tolanI4piqrKY0m4rAW/r743iNfFvNdeF4FHuVvHIL+/D+Y7h02o8covgSs0
        ZVi8N7ncbS4Gqh/Rha5fm2sipEFeGnLhxVO9v5KxON1Sw/nGNsMMBO6qV3oEuuUKu9wen1xk23sA
        +G269DGjVyy3n9RF8b6MmL+clw0LvtkTUvLEvlLbz7wJXt8ROdE87kfRXUNNeswM6FaBTt270zeD
        NyqtEe+KioZtOycDeQ6GGWTs0Su1UeCxwPvKJI6i0uNPP6JrdoP6Fql0/dm8iIb3yJ+Oh3v9ztxE
        vg9fH376gYc2E2M0ZDYQovhs2TQSGWXHvphRV+3oqYlxKN8/nzU4t3nIdl5wxmOxeLwB57cXPX6T
        WVL5ofwy66N6ZulqXgshrQbLzNRHwqgp9eFQ67EOelLK0/vuk0F3Pdn0FYF65f1NUS8fth7Imbdj
        7sP5IN5tLrpZPJPQR4E8b379EzlntKc7EhjhKJvUh0yBhlHQ2oQjsg6AnKKCecfdKx/i6PtEcVDa
        NAmPcjPV59H01WGg1OJLIvtYsgFX8KTnvCpzfr+/bBMsnPu/+hiIbKxRJh5zSgIgWMPr+Q0y8+H1
        HM7PZLhGhQx1pOrsYT7CcIj1XDZ9NMjMMjRKWjhv1lD015JRi6e5yORT9dOjP3rG4WqNRqD0mi/N
        pBSNxWp/NLtlaFO6sHwxyhjbEF/fJXvYexAMp4OFJt5gyf62yQXXXT6N19jqG/j5UMcXAJm5T5au
        NkMoMAcZUs8K6N01vFCJ5dqF+Lbe9YOqkrC9h6WBIuYKes7cMRHSaa5C1FeYPszPWzRSMX5NJ9Er
        ljrDodECnhtAEvnK0uW8zKtO20dmhu5pr5TeHauFSNYQdd8d3RuPUQj8BBuRzXGkt50IGrmV7i3i
        si/8fNiWYnSywTUDvl2yQDltwrteLVxTUtPw33xWZPEbdRtOaBIch0RIDz1A6c6qaVTOD0KGDd5D
        NoM1i4rhlTMJKTKkS+vKrolvI+5r4xay8eFT77ijYnScuW50NNj4JnlpiHdk/zYLdr1Rm99HIrAn
        u+akL2znjqSRWxqVpnOdD/5n0uMff8HdyDV/HFcMc//EDeBy29P4ZDbNGJ1eSxNsTJmTVRZRo11f
        QXxap3T1jXehxt9Eh6j/YuovDzH+vS9zur9JL+VkqOJHiYoizv70ew7SIoWiRVYvzR5qzpFD3lBf
        T8eeWlbT8Dt+ccj44+VLlv4So7zwK3DiWGNbXyhoiMd2RD4MX0YOspoM8SufQXwuV77oW6mp9di1
        QGbfpFffh0OjytrmBROP0D3QRd7Lc6oC2FZMD/O1HcptHqaAy5lM/dWiSIZ6fVtCUV27frbO8kbg
        pxT99Is+bGskgvNVZOqX8s6226QQE2+BSV1e0WMbNoIFVeei+nQM2HaLfCKjz84H/JUWzL19wmaU
        lZUH3SrsKMX7Va7ho/H+8zs5tF7Y+oNwTSdGH7alOWpqMkOyMdUnJROParz1vqiodDHxOULtjz+K
        l27QVRWXaJSZPUKRzZdsD2cr5L4yvsxMfzhsgcocicy4xojsFMao+VkiTWqG1vRhb1AsZIdoP77E
        arpk9tAFSEhzQ9XlT6UyalcEjUU29T/f2vZ8TpeIt+F9ZnT+PuwzI8JkLLLgbaTkqbPrtSzQoL9W
        MUS9Z0798YkHvbWeJv5ID+pN9VXB3fLM+FqWbF0evmIsVhFAAH3JrLlTklEediM4Adr5xrFo8UCO
        PDWjxispObx1MfHxDOG3afajXOBGZNE+nr6/ObtvbDXsdGMJ5sR31ObmDo/R5/tGTqUfpu9/aHq5
        ti7oxydbP29IHzlPMHEDGoujIsj7H48WpR7QZY0i3DuDuf89b3b3XsemCS6fL0jK7EK9UwF575hq
        DxlPO1+y9hui8b0Vm5n5kHq1rOWku5b9F6U7u+6NyFw0SvzOK/TzM6sm3ueDrtoBBLqfss3EU33E
        siPiEn2zhT7Lm9FB+gWCvgO2uZaFGKPH82k6dzTzR/m+REM8+xoGLF4P5mTPRcgh3LRQh1HJ8Ah2
        o8oSeQMsFldqi+6aDLV9ayF1bE6XLPmQOrZfRwO21pM+LKvJJ97RzXRrCzoxRjMWhysHbu70ie/G
        5u0X8x6A4AezlXtFRnmBI3Pyn5QE8rURmX63UPG5amzZNST506+B2tp0vMlfbnsf6tPZ8Y1zUTWj
        TDYcit5o++ourYSWecEeJCW9MxIch7DynVmM4vVn7n+nfvhuw+BrFs847cdxh8hwTZkBMiMnNvFN
        KCSiLyGbw/T+d3I+RkW/hUy+rejWF24z8dQNMn5/MRLc9kTDT72F+PLmf3ift1o0g0yCga4nfR5I
        LyyEX7PP5H8n/Xg8YnD2Rsv8Zb0hcvs4LiHjULHdZlzkY0Rzy4TNcz7Vxwu37eN8gTqW9ywJjrNm
        0C1rhM4bVWarq0yMzgc90eSnKB6gzQf9QiqQm6/PqOnUqNP1xQz040rypRm9Ep6GVQCpkz0ZBTrm
        tfcCA6g3HnvYkSbnbXiCiRcl9rCeQnT1uH8iPV3tfeOkGIRhLqtQx2evlxl6YZHd0jWkm+eZXi/r
        I/pO1wP66b1mD9vFDe/IpQSfD3qv5Rc1EUFVy0BWyoltf+e74+9oSrO0o74z34eKflzuQQIAuvra
        B8x9LHqDLnnCfs9Tda5jbMotEWyTRItGRuHKBv3oSNQ7ny4hv2/qf+s12S93yehcxwvSk+Vy6h+b
        8KfHCLfpSJc92uIf3wMf+jNdaOVABq+U3tB5wdof5VNJRmczL40U23lvnO8bIftoeJuT/2G3bSgn
        AqdwNLr1vqcPkBo8xH51MwLkK9RCD5U0wY1ZEMzZkmHRuqF8z98XE6xXzTZxWeOxaG5HwO2sY7ht
        53j6fiPTuRow8VnaDHq1cVGgbB26+n6/P//rIglmwJZtvknUYrWPDGePsn6QZ1wIrPPAjKrNku1c
        W5A//PTrp05utWgs8ngPEpc8P//xszeaPdTJyaLnuyvlAsvSC+S6elESLIUY6n3whGycHZl7f3yF
        CNTXDIgfKXRdLiys8YrsQRphwahJFSR4QOI5Nylmv3rvvHjmIf3q7NiyFxbWpNC8oaIwbtR5upDw
        dF7NIHYK1kvS+ZkP+nvBf3pO3fsnEqOcb44AS2vim01EBL/sRhS86ZqS4Jg2yi+v+j3PLc0zMtTG
        3YCU2CYj4fuR9DLa+kBcpaUkXJ4Jb53j09RTZ8+2VBCk/vSDrvYRS9eDn8itFn5NXJmnXnte5tP9
        4Ivxpx+I3SxsO+1yRPpjheiq+i4nP8DWkNLnnl6v/bKR76xQIWBsZNb8jPMxIs/IlGZQUQqSHjZT
        fwRJgUtvbsYedcQSJeKaf+onPcs5evg3Izo3s56Z9IRG5zy2UBTXiF7unyk/Kk57CIb+288vpYn5
        vc5vQFdBxFblV8eC69sl1Ifjyv883YVQ5fnyCVP/ZXuDKskQPysAbm59tjfYveH3T8MhfhRHaqud
        K+R7UlTgMMNimC39fJQL+gX9tLKov1xALvBN/iJYvha+XDeXpi9O59GMj2XO1u/DO+Tt/JeHnBXq
        5O4FKfHrl1/tdRZ95gppIVy0ZjY8Lmz9OVhI4+sFmIHRrtiW4jAcncRYAn7fLixdDgqpSTyqKMg7
        0msv/vrjvyF1nxklIfiJxmE9/vwqXTJkYyVO2xKi2jUotZ9xwrLy6gNsnwldVokm+oLtL2YdnhLm
        Pj4v0XBu2yZdjYf+gS9D3pFIpODkl5BZSMqRfBeFbzq94fXocNsiln1DA8Hi+aDx8ZQlgsvb1uDz
        fqBUesxRH0kvD0W1t2JOts8F7+6HJUz10gvx3jQafy5fED+WP//uNop+dAGlvv1glu5wzO/3rw31
        QVbYskIj6vQWy6b+cBC1jM8h/50POhrMJv4cG8HHRWvC7nlgJFiWDffr+RrwS7qx7S5XsJAUlEJn
        jdwfRdeSrpYvW1MuvT29T/5TBHFr//pfD9P1yL6jxihOyoCuansnxkjLLz89nvzwQbBsdlyjqHdN
        urn6m1zLZscl4OeNTP1dQqM8X/hm8U6ebKGqJJEnfwHZ7KH8eV9C2vCLSe2RMWuqPxFc2iPKtNmZ
        2uO9ScbCCSP45Z+bS2k3Gm6VG9SP44UFSlc0gseeh+Te/VI8yFauBZdP9cuD6H4mPZuBfEX10wsa
        9lstGZ05WNBtxxejlm6LsRjCGFKSdWxL0bf5TnpqkvC4pAe17ET/Jy8I1IZtrqlDtED9zoyiNAKf
        Tf5d4FSOAHx7S5eMbHLuf+YxxPsPYetPRhqBl+YapMFU6Po1l7AIlpUKHLYXtrnMtkR1auTD5DcZ
        Zu0pF1kbGWZUubSHLa7zgnx5BEV/Gegmnq2Sof7el0Auqu2Dj20ksjH+Iuc+n7Ht738ub2Iouiv/
        5RtYm/yHQbba0h8U3w/7Ig/2EH3c6x/erNo8Go2i01Xmrw4v0ZGjfkPOVYceTXlyR0p9hiZe/vmJ
        nE3HgwxuD3a5fUQ+FqukQpIwM7bQ/M+/9SOQ/F1fX1QLa9ky3EI23C/0nFVx/o3ubw/q4+lAtz5y
        kqF6pZFBd3tOT4U3FyLYvwIzm6WEbnc4JqMsbM+UGzekZA8HNEb3pzzVn8OifAMNw0/eIucyRz/9
        EBoORt2sr+cjs8zPYjp/9IRsPsOMYn3bNL9+8eMTd+JP3uK7C902+LDNdX3Bim67KfzyIn+5YGEv
        X9db6HaHgNm8u6Eu1nMVAtRzuqpsr1GuHuv/8JJxKmjI20Mwg5/fm/wdav1cOSLwFjW1VcVpBL/Y
        N3Cu1wU7aLO4EVlwsE3c3Sp67Bu/6QslHkHON/mUjw7JMPk3SLdZTp2ntU96ucYt0g/vnsVnRSSD
        nvo2pJt8S/H4LsNB93YqTP3FV6oxJ2O0yWcQXWrZN6KV1nSk4l+A9eLmf7LLK6/QCv/6o8v85aDg
        9i71X+Q85t9pf4wV7yXrRrqwNeo72RFzKOwt+vk5J9N1PDpz6QhR2YQ9Co9OqDoSWkMmQGLRZ4GT
        P3lKptyL6fm8CEf5SoXO4yrdG04d8om/YeIb318Nl7D95cvkLq8oNR/rKb9LZnPOd8lUL59wyhPW
        JizwlW1iVaA+Up4XU4LHYuKHhZD9D7TQWYHC3LvmEI2Dz1Hnh64v4YrhaXxs/vwklSQZjc5cb2Ga
        D/MFl72cd851C5Jh7v/kgyLjAZhFe9lPPElw7xxEBc73YjNrLn2bvriHW5MsFJd65/sGDbUfq9At
        gsbXsgtDn0nfDf3kKMxfzzsyVPG5BK7vLObepRUSElXXJlkqDxq962cuMv3am1M+x/zV5k1ENgbf
        ia8lumR58fO3s6mfG8welKGZ8rwXxJelxW47vEWD18+OIHFT6iX4LHI1mpcxitNlz+wpT1J0f7WF
        iS+Yv9xY4lto8Q0VX+NB/XVtJ1p2SXvINDizdH0QIe9OcWToaRlR3Ldj2Ed18YYoa/Y/PkATv6UQ
        bz8jpSatGiYdxtQMlNamd/eFwraVTj0Kmq6guAU1b395JGbSnP2ZT6RnXgJJjxYLxImhP34gTt4B
        e0iS0wy1m/A/8w934vkJCzjzkfwmU54hC9IX55s3lwtCGTm8m1z83jcH//LjyZBJZ1md9HHDVl+P
        Id6iKJpzdXfsTdfwkvaevCJUvK/P6fumuZAe4g3ylwTT/F7X8C45xEDO6pbaSrfK5VY83ka6eZ3p
        difGZnTEUEG9P1IaR2YvhHSdb+fRx7tO+fOKMImo7S9fZr/vk0m5MQOZeU+GxdYimnQG9ZcH9OCT
        RcMCt/KRr45A/dVmiUVQdR788o5NHJFGC/bdG4Inq5nzcqtk0i9vymFruqq8Ohx+esSl/uvPE3Uj
        tEwNbBNW9uAzeHA85TcXg7iy9suf0FCn+yOShvuKbf2kTRh/bp8QHZuPL0kUkjZFz8qc/CXdxGqZ
        C35byKDfVm+6UHr1j/6axD0CO2cXu9ECfZoPXiguc3KXNAoJYAn18Xxg7uP8QNwP4QWTH/LFsF2L
        gVh8b8pNjX0tq7qEZXBdwm++xIuUl5j6W2V01t5iBzSziIZfqEXRq1n3ajZPhOBvrIPcbWpK4bEn
        goPlmfVZ7dn6XVuNUr/jHkggb9jCiHLC+Nvxofher8yW70shgkttg3Oav+mxxX04OnPemkWnL6h7
        cy5598uzM+NRTfx/FUrthSl0fmgy3N2GnNMVX5v4a6rT/n7y8ycofnyObP3ZsGSUF+sKnDresoXS
        R/kQe/USJGmW+pL1FIijbhsA+C+HOU/9iLRgrAKIcnc78VSdcxCujepLVLNVGceN+usPUz7L3JTm
        YUe28x4CtLUmfzhrBmIMrqmflqTnU97eXe3ui6TRbHrJunzEn3z079+qgP/8x19//Z/fCoOyuqfF
        tDCgS4fun/+1VOCf2j/bMi6KP8sQ+jZ+pn//698rEP6um6qsu//bVe/00/79r78U+LPW4O+u6uLi
        v2//x3Sq//zH/wMAAP//AwBe9S8+4CAAAA==
    headers:
      CF-RAY:
      - 9587aa57ec84f233-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 01 Jul 2025 17:36:25 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-allow-origin:
      - '*'
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-model:
      - text-embedding-3-small
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '80'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      via:
      - envoy-router-568d6b5f7d-rg2d2
      x-envoy-upstream-service-time:
      - '87'
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '10000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '9999977'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_3d33f924da786be80fc8dc9d108dfbf3
    status:
      code: 200
      message: OK
- request:
    body: '{"input": ["Games and Stories(Teaching Approach): Interactive methods to
      engage children while teaching math concepts."], "model": "text-embedding-3-small",
      "encoding_format": "base64"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '185'
      content-type:
      - application/json
      cookie:
      - __cf_bm=7XHLdnaCxW3mhu.O7RaJ8t1a4uAHI6mlHu3VfEYs0Ew-1751391361-*******-mpMlI8wLN_fK7ILCCmaOem0t8eR6cQqjkZvXcm.vhuuoAoCMNuwhHaX2830nq83AFFZRB3Y0tFuUFY9OsHyafJRpkk437K7NztFUVBWvNUs;
        _cfuvid=vLbBcLMQoKUtOCugPnUg_H9aADRheAVHbrMDJqmikBA-1751391361577-*******-*********
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.78.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.78.0
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.12
    method: POST
    uri: https://api.openai.com/v1/embeddings
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1R6WROyPrPn/fspnvrfOqdkkyTvHZuIgATEBaampgBRAZE1AXLqfPcpfKZmufEC
        U0o63f1bOv/5rz9//mnSMs/Gf/79559PMYz//Lf12SMZk3/+/ee//+vPnz9//vP3+f+tzOs0fzyK
        7+u3/Pdl8X3k8z///sP9nyf/d9G///zTCWJDtnha+vkptjkIIuPt7Z6XRp+A6/nyEhPBo2kRl7xU
        JzXUjxuGtbrb6jMMxAWRVOzoPt5hNnRPBSJPAlts3Z4d6w5oPMMnBCE+druKLV86X2DR9FePs7Ga
        TnZbXNBlfF1xHmhOOtJ35sL7016wSWe/HJ3heIZJVX1x5NWHdDlcsQbtEEr0FlcnXVTeSIGb99ul
        V/FThAznYYbe3ONAfTmawuk6uRY4pu8XvW2Pui7OdWLClD52+EnqT0rU7ctEM+efsO3dbiGv4bcH
        67Bk2CvOsTPXTCzQacdKaprmAKbgsKtha1y/eF/TozPdt3ENJfoasOZ9Xs7yah0O8vlNxVfxo4W8
        9/EzSHyDo/H+YPTiLL89VGiRjvXb8cVmLoI+pE+MsaoFjM3B5lGDeEASPbyvkcP75fsGBni5Y287
        q6lYhXyEvi5j9HgZ1JAr5uEsvxIB00PyqgHb1o4J58N3oHpQP/vF8AMNSYk8UAVnmE2bzeRDXfJr
        erhOIGTVlnhQCj4zVRhi/SghzkeM3Qlhpfws+bhfzkij+R2bJGxZE5JmQHr7VvEVHUVn+XBAgXVd
        vGl2l3WnLZ1hg25XeSGoC68OsTPLg1k0yjgLdkAnu0sXwWsvWDRRtFvIvKWoUdVvt1RZzkXPTi+7
        hh0ACXUsMewpPt1klDcXjyYhh5wZPN8Tum2rCt9csHHGSo8IzInXko0J+JCykCSSj+o9dSGf9nxl
        lj7y6sygnvB9p6xZojO4beuK6ppkM7HaqQbKMqRTb6rjVHDJy0WX5yfGAXhfQvFhFgsEW4lQ7eNG
        Kd+N3gC3F9Oi7qhzJSl3kEDNaRV6L8VAF4zqZaNfvir3axjO0tnJ4SdqXvhWkNEhWToWAGlc4Ymi
        ddBbh4Yc7NJXSbOZKbpINf4FNx13oMZjVEuBT1MPqoOx4KOXEzCFx4f2qyd6nC/vnt3LgEODZR1p
        3NjvcvSgbCOhOhZY8eMPWz6P+AKjKGI0PIbfnmRXK0KtSF2M+8MtXbyo3cCFVwFOGq3V5ypZInQu
        thw1PTcpBVvGGmzE5UDVkzD1Iz89IDCWu09d87IJF3S5EZCwm4v1D/iyIXRpDRsjKrDziE2Hv5SP
        Djqa32OVNRNj5wS5EO32HLU/Xzflcrzh4MF760TaZ4eQA3OwQe3MzTjX3goQPkfbhXF/q+kBTcRh
        6gdXIAzCLwl9S2LtsxVzaKVVivO1Pvi+WjL5/DhEFOcBcqjwdgf4sLMTjrudwcReP3p/6z3j6Mia
        nNdeaIqDHU5jWdSXazDmsoeOFtXpM0v57QFGUGhPF5pvXgYQYmHM4JsPJqziw0lncpLK0OJ9jwax
        pzvcc7I4GLaXA0125xtYWi2I0FrPntCWeTg/fPkG5QECeotnMZ1cPy7QbUo9erISKVx2yGrkNx9O
        pNlBGJIdUhp0seQrIQLXl7T2wAAXeT96i4ABGFztWyCTb2VsHrd+yPVF+oJyoRHCO9EnZYlfW0A/
        2ja2pJwvh5DfC3DSKh5n9DOWY3Dd3sBA4pQs4itKJ9XPBJDkg4NDs6octpUTD1I9i/Hlplohf96F
        soz7HFDle0jCpU/zF4y+SMVqPrRgek6KgMqtEFBv7PpyPtv+C6ImcKi3keN02glh87d/eUacpeyR
        7CQAhA/DIT2fddGfQAaFPDBpHpGqXIT8swA1e1X4qqAGEMOPFXj4Ricig+++FDHP27Cxz4hUEbo5
        8/wcDXh5fmOMDfAuybXFNpyvr4YeLLcGtNrWLhzmyMY3Pzk5fFu3NeC73Rv75aZImdXzJrLDjYT3
        +ztKl12zK8C8dRMawuuVsdsWeTCp8Y6euJMC+OZ+sWAWuKff/tmUFUWBNq+hpFf9+WYzNzoRPHF0
        xjgtdv0UtekNFl9hoCfx6IUi81MOskhp6SXL9g51BIVDG9W+4jVeYRWadobq+wRpYmxdh/vsvwsK
        4PCm2ahz/TBKpoFc93qhl6fTpIx7vs/wHFyKX72WtIEHCNOzlBDOOkC24JssQOVyOOEVL9Z65D2k
        Pq4nfK67rfM5C+oF2bEYkq9oHRyxeYcDDJYa4cPizOGyhyr3wxfCa94+5A/LYiL2AAY2Em8p2eeO
        N/JBrwXszSIP2lN3ttDaX+jjJD0d1kgogt2ETtiS+ieb8NuOduo5VbBzfRU9PXEx+dWXNy3DXV8S
        EnvgaTwgVllq9uINdh3k6jGkXrH3GEEKrn79kT6bDwobBloFBmT+UNs7DvrsfaIM6dEQ4UxKv/qc
        do4lO2/Np8dYUMPFMxIObYZHjDOr8BmtS41DQalRqrtbhYlp9tZgllkJzgqY9+yRuzkUWnzBniP1
        zujQUIDFlxvItFdZOuW8ViDWcwoNtHyjE/sjykgzPx72PrPlzFJ9rpCBwgdWivHDyNttMvi8Zx1O
        H7WfinLvZ2gY6wVbevrUJxL5FtoV0pmeW1gyti2mF6K+vsNHQ1/C+fyWXbgzrBE/xqXSmamCCFqq
        1OLzw+zKv/2fp1ZHk+r4KWcYbBdwCl8BVt0P50zvPcigj6o9fqCq16cPMRW4vRgWvm+9CxCuwSeH
        xp3jaJL5hbOgZuSg0OmI9JqmpdyIqQB3+s3ApvU+9kuU7rwffnmFSk0meCksYD4mCraXx6ccpodn
        wevBs+iJlwLAGUlToevQzaT+9hpbwiq2wE09Lyu+5Ol8o/4LBWJHqSm4p1S8+TsBbrbZwWPP8RoO
        12FXgcddH4nQ7MV+TDeJD50IUcJlLU6n/WL7YM1Hb9apXE4s1CZErgj/5SucWLIOne8uovks8mxU
        0NGGwbxc8Io3+uKl3AvtPD+g5ztVHFFZ5hq9t/EFJ2p4BmRy4xwe/W+IbUmVw0k8i8YPL/D9rt4B
        81JLgl86vfHpGCgpV3AbAXYvq8JhEF9DsTnKGmIPI8XH6FMByhWAg60gldiWJC2c8uq0AZNLYqrr
        e97plhMnI2O5+vR+eeaA0W0hw+E73KnijkM515Y5gBYuGw8FdslmqVQUdCCHgOJ3qYLxLBwvMtsE
        wNt2sqvPUHRyANIzIYv08MI5Ha8E7rdCTI8zuTlMWI5nKBuHhZrw2+njLQ83SFPLjB5YdgJ9804H
        +BT5N3YhD/r5sYtq2HW+RXb7CwGT+GoNmGwPGtUept0zw0QmTDibp7ZcspQNi1Ej2a0MHACy6ef7
        +I7g9Vtt6emzU0oO83EBnoKLqW+eMZu4rsrQDVxTbNTKrpxXPgIPUDnhG0fqnonV8wx+eiAPKyOs
        EeQ2MI2/uSdXNwqmttK6X7+j7sMoQ4L74wYm7OJi/16gciqvivzDL6y8W1aS+Oks8KptttREktkP
        h8xQ0Io3WKdPmJJDAHMYMSpRD2Xncl75l3y5PnmszGrtDEFmDVB5qgnNw9krp01cVYjslhrvx0oO
        mZcqElIuO4Hm5fQEo1Sf61/8yXRhcjp6cLFR0I4Mn/j+0Ivy8XmDBSknaqZayJYzFTdwVmmA9z6l
        gOZXyQWjDyN6FnDK5kr3CdCwfMSHFyj7iveOBsydEyCLfhp7NntSBL378KRYnD7hgvsjhJeitvBh
        eCnh33yyL+xN92YTsyWoFR+6G8mn0Yk+0ymqrQWgIpkwdkorHUROcgEnlpDsGlstZ3QsCxS/sYb3
        K16wV+O7qJGqydsxFwImPBYLYToY2H9Mk84efG1D5aknNNoVXzDpz+4MZ62U6REXb2cyo10OYOlf
        aEqyY9+awLxA2pgx1pVbwDi+8D3Evfs3kY1FBb0/gRzs5enrzU9zE87dRj9DICshYb4lgdHRSwWS
        ba/Sw6k7lUt2tRL426/rN3nPQ1HPoOVyAo3t7ZMx9kUvMOlpTqCMXvo8Ow8Ic69LsfU4zGDKHIWT
        lUdi0X0ufPrJuts3eEnKrwcm0Qx76QBkuCy1Qm31hXSG30QBq37A7spHl7VfAh5JeNVf234pi1SD
        nHrusF49VH3a3M4KOnHj7E03Q2BtN3oEQA7U9Hh873TCcYMGpVtbEiAU91A8WY0EvyaICLp/lHDa
        jGwA8rABRFbNph9NESwwrPwrfezuL0Y3Wl3AZRcYGPOqpjPlNMqwKBYZ20P26Kd0u5cgAO0H//Qn
        DwvuIsuFQsiGL6lOH3rkgqDkI/rTh4SPlA6q51jBt1E7pcPEUQ7eTmpDg4uhOwIKDBvZMR963Kf+
        ONSPgwXkrS2u/SEMWfzuCDoGKSTyOCb6Ak9zLVOrcLDp6FrJxtGWofeoZawP006fAFcV6BRVOk3d
        W9FPhO44eD2ZJd4fdkI4//qXjNWtx50tA6x4Y8NVH3tbdMBMkHEzSE4PH1g5TEJfmLVTg7VesC53
        Rim+msiFjXOosbKctV7I0vEFVn5H2Nc1QoHs5gipIW6pnW/34bA7RP5PD+AY0z6cjKSpoSmPAz7G
        wjucW3kaUGlWAb2Edd/TNR+gldYpkXPrES5i1zTQ3M45kUMO6cvwDRJ0HZoZm6nGwCz4jQI18+sR
        fvVfVn2owAPZB3Q/2h/Qnk+lCQXR9qmTWcd+LDjdQxawWrKdDDfldCe1weoHeGR8vNNpeMQcnLx7
        T0/OMe1JvrUNmOatjxWDa/X5yF0LeO05C6/np4uXTmug1kUfHGJiOMKy+wxyXvoZjoDilWJVzRDd
        L97HYyu/n9zbpoFXDW5p1Nl8uIRSc4avbY6p1aJen4POzMCPj5y/ZE5pJIMC/PSf5uqHn9+jgaFo
        PtTAsRoKQZsIQKteNj3foZ2y3/ks6HXCBgpo31v6jfvpB2pxxlufgCgacrOLY5qRQ73qFUv42z/N
        4q2BRU+33A/PidROL7BYx8ZFJzPdED7sUTphPiggv88k/IsPi09pDXMlJHivD64+JVXQwB+f1M2q
        0ofJ2ioAaPEH28Z2cH77BzyuM293q7JyWvUaCB52SI+l8k57cxw82Jg6R25nWOuzpJoSfE94Q42s
        peEslZby4xN0H6WtM57fiwvX+GLz7Zsh756lHJb11sI/PFr2YV+DFf+ouzEjndXP6QzRuz6QeRr0
        cHmhM4TDnNhUk3zD4Vc+CVY/j8Bba5ZLoHA1cJUqp8peZSE9vewKrn4gQdA0wsWSygqeH/uIWs3T
        CIVehwJ83vOOzCytS0Y1VMCLt0zYEpgRivsu6NA5O1N86l7b9Mc/5UacDjSS+ieYMB+/fu9P+sq7
        p6yo3wZyT0dEtUJ/hT99BOHlRenlbFWMCm+DwBTWLt0Llz3gn/Ugy01oH7Bm2GVJ+3t0k7vemPHT
        3qslByPVRrUqv7HzGTY6eye19zffLsdol86OEmWw71XBi1d+vTxlJUF8flHpdQoRGDp75v7G33df
        Wsq9SUygRIuBMKXM9FU/c3Dlq4Qj08dZmAU8QJPjw/vx01ZqRxus9UMDd8nTyYX25S9ftBkY0kF0
        4w38xI8jvh52t5SLamuC50it6c9vmOKr68FJXVyPu58NMM+iN/3VI7pZGfpffDzzm4JMXd2mi27k
        FjwHt8KbPVkD4tmsDfmnTwNPl9k8F9vu56dgJ40Qm1e9Bscbx3B6HV0gLAHOIdezPT3mutML5yg/
        g9WPwJ7TK2ymR/kFtrvXvPK1VzkA1/R/eoI+Wi0quy9zZCj6nO6NvDQz1ieFDGWe29N0PV/y/Ox8
        uLN1a/XLWEgG8HrBJrQOVCcq37PrzjfQZpsfPIKypSTB8ElkWQ+PdPVPAVnrH14tMaGuIoj9Uu44
        AsHW2OO7v7fDiQ/CBrganrAZmWbKTfPD/8XXm8HFdkQWkgjsK9n1NgQ0OjvieAAmsV3qKpmur/km
        QNEXdLzWjzOv/BkK3iBh3+Bahz3Vtwk3pneg+1LHjG3I/QZXfKYqIJtylBA8wweVBW+a7I1Of37r
        z59b/Tw2Vktmwt13Y5BN+jgAXiotDeFP9aIueYOQ0kb1kIfQjK3mWaXt6lfAX/4eZyI4w97d1IC9
        SeaNNgzCJdFeEcB1eqSHrceBeTslHUQH6YJ9jD8lP5b6BTb4RongEsYmOZZ8WLr5gLXLbesM+/dp
        AO+yyylOjOXnD7gg+cQ+1sSXFDahSyv5eOEy+pQHpi/eE5twkbIUHyefhkybtRwYmVR7dfEuVj5k
        cfB1GiHe+zBxSLvvIXjqbYhT2ZvKpbm9XlDIQxMfjFFkw4eYGuSGL8He69vo7OSAHIrgkmO8S4We
        xTsjAvWjv2OHDxK2fHO4gNd1edB96ctOw3uqiWoThtgU3DFcfvgN+Nmm13jYl/zv/E4MWFTTT2PZ
        /vDM+l7P1M0L2xml602B06nUPCmSDyUH9m8bQm5XYywPobPq6wR+wpp4Und/9uOcJQV8AWx681bR
        U0ERxAVaIx6xEp2PKV/Hji8/rXGPnVi+6+PP/9PLrsYGd8DleLHaBGrKwOhx0vcO97lVFxiMG7jq
        6bZvn+fHAi1Vbqk1t0Y6P9ttBlb9hV1yMME0EY387d+nwC7BUoBSgFX8MvDzd36XTTyB1Q/zxMJ9
        Ocudt7W//ECbqg6wPukkCCYaEfTMS8Awz1uwUY4x/cXvy4naBp2Qq1NH9vyS70ZzgOfJOGBjU7g6
        M/LbAlf/iUivWmHig68t+Jtf7Ff/cfrEggytWd3Tm7oryvm0eZ8hOi3UQ3z/7afEtwtZwjDH4fPV
        OzR9guqvH+QdrG05O4qfww/yXzhzjBaM2teWwepHr/7FRSdvKThD5EKTuqp8ZMu7si142n+vHinL
        aznH/eLD4g0DGq98tRmO2gCFKOmxrhy/PUlI4KFnVmRYp/sDEDZlZUHtHovYPC6mLpAGcvC+fbd4
        rQcwFI+HBXmjwxhPxhDOSuDffv459VTfSMWV48G1nXlD7ZXh3KlXGdy0Q+pNYqb1glk7FcCUGJ5s
        LG+wZIc8Avp+7KhTl6beNtslQdmDDat/dmXredhoLL4dNdf4Tcl38GD8yRysvRYdTMXR0yDYmnt8
        /CY8mDXcevDnz+/DPHTYrScm2HyWKzWsjcFYc3Ir+CZ1P9brfG1Q+aaAizPL1NPeLzCoJ9uHzuHy
        wL96YueLb6LnrVOxvqvPKV37AXRqU8Oqluf65H+/BQyTqsBJyD0cAkBcQOCWR4/3oayPq/8m//Zj
        FmTUp+6pbP7Oy1Rj8PtV/2h/9eyJO70Afaqt8cMjb7sw2s9HqyIwihJG1S4we9GBnw1c53lk7adO
        J9h5J4tBM+AstMV+uid7CXzdmdGs9vSUeOzmgpvqL9RqBq9cls+tAt3MBMJdSrUXSBBaYM0frHlL
        Fy6m10oQ5l1IT+NSOcyPgwnJR6fCViQfej7ovBz89m9GoV/yLOmTnZOrb++92J3DHrmRwVsCK4rN
        2Arn/hRNcOF14ImWawK+9/sLyInb0lt7uziT758EAHcoogc3DvT1PBUJw7Aiggs2+uDJqffztz1x
        GrtyTMfHAFZ+6MknqAKueH2HnXIBAhFfRtovQbCzof8pP/gEQ6/vq51qovV9PEBHU+c+YNJ+/jEO
        7dkLBWYBF/78pMPluQHzU4YamA7dl6r5jnPmNPYquNMvBj2pn6/eCfk4QV1pJbwnvMCI4pgQqllR
        YaW9umx2BtVHfgP3NNOdOl2ywy2Cfve5//Uzm7w6QWCIsUb3r8vQM+eCCfjp5eOdj/UlGMYIdrd8
        9l5Xcejb1f+DTEpfnuiBzmGHS5rAqDBrqt0kyWEASfmPf2HDF5twMkergs3m4az6fdMvGYUNaBQn
        9pbrF/TzqUssCLciI5Mc+em8HS0TIXKsqVMjue/U3t/AgLAPtsqroQtkwBH8OtYbr/OqkFW7owEn
        dXLx4fzVw98896dnVpfF7WcvON7gKHc2vun3Y9j/6uc3f9W3xs4hixBasNmfHHzd5h9Wju10gdun
        cl3nH4uzhNLrjOxBUIksSVrKEToLQFm4BdvrfK4/idUZpCHV6enc2L0Y576N0HhD3i+fF1HTEuTu
        uByn9jVJeZlvFFjW9hPr6+83+/d+kMdvnxGaXmk489MVwn9+twL+619//vyP3w2Dunnkn/ViwJjP
        43/8n6sC/yH+x1Ann8/fawhkSF75P//+3zcQ/mn7pm7H/zk2Vf4d/vn3Hx7+vWvwz9iMyef/ff6v
        9a/+61//CwAA//8DAMTTnszgIAAA
    headers:
      CF-RAY:
      - 9587aa5d7edaf233-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 01 Jul 2025 17:36:26 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-allow-origin:
      - '*'
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-model:
      - text-embedding-3-small
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '117'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      via:
      - envoy-router-66555f96b5-xgc6c
      x-envoy-upstream-service-time:
      - '140'
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '10000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '9999973'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_ad6d2f4f1012e86cb35584e1b4aaf1f2
    status:
      code: 200
      message: OK
- request:
    body: '{"input": ["Story Problem(Math Example): A scenario presented in story
      form to help children apply math concepts to real-life situations."], "model":
      "text-embedding-3-small", "encoding_format": "base64"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '204'
      content-type:
      - application/json
      cookie:
      - __cf_bm=7XHLdnaCxW3mhu.O7RaJ8t1a4uAHI6mlHu3VfEYs0Ew-1751391361-*******-mpMlI8wLN_fK7ILCCmaOem0t8eR6cQqjkZvXcm.vhuuoAoCMNuwhHaX2830nq83AFFZRB3Y0tFuUFY9OsHyafJRpkk437K7NztFUVBWvNUs;
        _cfuvid=vLbBcLMQoKUtOCugPnUg_H9aADRheAVHbrMDJqmikBA-1751391361577-*******-*********
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.78.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.78.0
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.12
    method: POST
    uri: https://api.openai.com/v1/embeddings
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1SaWQ+6TNrmz/tTPHlOnY5sUlV9xiaLLIWAipPJBBARFJGlCqg373ef6L/TM3Ni
        IlaCUPfyu667/usff/31d5c3ZTH9/a+//n7V4/T3//heu2VT9ve//vqf//jrr7/++q/f5/+3smzz
        8nar39Vv+e/H+n0rl7//9Rf3nyv/d9G//vrbqZ0r9uER5Ww8hCu6hGGIM+O4ssV7bAO4TsqdrNtX
        Fq+Hqn1C/QYy7BiyC4byEhOk1e2G7o1d10wd147IqioRn4wN38zyOQjgVsBvqh9OybCODy+E1gvZ
        uPSHga2qEafIpLONo673h1mlsQT72zHH5rJRGE/rxgSXMApxQM8RYEnYZOi0HUYayG6m80RUJFQN
        o0UzuJNyutXsBDHiPql2XKHbW6tTQXV1DBpyPNAXqVMOyOSDDCetUubzeNi1ULXcHCu9EjPhdIQa
        1NO7Q8Cwqxgt1bpHZ3mU6HG5evE4110BxcXig8Xb6mDeL1cCgi2n4GSIg3xdmjCEUarucJzdXMCU
        zDLghi93QWccp4G5j32LziffwEctRmwVO21Gd/AocXEVrrHY7B0JiooSU/dwyV2egckDmeQk1BGI
        BnihExWkHI8hTaBE2WJZHwFqkrFiu9eyfA6EqkWv5K5QP1+tQdydLgGS351Jk2A7DEMafTwonioL
        33elx/h2kRVwmVFOT5Kz6GR76Z5oul9ErLgabNhUhRpS1eyMcXC96HOMZ4hgea5xyXEnMLfluUPc
        *************************************jumbK1I84Szm2S0FG9LzILgksHWkGSq3cMSrK6r
        E6i60KDZEGoNvwSPHiGn29LinUB3noNDB512o+FkQhrgbudPCEO34uhlw+k5r15BB25Bp9HUOGfD
        ouxxv3tnkovL8KTEzPPeM4g3xkTv740U0/08S8j1AcUmqES2TF7DoTBvLzgs6kVfoIYzEKqxHMxJ
        4eZcs+8JgJtxhz1QX3Vut2E9Gp9qTmZpadksHD8ybEqdCx7nSwXWVS5akHmcSPHKtgM5PgsNQj27
        4eCBbvqIfXZArn0r6fkFaT6XF1hDGU3bQFLcN2BYfASIN+59wO3fZOirKT2DcJk4eq4+23id+DlE
        SggLsv0+z7o+mxo5i2TjkqMdo0f5JkGmOxArplQPLPA9Gz7UesZGeOkAYzAQ4OS0K1bdkQ3rp6IS
        7AR0pidfeeu8dcskGAJISIuVTzPXVzmBxeZ5p5hTNcYre6uD6XIw8P4VPN1RpbEMBz99UFwWai4Y
        N+uMVim+ktnAlk7eDvagtzNV6pJOy3nl+ipgmD8vhLfGjb7UlAvQsHEwvnyw5v7iE5XZWcCK4N1z
        Sh67Elb5NsWHF1qbeW7ACPbXWcSl+vTz+bpZE/TNB+rRY9VQjToR7CM64f35tugzxRZBi/pEODWO
        /iB293sITXwOsGWetjrbqLINH+ZWxXjeX3Ry3SgjInLl0OvDUQeCNJICcKuPNJg/us61NzeDs7Ad
        qKd4ritK3WOFNJMqWtqBysQ5QAY09/sEK7fdKV+j0MvgK1k4enscDMDxEAmwc7sd1uXVirkHvXmw
        pEIfbFxVjBfPdQw4m81C1hOxmGDMVwmeK+tNDS+/uYuyt3pUHQ2ByD49NQI4UwEq5+mBT27ngLUt
        jjMExN1hC38sJr6KIgU+gR4OZXvQWfJ+jxBtvTPVWJmD5dIhslOR3ga7th2GZZegJ3xR4xgwezPn
        LNqlAmw2thyIr8CJVzVdPMR/Rh8nyuE5fPNrhVz5KrFjero7v2/HFvqyb1KHvJ/54rAqQU8SKQTx
        6YPNxv6RItV7j/j3Pnm75BJkw6NPr/lixHwxOCNYrXrAaiPPzRQ0RADXsaio/zg8wbgdZw28Z8IR
        wbsE7sKO5wRG0avDpate8kVNq+/+oIEmNHm7zFwNG6JneMSXfG5jsl9AAqvH4YpTwZEYM/ZKC2vt
        ohJw0lVXtGy3hBJ0FHq2j3bOqotkQhWOa9DdfeYuxZhAKJyUHvvvnanzr5upIbiLOqr3/ayzuzpz
        yHm/eyIIDmvmwLsSaC/ZQG2xuun8EU8pcFPPp6mYhzrbfZAChPszxfnhAlz2zB0bFaVnU03BXi6e
        21lAzvvVU8VEtPmTT+PnPlL9ZlUxHQ/pCiKm1tSOTa75038ajSupMvZdTH/77xwXDRcHuHfndoUl
        pP1Gxyd4+LBF0WEKxf2SEhaTPp+Fd7aB96qHWAF3Gi/XrW4jP/lQMtxFHAux/CjRigwNnyA+M966
        RTL4xjN172sd07xXZfgp7DM9X2edrSugHOSdt4XNu10x1q55D7/9iixXs86Xt7WcwWOBfSDyud8s
        O+W+QvElAOx/jk2+6lnWwXXlbRwOl9RdTq2qoHe7r6lvPfthspgL4cup5WCgW3OYPyVTEOE/KJiJ
        6g29SlEF361VU+0dzfEy17sQ2bkYY/VqavmMpYMClSVh1DX9hy4sgVzA2zab8eEALHdOk1ME7/sh
        xgcvfubz+OBleA+jF1bPDxlMxXmQYQqVluplszSdkUclul2fNs6eDzXn21viQeT0W7zPyg+j/iOf
        weP4jAMeFarLXDepkM/NPOEa325WGpQGtHLzhX2Btu5yJ94T1qKUUkN9dIy9C2mFDMkxdfXGa4Rw
        t5HAeotn7KMmaTh5EEOo5NWB3pcC5J+gzp8wtwORQE1mzWrknIHoEIo4nTamu8rKZga78SkHMuTc
        nDdtIsF71UEa3sPdsNpOYqAy+/S//uWKd9IGMLKmPQGNZLtL4PMr+KxaTZi3dXReQosHuxxNGOuf
        AiyvW6DIixo5NPjmJw81K0VCv1C6P5kjWLoSdTCKkgtVHrzq8nAsQxh37Y4GD4Rclg0mhEZg6lif
        EmPgObk7wAt4ONRQ8LFhMQ5tdHEtTOjVWXLyuScdLEgFaSDejjFnWUcF+dzKU1XyZTD2TnqAEXqF
        1EvvvD4tklTBb30nUnE/NEI+Xp7I5B8BTe9+7IrfeEZHtWhwtH3JMRvdpwa3B3AI4GkZ43WWgA2j
        ydvhaJ7jfHYFw4MbY3Sp1xw0d5UHv4fXgi6/fqkLoLdbeHqWZyKdZsfl0IXn4Pn1kPB+tqV4nKVr
        BTenE/uTv4/7tV0Rorc39Zfi/K3/uJO9wEwD+P3/83aUtN1FeTzw3pbVWLzEJITNtDY0yA11YF6l
        1PDEsQOZvZPHeiqNG7h6G0jYuYjdSXwvG2DoS4sP7+QMxgO/0eB1gSd6i2fBpT/+fkDeIy9jc2oW
        MbIlWE9vOXgqeBlWhpX+Fx9EaPwJjMdmTdFxkW/UA7fR/YDTcYPEDKb0y1tgqdIHQbZb3amacz1Y
        37ZmQ4+wBptpOTa0zp4zbKa5oelLUmLhYSQjmpLACPrVKfVO3LwK+Dm2HdbdUQJEJXUEkuvpTXP3
        s8YsgX4Nb/TiY2+cVn3JlHMhi+BMicxyPp7ESJGRR5aGqvHkxbxC3BDcnuWAbVB0jF4/bITtvXKp
        oSRlQ4UoSdCJtzZkOe2dYUk/ugxdxgBVHV3SZyPPShjIY0PWeTBjWlz2FRxTwaBK8S5dQYyuI/ze
        H9vf51mun8hA1yOPaXw4cQN7zbInP5+6SaSN28fkhMoDDHBmBqF38gA311WJ0PO9p0ZRvmPyzUeY
        XWYU8L5iuetm0kvIuikmaN4f2ZxutynImVfhcxPJjGwuWQr3HyGj3v7ZuXPvpDY0UtgGdW4+Gdt9
        eAV6n0uEjYt5cIXohUqY1ZOO9brbNEvykgWoqtOB+m2tMv76yQxwuIwzzr/9YLprdSGfPvI7AGU1
        6HOIzRT4V+ZRbL3sgSpklCCI48uPZxtmrdGXJ0GMjbTOGyZExRl250dBoxtv5iL3CgmKWeDSg2xq
        gzA3mokya8podKXVMLf264nCo/bBWAFcPLe5dAAReodkd7U7MLYrV8o+2XgBjx6BzvZmrYHeKlhA
        P8du6OPnnKIWFx21xTx0V/7In3/xRX3TN3PhU6oZXN75iyo3roupbV97ULkvleoXJsZsEhwDXmGt
        BxtNeA5UN/1e1uPLSHbmcXaXOcgLOMMrxMo93DUj9bkSOkUyE1ksgn/Xs/no6FQfoBWLoD9mcEO3
        Btn4gwtmX3z1UO+qEdvjW2VM6h4zChVkUgf3ezbnZ08GO3OO6C0qN4zpuTACcjAxtadNq8+zdKzQ
        WpcLtbKS6mt7mzRIq/SEnXXq9aU2MhO2cnnEwf1g5aJCRhnIBVGo7770fDZMrodHU+LwDVpNvF7j
        nQByS9Lx/XWC/76//VASfIsHR//qfQ+sF/5OAy+xhjV87rndoe4FrJkoYVPesxqKM1OpsnF0wG/G
        tUe2IECK8Zzni+sFNriDyKdm9VxjysInQYtnD/j8/b4oaRbAGp0/pP3Wl/dtFJ6//SEVUb1GtBlb
        Ufc4EOrsHtKwHHdcBEXBj3B88/yYb/agBttTXQSwOWg6EdrP+VfPqQJFrxFjHB7gd/+oRTd6vpba
        ZMJMix2yjUfLXT33KsFvYcFO3W8B+9gPGz0bEn3rw6b51S8UVFZEDWykA/Mf8SoXOzvA6TgStrAX
        Z8OTcQr/6OPVcvYS6o75lh7uzlNfDauMYNztJmoXT7FZ0yhNoREYOsXBVXSn6HnM0CF6xdjNsn3O
        icfZRNISlsHmuh7ypdACE25D06X+LdjprOeuJbgrvkZTbuWaxeamDG7neh+ATgfuamSVIEHOdQnf
        CC1oxU+pwDyNMnoQLw99dqvYRsvICzh6Pi3w5X8OWhU/UHvfHRnfO9UGff0c6l+dUV+yTxXC2f0E
        eI/aXP/q8wj003lDKn9c9Jm9mgTtA2GhwU+vHQMnkX/6PVSr19Ad3Pfhx+v44Pd2M8tbVYBb8JGo
        HpyneHEPqwxioh6oI0sF48aq7uG9tXx6WLZPfZHjuYYBnW+4tGMC1jRueujah56qRyNkc5rcItgX
        mzd1y5fkUoU8ZdQd9y72NL4dlsrQJWTyTYA9rK7uJ3kHBhSrtMdYv2tgLnpJlmWL56jlK2O+sjCX
        4blPDXxaTtqwKmmj7Np77eLg628t79X2YDp0GMd3vgPj9aOGULi3KeHXhbH1ke1X0M7+hZ6T1GT0
        x1NfvwZfr7YNxtaeniCPbUQPzlNjYtGHBvrVh0WyfbaO9aWCx9oh2JfMQzOdwGr+4c2D4u0ZF7dv
        AzKp0OjJPO3YVC+4hB/4DGkpu7LOXEHogfON9uAdTu5a6U4BuE8w04NxvjOmEmbCC2gcar4gzpfW
        Gmzo2RsQANy7rnhwmPFHr2HUXJnw82+2w8UgG47j2aJlqgCtMNADMUo/30IuyZDmOMEKyLRcPDbX
        J7grWKMu7gd3MW8ekWNy2uCvfh/o4f60UXlfrGBWgiAW0ihM0TI+NJqvE42nSVgJfM+pQm9fv4sP
        hF0Kf37h/i4DfdRzI4LtS1FwQfOsmYLdXYbRSgj2irxi7FNmAbROXYJNpxsH4nuTDZO4/beeoEhJ
        CfrpTV1/mC6nTHIBxbnwaem8TJeLgCEjV1d9qrHkweYfjxyi8I49aXdrPmZ5gT8+JG/pgN35lX82
        gKd2SY7xNOZfP2KEksI9cfnl2+n4OpdowqqOE0m8uNzPT0o+xpnmJ4vP1/xMaojDIP36FXd3tsum
        Qg8TqRj3ncdIGoUZVAb5QfWeIp1mQwDhPMwJ/emNQYiKBPIOANQ+0q/+2aH2T75paX3O5yaza4ie
        YIddX8UDs9eqRj+/QbnsWzB+ePEMI4+rA/DCYzMaxUTgtx+TdBwDtvhekYBCf4pE+KBOf++2rQkP
        0TsmO0dZGnrpPBPsM2ugP39lLC9chX76KVDvVTwizVih1caQ8MbOHoTJ/WQ//Y0tt8P6SOsshG8Q
        1RjjgcS0L7sMDJF0puWXb7nJyxU58wQxqBrvFRM1Mwo4kh0KmHZQAZcPPATSEpV//FtyVzsNlH12
        xYEdqIBHp+7wJz/XjeO4bD9LEqQHAWFvXrdubxVhAfydHOHDvhP1NUVrDc0sSgLptqhM/Pqr4Jmu
        XiB8eas/t50GrbnNCL+c6oYmbVKi3cUysdo+X/rqsOWAvv0LOw/nMax+XQY/Pgx4il75LKIsgLsi
        aPBhONFmsUt4ls9b7kRv834B45d34Jd3/uhXdm4lQX4lN4XirOKH+c20HhLMFmwPybHh28KGcAe7
        G3nz56pZoqBKYP+Wpp8/5y7J8ab99CO1bn44rIapa7A9KBfs3lctZsJRNaDUnlOK+dTSxcn3SyCZ
        Ef+Nb6qv0tYy/+1fy7QCf/y4b32i+68/QOXeDeCXx7DjPhV9TeOhhz/9bT6AMIxI82ZAw2TF4WHT
        xasIu/bXvwP5Je2H+atnwf21Xuk3X5vhEpMIDrdZCjbEyAciwCiDX38Jm0MyxsvoHlLIbMWm8YZV
        +rJJEw4CvyBUd3SUT5F8KOFDFZ/TVp6f+mJbkgJ+fu7uqB5iDpwcG65o6rFBr4m+nKEdSnCrTnQv
        OUd3rg01hBp66HRv1U93fhZUg/u7b1MHE1mnhVoUUG/M/BsvF32B55f384/IjoxL3ruOVEA3DXyq
        CFaUT1OdSPC0PUvY2hVmvHKyBIEjnq/UNs5yM3Kgb8H1fdHJwF90Nt/G7Ay//jbN2zYEPNhWHPzq
        KXz+zk8YO24h+OpvsntLNeuaxeNAFp1HbHz95OUXD1++p3s+t+MplsMOPuOeUrPxJzaLR8lEu+P2
        E/zid84HtAGNn3l0HxVSPD8yMYPH92tL7bvd6uv4MCIYWemRno57zp0353mGu8eY/vgUEBEBAV4G
        e6XKPKjut55nqLDHBIc0nRk1b+S8W6XjFZegLuM1H7wEjuTl4DDM12a6JHcZZFO4YO+dQH313KOM
        9sknw995j07SuOkQ3L0Kam04PZ55iDiwpw9Is1bbDky7ph4cNi7G+zf0GnZB6ABXlApY617KIKSd
        WgKtUzFVJXti3acyQxkGb4a9aevkX/2goDZLCTa4ZYkX+/YxoIArnoTeaQRTppQF+OphAsOkjkl+
        ykOQWTQL4Ni99DpwKxO87hohY+6jYdEX04aSIjypPSTLwFx+yCBoFPPL23pMlenaw4RUb2x//cap
        OKX9Hx5Rns+6WZustiGfBFfs5stn+M3HoIYanTqHywl89amEXp8NR93d7TN0ITYz6RefhtqeQC3G
        4wbS3E/ofSXrwJ771ICDBcOvnz7lhDyqHiab54Fe/X3ZjOJxNn7zNPzzn9nAP0dgvbY23n95hsSA
        1EBpizDYemjPWKEWJZiS2wkHyV2Ox9NrDuDTUW8BK8vn0H0OJw9kUTLSg993A3kYBdllkpvQn983
        o4lIsPeAQe1uENxFoecaft7XkGrhpRlY0ibFj5/w0X2bDT94uARfnqPhidTNx2aRDRvEmV/9WDdM
        ma4d2NAupKfb8d58jviVomGnr4G4ZAkjWaIoaFlGHe/vPNFH9VpzCESwwfbPn5F63MOxjE7Yb9+E
        MTHqK/AQJ0pQPAs6OcOrAXeXXA6uD+fRrIFbGYi8lBM2Ly7MZ3EzlVAZpAc9n6jrioWalEB7kxyb
        L7HOZ8exexDQ9fbVSxyY6a5s5WVVRKrqjwp0uw3owVFMCP3u/8CkaCZwfngtvcw2Ghh7aTWU6+qD
        43PB9O7L33BgrwfZKTum06JPTWQ8cYX/U78LOLvnjO7L5gxoEFxSOUr1HXUMeQBEz6IOsUmYsR/P
        Z5djsmqjUyBvyPZztBsG+mMKK+HZY48Y5/in9wCX6DP5zjcBiY/3Dl6PIv7xISD5ua3g+yB01BQM
        c1g1MhdwvTwDWshuwpZiLCCMPKHG++gy5GsSZk/5528aV25l83HXFHISgzORek3OV6vcbKB9PL6C
        6u56zaKb2xDyHHhi+yYEQPQ8Ov/8DLJ+3+ciQKeFptwP1BAzDyx15tpA2EL6jSd9+M5bA3jbpjP+
        8QyXJ0v347vgOuo8Y5Mre6C+1Co1i/s+FpR00KBvnMFvvt189fwG0h7quNh400AmXorg/ZRq9Fq/
        VrDuPqP3m1cGsMFYX6MXKuB8jirsbayqmUchJqiVlz1Wd8+tu7quS8BgHw18h/YjrrGYZmBPG0gD
        YoBhRlMrw68eJIuwfGLamrsaffk5ECSxyHklzTzw7a8E3taX3jZz7sFuIAV12nb46sswRX//TgX8
        9z/++ut//U4YtN2tfH0PBkzlMv3zP0cF/in+c2yz1+vPMQQyZlX597/+fQLh78/QtZ/pf0/ds3yP
        f//rL0H4c9bg76mbstf/e/0f31v99z/+DwAAAP//AwAZphHm4CAAAA==
    headers:
      CF-RAY:
      - 9587aa652c96f233-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 01 Jul 2025 17:36:26 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-allow-origin:
      - '*'
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-model:
      - text-embedding-3-small
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '42'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      via:
      - envoy-router-7d69876c55-zrvq5
      x-envoy-upstream-service-time:
      - '48'
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '10000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '9999968'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_ca163a35865391f99979a55e50defc49
    status:
      code: 200
      message: OK
version: 1
