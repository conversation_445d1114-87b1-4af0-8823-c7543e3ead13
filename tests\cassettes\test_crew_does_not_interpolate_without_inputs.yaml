interactions:
- request:
    body: '{"messages": [{"role": "system", "content": "You are {topic} Researcher.
      You have a lot of experience with {topic}.\nYour personal goal is: Express hot
      takes on {topic}.\nTo give my best complete final answer to the task use the
      exact following format:\n\nThought: I now can give a great answer\nFinal Answer:
      Your final answer must be the great and the most complete as possible, it must
      be outcome described.\n\nI MUST use these formats, my job depends on it!"},
      {"role": "user", "content": "\nCurrent Task: Give me an analysis around {topic}.\n\nThis
      is the expect criteria for your final answer: {points} bullet points about {topic}.\nyou
      MUST return the actual complete content as the final answer, not a summary.\n\nBegin!
      This is VERY important to you, use the tools available and give your best Final
      Answer, your job depends on it!\n\nThought:"}], "model": "gpt-4o"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '875'
      content-type:
      - application/json
      cookie:
      - __cf_bm=9.8sBYBkvBR8R1K_bVF7xgU..80XKlEIg3N2OBbTSCU-1727214102-*******-.qiTLXbPamYUMSuyNsOEB9jhGu.jOifujOrx9E2JZvStbIZ9RTIiE44xKKNfLPxQkOi6qAT3h6htK8lPDGV_5g;
        _cfuvid=lbRdAddVWV6W3f5Dm9SaOPWDUOxqtZBSPr_fTW26nEA-1727213194587-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.47.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.47.0
      x-stainless-raw-response:
      - 'true'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.7
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-AB7cs0RAdNIbNquwDxeaBDUDQ9fEG\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1727214286,\n  \"model\": \"gpt-4o-2024-05-13\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I now can give a great answer\\nFinal
      Answer: \\n\\n1. **Comprehensive Understanding** - A researcher's profound comprehension
      of a subject ensures their analysis is well-grounded in existing literature
      and empirical evidence. This balanced perspective is critical for nuanced insights
      and supports the development of innovative theories or applications within the
      topic.\\n\\n2. **Analytical Depth** - By meticulously examining various aspects
      of the topic, researchers can identify underlying patterns and trends. This
      depth of analysis helps in unearthing subtle, yet significant factors that influence
      the substantive area, leading to more robust and defensible conclusions.\\n\\n3.
      **Methodological Rigor** - Researchers employ stringent methodological frameworks
      to ensure the accuracy and reliability of their analyses. This rigorous approach
      minimizes biases, enhances the reproducibility of results, and reinforces the
      validity of the research findings.\\n\\n4. **Interdisciplinary Insight** - Integrating
      perspectives from multiple disciplines allows for a richer, more comprehensive
      understanding of complex topics. This interdisciplinary approach can reveal
      novel insights and foster innovative solutions that might not be apparent through
      a single-discipline lens.\\n\\n5. **Practical Relevance** - Effective analysis
      connects theoretical concepts with practical applications. By demonstrating
      how research findings can be applied in real-world scenarios, researchers add
      value to their work and contribute to the advancement of both academic knowledge
      and industry practices. \\n\\nBy adhering to these criteria, a researcher can
      provide thorough and impactful analysis on any given topic.\",\n        \"refusal\":
      null\n      },\n      \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n
      \   }\n  ],\n  \"usage\": {\n    \"prompt_tokens\": 178,\n    \"completion_tokens\":
      285,\n    \"total_tokens\": 463,\n    \"completion_tokens_details\": {\n      \"reasoning_tokens\":
      0\n    }\n  },\n  \"system_fingerprint\": \"fp_e375328146\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c85f52bbcb81cf3-GRU
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 24 Sep 2024 21:44:51 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '4023'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999791'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_6d1029f581add812ebd13dbd6eef3959
    http_version: HTTP/1.1
    status_code: 200
version: 1
