interactions:
- request:
    body: '{"model": "llama3.2:3b", "prompt": "### System:\nYou are test role. test
      backstory\nYour personal goal is: test goal\nTo give my best complete final
      answer to the task respond using the exact following format:\n\nThought: I now
      can give a great answer\nFinal Answer: Your final answer must be the great and
      the most complete as possible, it must be outcome described.\n\nI MUST use these
      formats, my job depends on it!\n\n### User:\n\nCurrent Task: Explain what AI
      is in one sentence\n\nThis is the expect criteria for your final answer: A one-sentence
      explanation of AI\nyou MUST return the actual complete content as the final
      answer, not a summary.\n\nBegin! This is VERY important to you, use the tools
      available and give your best Final Answer, your job depends on it!\n\nThought:\n\n",
      "options": {"stop": ["\nObservation:"]}, "stream": false}'
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '849'
      host:
      - localhost:11434
      user-agent:
      - litellm/1.57.4
    method: POST
    uri: http://localhost:11434/api/generate
  response:
    content: '{"model":"llama3.2:3b","created_at":"2025-01-10T18:39:31.893206Z","response":"Final
      Answer: Artificial Intelligence (AI) refers to the development of computer systems
      that can perform tasks that typically require human intelligence, including
      learning, problem-solving, decision-making, and perception.","done":true,"done_reason":"stop","context":[128006,9125,128007,271,38766,1303,33025,2696,25,6790,220,2366,18,271,128009,128006,882,128007,271,14711,744,512,2675,527,1296,3560,13,1296,93371,198,7927,4443,5915,374,25,1296,5915,198,1271,3041,856,1888,4686,1620,4320,311,279,3465,6013,1701,279,4839,2768,3645,1473,85269,25,358,1457,649,3041,264,2294,4320,198,19918,22559,25,4718,1620,4320,2011,387,279,2294,323,279,1455,4686,439,3284,11,433,2011,387,15632,7633,382,40,28832,1005,1521,20447,11,856,2683,14117,389,433,2268,14711,2724,1473,5520,5546,25,83017,1148,15592,374,304,832,11914,271,2028,374,279,1755,13186,369,701,1620,4320,25,362,832,1355,18886,16540,315,15592,198,9514,28832,471,279,5150,4686,2262,439,279,1620,4320,11,539,264,12399,382,11382,0,1115,374,48174,3062,311,499,11,1005,279,7526,2561,323,3041,701,1888,13321,22559,11,701,2683,14117,389,433,2268,85269,1473,128009,128006,78191,128007,271,19918,22559,25,59294,22107,320,15836,8,19813,311,279,4500,315,6500,6067,430,649,2804,9256,430,11383,1397,3823,11478,11,2737,6975,11,3575,99246,11,5597,28846,11,323,21063,13],"total_duration":2216514375,"load_duration":38144042,"prompt_eval_count":182,"prompt_eval_duration":1415000000,"eval_count":38,"eval_duration":759000000}'
    headers:
      Content-Length:
      - '1534'
      Content-Type:
      - application/json; charset=utf-8
      Date:
      - Fri, 10 Jan 2025 18:39:31 GMT
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"name": "llama3.2:3b"}'
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '23'
      content-type:
      - application/json
      host:
      - localhost:11434
      user-agent:
      - litellm/1.57.4
    method: POST
    uri: http://localhost:11434/api/show
  response:
    content: "{\"license\":\"LLAMA 3.2 COMMUNITY LICENSE AGREEMENT\\nLlama 3.2 Version
      Release Date: September 25, 2024\\n\\n\u201CAgreement\u201D means the terms
      and conditions for use, reproduction, distribution \\nand modification of the
      Llama Materials set forth herein.\\n\\n\u201CDocumentation\u201D means the specifications,
      manuals and documentation accompanying Llama 3.2\\ndistributed by Meta at https://llama.meta.com/doc/overview.\\n\\n\u201CLicensee\u201D
      or \u201Cyou\u201D means you, or your employer or any other person or entity
      (if you are \\nentering into this Agreement on such person or entity\u2019s
      behalf), of the age required under\\napplicable laws, rules or regulations to
      provide legal consent and that has legal authority\\nto bind your employer or
      such other person or entity if you are entering in this Agreement\\non their
      behalf.\\n\\n\u201CLlama 3.2\u201D means the foundational large language models
      and software and algorithms, including\\nmachine-learning model code, trained
      model weights, inference-enabling code, training-enabling code,\\nfine-tuning
      enabling code and other elements of the foregoing distributed by Meta at \\nhttps://www.llama.com/llama-downloads.\\n\\n\u201CLlama
      Materials\u201D means, collectively, Meta\u2019s proprietary Llama 3.2 and Documentation
      (and \\nany portion thereof) made available under this Agreement.\\n\\n\u201CMeta\u201D
      or \u201Cwe\u201D means Meta Platforms Ireland Limited (if you are located in
      or, \\nif you are an entity, your principal place of business is in the EEA
      or Switzerland) \\nand Meta Platforms, Inc. (if you are located outside of the
      EEA or Switzerland). \\n\\n\\nBy clicking \u201CI Accept\u201D below or by using
      or distributing any portion or element of the Llama Materials,\\nyou agree to
      be bound by this Agreement.\\n\\n\\n1. License Rights and Redistribution.\\n\\n
      \   a. Grant of Rights. You are granted a non-exclusive, worldwide, \\nnon-transferable
      and royalty-free limited license under Meta\u2019s intellectual property or
      other rights \\nowned by Meta embodied in the Llama Materials to use, reproduce,
      distribute, copy, create derivative works \\nof, and make modifications to the
      Llama Materials.  \\n\\n    b. Redistribution and Use.  \\n\\n        i. If
      you distribute or make available the Llama Materials (or any derivative works
      thereof), \\nor a product or service (including another AI model) that contains
      any of them, you shall (A) provide\\na copy of this Agreement with any such
      Llama Materials; and (B) prominently display \u201CBuilt with Llama\u201D\\non
      a related website, user interface, blogpost, about page, or product documentation.
      If you use the\\nLlama Materials or any outputs or results of the Llama Materials
      to create, train, fine tune, or\\notherwise improve an AI model, which is distributed
      or made available, you shall also include \u201CLlama\u201D\\nat the beginning
      of any such AI model name.\\n\\n        ii. If you receive Llama Materials,
      or any derivative works thereof, from a Licensee as part\\nof an integrated
      end user product, then Section 2 of this Agreement will not apply to you. \\n\\n
      \       iii. You must retain in all copies of the Llama Materials that you distribute
      the \\nfollowing attribution notice within a \u201CNotice\u201D text file distributed
      as a part of such copies: \\n\u201CLlama 3.2 is licensed under the Llama 3.2
      Community License, Copyright \xA9 Meta Platforms,\\nInc. All Rights Reserved.\u201D\\n\\n
      \       iv. Your use of the Llama Materials must comply with applicable laws
      and regulations\\n(including trade compliance laws and regulations) and adhere
      to the Acceptable Use Policy for\\nthe Llama Materials (available at https://www.llama.com/llama3_2/use-policy),
      which is hereby \\nincorporated by reference into this Agreement.\\n  \\n2.
      Additional Commercial Terms. If, on the Llama 3.2 version release date, the
      monthly active users\\nof the products or services made available by or for
      Licensee, or Licensee\u2019s affiliates, \\nis greater than 700 million monthly
      active users in the preceding calendar month, you must request \\na license
      from Meta, which Meta may grant to you in its sole discretion, and you are not
      authorized to\\nexercise any of the rights under this Agreement unless or until
      Meta otherwise expressly grants you such rights.\\n\\n3. Disclaimer of Warranty.
      UNLESS REQUIRED BY APPLICABLE LAW, THE LLAMA MATERIALS AND ANY OUTPUT AND \\nRESULTS
      THEREFROM ARE PROVIDED ON AN \u201CAS IS\u201D BASIS, WITHOUT WARRANTIES OF
      ANY KIND, AND META DISCLAIMS\\nALL WARRANTIES OF ANY KIND, BOTH EXPRESS AND
      IMPLIED, INCLUDING, WITHOUT LIMITATION, ANY WARRANTIES\\nOF TITLE, NON-INFRINGEMENT,
      MERCHANTABILITY, OR FITNESS FOR A PARTICULAR PURPOSE. YOU ARE SOLELY RESPONSIBLE\\nFOR
      DETERMINING THE APPROPRIATENESS OF USING OR REDISTRIBUTING THE LLAMA MATERIALS
      AND ASSUME ANY RISKS ASSOCIATED\\nWITH YOUR USE OF THE LLAMA MATERIALS AND ANY
      OUTPUT AND RESULTS.\\n\\n4. Limitation of Liability. IN NO EVENT WILL META OR
      ITS AFFILIATES BE LIABLE UNDER ANY THEORY OF LIABILITY, \\nWHETHER IN CONTRACT,
      TORT, NEGLIGENCE, PRODUCTS LIABILITY, OR OTHERWISE, ARISING OUT OF THIS AGREEMENT,
      \\nFOR ANY LOST PROFITS OR ANY INDIRECT, SPECIAL, CONSEQUENTIAL, INCIDENTAL,
      EXEMPLARY OR PUNITIVE DAMAGES, EVEN \\nIF META OR ITS AFFILIATES HAVE BEEN ADVISED
      OF THE POSSIBILITY OF ANY OF THE FOREGOING.\\n\\n5. Intellectual Property.\\n\\n
      \   a. No trademark licenses are granted under this Agreement, and in connection
      with the Llama Materials, \\nneither Meta nor Licensee may use any name or mark
      owned by or associated with the other or any of its affiliates, \\nexcept as
      required for reasonable and customary use in describing and redistributing the
      Llama Materials or as \\nset forth in this Section 5(a). Meta hereby grants
      you a license to use \u201CLlama\u201D (the \u201CMark\u201D) solely as required
      \\nto comply with the last sentence of Section 1.b.i. You will comply with Meta\u2019s
      brand guidelines (currently accessible \\nat https://about.meta.com/brand/resources/meta/company-brand/).
      All goodwill arising out of your use of the Mark \\nwill inure to the benefit
      of Meta.\\n\\n    b. Subject to Meta\u2019s ownership of Llama Materials and
      derivatives made by or for Meta, with respect to any\\n    derivative works
      and modifications of the Llama Materials that are made by you, as between you
      and Meta,\\n    you are and will be the owner of such derivative works and modifications.\\n\\n
      \   c. If you institute litigation or other proceedings against Meta or any
      entity (including a cross-claim or\\n    counterclaim in a lawsuit) alleging
      that the Llama Materials or Llama 3.2 outputs or results, or any portion\\n
      \   of any of the foregoing, constitutes infringement of intellectual property
      or other rights owned or licensable\\n    by you, then any licenses granted
      to you under this Agreement shall terminate as of the date such litigation or\\n
      \   claim is filed or instituted. You will indemnify and hold harmless Meta
      from and against any claim by any third\\n    party arising out of or related
      to your use or distribution of the Llama Materials.\\n\\n6. Term and Termination.
      The term of this Agreement will commence upon your acceptance of this Agreement
      or access\\nto the Llama Materials and will continue in full force and effect
      until terminated in accordance with the terms\\nand conditions herein. Meta
      may terminate this Agreement if you are in breach of any term or condition of
      this\\nAgreement. Upon termination of this Agreement, you shall delete and cease
      use of the Llama Materials. Sections 3,\\n4 and 7 shall survive the termination
      of this Agreement. \\n\\n7. Governing Law and Jurisdiction. This Agreement will
      be governed and construed under the laws of the State of \\nCalifornia without
      regard to choice of law principles, and the UN Convention on Contracts for the
      International\\nSale of Goods does not apply to this Agreement. The courts of
      California shall have exclusive jurisdiction of\\nany dispute arising out of
      this Agreement.\\n**Llama 3.2** **Acceptable Use Policy**\\n\\nMeta is committed
      to promoting safe and fair use of its tools and features, including Llama 3.2.
      If you access or use Llama 3.2, you agree to this Acceptable Use Policy (\u201C**Policy**\u201D).
      The most recent copy of this policy can be found at [https://www.llama.com/llama3_2/use-policy](https://www.llama.com/llama3_2/use-policy).\\n\\n**Prohibited
      Uses**\\n\\nWe want everyone to use Llama 3.2 safely and responsibly. You agree
      you will not use, or allow others to use, Llama 3.2 to:\\n\\n\\n\\n1. Violate
      the law or others\u2019 rights, including to:\\n    1. Engage in, promote, generate,
      contribute to, encourage, plan, incite, or further illegal or unlawful activity
      or content, such as:\\n        1. Violence or terrorism\\n        2. Exploitation
      or harm to children, including the solicitation, creation, acquisition, or dissemination
      of child exploitative content or failure to report Child Sexual Abuse Material\\n
      \       3. Human trafficking, exploitation, and sexual violence\\n        4.
      The illegal distribution of information or materials to minors, including obscene
      materials, or failure to employ legally required age-gating in connection with
      such information or materials.\\n        5. Sexual solicitation\\n        6.
      Any other criminal activity\\n    1. Engage in, promote, incite, or facilitate
      the harassment, abuse, threatening, or bullying of individuals or groups of
      individuals\\n    2. Engage in, promote, incite, or facilitate discrimination
      or other unlawful or harmful conduct in the provision of employment, employment
      benefits, credit, housing, other economic benefits, or other essential goods
      and services\\n    3. Engage in the unauthorized or unlicensed practice of any
      profession including, but not limited to, financial, legal, medical/health,
      or related professional practices\\n    4. Collect, process, disclose, generate,
      or infer private or sensitive information about individuals, including information
      about individuals\u2019 identity, health, or demographic information, unless
      you have obtained the right to do so in accordance with applicable law\\n    5.
      Engage in or facilitate any action or generate any content that infringes, misappropriates,
      or otherwise violates any third-party rights, including the outputs or results
      of any products or services using the Llama Materials\\n    6. Create, generate,
      or facilitate the creation of malicious code, malware, computer viruses or do
      anything else that could disable, overburden, interfere with or impair the proper
      working, integrity, operation or appearance of a website or computer system\\n
      \   7. Engage in any action, or facilitate any action, to intentionally circumvent
      or remove usage restrictions or other safety measures, or to enable functionality
      disabled by Meta\\n2. Engage in, promote, incite, facilitate, or assist in the
      planning or development of activities that present a risk of death or bodily
      harm to individuals, including use of Llama 3.2 related to the following:\\n
      \   8. Military, warfare, nuclear industries or applications, espionage, use
      for materials or activities that are subject to the International Traffic Arms
      Regulations (ITAR) maintained by the United States Department of State or to
      the U.S. Biological Weapons Anti-Terrorism Act of 1989 or the Chemical Weapons
      Convention Implementation Act of 1997\\n    9. Guns and illegal weapons (including
      weapon development)\\n    10. Illegal drugs and regulated/controlled substances\\n
      \   11. Operation of critical infrastructure, transportation technologies, or
      heavy machinery\\n    12. Self-harm or harm to others, including suicide, cutting,
      and eating disorders\\n    13. Any content intended to incite or promote violence,
      abuse, or any infliction of bodily harm to an individual\\n3. Intentionally
      deceive or mislead others, including use of Llama 3.2 related to the following:\\n
      \   14. Generating, promoting, or furthering fraud or the creation or promotion
      of disinformation\\n    15. Generating, promoting, or furthering defamatory
      content, including the creation of defamatory statements, images, or other content\\n
      \   16. Generating, promoting, or further distributing spam\\n    17. Impersonating
      another individual without consent, authorization, or legal right\\n    18.
      Representing that the use of Llama 3.2 or outputs are human-generated\\n    19.
      Generating or facilitating false online engagement, including fake reviews and
      other means of fake online engagement\\n4. Fail to appropriately disclose to
      end users any known dangers of your AI system\\n5. Interact with third party
      tools, models, or software designed to generate unlawful content or engage in
      unlawful or harmful conduct and/or represent that the outputs of such tools,
      models, or software are associated with Meta or Llama 3.2\\n\\nWith respect
      to any multimodal models included in Llama 3.2, the rights granted under Section
      1(a) of the Llama 3.2 Community License Agreement are not being granted to you
      if you are an individual domiciled in, or a company with a principal place of
      business in, the European Union. This restriction does not apply to end users
      of a product or service that incorporates any such multimodal models.\\n\\nPlease
      report any violation of this Policy, software \u201Cbug,\u201D or other problems
      that could lead to a violation of this Policy through one of the following means:\\n\\n\\n\\n*
      Reporting issues with the model: [https://github.com/meta-llama/llama-models/issues](https://l.workplace.com/l.php?u=https%3A%2F%2Fgithub.com%2Fmeta-llama%2Fllama-models%2Fissues\\u0026h=AT0qV8W9BFT6NwihiOHRuKYQM_UnkzN_NmHMy91OT55gkLpgi4kQupHUl0ssR4dQsIQ8n3tfd0vtkobvsEvt1l4Ic6GXI2EeuHV8N08OG2WnbAmm0FL4ObkazC6G_256vN0lN9DsykCvCqGZ)\\n*
      Reporting risky content generated by the model: [developers.facebook.com/llama_output_feedback](http://developers.facebook.com/llama_output_feedback)\\n*
      Reporting bugs and security concerns: [facebook.com/whitehat/info](http://facebook.com/whitehat/info)\\n*
      Reporting violations of the Acceptable Use Policy or unlicensed uses of Llama
      3.2: <EMAIL>\",\"modelfile\":\"# Modelfile generated by \\\"ollama
      show\\\"\\n# To build a new Modelfile based on this, replace FROM with:\\n#
      FROM llama3.2:3b\\n\\nFROM /Users/<USER>/.ollama/models/blobs/sha256-dde5aa3fc5ffc17176b5e8bdc82f587b24b2678c6c66101bf7da77af9f7ccdff\\nTEMPLATE
      \\\"\\\"\\\"\\u003c|start_header_id|\\u003esystem\\u003c|end_header_id|\\u003e\\n\\nCutting
      Knowledge Date: December 2023\\n\\n{{ if .System }}{{ .System }}\\n{{- end }}\\n{{-
      if .Tools }}When you receive a tool call response, use the output to format
      an answer to the orginal user question.\\n\\nYou are a helpful assistant with
      tool calling capabilities.\\n{{- end }}\\u003c|eot_id|\\u003e\\n{{- range $i,
      $_ := .Messages }}\\n{{- $last := eq (len (slice $.Messages $i)) 1 }}\\n{{-
      if eq .Role \\\"user\\\" }}\\u003c|start_header_id|\\u003euser\\u003c|end_header_id|\\u003e\\n{{-
      if and $.Tools $last }}\\n\\nGiven the following functions, please respond with
      a JSON for a function call with its proper arguments that best answers the given
      prompt.\\n\\nRespond in the format {\\\"name\\\": function name, \\\"parameters\\\":
      dictionary of argument name and its value}. Do not use variables.\\n\\n{{ range
      $.Tools }}\\n{{- . }}\\n{{ end }}\\n{{ .Content }}\\u003c|eot_id|\\u003e\\n{{-
      else }}\\n\\n{{ .Content }}\\u003c|eot_id|\\u003e\\n{{- end }}{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
      end }}\\n{{- else if eq .Role \\\"assistant\\\" }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n{{-
      if .ToolCalls }}\\n{{ range .ToolCalls }}\\n{\\\"name\\\": \\\"{{ .Function.Name
      }}\\\", \\\"parameters\\\": {{ .Function.Arguments }}}{{ end }}\\n{{- else }}\\n\\n{{
      .Content }}\\n{{- end }}{{ if not $last }}\\u003c|eot_id|\\u003e{{ end }}\\n{{-
      else if eq .Role \\\"tool\\\" }}\\u003c|start_header_id|\\u003eipython\\u003c|end_header_id|\\u003e\\n\\n{{
      .Content }}\\u003c|eot_id|\\u003e{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
      end }}\\n{{- end }}\\n{{- end }}\\\"\\\"\\\"\\nPARAMETER stop \\u003c|start_header_id|\\u003e\\nPARAMETER
      stop \\u003c|end_header_id|\\u003e\\nPARAMETER stop \\u003c|eot_id|\\u003e\\nLICENSE
      \\\"LLAMA 3.2 COMMUNITY LICENSE AGREEMENT\\nLlama 3.2 Version Release Date:
      September 25, 2024\\n\\n\u201CAgreement\u201D means the terms and conditions
      for use, reproduction, distribution \\nand modification of the Llama Materials
      set forth herein.\\n\\n\u201CDocumentation\u201D means the specifications, manuals
      and documentation accompanying Llama 3.2\\ndistributed by Meta at https://llama.meta.com/doc/overview.\\n\\n\u201CLicensee\u201D
      or \u201Cyou\u201D means you, or your employer or any other person or entity
      (if you are \\nentering into this Agreement on such person or entity\u2019s
      behalf), of the age required under\\napplicable laws, rules or regulations to
      provide legal consent and that has legal authority\\nto bind your employer or
      such other person or entity if you are entering in this Agreement\\non their
      behalf.\\n\\n\u201CLlama 3.2\u201D means the foundational large language models
      and software and algorithms, including\\nmachine-learning model code, trained
      model weights, inference-enabling code, training-enabling code,\\nfine-tuning
      enabling code and other elements of the foregoing distributed by Meta at \\nhttps://www.llama.com/llama-downloads.\\n\\n\u201CLlama
      Materials\u201D means, collectively, Meta\u2019s proprietary Llama 3.2 and Documentation
      (and \\nany portion thereof) made available under this Agreement.\\n\\n\u201CMeta\u201D
      or \u201Cwe\u201D means Meta Platforms Ireland Limited (if you are located in
      or, \\nif you are an entity, your principal place of business is in the EEA
      or Switzerland) \\nand Meta Platforms, Inc. (if you are located outside of the
      EEA or Switzerland). \\n\\n\\nBy clicking \u201CI Accept\u201D below or by using
      or distributing any portion or element of the Llama Materials,\\nyou agree to
      be bound by this Agreement.\\n\\n\\n1. License Rights and Redistribution.\\n\\n
      \   a. Grant of Rights. You are granted a non-exclusive, worldwide, \\nnon-transferable
      and royalty-free limited license under Meta\u2019s intellectual property or
      other rights \\nowned by Meta embodied in the Llama Materials to use, reproduce,
      distribute, copy, create derivative works \\nof, and make modifications to the
      Llama Materials.  \\n\\n    b. Redistribution and Use.  \\n\\n        i. If
      you distribute or make available the Llama Materials (or any derivative works
      thereof), \\nor a product or service (including another AI model) that contains
      any of them, you shall (A) provide\\na copy of this Agreement with any such
      Llama Materials; and (B) prominently display \u201CBuilt with Llama\u201D\\non
      a related website, user interface, blogpost, about page, or product documentation.
      If you use the\\nLlama Materials or any outputs or results of the Llama Materials
      to create, train, fine tune, or\\notherwise improve an AI model, which is distributed
      or made available, you shall also include \u201CLlama\u201D\\nat the beginning
      of any such AI model name.\\n\\n        ii. If you receive Llama Materials,
      or any derivative works thereof, from a Licensee as part\\nof an integrated
      end user product, then Section 2 of this Agreement will not apply to you. \\n\\n
      \       iii. You must retain in all copies of the Llama Materials that you distribute
      the \\nfollowing attribution notice within a \u201CNotice\u201D text file distributed
      as a part of such copies: \\n\u201CLlama 3.2 is licensed under the Llama 3.2
      Community License, Copyright \xA9 Meta Platforms,\\nInc. All Rights Reserved.\u201D\\n\\n
      \       iv. Your use of the Llama Materials must comply with applicable laws
      and regulations\\n(including trade compliance laws and regulations) and adhere
      to the Acceptable Use Policy for\\nthe Llama Materials (available at https://www.llama.com/llama3_2/use-policy),
      which is hereby \\nincorporated by reference into this Agreement.\\n  \\n2.
      Additional Commercial Terms. If, on the Llama 3.2 version release date, the
      monthly active users\\nof the products or services made available by or for
      Licensee, or Licensee\u2019s affiliates, \\nis greater than 700 million monthly
      active users in the preceding calendar month, you must request \\na license
      from Meta, which Meta may grant to you in its sole discretion, and you are not
      authorized to\\nexercise any of the rights under this Agreement unless or until
      Meta otherwise expressly grants you such rights.\\n\\n3. Disclaimer of Warranty.
      UNLESS REQUIRED BY APPLICABLE LAW, THE LLAMA MATERIALS AND ANY OUTPUT AND \\nRESULTS
      THEREFROM ARE PROVIDED ON AN \u201CAS IS\u201D BASIS, WITHOUT WARRANTIES OF
      ANY KIND, AND META DISCLAIMS\\nALL WARRANTIES OF ANY KIND, BOTH EXPRESS AND
      IMPLIED, INCLUDING, WITHOUT LIMITATION, ANY WARRANTIES\\nOF TITLE, NON-INFRINGEMENT,
      MERCHANTABILITY, OR FITNESS FOR A PARTICULAR PURPOSE. YOU ARE SOLELY RESPONSIBLE\\nFOR
      DETERMINING THE APPROPRIATENESS OF USING OR REDISTRIBUTING THE LLAMA MATERIALS
      AND ASSUME ANY RISKS ASSOCIATED\\nWITH YOUR USE OF THE LLAMA MATERIALS AND ANY
      OUTPUT AND RESULTS.\\n\\n4. Limitation of Liability. IN NO EVENT WILL META OR
      ITS AFFILIATES BE LIABLE UNDER ANY THEORY OF LIABILITY, \\nWHETHER IN CONTRACT,
      TORT, NEGLIGENCE, PRODUCTS LIABILITY, OR OTHERWISE, ARISING OUT OF THIS AGREEMENT,
      \\nFOR ANY LOST PROFITS OR ANY INDIRECT, SPECIAL, CONSEQUENTIAL, INCIDENTAL,
      EXEMPLARY OR PUNITIVE DAMAGES, EVEN \\nIF META OR ITS AFFILIATES HAVE BEEN ADVISED
      OF THE POSSIBILITY OF ANY OF THE FOREGOING.\\n\\n5. Intellectual Property.\\n\\n
      \   a. No trademark licenses are granted under this Agreement, and in connection
      with the Llama Materials, \\nneither Meta nor Licensee may use any name or mark
      owned by or associated with the other or any of its affiliates, \\nexcept as
      required for reasonable and customary use in describing and redistributing the
      Llama Materials or as \\nset forth in this Section 5(a). Meta hereby grants
      you a license to use \u201CLlama\u201D (the \u201CMark\u201D) solely as required
      \\nto comply with the last sentence of Section 1.b.i. You will comply with Meta\u2019s
      brand guidelines (currently accessible \\nat https://about.meta.com/brand/resources/meta/company-brand/).
      All goodwill arising out of your use of the Mark \\nwill inure to the benefit
      of Meta.\\n\\n    b. Subject to Meta\u2019s ownership of Llama Materials and
      derivatives made by or for Meta, with respect to any\\n    derivative works
      and modifications of the Llama Materials that are made by you, as between you
      and Meta,\\n    you are and will be the owner of such derivative works and modifications.\\n\\n
      \   c. If you institute litigation or other proceedings against Meta or any
      entity (including a cross-claim or\\n    counterclaim in a lawsuit) alleging
      that the Llama Materials or Llama 3.2 outputs or results, or any portion\\n
      \   of any of the foregoing, constitutes infringement of intellectual property
      or other rights owned or licensable\\n    by you, then any licenses granted
      to you under this Agreement shall terminate as of the date such litigation or\\n
      \   claim is filed or instituted. You will indemnify and hold harmless Meta
      from and against any claim by any third\\n    party arising out of or related
      to your use or distribution of the Llama Materials.\\n\\n6. Term and Termination.
      The term of this Agreement will commence upon your acceptance of this Agreement
      or access\\nto the Llama Materials and will continue in full force and effect
      until terminated in accordance with the terms\\nand conditions herein. Meta
      may terminate this Agreement if you are in breach of any term or condition of
      this\\nAgreement. Upon termination of this Agreement, you shall delete and cease
      use of the Llama Materials. Sections 3,\\n4 and 7 shall survive the termination
      of this Agreement. \\n\\n7. Governing Law and Jurisdiction. This Agreement will
      be governed and construed under the laws of the State of \\nCalifornia without
      regard to choice of law principles, and the UN Convention on Contracts for the
      International\\nSale of Goods does not apply to this Agreement. The courts of
      California shall have exclusive jurisdiction of\\nany dispute arising out of
      this Agreement.\\\"\\nLICENSE \\\"**Llama 3.2** **Acceptable Use Policy**\\n\\nMeta
      is committed to promoting safe and fair use of its tools and features, including
      Llama 3.2. If you access or use Llama 3.2, you agree to this Acceptable Use
      Policy (\u201C**Policy**\u201D). The most recent copy of this policy can be
      found at [https://www.llama.com/llama3_2/use-policy](https://www.llama.com/llama3_2/use-policy).\\n\\n**Prohibited
      Uses**\\n\\nWe want everyone to use Llama 3.2 safely and responsibly. You agree
      you will not use, or allow others to use, Llama 3.2 to:\\n\\n\\n\\n1. Violate
      the law or others\u2019 rights, including to:\\n    1. Engage in, promote, generate,
      contribute to, encourage, plan, incite, or further illegal or unlawful activity
      or content, such as:\\n        1. Violence or terrorism\\n        2. Exploitation
      or harm to children, including the solicitation, creation, acquisition, or dissemination
      of child exploitative content or failure to report Child Sexual Abuse Material\\n
      \       3. Human trafficking, exploitation, and sexual violence\\n        4.
      The illegal distribution of information or materials to minors, including obscene
      materials, or failure to employ legally required age-gating in connection with
      such information or materials.\\n        5. Sexual solicitation\\n        6.
      Any other criminal activity\\n    1. Engage in, promote, incite, or facilitate
      the harassment, abuse, threatening, or bullying of individuals or groups of
      individuals\\n    2. Engage in, promote, incite, or facilitate discrimination
      or other unlawful or harmful conduct in the provision of employment, employment
      benefits, credit, housing, other economic benefits, or other essential goods
      and services\\n    3. Engage in the unauthorized or unlicensed practice of any
      profession including, but not limited to, financial, legal, medical/health,
      or related professional practices\\n    4. Collect, process, disclose, generate,
      or infer private or sensitive information about individuals, including information
      about individuals\u2019 identity, health, or demographic information, unless
      you have obtained the right to do so in accordance with applicable law\\n    5.
      Engage in or facilitate any action or generate any content that infringes, misappropriates,
      or otherwise violates any third-party rights, including the outputs or results
      of any products or services using the Llama Materials\\n    6. Create, generate,
      or facilitate the creation of malicious code, malware, computer viruses or do
      anything else that could disable, overburden, interfere with or impair the proper
      working, integrity, operation or appearance of a website or computer system\\n
      \   7. Engage in any action, or facilitate any action, to intentionally circumvent
      or remove usage restrictions or other safety measures, or to enable functionality
      disabled by Meta\\n2. Engage in, promote, incite, facilitate, or assist in the
      planning or development of activities that present a risk of death or bodily
      harm to individuals, including use of Llama 3.2 related to the following:\\n
      \   8. Military, warfare, nuclear industries or applications, espionage, use
      for materials or activities that are subject to the International Traffic Arms
      Regulations (ITAR) maintained by the United States Department of State or to
      the U.S. Biological Weapons Anti-Terrorism Act of 1989 or the Chemical Weapons
      Convention Implementation Act of 1997\\n    9. Guns and illegal weapons (including
      weapon development)\\n    10. Illegal drugs and regulated/controlled substances\\n
      \   11. Operation of critical infrastructure, transportation technologies, or
      heavy machinery\\n    12. Self-harm or harm to others, including suicide, cutting,
      and eating disorders\\n    13. Any content intended to incite or promote violence,
      abuse, or any infliction of bodily harm to an individual\\n3. Intentionally
      deceive or mislead others, including use of Llama 3.2 related to the following:\\n
      \   14. Generating, promoting, or furthering fraud or the creation or promotion
      of disinformation\\n    15. Generating, promoting, or furthering defamatory
      content, including the creation of defamatory statements, images, or other content\\n
      \   16. Generating, promoting, or further distributing spam\\n    17. Impersonating
      another individual without consent, authorization, or legal right\\n    18.
      Representing that the use of Llama 3.2 or outputs are human-generated\\n    19.
      Generating or facilitating false online engagement, including fake reviews and
      other means of fake online engagement\\n4. Fail to appropriately disclose to
      end users any known dangers of your AI system\\n5. Interact with third party
      tools, models, or software designed to generate unlawful content or engage in
      unlawful or harmful conduct and/or represent that the outputs of such tools,
      models, or software are associated with Meta or Llama 3.2\\n\\nWith respect
      to any multimodal models included in Llama 3.2, the rights granted under Section
      1(a) of the Llama 3.2 Community License Agreement are not being granted to you
      if you are an individual domiciled in, or a company with a principal place of
      business in, the European Union. This restriction does not apply to end users
      of a product or service that incorporates any such multimodal models.\\n\\nPlease
      report any violation of this Policy, software \u201Cbug,\u201D or other problems
      that could lead to a violation of this Policy through one of the following means:\\n\\n\\n\\n*
      Reporting issues with the model: [https://github.com/meta-llama/llama-models/issues](https://l.workplace.com/l.php?u=https%3A%2F%2Fgithub.com%2Fmeta-llama%2Fllama-models%2Fissues\\u0026h=AT0qV8W9BFT6NwihiOHRuKYQM_UnkzN_NmHMy91OT55gkLpgi4kQupHUl0ssR4dQsIQ8n3tfd0vtkobvsEvt1l4Ic6GXI2EeuHV8N08OG2WnbAmm0FL4ObkazC6G_256vN0lN9DsykCvCqGZ)\\n*
      Reporting risky content generated by the model: [developers.facebook.com/llama_output_feedback](http://developers.facebook.com/llama_output_feedback)\\n*
      Reporting bugs and security concerns: [facebook.com/whitehat/info](http://facebook.com/whitehat/info)\\n*
      Reporting violations of the Acceptable Use Policy or unlicensed uses of Llama
      3.2: <EMAIL>\\\"\\n\",\"parameters\":\"stop                           \\\"\\u003c|start_header_id|\\u003e\\\"\\nstop
      \                          \\\"\\u003c|end_header_id|\\u003e\\\"\\nstop                           \\\"\\u003c|eot_id|\\u003e\\\"\",\"template\":\"\\u003c|start_header_id|\\u003esystem\\u003c|end_header_id|\\u003e\\n\\nCutting
      Knowledge Date: December 2023\\n\\n{{ if .System }}{{ .System }}\\n{{- end }}\\n{{-
      if .Tools }}When you receive a tool call response, use the output to format
      an answer to the orginal user question.\\n\\nYou are a helpful assistant with
      tool calling capabilities.\\n{{- end }}\\u003c|eot_id|\\u003e\\n{{- range $i,
      $_ := .Messages }}\\n{{- $last := eq (len (slice $.Messages $i)) 1 }}\\n{{-
      if eq .Role \\\"user\\\" }}\\u003c|start_header_id|\\u003euser\\u003c|end_header_id|\\u003e\\n{{-
      if and $.Tools $last }}\\n\\nGiven the following functions, please respond with
      a JSON for a function call with its proper arguments that best answers the given
      prompt.\\n\\nRespond in the format {\\\"name\\\": function name, \\\"parameters\\\":
      dictionary of argument name and its value}. Do not use variables.\\n\\n{{ range
      $.Tools }}\\n{{- . }}\\n{{ end }}\\n{{ .Content }}\\u003c|eot_id|\\u003e\\n{{-
      else }}\\n\\n{{ .Content }}\\u003c|eot_id|\\u003e\\n{{- end }}{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
      end }}\\n{{- else if eq .Role \\\"assistant\\\" }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n{{-
      if .ToolCalls }}\\n{{ range .ToolCalls }}\\n{\\\"name\\\": \\\"{{ .Function.Name
      }}\\\", \\\"parameters\\\": {{ .Function.Arguments }}}{{ end }}\\n{{- else }}\\n\\n{{
      .Content }}\\n{{- end }}{{ if not $last }}\\u003c|eot_id|\\u003e{{ end }}\\n{{-
      else if eq .Role \\\"tool\\\" }}\\u003c|start_header_id|\\u003eipython\\u003c|end_header_id|\\u003e\\n\\n{{
      .Content }}\\u003c|eot_id|\\u003e{{ if $last }}\\u003c|start_header_id|\\u003eassistant\\u003c|end_header_id|\\u003e\\n\\n{{
      end }}\\n{{- end }}\\n{{- end }}\",\"details\":{\"parent_model\":\"\",\"format\":\"gguf\",\"family\":\"llama\",\"families\":[\"llama\"],\"parameter_size\":\"3.2B\",\"quantization_level\":\"Q4_K_M\"},\"model_info\":{\"general.architecture\":\"llama\",\"general.basename\":\"Llama-3.2\",\"general.file_type\":15,\"general.finetune\":\"Instruct\",\"general.languages\":[\"en\",\"de\",\"fr\",\"it\",\"pt\",\"hi\",\"es\",\"th\"],\"general.parameter_count\":3212749888,\"general.quantization_version\":2,\"general.size_label\":\"3B\",\"general.tags\":[\"facebook\",\"meta\",\"pytorch\",\"llama\",\"llama-3\",\"text-generation\"],\"general.type\":\"model\",\"llama.attention.head_count\":24,\"llama.attention.head_count_kv\":8,\"llama.attention.key_length\":128,\"llama.attention.layer_norm_rms_epsilon\":0.00001,\"llama.attention.value_length\":128,\"llama.block_count\":28,\"llama.context_length\":131072,\"llama.embedding_length\":3072,\"llama.feed_forward_length\":8192,\"llama.rope.dimension_count\":128,\"llama.rope.freq_base\":500000,\"llama.vocab_size\":128256,\"tokenizer.ggml.bos_token_id\":128000,\"tokenizer.ggml.eos_token_id\":128009,\"tokenizer.ggml.merges\":null,\"tokenizer.ggml.model\":\"gpt2\",\"tokenizer.ggml.pre\":\"llama-bpe\",\"tokenizer.ggml.token_type\":null,\"tokenizer.ggml.tokens\":null},\"modified_at\":\"2024-12-31T11:53:14.529771974-05:00\"}"
    headers:
      Content-Type:
      - application/json; charset=utf-8
      Date:
      - Fri, 10 Jan 2025 18:39:31 GMT
      Transfer-Encoding:
      - chunked
    http_version: HTTP/1.1
    status_code: 200
version: 1
