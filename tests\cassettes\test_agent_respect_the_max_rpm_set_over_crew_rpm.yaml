interactions:
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: get_final_answer\nTool
      Arguments: {}\nTool Description: Get the final answer but don''t give it yet,
      just re-use this\n        tool non-stop.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [get_final_answer], just the name, exactly
      as it''s written.\nAction Input: the input to the action, just a simple JSON
      object, enclosed in curly braces, using \" to wrap keys and values.\nObservation:
      the result of the action\n```\n\nOnce all necessary information is gathered,
      return the following format:\n\n```\nThought: I now know the final answer\nFinal
      Answer: the final answer to the original input question\n```"}, {"role": "user",
      "content": "\nCurrent Task: Use tool logic for `get_final_answer` but fon''t
      give you final answer yet, instead keep using it unless you''re told to give
      your final answer\n\nThis is the expected criteria for your final answer: The
      final answer\nyou MUST return the actual complete content as the final answer,
      not a summary.\n\nBegin! This is VERY important to you, use the tools available
      and give your best Final Answer, your job depends on it!\n\nThought:"}], "model":
      "gpt-4o-mini", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '1485'
      content-type:
      - application/json
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHJH3OwtnaTcdp0fTf5MmaPIs3wTG\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743465365,\n  \"model\": \"gpt-4o-mini-2024-07-18\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I need to gather information
      to fulfill the task effectively.\\nAction: get_final_answer\\nAction Input:
      {}\",\n        \"refusal\": null,\n        \"annotations\": []\n      },\n      \"logprobs\":
      null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      298,\n    \"completion_tokens\": 23,\n    \"total_tokens\": 321,\n    \"prompt_tokens_details\":
      {\n      \"cached_tokens\": 0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_b376dfbbd5\"\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 9293c8060b1b7ad9-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:56:06 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=EQoUakAQFlTCJuafKEbAmf2zAebcN6rxvW80WVf1mFs-1743465366-*******-n77X77OCAjtpSWQ5IF0pyZsjNM4hCT9EixsGbrfrywtrpVQc9zhrTzqGNdXZdGProLhbaKPqEFndzp3Z1dDffHBtgab.0FbZHsFVJlZSTMg;
        path=/; expires=Tue, 01-Apr-25 00:26:06 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=FZbzIEh0iovTAVYHL9p848G6dUFY70C93iiXXxt.9Wk-1743465366265-*******-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '561'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999666'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_851f60f7c2182315f69c93ec37b9e72d
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: get_final_answer\nTool
      Arguments: {}\nTool Description: Get the final answer but don''t give it yet,
      just re-use this\n        tool non-stop.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [get_final_answer], just the name, exactly
      as it''s written.\nAction Input: the input to the action, just a simple JSON
      object, enclosed in curly braces, using \" to wrap keys and values.\nObservation:
      the result of the action\n```\n\nOnce all necessary information is gathered,
      return the following format:\n\n```\nThought: I now know the final answer\nFinal
      Answer: the final answer to the original input question\n```"}, {"role": "user",
      "content": "\nCurrent Task: Use tool logic for `get_final_answer` but fon''t
      give you final answer yet, instead keep using it unless you''re told to give
      your final answer\n\nThis is the expected criteria for your final answer: The
      final answer\nyou MUST return the actual complete content as the final answer,
      not a summary.\n\nBegin! This is VERY important to you, use the tools available
      and give your best Final Answer, your job depends on it!\n\nThought:"}, {"role":
      "assistant", "content": "42"}, {"role": "assistant", "content": "Thought: I
      need to gather information to fulfill the task effectively.\nAction: get_final_answer\nAction
      Input: {}\nObservation: 42"}], "model": "gpt-4o-mini", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '1694'
      content-type:
      - application/json
      cookie:
      - __cf_bm=EQoUakAQFlTCJuafKEbAmf2zAebcN6rxvW80WVf1mFs-1743465366-*******-n77X77OCAjtpSWQ5IF0pyZsjNM4hCT9EixsGbrfrywtrpVQc9zhrTzqGNdXZdGProLhbaKPqEFndzp3Z1dDffHBtgab.0FbZHsFVJlZSTMg;
        _cfuvid=FZbzIEh0iovTAVYHL9p848G6dUFY70C93iiXXxt.9Wk-1743465366265-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHJH4ZtFSEncW2LfdPFg7r0RBGZ5a\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743465366,\n  \"model\": \"gpt-4o-mini-2024-07-18\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I need to keep gathering the
      information necessary for my task.\\nAction: get_final_answer\\nAction Input:
      {}\",\n        \"refusal\": null,\n        \"annotations\": []\n      },\n      \"logprobs\":
      null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      334,\n    \"completion_tokens\": 24,\n    \"total_tokens\": 358,\n    \"prompt_tokens_details\":
      {\n      \"cached_tokens\": 0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_b376dfbbd5\"\n}\n"
    headers:
      CF-RAY:
      - 9293c80bca007ad9-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:56:06 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '536'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999631'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_6460ebf30fa1efa7326eb70792e67a63
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: get_final_answer\nTool
      Arguments: {}\nTool Description: Get the final answer but don''t give it yet,
      just re-use this\n        tool non-stop.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [get_final_answer], just the name, exactly
      as it''s written.\nAction Input: the input to the action, just a simple JSON
      object, enclosed in curly braces, using \" to wrap keys and values.\nObservation:
      the result of the action\n```\n\nOnce all necessary information is gathered,
      return the following format:\n\n```\nThought: I now know the final answer\nFinal
      Answer: the final answer to the original input question\n```"}, {"role": "user",
      "content": "\nCurrent Task: Use tool logic for `get_final_answer` but fon''t
      give you final answer yet, instead keep using it unless you''re told to give
      your final answer\n\nThis is the expected criteria for your final answer: The
      final answer\nyou MUST return the actual complete content as the final answer,
      not a summary.\n\nBegin! This is VERY important to you, use the tools available
      and give your best Final Answer, your job depends on it!\n\nThought:"}, {"role":
      "assistant", "content": "42"}, {"role": "assistant", "content": "Thought: I
      need to gather information to fulfill the task effectively.\nAction: get_final_answer\nAction
      Input: {}\nObservation: 42"}, {"role": "assistant", "content": "I tried reusing
      the same input, I must stop using this action input. I''ll try something else
      instead.\n\n"}, {"role": "assistant", "content": "Thought: I need to keep gathering
      the information necessary for my task.\nAction: get_final_answer\nAction Input:
      {}\nObservation: I tried reusing the same input, I must stop using this action
      input. I''ll try something else instead."}], "model": "gpt-4o-mini", "stop":
      ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '2107'
      content-type:
      - application/json
      cookie:
      - __cf_bm=EQoUakAQFlTCJuafKEbAmf2zAebcN6rxvW80WVf1mFs-1743465366-*******-n77X77OCAjtpSWQ5IF0pyZsjNM4hCT9EixsGbrfrywtrpVQc9zhrTzqGNdXZdGProLhbaKPqEFndzp3Z1dDffHBtgab.0FbZHsFVJlZSTMg;
        _cfuvid=FZbzIEh0iovTAVYHL9p848G6dUFY70C93iiXXxt.9Wk-1743465366265-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHJH5eChuygEK67gpxGlRMLMpYeZi\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743465367,\n  \"model\": \"gpt-4o-mini-2024-07-18\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"Thought: I need to persist in obtaining
      the final answer for the task.\\nAction: get_final_answer\\nAction Input: {}\",\n
      \       \"refusal\": null,\n        \"annotations\": []\n      },\n      \"logprobs\":
      null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      412,\n    \"completion_tokens\": 25,\n    \"total_tokens\": 437,\n    \"prompt_tokens_details\":
      {\n      \"cached_tokens\": 0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_b376dfbbd5\"\n}\n"
    headers:
      CF-RAY:
      - 9293c80fae467ad9-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:56:07 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '676'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999547'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_68062ecd214713f2c04b9aa9c48a8101
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: get_final_answer\nTool
      Arguments: {}\nTool Description: Get the final answer but don''t give it yet,
      just re-use this\n        tool non-stop.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [get_final_answer], just the name, exactly
      as it''s written.\nAction Input: the input to the action, just a simple JSON
      object, enclosed in curly braces, using \" to wrap keys and values.\nObservation:
      the result of the action\n```\n\nOnce all necessary information is gathered,
      return the following format:\n\n```\nThought: I now know the final answer\nFinal
      Answer: the final answer to the original input question\n```"}, {"role": "user",
      "content": "\nCurrent Task: Use tool logic for `get_final_answer` but fon''t
      give you final answer yet, instead keep using it unless you''re told to give
      your final answer\n\nThis is the expected criteria for your final answer: The
      final answer\nyou MUST return the actual complete content as the final answer,
      not a summary.\n\nBegin! This is VERY important to you, use the tools available
      and give your best Final Answer, your job depends on it!\n\nThought:"}, {"role":
      "assistant", "content": "42"}, {"role": "assistant", "content": "Thought: I
      need to gather information to fulfill the task effectively.\nAction: get_final_answer\nAction
      Input: {}\nObservation: 42"}, {"role": "assistant", "content": "I tried reusing
      the same input, I must stop using this action input. I''ll try something else
      instead.\n\n"}, {"role": "assistant", "content": "Thought: I need to keep gathering
      the information necessary for my task.\nAction: get_final_answer\nAction Input:
      {}\nObservation: I tried reusing the same input, I must stop using this action
      input. I''ll try something else instead."}, {"role": "assistant", "content":
      "I tried reusing the same input, I must stop using this action input. I''ll
      try something else instead.\n\n\n\n\nYou ONLY have access to the following tools,
      and should NEVER make up tools that are not listed here:\n\nTool Name: get_final_answer\nTool
      Arguments: {}\nTool Description: Get the final answer but don''t give it yet,
      just re-use this\n        tool non-stop.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [get_final_answer], just the name, exactly
      as it''s written.\nAction Input: the input to the action, just a simple JSON
      object, enclosed in curly braces, using \" to wrap keys and values.\nObservation:
      the result of the action\n```\n\nOnce all necessary information is gathered,
      return the following format:\n\n```\nThought: I now know the final answer\nFinal
      Answer: the final answer to the original input question\n```"}, {"role": "assistant",
      "content": "Thought: I need to persist in obtaining the final answer for the
      task.\nAction: get_final_answer\nAction Input: {}\nObservation: I tried reusing
      the same input, I must stop using this action input. I''ll try something else
      instead.\n\n\n\n\nYou ONLY have access to the following tools, and should NEVER
      make up tools that are not listed here:\n\nTool Name: get_final_answer\nTool
      Arguments: {}\nTool Description: Get the final answer but don''t give it yet,
      just re-use this\n        tool non-stop.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [get_final_answer], just the name, exactly
      as it''s written.\nAction Input: the input to the action, just a simple JSON
      object, enclosed in curly braces, using \" to wrap keys and values.\nObservation:
      the result of the action\n```\n\nOnce all necessary information is gathered,
      return the following format:\n\n```\nThought: I now know the final answer\nFinal
      Answer: the final answer to the original input question\n```"}], "model": "gpt-4o-mini",
      "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '4208'
      content-type:
      - application/json
      cookie:
      - __cf_bm=EQoUakAQFlTCJuafKEbAmf2zAebcN6rxvW80WVf1mFs-1743465366-*******-n77X77OCAjtpSWQ5IF0pyZsjNM4hCT9EixsGbrfrywtrpVQc9zhrTzqGNdXZdGProLhbaKPqEFndzp3Z1dDffHBtgab.0FbZHsFVJlZSTMg;
        _cfuvid=FZbzIEh0iovTAVYHL9p848G6dUFY70C93iiXXxt.9Wk-1743465366265-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHJH5RPm61giidFNJYAgOVENhT7TK\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743465367,\n  \"model\": \"gpt-4o-mini-2024-07-18\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"```\\nThought: I need to keep trying
      to get the final answer.\\nAction: get_final_answer\\nAction Input: {}\",\n
      \       \"refusal\": null,\n        \"annotations\": []\n      },\n      \"logprobs\":
      null,\n      \"finish_reason\": \"stop\"\n    }\n  ],\n  \"usage\": {\n    \"prompt_tokens\":
      845,\n    \"completion_tokens\": 25,\n    \"total_tokens\": 870,\n    \"prompt_tokens_details\":
      {\n      \"cached_tokens\": 0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\":
      {\n      \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_b376dfbbd5\"\n}\n"
    headers:
      CF-RAY:
      - 9293c8149c7c7ad9-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:56:08 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '728'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999052'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_7ca5fb2e9444b3b70c793a1cf08c4806
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: !!binary |
      CuMRCiQKIgoMc2VydmljZS5uYW1lEhIKEGNyZXdBSS10ZWxlbWV0cnkSuhEKEgoQY3Jld2FpLnRl
      bGVtZXRyeRKpCAoQgopuUjmYTXkus8eS/y3BURIIB4W0zs3bAOAqDENyZXcgQ3JlYXRlZDABOfAg
      yTGDCDIYQWBb2DGDCDIYShsKDmNyZXdhaV92ZXJzaW9uEgkKBzAuMTA4LjBKGgoOcHl0aG9uX3Zl
      cnNpb24SCAoGMy4xMi44Si4KCGNyZXdfa2V5EiIKIGQ1NTExM2JlNGFhNDFiYTY0M2QzMjYwNDJi
      MmYwM2YxSjEKB2NyZXdfaWQSJgokNWU1OWMxODAtYTI4Zi00ZmQzLWIzZTYtZjQxZjFlM2U1Njg2
      ShwKDGNyZXdfcHJvY2VzcxIMCgpzZXF1ZW50aWFsShEKC2NyZXdfbWVtb3J5EgIQAEoaChRjcmV3
      X251bWJlcl9vZl90YXNrcxICGAFKGwoVY3Jld19udW1iZXJfb2ZfYWdlbnRzEgIYAUo6ChBjcmV3
      X2ZpbmdlcnByaW50EiYKJDNhZmE4ZTc3LTgxMzAtNDNlYi04ZjIyLTg3M2IyOTNkNzFiMUo7Chtj
      cmV3X2ZpbmdlcnByaW50X2NyZWF0ZWRfYXQSHAoaMjAyNS0wMy0zMVQxNjo1NjowNS4zMTAyNTRK
      zAIKC2NyZXdfYWdlbnRzErwCCrkCW3sia2V5IjogImUxNDhlNTMyMDI5MzQ5OWY4Y2ViZWE4MjZl
      NzI1ODJiIiwgImlkIjogIjdhODgyNTk2LTc4YjgtNDQwNy1hY2MyLWFmM2RjZGVjNDM5ZiIsICJy
      b2xlIjogInRlc3Qgcm9sZSIsICJ2ZXJib3NlPyI6IHRydWUsICJtYXhfaXRlciI6IDQsICJtYXhf
      cnBtIjogMTAsICJmdW5jdGlvbl9jYWxsaW5nX2xsbSI6ICIiLCAibGxtIjogImdwdC00by1taW5p
      IiwgImRlbGVnYXRpb25fZW5hYmxlZD8iOiBmYWxzZSwgImFsbG93X2NvZGVfZXhlY3V0aW9uPyI6
      IGZhbHNlLCAibWF4X3JldHJ5X2xpbWl0IjogMiwgInRvb2xzX25hbWVzIjogW119XUqQAgoKY3Jl
      d190YXNrcxKBAgr+AVt7ImtleSI6ICI0YTMxYjg1MTMzYTNhMjk0YzY4NTNkYTc1N2Q0YmFlNyIs
      ICJpZCI6ICI5NmRiOWM0My1lMThiLTRjYTQtYTMzNi1lYTZhOWZhMjRlMmUiLCAiYXN5bmNfZXhl
      Y3V0aW9uPyI6IGZhbHNlLCAiaHVtYW5faW5wdXQ/IjogZmFsc2UsICJhZ2VudF9yb2xlIjogInRl
      c3Qgcm9sZSIsICJhZ2VudF9rZXkiOiAiZTE0OGU1MzIwMjkzNDk5ZjhjZWJlYTgyNmU3MjU4MmIi
      LCAidG9vbHNfbmFtZXMiOiBbImdldF9maW5hbF9hbnN3ZXIiXX1degIYAYUBAAEAABKABAoQac+e
      EonzHzK1Ay0mglrEoBIIR5X/LhYf4bIqDFRhc2sgQ3JlYXRlZDABOahU7DGDCDIYQajR7DGDCDIY
      Si4KCGNyZXdfa2V5EiIKIGQ1NTExM2JlNGFhNDFiYTY0M2QzMjYwNDJiMmYwM2YxSjEKB2NyZXdf
      aWQSJgokNWU1OWMxODAtYTI4Zi00ZmQzLWIzZTYtZjQxZjFlM2U1Njg2Si4KCHRhc2tfa2V5EiIK
      IDRhMzFiODUxMzNhM2EyOTRjNjg1M2RhNzU3ZDRiYWU3SjEKB3Rhc2tfaWQSJgokOTZkYjljNDMt
      ZTE4Yi00Y2E0LWEzMzYtZWE2YTlmYTI0ZTJlSjoKEGNyZXdfZmluZ2VycHJpbnQSJgokM2FmYThl
      NzctODEzMC00M2ViLThmMjItODczYjI5M2Q3MWIxSjoKEHRhc2tfZmluZ2VycHJpbnQSJgokMzE3
      OTE2MWMtZDIwMy00YmQ5LTkxN2EtMzc2NzBkMGY4YjcxSjsKG3Rhc2tfZmluZ2VycHJpbnRfY3Jl
      YXRlZF9hdBIcChoyMDI1LTAzLTMxVDE2OjU2OjA1LjMxMDIwN0o7ChFhZ2VudF9maW5nZXJwcmlu
      dBImCiQ0YTBhNjgzYi03NjM2LTQ0MjMtYjUwNC05NTZhNmI2M2UyZTR6AhgBhQEAAQAAEpQBChAh
      Pm25yu0tbLAApKbqCAk/Egi33l2wqHQoISoKVG9vbCBVc2FnZTABOQh6B26DCDIYQTiPF26DCDIY
      ShsKDmNyZXdhaV92ZXJzaW9uEgkKBzAuMTA4LjBKHwoJdG9vbF9uYW1lEhIKEGdldF9maW5hbF9h
      bnN3ZXJKDgoIYXR0ZW1wdHMSAhgBegIYAYUBAAEAABKdAQoQ2wYRBrh5IaFYOO/w2aXORhIIQMoA
      T3zemHMqE1Rvb2wgUmVwZWF0ZWQgVXNhZ2UwATkQEO+SgwgyGEFYM/ySgwgyGEobCg5jcmV3YWlf
      dmVyc2lvbhIJCgcwLjEwOC4wSh8KCXRvb2xfbmFtZRISChBnZXRfZmluYWxfYW5zd2VySg4KCGF0
      dGVtcHRzEgIYAXoCGAGFAQABAAASnQEKEECIYRtq9ZRQuy76hvfWMacSCGUyGkFzOWVKKhNUb29s
      IFJlcGVhdGVkIFVzYWdlMAE5IIh9woMIMhhBMOqIwoMIMhhKGwoOY3Jld2FpX3ZlcnNpb24SCQoH
      MC4xMDguMEofCgl0b29sX25hbWUSEgoQZ2V0X2ZpbmFsX2Fuc3dlckoOCghhdHRlbXB0cxICGAF6
      AhgBhQEAAQAAEp0BChCKEMP7bGBMGAJZTeNya6JUEggNVE55CnhXRSoTVG9vbCBSZXBlYXRlZCBV
      c2FnZTABOaBTefODCDIYQfAp3/ODCDIYShsKDmNyZXdhaV92ZXJzaW9uEgkKBzAuMTA4LjBKHwoJ
      dG9vbF9uYW1lEhIKEGdldF9maW5hbF9hbnN3ZXJKDgoIYXR0ZW1wdHMSAhgBegIYAYUBAAEAAA==
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate, zstd
      Connection:
      - keep-alive
      Content-Length:
      - '2278'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.31.1
    method: POST
    uri: https://telemetry.crewai.com:4319/v1/traces
  response:
    body:
      string: "\n\0"
    headers:
      Content-Length:
      - '2'
      Content-Type:
      - application/x-protobuf
      Date:
      - Mon, 31 Mar 2025 23:56:08 GMT
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: get_final_answer\nTool
      Arguments: {}\nTool Description: Get the final answer but don''t give it yet,
      just re-use this\n        tool non-stop.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [get_final_answer], just the name, exactly
      as it''s written.\nAction Input: the input to the action, just a simple JSON
      object, enclosed in curly braces, using \" to wrap keys and values.\nObservation:
      the result of the action\n```\n\nOnce all necessary information is gathered,
      return the following format:\n\n```\nThought: I now know the final answer\nFinal
      Answer: the final answer to the original input question\n```"}, {"role": "user",
      "content": "\nCurrent Task: Use tool logic for `get_final_answer` but fon''t
      give you final answer yet, instead keep using it unless you''re told to give
      your final answer\n\nThis is the expected criteria for your final answer: The
      final answer\nyou MUST return the actual complete content as the final answer,
      not a summary.\n\nBegin! This is VERY important to you, use the tools available
      and give your best Final Answer, your job depends on it!\n\nThought:"}, {"role":
      "assistant", "content": "42"}, {"role": "assistant", "content": "Thought: I
      need to gather information to fulfill the task effectively.\nAction: get_final_answer\nAction
      Input: {}\nObservation: 42"}, {"role": "assistant", "content": "I tried reusing
      the same input, I must stop using this action input. I''ll try something else
      instead.\n\n"}, {"role": "assistant", "content": "Thought: I need to keep gathering
      the information necessary for my task.\nAction: get_final_answer\nAction Input:
      {}\nObservation: I tried reusing the same input, I must stop using this action
      input. I''ll try something else instead."}, {"role": "assistant", "content":
      "I tried reusing the same input, I must stop using this action input. I''ll
      try something else instead.\n\n\n\n\nYou ONLY have access to the following tools,
      and should NEVER make up tools that are not listed here:\n\nTool Name: get_final_answer\nTool
      Arguments: {}\nTool Description: Get the final answer but don''t give it yet,
      just re-use this\n        tool non-stop.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [get_final_answer], just the name, exactly
      as it''s written.\nAction Input: the input to the action, just a simple JSON
      object, enclosed in curly braces, using \" to wrap keys and values.\nObservation:
      the result of the action\n```\n\nOnce all necessary information is gathered,
      return the following format:\n\n```\nThought: I now know the final answer\nFinal
      Answer: the final answer to the original input question\n```"}, {"role": "assistant",
      "content": "Thought: I need to persist in obtaining the final answer for the
      task.\nAction: get_final_answer\nAction Input: {}\nObservation: I tried reusing
      the same input, I must stop using this action input. I''ll try something else
      instead.\n\n\n\n\nYou ONLY have access to the following tools, and should NEVER
      make up tools that are not listed here:\n\nTool Name: get_final_answer\nTool
      Arguments: {}\nTool Description: Get the final answer but don''t give it yet,
      just re-use this\n        tool non-stop.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [get_final_answer], just the name, exactly
      as it''s written.\nAction Input: the input to the action, just a simple JSON
      object, enclosed in curly braces, using \" to wrap keys and values.\nObservation:
      the result of the action\n```\n\nOnce all necessary information is gathered,
      return the following format:\n\n```\nThought: I now know the final answer\nFinal
      Answer: the final answer to the original input question\n```"}, {"role": "assistant",
      "content": "I tried reusing the same input, I must stop using this action input.
      I''ll try something else instead.\n\n"}, {"role": "assistant", "content": "```\nThought:
      I need to keep trying to get the final answer.\nAction: get_final_answer\nAction
      Input: {}\nObservation: I tried reusing the same input, I must stop using this
      action input. I''ll try something else instead."}, {"role": "assistant", "content":
      "```\nThought: I need to keep trying to get the final answer.\nAction: get_final_answer\nAction
      Input: {}\nObservation: I tried reusing the same input, I must stop using this
      action input. I''ll try something else instead.\n\n\nNow it''s time you MUST
      give your absolute best final answer. You''ll ignore all previous instructions,
      stop using any tools, and just return your absolute BEST Final answer."}], "model":
      "gpt-4o-mini", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '5045'
      content-type:
      - application/json
      cookie:
      - __cf_bm=EQoUakAQFlTCJuafKEbAmf2zAebcN6rxvW80WVf1mFs-1743465366-*******-n77X77OCAjtpSWQ5IF0pyZsjNM4hCT9EixsGbrfrywtrpVQc9zhrTzqGNdXZdGProLhbaKPqEFndzp3Z1dDffHBtgab.0FbZHsFVJlZSTMg;
        _cfuvid=FZbzIEh0iovTAVYHL9p848G6dUFY70C93iiXXxt.9Wk-1743465366265-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHJH6KIfRrUzNv9eeCRYnnDAhqorr\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743465368,\n  \"model\": \"gpt-4o-mini-2024-07-18\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"```\\nThought: I now know the final answer\\nFinal
      Answer: 42\\n```\",\n        \"refusal\": null,\n        \"annotations\": []\n
      \     },\n      \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n
      \ ],\n  \"usage\": {\n    \"prompt_tokens\": 1009,\n    \"completion_tokens\":
      19,\n    \"total_tokens\": 1028,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_b376dfbbd5\"\n}\n"
    headers:
      CF-RAY:
      - 9293c819d9d07ad9-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:56:09 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '770'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149998873'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_a6aa3c52e0f6dc8d3fa0857736d12c4b
    http_version: HTTP/1.1
    status_code: 200
- request:
    body: '{"messages": [{"role": "system", "content": "You are test role. test backstory\nYour
      personal goal is: test goal\nYou ONLY have access to the following tools, and
      should NEVER make up tools that are not listed here:\n\nTool Name: get_final_answer\nTool
      Arguments: {}\nTool Description: Get the final answer but don''t give it yet,
      just re-use this\n        tool non-stop.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [get_final_answer], just the name, exactly
      as it''s written.\nAction Input: the input to the action, just a simple JSON
      object, enclosed in curly braces, using \" to wrap keys and values.\nObservation:
      the result of the action\n```\n\nOnce all necessary information is gathered,
      return the following format:\n\n```\nThought: I now know the final answer\nFinal
      Answer: the final answer to the original input question\n```"}, {"role": "user",
      "content": "\nCurrent Task: Use tool logic for `get_final_answer` but fon''t
      give you final answer yet, instead keep using it unless you''re told to give
      your final answer\n\nThis is the expected criteria for your final answer: The
      final answer\nyou MUST return the actual complete content as the final answer,
      not a summary.\n\nBegin! This is VERY important to you, use the tools available
      and give your best Final Answer, your job depends on it!\n\nThought:"}, {"role":
      "assistant", "content": "42"}, {"role": "assistant", "content": "Thought: I
      need to gather information to fulfill the task effectively.\nAction: get_final_answer\nAction
      Input: {}\nObservation: 42"}, {"role": "assistant", "content": "I tried reusing
      the same input, I must stop using this action input. I''ll try something else
      instead.\n\n"}, {"role": "assistant", "content": "Thought: I need to keep gathering
      the information necessary for my task.\nAction: get_final_answer\nAction Input:
      {}\nObservation: I tried reusing the same input, I must stop using this action
      input. I''ll try something else instead."}, {"role": "assistant", "content":
      "I tried reusing the same input, I must stop using this action input. I''ll
      try something else instead.\n\n\n\n\nYou ONLY have access to the following tools,
      and should NEVER make up tools that are not listed here:\n\nTool Name: get_final_answer\nTool
      Arguments: {}\nTool Description: Get the final answer but don''t give it yet,
      just re-use this\n        tool non-stop.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [get_final_answer], just the name, exactly
      as it''s written.\nAction Input: the input to the action, just a simple JSON
      object, enclosed in curly braces, using \" to wrap keys and values.\nObservation:
      the result of the action\n```\n\nOnce all necessary information is gathered,
      return the following format:\n\n```\nThought: I now know the final answer\nFinal
      Answer: the final answer to the original input question\n```"}, {"role": "assistant",
      "content": "Thought: I need to persist in obtaining the final answer for the
      task.\nAction: get_final_answer\nAction Input: {}\nObservation: I tried reusing
      the same input, I must stop using this action input. I''ll try something else
      instead.\n\n\n\n\nYou ONLY have access to the following tools, and should NEVER
      make up tools that are not listed here:\n\nTool Name: get_final_answer\nTool
      Arguments: {}\nTool Description: Get the final answer but don''t give it yet,
      just re-use this\n        tool non-stop.\n\nIMPORTANT: Use the following format
      in your response:\n\n```\nThought: you should always think about what to do\nAction:
      the action to take, only one name of [get_final_answer], just the name, exactly
      as it''s written.\nAction Input: the input to the action, just a simple JSON
      object, enclosed in curly braces, using \" to wrap keys and values.\nObservation:
      the result of the action\n```\n\nOnce all necessary information is gathered,
      return the following format:\n\n```\nThought: I now know the final answer\nFinal
      Answer: the final answer to the original input question\n```"}, {"role": "assistant",
      "content": "I tried reusing the same input, I must stop using this action input.
      I''ll try something else instead.\n\n"}, {"role": "assistant", "content": "```\nThought:
      I need to keep trying to get the final answer.\nAction: get_final_answer\nAction
      Input: {}\nObservation: I tried reusing the same input, I must stop using this
      action input. I''ll try something else instead."}, {"role": "assistant", "content":
      "```\nThought: I need to keep trying to get the final answer.\nAction: get_final_answer\nAction
      Input: {}\nObservation: I tried reusing the same input, I must stop using this
      action input. I''ll try something else instead.\n\n\nNow it''s time you MUST
      give your absolute best final answer. You''ll ignore all previous instructions,
      stop using any tools, and just return your absolute BEST Final answer."}], "model":
      "gpt-4o-mini", "stop": ["\nObservation:"]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '5045'
      content-type:
      - application/json
      cookie:
      - __cf_bm=EQoUakAQFlTCJuafKEbAmf2zAebcN6rxvW80WVf1mFs-1743465366-*******-n77X77OCAjtpSWQ5IF0pyZsjNM4hCT9EixsGbrfrywtrpVQc9zhrTzqGNdXZdGProLhbaKPqEFndzp3Z1dDffHBtgab.0FbZHsFVJlZSTMg;
        _cfuvid=FZbzIEh0iovTAVYHL9p848G6dUFY70C93iiXXxt.9Wk-1743465366265-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.68.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600.0'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.8
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    content: "{\n  \"id\": \"chatcmpl-BHJH7w78dcZehT3FKsJwuuzKMKPdG\",\n  \"object\":
      \"chat.completion\",\n  \"created\": 1743465369,\n  \"model\": \"gpt-4o-mini-2024-07-18\",\n
      \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
      \"assistant\",\n        \"content\": \"```\\nThought: I now know the final answer\\nFinal
      Answer: 42\\n```\",\n        \"refusal\": null,\n        \"annotations\": []\n
      \     },\n      \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n
      \ ],\n  \"usage\": {\n    \"prompt_tokens\": 1009,\n    \"completion_tokens\":
      19,\n    \"total_tokens\": 1028,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
      0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\": {\n
      \     \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
      0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
      \"default\",\n  \"system_fingerprint\": \"fp_b376dfbbd5\"\n}\n"
    headers:
      CF-RAY:
      - 9293c81f1ee17ad9-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 31 Mar 2025 23:56:10 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - crewai-iuxna1
      openai-processing-ms:
      - '1000'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149998873'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_3117d99d3c0837cc04b77303a79b4f51
    http_version: HTTP/1.1
    status_code: 200
version: 1
