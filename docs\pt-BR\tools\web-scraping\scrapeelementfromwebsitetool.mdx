---
title: Ferramenta de Extração de Elementos de Website
description: A `ScrapeElementFromWebsiteTool` permite que agentes CrewAI extraiam elementos específicos de websites usando seletores CSS.
icon: code
---

# `ScrapeElementFromWebsiteTool`

## Descrição

A `ScrapeElementFromWebsiteTool` foi projetada para extrair elementos específicos de websites utilizando seletores CSS. Esta ferramenta permite que agentes CrewAI capturem conteúdos direcionados de páginas web, tornando-se útil para tarefas de extração de dados em que apenas partes específicas de uma página são necessárias.

## Instalação

Para utilizar esta ferramenta, você precisa instalar as dependências necessárias:

```shell
uv add requests beautifulsoup4
```

## Passos para Começar

Para usar a `ScrapeElementFromWebsiteTool` de maneira eficaz, siga estes passos:

1. **Instale as Dependências**: Instale os pacotes necessários com o comando acima.
2. **Identifique os Seletores CSS**: Determine os seletores CSS dos elementos que deseja extrair do site.
3. **Inicialize a Ferramenta**: Crie uma instância da ferramenta com os parâmetros necessários.

## Exemplo

O exemplo abaixo demonstra como usar a `ScrapeElementFromWebsiteTool` para extrair elementos específicos de um website:

```python Code
from crewai import Agent, Task, Crew
from crewai_tools import ScrapeElementFromWebsiteTool

# Inicie a ferramenta
scrape_tool = ScrapeElementFromWebsiteTool()

# Defina um agente que utilizará a ferramenta
web_scraper_agent = Agent(
    role="Web Scraper",
    goal="Extrair informações específicas de websites",
    backstory="Um especialista em web scraping capaz de capturar conteúdos direcionados de páginas web.",
    tools=[scrape_tool],
    verbose=True,
)

# Exemplo de tarefa para extrair manchetes de um site de notícias
scrape_task = Task(
    description="Extraia as principais manchetes da página inicial da CNN. Use o seletor CSS '.headline' para atingir os elementos de manchete.",
    expected_output="Uma lista das principais manchetes da CNN.",
    agent=web_scraper_agent,
)

# Crie e execute o crew
crew = Crew(agents=[web_scraper_agent], tasks=[scrape_task])
result = crew.kickoff()
```

Você também pode inicializar a ferramenta com parâmetros pré-definidos:

```python Code
# Inicialize a ferramenta com parâmetros pré-definidos
scrape_tool = ScrapeElementFromWebsiteTool(
    website_url="https://www.example.com",
    css_element=".main-content"
)
```

## Parâmetros

A `ScrapeElementFromWebsiteTool` aceita os seguintes parâmetros durante a inicialização:

- **website_url**: Opcional. A URL do website a ser extraído. Se fornecido na inicialização, o agente não precisará especificá-lo ao utilizar a ferramenta.
- **css_element**: Opcional. O seletor CSS para os elementos a serem extraídos. Se fornecido na inicialização, o agente não precisará especificá-lo ao utilizar a ferramenta.
- **cookies**: Opcional. Um dicionário contendo cookies a serem enviados com a requisição. Isso pode ser útil para sites que requerem autenticação.

## Uso

Ao utilizar a `ScrapeElementFromWebsiteTool` com um agente, o agente precisará fornecer os seguintes parâmetros (a menos que já tenham sido especificados na inicialização):

- **website_url**: A URL do website a ser extraído.
- **css_element**: O seletor CSS dos elementos a serem extraídos.

A ferramenta retornará o conteúdo de texto de todos os elementos que correspondam ao seletor CSS, separados por quebras de linha.

```python Code
# Exemplo de uso da ferramenta com um agente
web_scraper_agent = Agent(
    role="Web Scraper",
    goal="Extrair elementos específicos de websites",
    backstory="Um especialista em web scraping capaz de extrair conteúdo direcionado por meio de seletores CSS.",
    tools=[scrape_tool],
    verbose=True,
)

# Crie uma tarefa para o agente extrair elementos específicos
extract_task = Task(
    description="""
    Extraia todos os títulos de produtos da seção de produtos em destaque no example.com.
    Use o seletor CSS '.product-title' para atingir os elementos de título.
    """,
    expected_output="Uma lista de títulos de produtos do site",
    agent=web_scraper_agent,
)

# Execute a tarefa utilizando um crew
crew = Crew(agents=[web_scraper_agent], tasks=[extract_task])
result = crew.kickoff()
```

## Detalhes de Implementação

A `ScrapeElementFromWebsiteTool` utiliza a biblioteca `requests` para buscar a página web e `BeautifulSoup` para analisar o HTML e extrair os elementos especificados:

```python Code
class ScrapeElementFromWebsiteTool(BaseTool):
    name: str = "Read a website content"
    description: str = "A tool that can be used to read a website content."
    
    # Implementation details...
    
    def _run(self, **kwargs: Any) -> Any:
        website_url = kwargs.get("website_url", self.website_url)
        css_element = kwargs.get("css_element", self.css_element)
        page = requests.get(
            website_url,
            headers=self.headers,
            cookies=self.cookies if self.cookies else {},
        )
        parsed = BeautifulSoup(page.content, "html.parser")
        elements = parsed.select(css_element)
        return "\n".join([element.get_text() for element in elements])
```

## Conclusão

A `ScrapeElementFromWebsiteTool` oferece uma maneira poderosa de extrair elementos específicos de websites utilizando seletores CSS. Ao possibilitar que agentes direcionem apenas o conteúdo que necessitam, ela torna as tarefas de web scraping mais eficientes e objetivas. Esta ferramenta é particularmente útil para extração de dados, monitoramento de conteúdos e tarefas de pesquisa em que informações específicas precisam ser extraídas de páginas web.